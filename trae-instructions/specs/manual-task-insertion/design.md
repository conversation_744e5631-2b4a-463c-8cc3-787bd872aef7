# Design Document

## Overview

This design document outlines the implementation of a comprehensive manual task insertion system for the KI Projekt-Planer application. The feature will enhance the existing task management capabilities by allowing users to insert new tasks at any position within the task hierarchy through intuitive visual indicators and keyboard shortcuts. The design builds upon the existing task structure and leverages the current `handleAddTaskAfter` functionality while adding sophisticated hover-based insertion points and improved user experience.

## Architecture

### Design Philosophy
- **Seamless Integration**: Build upon existing task management infrastructure without disrupting current functionality
- **Visual Clarity**: Provide clear, intuitive visual feedback for insertion points
- **Flexible Positioning**: Support insertion at any level of the task hierarchy
- **Performance Optimized**: Minimize re-renders and maintain smooth interactions
- **Accessibility First**: Ensure keyboard navigation and screen reader compatibility

### Current System Analysis

The application already has a solid foundation for task insertion:
- `handleAddTaskAfter` function supports inserting tasks after any given task
- Special `__FIRST__` identifier for inserting at the beginning of lists
- Recursive task structure with proper parent-child relationships
- TaskList component with basic insertion buttons between tasks

### Enhancement Strategy

The design will enhance the current system by:
1. Adding hover-based insertion indicators throughout the task hierarchy
2. Implementing insertion points between parent tasks and their subtasks
3. Adding keyboard shortcuts for quick task insertion
4. Improving visual feedback and user experience
5. Maintaining backward compatibility with existing functionality

## Components and Interfaces

### Enhanced TaskList Component

#### New Props Interface
```typescript
interface TaskListProps {
  // ... existing props
  showInsertionIndicators?: boolean;
  onInsertTask?: (position: InsertionPosition) => void;
  insertionMode?: 'hover' | 'always' | 'keyboard';
}

interface InsertionPosition {
  type: 'before' | 'after' | 'between_parent_child';
  targetTaskId: string;
  parentId: string | null;
  level: number;
}
```

#### Insertion Indicator Component
```typescript
interface InsertionIndicatorProps {
  position: InsertionPosition;
  isVisible: boolean;
  onInsert: () => void;
  className?: string;
}

const InsertionIndicator: React.FC<InsertionIndicatorProps> = ({
  position,
  isVisible,
  onInsert,
  className
}) => {
  return (
    <div 
      className={`insertion-indicator ${isVisible ? 'visible' : 'hidden'} ${className}`}
      onClick={onInsert}
    >
      <div className="insertion-line" />
      <button className="insertion-button">
        <Plus size={12} />
      </button>
    </div>
  );
};
```

### Enhanced TaskItem Component

#### New State Management
```typescript
interface TaskItemState {
  // ... existing state
  hoveredInsertionPoint: InsertionPosition | null;
  showInsertionIndicators: boolean;
}

interface TaskItemProps {
  // ... existing props
  onInsertTask: (position: InsertionPosition) => void;
  insertionMode: 'hover' | 'always' | 'keyboard';
  keyboardShortcuts: KeyboardShortcuts;
}
```

#### Insertion Point Detection
```typescript
interface InsertionZone {
  id: string;
  type: 'before' | 'after' | 'between_parent_child';
  bounds: DOMRect;
  targetTaskId: string;
  parentId: string | null;
  level: number;
}

const useInsertionZones = (taskElement: HTMLElement, task: Task, level: number) => {
  const [zones, setZones] = useState<InsertionZone[]>([]);
  
  useEffect(() => {
    if (!taskElement) return;
    
    const calculateZones = (): InsertionZone[] => {
      const taskRect = taskElement.getBoundingClientRect();
      const zones: InsertionZone[] = [];
      
      // Before task zone
      zones.push({
        id: `before-${task.id}`,
        type: 'before',
        bounds: new DOMRect(taskRect.left, taskRect.top - 10, taskRect.width, 20),
        targetTaskId: task.id,
        parentId: task.parentId || null,
        level
      });
      
      // After task zone
      zones.push({
        id: `after-${task.id}`,
        type: 'after',
        bounds: new DOMRect(taskRect.left, taskRect.bottom - 10, taskRect.width, 20),
        targetTaskId: task.id,
        parentId: task.parentId || null,
        level
      });
      
      // Between parent and first subtask zone
      if (task.subtasks && task.subtasks.length > 0) {
        const firstSubtaskElement = taskElement.querySelector(`[data-task-id="${task.subtasks[0].id}"]`);
        if (firstSubtaskElement) {
          const firstSubtaskRect = firstSubtaskElement.getBoundingClientRect();
          zones.push({
            id: `between-${task.id}-${task.subtasks[0].id}`,
            type: 'between_parent_child',
            bounds: new DOMRect(
              taskRect.left, 
              taskRect.bottom, 
              taskRect.width, 
              firstSubtaskRect.top - taskRect.bottom
            ),
            targetTaskId: task.id,
            parentId: task.parentId || null,
            level
          });
        }
      }
      
      return zones;
    };
    
    setZones(calculateZones());
    
    // Recalculate on resize
    const handleResize = () => setZones(calculateZones());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [taskElement, task, level]);
  
  return zones;
};
```

### Keyboard Shortcuts System

#### Shortcut Configuration
```typescript
interface KeyboardShortcuts {
  insertAfter: string; // Default: 'Enter'
  insertBefore: string; // Default: 'Shift+Enter'
  insertSubtask: string; // Default: 'Tab+Enter'
  insertBetweenParentChild: string; // Default: 'Ctrl+Enter'
}

const useKeyboardInsertion = (
  focusedTaskId: string | null,
  shortcuts: KeyboardShortcuts,
  onInsertTask: (position: InsertionPosition) => void
) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!focusedTaskId) return;
      
      const isShortcut = (shortcut: string) => {
        const keys = shortcut.split('+');
        return keys.every(key => {
          switch (key.toLowerCase()) {
            case 'ctrl': return e.ctrlKey;
            case 'shift': return e.shiftKey;
            case 'alt': return e.altKey;
            case 'meta': return e.metaKey;
            default: return e.key.toLowerCase() === key.toLowerCase();
          }
        });
      };
      
      if (isShortcut(shortcuts.insertAfter)) {
        e.preventDefault();
        onInsertTask({
          type: 'after',
          targetTaskId: focusedTaskId,
          parentId: null, // Will be resolved by the handler
          level: 0 // Will be resolved by the handler
        });
      }
      // ... handle other shortcuts
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [focusedTaskId, shortcuts, onInsertTask]);
};
```

## Data Models

### Enhanced Task Interface
```typescript
interface Task {
  // ... existing properties
  
  // Insertion-related metadata
  insertionMetadata?: {
    canInsertBefore: boolean;
    canInsertAfter: boolean;
    canInsertBetweenParentChild: boolean;
    insertionRestrictions?: string[];
  };
  
  // Focus and selection state
  focusState?: {
    isFocused: boolean;
    isSelected: boolean;
    lastFocusedAt: Date;
  };
}
```

### Insertion Position Model
```typescript
interface InsertionPosition {
  type: 'before' | 'after' | 'between_parent_child';
  targetTaskId: string;
  parentId: string | null;
  level: number;
  
  // Additional context
  context?: {
    siblingCount: number;
    insertionIndex: number;
    hierarchyPath: string[];
  };
}

interface InsertionResult {
  success: boolean;
  newTaskId?: string;
  error?: string;
  position: InsertionPosition;
}
```

### Insertion State Management
```typescript
interface InsertionState {
  activeInsertionPoint: InsertionPosition | null;
  hoveredZone: InsertionZone | null;
  keyboardMode: boolean;
  insertionHistory: InsertionPosition[];
  
  // Performance optimization
  lastCalculatedZones: Map<string, InsertionZone[]>;
  zoneCalculationCache: Map<string, { zones: InsertionZone[], timestamp: number }>;
}
```

## Error Handling

### Insertion Validation
```typescript
interface InsertionValidator {
  validatePosition(position: InsertionPosition, tasks: Task[]): ValidationResult;
  canInsertAtPosition(position: InsertionPosition): boolean;
  getInsertionConstraints(taskId: string): InsertionConstraints;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestedAlternatives?: InsertionPosition[];
}

interface InsertionConstraints {
  maxDepth: number;
  allowedPositions: ('before' | 'after' | 'between_parent_child')[];
  parentRestrictions: string[];
}
```

### Error Recovery Strategies
1. **Invalid Position Handling**: Fallback to nearest valid insertion point
2. **Concurrent Modification**: Detect and resolve conflicts when multiple users insert tasks
3. **Performance Degradation**: Disable hover indicators for very large task trees
4. **Keyboard Navigation Conflicts**: Provide alternative shortcuts when conflicts are detected

## Testing Strategy

### Unit Testing
```typescript
describe('Manual Task Insertion', () => {
  describe('InsertionZone Calculation', () => {
    it('should calculate correct zones for task with subtasks');
    it('should handle edge cases with deeply nested tasks');
    it('should update zones when task structure changes');
  });
  
  describe('Keyboard Shortcuts', () => {
    it('should insert task after current task with Enter');
    it('should insert task before current task with Shift+Enter');
    it('should handle conflicting shortcuts gracefully');
  });
  
  describe('Position Validation', () => {
    it('should validate insertion positions correctly');
    it('should prevent invalid insertions');
    it('should suggest alternative positions when needed');
  });
});
```

### Integration Testing
- Test insertion with existing AI-generated tasks
- Verify compatibility with task editing and deletion
- Test performance with large task hierarchies
- Validate export functionality includes inserted tasks

### User Experience Testing
- Test hover responsiveness and visual feedback
- Validate keyboard navigation flow
- Test mobile touch interaction
- Verify accessibility with screen readers

## Implementation Approach

### Phase 1: Core Insertion Infrastructure
1. Enhance `handleAddTaskAfter` to support new insertion types
2. Implement `InsertionPosition` data model
3. Create basic insertion validation system
4. Add insertion state management to main application

### Phase 2: Visual Insertion Indicators
1. Implement `InsertionIndicator` component
2. Add hover detection system to TaskItem
3. Create insertion zone calculation logic
4. Implement smooth animations for indicator appearance

### Phase 3: Keyboard Shortcuts
1. Design keyboard shortcut system
2. Implement focus management for tasks
3. Add keyboard event handling
4. Create shortcut conflict resolution

### Phase 4: Advanced Features
1. Add insertion between parent and child tasks
2. Implement insertion history and undo
3. Add bulk insertion capabilities
4. Optimize performance for large task trees

### Phase 5: Polish and Optimization
1. Refine animations and transitions
2. Improve mobile touch experience
3. Add accessibility enhancements
4. Performance optimization and caching

## Design Specifications

### Visual Design

#### Insertion Indicators
- **Hover State**: Subtle blue line with small plus icon
- **Active State**: Brighter blue with larger, more prominent plus icon
- **Animation**: 200ms ease-in-out transition for appearance/disappearance
- **Positioning**: Centered horizontally, positioned at insertion point

#### Color Scheme
```css
.insertion-indicator {
  --insertion-line-color: rgba(99, 102, 241, 0.6);
  --insertion-button-bg: rgb(99, 102, 241);
  --insertion-button-hover: rgb(79, 70, 229);
  --insertion-line-width: 2px;
  --insertion-button-size: 20px;
}
```

#### Responsive Behavior
- **Desktop**: 20px insertion zones with hover detection
- **Tablet**: 30px insertion zones with touch-friendly buttons
- **Mobile**: 40px insertion zones with larger touch targets

### Keyboard Shortcuts
- **Insert After**: `Enter` (when task is focused)
- **Insert Before**: `Shift + Enter`
- **Insert Subtask**: `Tab + Enter`
- **Insert Between Parent/Child**: `Ctrl + Enter`

### Performance Specifications
- **Zone Calculation**: < 16ms for up to 100 tasks
- **Hover Response**: < 100ms indicator appearance
- **Insertion Animation**: 300ms smooth transition
- **Memory Usage**: < 1MB additional for insertion state

## Accessibility Considerations

### Keyboard Navigation
- All insertion points accessible via keyboard
- Clear focus indicators for insertion zones
- Logical tab order through insertion points
- Escape key cancels insertion mode

### Screen Reader Support
- ARIA labels for insertion indicators
- Announcements when insertion points become available
- Clear descriptions of insertion position and context
- Proper semantic markup for insertion controls

### Visual Accessibility
- High contrast insertion indicators
- Sufficient color contrast for all states
- Non-color indicators for insertion points
- Scalable insertion controls for zoom levels

## Integration Points

### Existing System Integration
- Seamless integration with current `handleAddTaskAfter` function
- Compatibility with AI task generation and elaboration
- Integration with task editing and deletion workflows
- Preservation of export functionality

### Future Extensibility
- Plugin system for custom insertion behaviors
- API endpoints for programmatic task insertion
- Integration with collaborative editing features
- Support for task templates and insertion presets