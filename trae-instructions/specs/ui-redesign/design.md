# Design Document

## Overview

This design document outlines a comprehensive UI redesign for the KI Projekt-Planer application, focusing on creating a cleaner, more modern interface that eliminates visual clutter while enhancing functionality. The redesign will transform the current card-based layout with heavy borders into a minimalist, content-focused design that improves user experience and visual hierarchy.

## Architecture

### Design Philosophy
- **Minimalism First**: Remove all unnecessary visual elements that don't serve a functional purpose
- **Content-Centric**: Prioritize readability and content visibility over decorative elements
- **Progressive Disclosure**: Use subtle visual cues to indicate hierarchy and relationships
- **Responsive Design**: Ensure the clean design works across all device sizes

### Visual Design System

#### Color Palette
- **Primary Background**: Clean whites/dark grays with subtle gradients
- **Task Containers**: Minimal background differentiation using subtle tints
- **Accent Colors**: Maintain existing indigo/blue theme for interactive elements
- **Text Hierarchy**: Clear contrast ratios for different content levels

#### Typography
- **Task Titles**: Larger, bolder fonts with better spacing
- **Descriptions**: Lighter weight with improved line height
- **AI Content**: Distinct styling to separate from user content
- **Metadata**: Smaller, muted text for counts and secondary information

## Components and Interfaces

### TaskItem Component Redesign

#### Current Issues
- Heavy card borders create visual noise
- Excessive use of Card/CardHeader/CardContent components
- Border-left indicators add unnecessary visual weight
- Button positioning creates cluttered appearance

#### New Design Approach
```typescript
interface CleanTaskItemProps {
  task: Task;
  level: number;
  // ... existing props
  showWordCount?: boolean;
  showTokenCount?: boolean;
}
```

#### Visual Structure
1. **Container**: Replace Card with simple div with subtle background
2. **Header**: Streamlined title area with inline editing
3. **Content**: Clean content area without heavy borders
4. **Actions**: Floating action buttons that appear on hover
5. **Hierarchy**: Use indentation and subtle visual cues instead of borders

### AI Content Section Redesign

#### Enhanced Metrics Display
```typescript
interface AIContentMetrics {
  characterCount: number;
  wordCount: number;
  estimatedTokenCount: number;
}

interface AIContentHeaderProps {
  isVisible: boolean;
  isLoading: boolean;
  metrics: AIContentMetrics;
  onToggleVisibility: () => void;
}
```

#### Content Statistics
- **Character Count**: Direct character count from content
- **Word Count**: Split by whitespace and filter empty strings
- **Token Count**: Estimated using character count / 4 (rough GPT token estimation)
- **Display**: Show in header alongside existing collapse button

### Collapse/Expand Functionality Fix

#### Current Problem Analysis
- State management issues with visibility toggles
- Inconsistent animation states
- Local storage persistence conflicts

#### Solution Architecture
```typescript
interface CollapseState {
  [taskId: string]: {
    aiContentVisible: boolean;
    subtasksVisible: boolean;
  }
}

// Centralized state management
const useCollapseState = (taskId: string) => {
  const [state, setState] = useState(() => getStoredState(taskId));
  
  const toggle = useCallback((section: 'aiContent' | 'subtasks') => {
    setState(prev => {
      const newState = { ...prev, [`${section}Visible`]: !prev[`${section}Visible`] };
      storeState(taskId, newState);
      return newState;
    });
  }, [taskId]);
  
  return { state, toggle };
};
```

## Data Models

### Enhanced Task Interface
```typescript
interface Task {
  // ... existing properties
  
  // UI state properties
  uiState?: {
    aiContentVisible: boolean;
    isExpanded: boolean;
    lastModified: Date;
  };
  
  // Content metrics
  contentMetrics?: {
    characterCount: number;
    wordCount: number;
    estimatedTokens: number;
  };
}
```

### Content Metrics Calculation
```typescript
interface ContentMetrics {
  calculateMetrics(content: string): {
    characters: number;
    words: number;
    estimatedTokens: number;
  };
}
```

## Error Handling

### Collapse State Recovery
- Implement fallback states when localStorage is corrupted
- Graceful degradation when animations fail
- Error boundaries around collapse components

### Content Metrics Fallbacks
- Handle empty or malformed content gracefully
- Provide default values when calculations fail
- Cache calculations to prevent repeated processing

## Testing Strategy

### Visual Regression Testing
- Screenshot comparisons for layout changes
- Cross-browser compatibility testing
- Mobile responsiveness validation

### Interaction Testing
- Collapse/expand functionality across different states
- Content metrics accuracy validation
- Performance testing with large task trees

### Accessibility Testing
- Keyboard navigation with new layout
- Screen reader compatibility
- Color contrast validation

## Implementation Approach

### Phase 1: Core Layout Redesign
1. Remove Card components from TaskItem
2. Implement clean container styling
3. Redesign task hierarchy visualization
4. Update spacing and typography

### Phase 2: Enhanced AI Content Section
1. Add content metrics calculation
2. Redesign AI content header with statistics
3. Implement improved collapse animations
4. Fix state management issues

### Phase 3: Interactive Improvements
1. Redesign hover states and transitions
2. Implement floating action buttons
3. Improve mobile touch targets
4. Add subtle loading states

### Phase 4: Polish and Optimization
1. Performance optimization for large task lists
2. Animation refinements
3. Accessibility improvements
4. Cross-browser testing and fixes

## Design Specifications

### Spacing System
- **Base Unit**: 4px (0.25rem)
- **Task Indentation**: 24px per level (reduced from current)
- **Content Padding**: 16px (reduced from current card padding)
- **Button Spacing**: 8px between action buttons

### Animation Specifications
- **Collapse/Expand**: 300ms ease-in-out transition
- **Hover States**: 150ms ease-out transition
- **Loading States**: Subtle pulse animation at 1.5s intervals

### Responsive Breakpoints
- **Mobile**: < 640px - Stack actions vertically, reduce padding
- **Tablet**: 640px - 1024px - Maintain horizontal layout with adjusted spacing
- **Desktop**: > 1024px - Full feature set with optimal spacing

### Content Metrics Display
```
KI-Ausarbeitung                    [1,247 Zeichen | 186 Wörter | ~312 Token] [↓]
```

### Visual Hierarchy
1. **Level 0**: No indentation, full width
2. **Level 1**: 24px left margin, subtle left border
3. **Level 2+**: Additional 24px per level, progressively lighter borders

## Accessibility Considerations

### Keyboard Navigation
- Tab order follows visual hierarchy
- Clear focus indicators on all interactive elements
- Escape key closes expanded sections

### Screen Reader Support
- Proper ARIA labels for collapse/expand buttons
- Content metrics announced when changed
- Hierarchical structure communicated through markup

### Color and Contrast
- Maintain WCAG AA compliance for all text
- Ensure interactive elements have sufficient contrast
- Provide non-color indicators for state changes