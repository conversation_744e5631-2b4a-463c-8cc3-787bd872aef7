# Design Document

## Overview

This design document outlines the technical approach to align the modular KI Projekt-Planer with the monolithic version functionality. The design maintains the clean separation of concerns while ensuring identical behavior and user experience.

## Architecture

### High-Level Architecture

```
src/
├── app/
│   ├── page.tsx (Main App Component)
│   └── layout.tsx (Root Layout)
├── components/
│   ├── TaskList.tsx
│   ├── TaskItem.tsx
│   ├── SolveTaskModal.tsx
│   └── ui/ (Existing UI components)
├── lib/
│   ├── types.ts (Enhanced type definitions)
│   ├── ai/ (AI integration layer)
│   │   ├── gemini.ts (Gemini API client)
│   │   └── context.ts (Context building utilities)
│   ├── hooks/ (Custom React hooks)
│   │   ├── useTheme.ts (Theme management)
│   │   ├── useLibraryLoader.ts (External library loading)
│   │   └── useAutoGrowTextarea.ts (Auto-growing textarea)
│   ├── utils/ (Utility functions)
│   │   ├── htmlParser.ts (HTML entity decoding)
│   │   └── taskOperations.ts (Task manipulation utilities)
│   └── export/ (Enhanced export functions)
│       ├── pdf.ts
│       ├── csv.ts
│       └── markdown.ts
```

## Components and Interfaces

### Enhanced Type Definitions

```typescript
// Enhanced Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  content: string; // AI-generated content (aiContent in monolithic)
  status: TaskStatus;
  assignees: User[];
  subtasks: Task[];
  // New editing states
  isEditing?: boolean;
  isDescriptionEditing?: boolean;
  isAiContentEditing?: boolean;
}

// Context strategies configuration
interface ContextStrategies {
  strategy1: boolean; // Task path
  strategy2: boolean; // Intelligent summary (default: true)
  strategy3: boolean; // Complete project tree
}

// Loading states per task
interface LoadingStates {
  [taskId: string]: 'breakdown' | 'elaborate' | false;
}

// Solve modal state
interface SolveModalState {
  step: 'closed' | 'choice' | 'autopilot_prompt' | 'copilot_asking' | 'copilot_answering';
  task: Task | null;
  questions: string[];
  answers: Record<string, string>;
  additionalPrompt: string;
  isRefinement: boolean;
  selectionWrapperId: string | null;
}
```

### AI Integration Layer

#### Gemini API Client (`src/lib/ai/gemini.ts`)

```typescript
interface GeminiAPIOptions {
  isJson?: boolean;
  isQuestionGeneration?: boolean;
}

interface GeminiResponse {
  content: string;
  success: boolean;
  error?: string;
}

class GeminiClient {
  private apiKey: string;
  private baseUrl: string;

  async generateContent(prompt: string, options?: GeminiAPIOptions): Promise<GeminiResponse>
  async generateTasks(projectGoal: string, description?: string): Promise<Task[]>
  async generateSubtasks(taskTitle: string, taskDescription: string): Promise<Task[]>
  async generateQuestions(context: string): Promise<string[]>
  async elaborateContent(content: string, context: string): Promise<string>
}
```

#### Context Builder (`src/lib/ai/context.ts`)

```typescript
interface ContextBuilderOptions {
  strategies: ContextStrategies;
  mainProject: string;
  mainProjectDescription: string;
  tasks: Task[];
  selectedHtml?: string;
}

class ContextBuilder {
  buildContextForAI(taskId: string, options: ContextBuilderOptions): string
  private findTaskPath(tasks: Task[], targetId: string): Task[]
  private generateTaskTreeString(tasks: Task[], targetId: string): string
  private buildSummaryContext(path: Task[], mainProject: string): string
}
```

### Custom Hooks

#### Theme Management (`src/lib/hooks/useTheme.ts`)

```typescript
interface UseThemeReturn {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

export function useTheme(): UseThemeReturn {
  // Manages theme state with localStorage persistence
  // Applies theme changes to document root
  // Provides theme toggle functionality
}
```

#### Library Loader (`src/lib/hooks/useLibraryLoader.ts`)

```typescript
interface LibraryConfig {
  id: string;
  src: string;
  name: string;
}

interface UseLibraryLoaderReturn {
  libsLoaded: boolean;
  loadingError: string | null;
  reloadLibraries: () => Promise<void>;
}

export function useLibraryLoader(libraries: LibraryConfig[]): UseLibraryLoaderReturn {
  // Dynamically loads external libraries
  // Handles loading errors and retries
  // Provides loading status
}
```

#### Auto-Growing Textarea (`src/lib/hooks/useAutoGrowTextarea.ts`)

```typescript
interface UseAutoGrowTextareaReturn {
  textareaRef: RefObject<HTMLTextAreaElement>;
  adjustHeight: () => void;
}

export function useAutoGrowTextarea(value: string): UseAutoGrowTextareaReturn {
  // Automatically adjusts textarea height based on content
  // Handles initial sizing and dynamic resizing
  // Optimized for performance
}
```

### Enhanced Components

#### Main App Component (`src/app/page.tsx`)

**New State Management:**
```typescript
// Additional state variables to match monolithic version
const [loading, setLoading] = useState<LoadingStates>({});
const [contextStrategies, setContextStrategies] = useState<ContextStrategies>({
  strategy1: false,
  strategy2: true, // Default enabled
  strategy3: false,
});
const [projectJustStarted, setProjectJustStarted] = useState(false);
const [libsLoaded, setLibsLoaded] = useState(false);
```

**Enhanced Functionality:**
- Real AI integration using GeminiClient
- Context strategy management
- Advanced loading states
- Auto-scroll after project start
- External library management

#### Enhanced TaskItem Component (`src/components/TaskItem.tsx`)

**New Features:**
- Text selection and elaboration
- Advanced editing states
- Auto-growing textareas
- Context menu for selected text
- Save & Rework functionality

**Text Selection Implementation:**
```typescript
interface SelectionInfo {
  range: Range | null;
  text: string;
}

const handleSelection = useCallback(() => {
  // Detect text selection in AI content
  // Show context menu with elaboration options
  // Handle selection-based task creation
});

const handleElaborateSelection = () => {
  // Wrap selected text with unique ID
  // Call AI to elaborate on selection
  // Replace wrapped content with elaborated version
};
```

#### Enhanced SolveTaskModal Component (`src/components/SolveTaskModal.tsx`)

**Enhanced Modal Flow:**
- Real AI question generation for Co-pilot mode
- Context-aware prompt building
- Selection-based elaboration support
- Advanced loading states
- Error handling and recovery

## Data Models

### Task Data Model

```typescript
interface Task {
  id: string;
  title: string;
  description: string;
  content: string; // HTML content from AI
  status: TaskStatus;
  assignees: User[];
  subtasks: Task[];
  
  // Editing states
  isEditing?: boolean;
  isDescriptionEditing?: boolean;
  isAiContentEditing?: boolean;
}
```

### Application State Model

```typescript
interface AppState {
  // Project data
  mainProject: string;
  mainProjectDescription: string;
  tasks: Task[];
  
  // UI state
  motivation: string;
  theme: 'light' | 'dark';
  isHelpModalOpen: boolean;
  isExamplesVisible: boolean;
  projectJustStarted: boolean;
  
  // Loading states
  loading: LoadingStates;
  libsLoaded: boolean;
  
  // Modal state
  solveModalState: SolveModalState;
  
  // Settings
  contextStrategies: ContextStrategies;
}
```

## Error Handling

### AI API Error Handling

```typescript
interface AIErrorHandler {
  handleAPIError(error: Error, operation: string): string;
  showUserFriendlyError(error: string): void;
  retryOperation(operation: () => Promise<any>, maxRetries: number): Promise<any>;
}
```

**Error Scenarios:**
1. API key missing or invalid
2. Network connectivity issues
3. API rate limiting
4. Malformed responses
5. Timeout errors

**Error Recovery:**
- Graceful degradation to mock responses
- User-friendly error messages
- Retry mechanisms with exponential backoff
- Fallback to cached responses when available

### Library Loading Error Handling

```typescript
interface LibraryErrorHandler {
  handleLoadError(libraryName: string, error: Error): void;
  provideFallback(libraryName: string): boolean;
  disableFeature(featureName: string): void;
}
```

## Testing Strategy

### Unit Testing

**Components to Test:**
- AI integration functions
- Context building logic
- Task manipulation utilities
- Custom hooks
- Export functions

**Test Categories:**
1. **AI Integration Tests**
   - Mock API responses
   - Error handling scenarios
   - Context building accuracy

2. **Component Tests**
   - User interaction flows
   - State management
   - Error boundary behavior

3. **Utility Function Tests**
   - HTML parsing accuracy
   - Task operation correctness
   - Export format validation

### Integration Testing

**Test Scenarios:**
1. **Complete User Flows**
   - Project creation to export
   - Task breakdown and elaboration
   - Modal interactions

2. **Error Recovery Flows**
   - API failures with graceful degradation
   - Library loading failures
   - Network connectivity issues

3. **Cross-Component Communication**
   - State updates across components
   - Event handling chains
   - Data consistency

### Performance Testing

**Performance Metrics:**
1. **Initial Load Time**
   - Library loading performance
   - Component rendering speed

2. **AI Operation Performance**
   - API response times
   - UI responsiveness during operations

3. **Memory Usage**
   - Task tree rendering efficiency
   - State management overhead

## Implementation Phases

### Phase 1: Core AI Integration
- Implement GeminiClient
- Add context building utilities
- Replace mock functions with real AI calls

### Phase 2: Enhanced State Management
- Add missing state variables
- Implement context strategies
- Add advanced loading states

### Phase 3: Advanced UI Features
- Text selection and elaboration
- Auto-growing textareas
- Enhanced modal system

### Phase 4: External Dependencies
- Library loading system
- Enhanced export functions
- Theme persistence

### Phase 5: Error Handling & Polish
- Comprehensive error handling
- User feedback improvements
- Performance optimizations

## Migration Strategy

### Backward Compatibility
- Maintain existing component interfaces
- Preserve current data structures
- Ensure existing functionality continues to work

### Incremental Rollout
1. Add new features as optional enhancements
2. Gradually replace mock implementations
3. Test each component independently
4. Full integration testing

### Data Migration
- No breaking changes to existing data structures
- Add new optional fields to Task interface
- Maintain compatibility with existing exports