# Technology Stack

## Framework & Runtime
- **Next.js 15.3.3** - React framework with App Router
- **React 18.3.1** - UI library
- **TypeScript 5** - Type safety and development experience
- **Node.js** - Runtime environment

## Styling & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Radix UI** - Headless UI components for accessibility
- **Lucide React** - Icon library
- **tailwindcss-animate** - Animation utilities
- **class-variance-authority** - Component variant management

## AI Integration
- **Firebase Genkit 1.14.1** - AI/ML framework for Firebase
- **Custom AI modules** in `src/lib/ai/` for Gemini integration

## Data & Forms
- **React Hook Form 7.54.2** - Form state management
- **Zod 3.24.2** - Schema validation
- **date-fns 3.6.0** - Date manipulation

## Export Functionality
- **PDFMake 0.2.10** - PDF generation
- **PapaParse 5.4.1** - CSV parsing/generation
- **Custom Markdown export** - Built-in markdown generation

## Development Tools
- **genkit-cli** - AI development CLI
- **PostCSS** - CSS processing
- **ESLint & TypeScript** - Code quality (build errors ignored for rapid development)

## Common Commands

### Development
```bash
npm run dev          # Start development server on port 9002 with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript type checking
```

### AI Development
```bash
genkit start         # Start Genkit development server
genkit flow:run      # Run AI flows for testing
```

## Build Configuration
- **Turbopack** enabled for faster development builds
- **Custom port 9002** for development
- TypeScript and ESLint errors ignored during builds for rapid iteration
- Image optimization configured for external placeholder services