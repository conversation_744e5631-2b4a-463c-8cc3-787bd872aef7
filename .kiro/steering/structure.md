# Project Structure

## Root Level
- **Configuration files**: `next.config.ts`, `tailwind.config.ts`, `tsconfig.json`, `components.json`
- **Package management**: `package.json`, `package-lock.json`
- **Environment**: `.env` for configuration, `apphosting.yaml` for Firebase hosting
- **Documentation**: `README.md`, `docs/` folder with blueprints and source code documentation

## Source Code Organization (`src/`)

### Application Layer (`src/app/`)
- **Next.js App Router structure**
- `layout.tsx` - Root layout with global styles and providers
- `page.tsx` - Main application page with project planning interface
- `globals.css` - Global CSS styles and Tailwind imports
- `favicon.ico` - Application icon

### Components (`src/components/`)
- **Main components**: `TaskList.tsx`, `TaskItem.tsx`, `SolveTaskModal.tsx`
- **UI components** (`src/components/ui/`): Radix UI-based reusable components
  - Form components: `button.tsx`, `input.tsx`, `textarea.tsx`, `checkbox.tsx`
  - Layout components: `card.tsx`, `dialog.tsx`, `sheet.tsx`, `tabs.tsx`
  - Data display: `table.tsx`, `badge.tsx`, `avatar.tsx`, `progress.tsx`
  - Navigation: `dropdown-menu.tsx`, `menubar.tsx`, `sidebar.tsx`

### Business Logic (`src/lib/`)
- **AI Integration** (`src/lib/ai/`):
  - `index.ts` - Main AI module exports
  - `gemini.ts` - Gemini AI client implementation
  - `context.ts` - Context building for AI prompts
- **Export functionality** (`src/lib/export/`):
  - `pdf.ts` - PDF generation using PDFMake
  - `csv.ts` - CSV export using PapaParse
  - `markdown.ts` - Markdown export functionality
- **Utilities** (`src/lib/utils/`):
  - `index.ts` - General utility functions and Tailwind class merging
  - `htmlParser.ts` - HTML parsing utilities
- **Types** (`src/lib/types.ts`) - TypeScript interfaces and type definitions
- **Core utilities** (`src/lib/utils.ts`) - Shared utility functions

### Hooks (`src/hooks/`)
- `use-mobile.tsx` - Mobile device detection
- `use-toast.ts` - Toast notification management

## Key Architectural Patterns

### Component Hierarchy
```
App (page.tsx)
├── TaskList
│   └── TaskItem (recursive for subtasks)
│       ├── Inline editing capabilities
│       └── AI integration buttons
└── SolveTaskModal (AI interaction)
```

### State Management
- **Local React state** for UI interactions
- **Prop drilling** for task updates through component hierarchy
- **Modal state** managed at app level for AI interactions

### File Naming Conventions
- **PascalCase** for React components (`TaskList.tsx`)
- **camelCase** for utilities and hooks (`use-toast.ts`)
- **kebab-case** for UI components (`alert-dialog.tsx`)
- **Descriptive names** that indicate functionality

### Import Patterns
- **Absolute imports** using `@/` alias for `src/` directory
- **Barrel exports** in `src/lib/ai/index.ts` for clean imports
- **Type-only imports** where appropriate for better tree-shaking