diff --git a/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js b/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js
index 28c3a1e..61947b6 100644
--- a/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js
+++ b/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js
@@ -17,7 +17,13 @@
  */
 Object.defineProperty(exports, "__esModule", { value: true });
 exports.AsyncHooksContextManager = void 0;
-const asyncHooks = require("async_hooks");
+let asyncHooks;
+try {
+    asyncHooks = require("async_hooks");
+} catch (e) {
+    // ignore: we may be in a browser environment
+}
 const AbstractAsyncHooksContextManager_1 = require("./AbstractAsyncHooksContextManager");
 class AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {
     constructor() {
diff --git a/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.mjs b/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.mjs
index 92c8b74..19050d2 100644
--- a/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.mjs
+++ b/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.mjs
@@ -14,7 +14,12 @@
  * @license
  */
 import { AsyncResource } from 'async_hooks';
-import * as asyncHooks from 'async_hooks';
+let asyncHooks;
+try {
+    asyncHooks = await import('async_hooks');
+} catch (e) {
+    // ignore: we may be in a browser environment
+}
 import { AbstractAsyncHooksContextManager } from './AbstractAsyncHooksContextManager.mjs';
 class AsyncHooksContextManager extends AbstractAsyncHooksContextManager {
     constructor() {
