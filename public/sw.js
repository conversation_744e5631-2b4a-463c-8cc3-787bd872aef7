// Service Worker for KI Projekt-Planer PWA
const CACHE_VERSION = 'v1.0.0';
const STATIC_CACHE_NAME = `ki-projekt-planer-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE_NAME = `ki-projekt-planer-dynamic-${CACHE_VERSION}`;
const API_CACHE_NAME = `ki-projekt-planer-api-${CACHE_VERSION}`;
const IMAGES_CACHE_NAME = `ki-projekt-planer-images-${CACHE_VERSION}`;

// Static assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/sw.js'
];

// Cache configuration
const CACHE_CONFIG = {
  // Maximum age for different cache types (in milliseconds)
  STATIC_MAX_AGE: 30 * 24 * 60 * 60 * 1000, // 30 days
  DYNAMIC_MAX_AGE: 7 * 24 * 60 * 60 * 1000,  // 7 days
  API_MAX_AGE: 5 * 60 * 1000,                 // 5 minutes
  IMAGES_MAX_AGE: 30 * 24 * 60 * 60 * 1000,  // 30 days
  
  // Maximum number of entries per cache
  STATIC_MAX_ENTRIES: 100,
  DYNAMIC_MAX_ENTRIES: 50,
  API_MAX_ENTRIES: 20,
  IMAGES_MAX_ENTRIES: 60
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME)
        .then((cache) => {
          console.log('Caching static assets');
          return cache.addAll(STATIC_ASSETS);
        })
        .catch((error) => {
          console.error('Failed to cache static assets:', error);
        }),
      
      // Set installation timestamp
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'SW_INSTALLING',
            version: CACHE_VERSION,
            timestamp: Date.now()
          });
        });
      })
    ])
  );
  
  // Don't automatically skip waiting - let the user decide when to update
  console.log('Service Worker installed, waiting for activation signal');
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  const currentCaches = [STATIC_CACHE_NAME, DYNAMIC_CACHE_NAME, API_CACHE_NAME, IMAGES_CACHE_NAME];
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (!currentCaches.includes(cacheName)) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Clean up expired entries in current caches
      cleanupExpiredCacheEntries(),
      
      // Notify clients of activation
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: 'SW_ACTIVATED',
            version: CACHE_VERSION,
            timestamp: Date.now()
          });
        });
      })
    ])
  );
  
  // Claim all clients immediately
  self.clients.claim();
  console.log('Service Worker activated and claimed all clients');
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip external requests (except for fonts and CDN resources)
  if (url.origin !== self.location.origin && !isAllowedExternalResource(url)) {
    return;
  }
  
  // Determine caching strategy based on resource type
  if (isStaticAsset(request.url)) {
    // Cache-first strategy for static assets (CSS, JS, fonts)
    event.respondWith(cacheFirstStrategy(request, STATIC_CACHE_NAME));
  }
  else if (isImageAsset(request.url)) {
    // Cache-first strategy for images with separate cache
    event.respondWith(cacheFirstStrategy(request, IMAGES_CACHE_NAME));
  }
  else if (isAPIRoute(request.url)) {
    // Network-first strategy for API routes with short cache
    event.respondWith(networkFirstWithTimeoutStrategy(request, API_CACHE_NAME, 3000));
  }
  else if (isDynamicContent(request.url)) {
    // Stale-while-revalidate for dynamic content
    event.respondWith(staleWhileRevalidateStrategy(request, DYNAMIC_CACHE_NAME));
  }
  else {
    // Default: Network-first for HTML pages
    event.respondWith(networkFirstStrategy(request, DYNAMIC_CACHE_NAME));
  }
});

// Cache-first strategy for static assets (CSS, JS, images)
async function cacheFirstStrategy(request, cacheName = STATIC_CACHE_NAME) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse && !isExpired(cachedResponse)) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseToCache = networkResponse.clone();
      
      // Add timestamp for expiration tracking
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());
      
      const responseWithTimestamp = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      await cache.put(request, responseWithTimestamp);
      await limitCacheSize(cacheName, CACHE_CONFIG.STATIC_MAX_ENTRIES);
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache-first strategy failed:', error);
    
    // Try to return stale cache if available
    const staleResponse = await caches.match(request);
    if (staleResponse) {
      return staleResponse;
    }
    
    return createOfflineResponse(request);
  }
}

// Network-first strategy for API routes and dynamic content
async function networkFirstStrategy(request, cacheName = DYNAMIC_CACHE_NAME) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseToCache = networkResponse.clone();
      
      // Add timestamp for expiration tracking
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());
      
      const responseWithTimestamp = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      await cache.put(request, responseWithTimestamp);
      await limitCacheSize(cacheName, CACHE_CONFIG.DYNAMIC_MAX_ENTRIES);
    }
    return networkResponse;
  } catch (error) {
    console.error('Network request failed, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse && !isExpired(cachedResponse)) {
      return cachedResponse;
    }
    
    return createOfflineResponse(request);
  }
}

// Network-first with timeout for API routes
async function networkFirstWithTimeoutStrategy(request, cacheName = API_CACHE_NAME, timeout = 3000) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const networkResponse = await fetch(request, { signal: controller.signal });
    clearTimeout(timeoutId);
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      const responseToCache = networkResponse.clone();
      
      // Add timestamp for expiration tracking
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());
      
      const responseWithTimestamp = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      await cache.put(request, responseWithTimestamp);
      await limitCacheSize(cacheName, CACHE_CONFIG.API_MAX_ENTRIES);
    }
    return networkResponse;
  } catch (error) {
    console.error('Network request failed or timed out, trying cache:', error);
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return createOfflineResponse(request);
  }
}

// Stale-while-revalidate strategy for HTML pages and dynamic content
async function staleWhileRevalidateStrategy(request, cacheName = DYNAMIC_CACHE_NAME) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then(async (networkResponse) => {
    if (networkResponse.ok) {
      const responseToCache = networkResponse.clone();
      
      // Add timestamp for expiration tracking
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-at', Date.now().toString());
      
      const responseWithTimestamp = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });
      
      await cache.put(request, responseWithTimestamp);
      await limitCacheSize(cacheName, CACHE_CONFIG.DYNAMIC_MAX_ENTRIES);
    }
    return networkResponse;
  }).catch(() => {
    // Return cached response if network fails
    return cachedResponse;
  });
  
  // Return cached response immediately if available, otherwise wait for network
  return cachedResponse || fetchPromise;
}

// Helper functions to determine request types
function isStaticAsset(url) {
  return url.includes('/_next/static/') || 
         url.includes('/favicon.ico') ||
         url.includes('/manifest.json') ||
         url.match(/\.(css|js|woff|woff2|ttf|eot)$/);
}

function isImageAsset(url) {
  return url.match(/\.(png|jpg|jpeg|gif|svg|webp|avif|ico)$/);
}

function isAPIRoute(url) {
  return url.includes('/api/') || url.includes('/genkit/');
}

function isDynamicContent(url) {
  return url.includes('/_next/') && !isStaticAsset(url) && !isImageAsset(url);
}

function isAllowedExternalResource(url) {
  // Allow fonts from Google Fonts and other CDNs
  return url.hostname.includes('fonts.googleapis.com') ||
         url.hostname.includes('fonts.gstatic.com') ||
         url.hostname.includes('cdnjs.cloudflare.com');
}

// Cache management utility functions
async function limitCacheSize(cacheName, maxEntries) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  
  if (keys.length > maxEntries) {
    // Remove oldest entries (FIFO)
    const entriesToDelete = keys.slice(0, keys.length - maxEntries);
    await Promise.all(entriesToDelete.map(key => cache.delete(key)));
    console.log(`Cleaned up ${entriesToDelete.length} entries from ${cacheName}`);
  }
}

function isExpired(response) {
  const cachedAt = response.headers.get('sw-cached-at');
  if (!cachedAt) return false;
  
  const age = Date.now() - parseInt(cachedAt);
  const maxAge = CACHE_CONFIG.STATIC_MAX_AGE; // Default to static max age
  
  return age > maxAge;
}

async function cleanupExpiredCacheEntries() {
  const cacheNames = [STATIC_CACHE_NAME, DYNAMIC_CACHE_NAME, API_CACHE_NAME, IMAGES_CACHE_NAME];
  
  for (const cacheName of cacheNames) {
    try {
      const cache = await caches.open(cacheName);
      const keys = await cache.keys();
      
      for (const request of keys) {
        const response = await cache.match(request);
        if (response && isExpired(response)) {
          await cache.delete(request);
          console.log(`Removed expired entry from ${cacheName}:`, request.url);
        }
      }
    } catch (error) {
      console.error(`Error cleaning up ${cacheName}:`, error);
    }
  }
}

function createOfflineResponse(request) {
  const url = new URL(request.url);
  
  if (isAPIRoute(request.url)) {
    return new Response(JSON.stringify({ 
      error: 'Service unavailable offline',
      offline: true,
      timestamp: new Date().toISOString()
    }), { 
      status: 503,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
  }
  
  if (isImageAsset(request.url)) {
    // Return a simple SVG placeholder for images
    const svg = `
      <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#6b7280">
          Offline
        </text>
      </svg>
    `;
    return new Response(svg, {
      status: 200,
      headers: { 'Content-Type': 'image/svg+xml' }
    });
  }
  
  // Default offline page
  return new Response(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Offline - KI Projekt-Planer</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: system-ui, sans-serif; text-align: center; padding: 2rem; }
          .offline { color: #6b7280; }
        </style>
      </head>
      <body>
        <h1>Offline</h1>
        <p class="offline">Diese Seite ist offline nicht verfügbar.</p>
        <p><a href="/">Zur Startseite</a></p>
      </body>
    </html>
  `, {
    status: 503,
    headers: { 'Content-Type': 'text/html' }
  });
}

// Background sync for offline actions (future enhancement)
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    console.log('Background sync triggered');
    // Future: Handle queued offline actions
  }
});

// Enhanced message handling for update control and communication
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data);
  
  const { data } = event;
  
  if (data && data.type === 'SKIP_WAITING') {
    console.log('Skipping waiting and activating new service worker');
    
    // Notify all clients that update is being applied
    self.clients.matchAll().then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: 'SW_UPDATE_APPLYING',
          version: CACHE_VERSION,
          timestamp: Date.now()
        });
      });
    });
    
    self.skipWaiting();
  }
  
  if (data && data.type === 'GET_VERSION') {
    // Respond with current version info
    event.ports[0].postMessage({
      type: 'VERSION_INFO',
      version: CACHE_VERSION,
      caches: [STATIC_CACHE_NAME, DYNAMIC_CACHE_NAME, API_CACHE_NAME, IMAGES_CACHE_NAME]
    });
  }
  
  if (data && data.type === 'FORCE_UPDATE_CHECK') {
    // Force an update check by clearing cache headers
    console.log('Forcing update check...');
    event.ports[0].postMessage({
      type: 'UPDATE_CHECK_COMPLETE',
      timestamp: Date.now()
    });
  }
  
  if (data && data.type === 'GET_CACHE_INFO') {
    // Provide cache information for debugging
    getCacheInfo().then((info) => {
      event.ports[0].postMessage({
        type: 'CACHE_INFO',
        data: info
      });
    });
  }
});

// Helper function to get cache information
async function getCacheInfo() {
  const cacheNames = await caches.keys();
  const info = {};
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();
    info[cacheName] = {
      entryCount: keys.length,
      urls: keys.map(request => request.url).slice(0, 10) // First 10 URLs
    };
  }
  
  return info;
}

// Push notification handling (future enhancement)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    console.log('Push notification received:', data);
    // Future: Show push notifications
  }
});