{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9003", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "genkit": "^1.14.1", "highlight.js": "^11.9.0", "lucide-react": "^0.475.0", "next": "15.3.3", "papaparse": "^5.4.1", "pdfmake": "^0.2.10", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/papaparse": "^5.3.14", "@types/pdfmake": "^0.2.9", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^4.7.0", "genkit-cli": "^1.14.1", "jsdom": "^23.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^1.6.1", "webpack-bundle-analyzer": "^4.10.2"}}