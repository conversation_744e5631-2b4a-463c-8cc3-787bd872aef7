{"time":"2025-08-16T02:40:22.265899+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-16T02:40:22.267723+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-16T02:40:22.538229+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"/Users/<USER>/.local/share/crush/providers.json"}
{"time":"2025-08-16T02:40:23.4133+02:00","level":"INFO","msg":"goose: no migrations to run. current version: 20250627000000"}
{"time":"2025-08-16T02:40:23.413374+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-16T02:40:23.43413+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":177},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-16T02:40:23.434288+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":179},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-16T02:40:23.456761+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":177},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-16T02:40:23.456855+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":179},"msg":"Initialized agent tools","agent":"coder"}
