'use client';

/**
 * Privacy Settings Panel Component
 * Provides granular privacy controls and data management features
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Trash2, 
  Download, 
  Eye, 
  Lock, 
  AlertTriangle,
  Info,
  Database,
  Clock
} from 'lucide-react';
import { DataManager, type PrivacySettings, type DataDeletionOptions } from '@/lib/security/dataManager';
import { KeyManager } from '@/lib/security/keyManager';
import { useToast } from '@/hooks/use-toast';

export function PrivacySettingsPanel() {
  const [settings, setSettings] = useState<PrivacySettings>(DataManager.getPrivacySettings());
  const [isLoading, setIsLoading] = useState(false);
  const [encryptionEnabled, setEncryptionEnabled] = useState(false);
  const [dataStats, setDataStats] = useState<any>(null);
  const [showDeletionConfirm, setShowDeletionConfirm] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setEncryptionEnabled(KeyManager.isEncryptionEnabled());
      const stats = await DataManager.getDataStatistics();
      setDataStats(stats);
    } catch (error) {
      console.error('Failed to load privacy data:', error);
    }
  };

  const handleSettingChange = (key: keyof PrivacySettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    DataManager.savePrivacySettings(newSettings);
    
    toast({
      title: "Einstellungen gespeichert",
      description: "Ihre Datenschutzeinstellungen wurden aktualisiert."
    });
  };

  const handleEnableEncryption = async () => {
    setIsLoading(true);
    try {
      const success = await KeyManager.initializeEncryption();
      if (success) {
        setEncryptionEnabled(true);
        toast({
          title: "Verschlüsselung aktiviert",
          description: "Ihre Daten werden nun verschlüsselt gespeichert."
        });
      } else {
        toast({
          title: "Fehler",
          description: "Verschlüsselung konnte nicht aktiviert werden.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Ein Fehler ist bei der Aktivierung der Verschlüsselung aufgetreten.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = async () => {
    setIsLoading(true);
    try {
      const data = await DataManager.exportAllUserData();
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `ki-projekt-planer-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      await DataManager.logDataUsage('export', 'all_data');
      
      toast({
        title: "Daten exportiert",
        description: "Ihre Daten wurden erfolgreich exportiert."
      });
    } catch (error) {
      toast({
        title: "Export fehlgeschlagen",
        description: "Ihre Daten konnten nicht exportiert werden.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAllData = async () => {
    if (!showDeletionConfirm) {
      setShowDeletionConfirm(true);
      return;
    }

    setIsLoading(true);
    try {
      const options: DataDeletionOptions = {
        includeProjects: true,
        includeTasks: true,
        includeSettings: true,
        includeEncryptionKeys: true,
        includeBrowserCache: true,
        includeLocalStorage: true
      };

      const result = await DataManager.deleteAllData(options);
      
      if (result.success) {
        toast({
          title: "Daten gelöscht",
          description: `${result.deletedItems.projects} Projekte und ${result.deletedItems.tasks} Aufgaben wurden gelöscht.`
        });
        
        // Reload the page to reflect changes
        setTimeout(() => window.location.reload(), 2000);
      } else {
        toast({
          title: "Löschung teilweise fehlgeschlagen",
          description: `${result.errors.length} Fehler aufgetreten.`,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Löschung fehlgeschlagen",
        description: "Ihre Daten konnten nicht vollständig gelöscht werden.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
      setShowDeletionConfirm(false);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Shield className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Datenschutz & Sicherheit</h2>
      </div>

      {/* Data Overview */}
      {dataStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Datenübersicht
            </CardTitle>
            <CardDescription>
              Übersicht über Ihre gespeicherten Daten
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{dataStats.totalProjects}</div>
                <div className="text-sm text-muted-foreground">Projekte</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{dataStats.totalTasks}</div>
                <div className="text-sm text-muted-foreground">Aufgaben</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{formatBytes(dataStats.storageUsed)}</div>
                <div className="text-sm text-muted-foreground">Speicher</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{dataStats.dataUsageLogEntries}</div>
                <div className="text-sm text-muted-foreground">Log-Einträge</div>
              </div>
            </div>
            
            {dataStats.oldestProject && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                Ältestes Projekt: {new Date(dataStats.oldestProject).toLocaleDateString('de-DE')}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Encryption Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            Datenverschlüsselung
          </CardTitle>
          <CardDescription>
            Verschlüsseln Sie sensible Daten lokal auf Ihrem Gerät
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Verschlüsselung aktiviert</Label>
              <p className="text-sm text-muted-foreground">
                Projekttitel, Beschreibungen und Aufgabeninhalte werden verschlüsselt
              </p>
            </div>
            <div className="flex items-center gap-2">
              {encryptionEnabled ? (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Aktiv
                </Badge>
              ) : (
                <Button 
                  onClick={handleEnableEncryption}
                  disabled={isLoading}
                  size="sm"
                >
                  Aktivieren
                </Button>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="encrypt-sensitive">Sensible Daten verschlüsseln</Label>
              <p className="text-sm text-muted-foreground">
                Automatische Verschlüsselung für neue Projekte und Aufgaben
              </p>
            </div>
            <Switch
              id="encrypt-sensitive"
              checked={settings.encryptSensitiveData}
              onCheckedChange={(checked) => handleSettingChange('encryptSensitiveData', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Datenschutzkontrollen
          </CardTitle>
          <CardDescription>
            Kontrollieren Sie, wie Ihre Daten verwendet werden
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="minimize-ai">KI-Datenübertragung minimieren</Label>
              <p className="text-sm text-muted-foreground">
                Nur notwendige Informationen an KI-Anbieter senden
              </p>
            </div>
            <Switch
              id="minimize-ai"
              checked={settings.minimizeAIDataSharing}
              onCheckedChange={(checked) => handleSettingChange('minimizeAIDataSharing', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="allow-analytics">Nutzungsanalyse erlauben</Label>
              <p className="text-sm text-muted-foreground">
                Anonyme Nutzungsdaten zur Verbesserung der App
              </p>
            </div>
            <Switch
              id="allow-analytics"
              checked={settings.allowAnalytics}
              onCheckedChange={(checked) => handleSettingChange('allowAnalytics', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="crash-reporting">Absturzberichte senden</Label>
              <p className="text-sm text-muted-foreground">
                Automatische Fehlerberichte zur Fehlerbehebung
              </p>
            </div>
            <Switch
              id="crash-reporting"
              checked={settings.allowCrashReporting}
              onCheckedChange={(checked) => handleSettingChange('allowCrashReporting', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Retention */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Datenaufbewahrung
          </CardTitle>
          <CardDescription>
            Automatisches Löschen alter Daten
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="auto-delete">Automatisches Löschen</Label>
              <p className="text-sm text-muted-foreground">
                Alte Projekte automatisch nach Ablauf der Aufbewahrungszeit löschen
              </p>
            </div>
            <Switch
              id="auto-delete"
              checked={settings.autoDeleteOldData}
              onCheckedChange={(checked) => handleSettingChange('autoDeleteOldData', checked)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="retention-days">Aufbewahrungszeit (Tage)</Label>
            <Input
              id="retention-days"
              type="number"
              min="1"
              max="3650"
              value={settings.dataRetentionDays}
              onChange={(e) => handleSettingChange('dataRetentionDays', parseInt(e.target.value) || 365)}
              className="w-32"
            />
            <p className="text-sm text-muted-foreground">
              Projekte werden nach {settings.dataRetentionDays} Tagen ohne Aktivität gelöscht
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Data Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Datenverwaltung
          </CardTitle>
          <CardDescription>
            Exportieren oder löschen Sie Ihre Daten
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleExportData}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Daten exportieren
            </Button>

            <Button
              onClick={handleDeleteAllData}
              disabled={isLoading}
              variant={showDeletionConfirm ? "destructive" : "outline"}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              {showDeletionConfirm ? "Bestätigen: Alle Daten löschen" : "Alle Daten löschen"}
            </Button>
          </div>

          {showDeletionConfirm && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warnung:</strong> Diese Aktion löscht alle Ihre Projekte, Aufgaben und Einstellungen 
                unwiderruflich. Stellen Sie sicher, dass Sie ein Backup haben, falls Sie die Daten später 
                benötigen.
              </AlertDescription>
            </Alert>
          )}

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Der Datenexport enthält alle Ihre Projekte und Einstellungen in einem JSON-Format. 
              API-Schlüssel und Passwörter werden aus Sicherheitsgründen nicht exportiert.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Separator />

      <div className="text-sm text-muted-foreground">
        <p>
          Ihre Daten werden ausschließlich lokal auf Ihrem Gerät gespeichert. 
          KI-Funktionen senden nur die minimal notwendigen Informationen an den gewählten Anbieter.
        </p>
      </div>
    </div>
  );
}