"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bo<PERSON>, 
  Settings, 
  CheckCircle, 
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAIProviderStatus } from '@/hooks/useAIProviderSettings';
import { AIProviderSettingsPage } from '@/components/AIProviderSettingsPage';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/ui/dialog';

interface AIProviderStatusButtonProps {
  variant?: 'button' | 'badge' | 'minimal';
  className?: string;
}

export function AIProviderStatusButton({ 
  variant = 'button', 
  className 
}: AIProviderStatusButtonProps) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const { isConfigured, activeProvider } = useAIProviderStatus();

  if (variant === 'badge') {
    return (
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={cn("flex items-center gap-2", className)}
          >
            <Bot className="h-4 w-4" />
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? "AI Aktiv" : "AI Inaktiv"}
            </Badge>
            {isConfigured ? (
              <CheckCircle className="h-3 w-3 text-green-500" />
            ) : (
              <XCircle className="h-3 w-3 text-red-500" />
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <AIProviderSettingsPage onBack={() => setIsSettingsOpen(false)} />
        </DialogContent>
      </Dialog>
    );
  }

  if (variant === 'minimal') {
    return (
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={cn("flex items-center gap-2", className)}
          >
            <Bot className={cn(
              "h-4 w-4",
              isConfigured ? "text-green-500" : "text-red-500"
            )} />
            {!isConfigured && (
              <AlertTriangle className="h-3 w-3 text-orange-500" />
            )}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <AIProviderSettingsPage onBack={() => setIsSettingsOpen(false)} />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={isConfigured ? "outline" : "default"}
          size="sm"
          className={cn("flex items-center gap-2", className)}
        >
          <Bot className="h-4 w-4" />
          <span className="hidden sm:inline">
            {isConfigured ? `AI: ${activeProvider}` : 'AI konfigurieren'}
          </span>
          <span className="sm:hidden">
            AI
          </span>
          {isConfigured ? (
            <CheckCircle className="h-3 w-3 text-green-500" />
          ) : (
            <Settings className="h-3 w-3" />
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <AIProviderSettingsPage onBack={() => setIsSettingsOpen(false)} />
      </DialogContent>
    </Dialog>
  );
}