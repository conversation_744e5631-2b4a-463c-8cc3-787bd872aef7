/**
 * Comprehensive loading indicator component
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

import { Loader2, Brain, Sparkles, ChevronDown, HelpCircle, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { LoadingOperation } from '@/lib/utils/loadingState';
import { getLoadingMessage, getLoadingIconClass } from '@/lib/utils/loadingState';

interface LoadingIndicatorProps {
  operation: LoadingOperation;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
  inline?: boolean;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-5 w-5',
  lg: 'h-6 w-6'
};

const textSizeClasses = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base'
};

function getOperationIcon(operation: LoadingOperation, size: string) {
  const iconProps = { className: cn(sizeClasses[size as keyof typeof sizeClasses], getLoadingIconClass(operation)) };
  
  switch (operation) {
    case 'breakdown':
      return <ChevronDown {...iconProps} />;
    case 'elaborate':
      return <Sparkles {...iconProps} />;
    case 'solve':
      return <Brain {...iconProps} />;
    case 'questions':
      return <HelpCircle {...iconProps} />;
    case 'content':
      return <FileText {...iconProps} />;
    default:
      return <Loader2 {...iconProps} />;
  }
}

export function LoadingIndicator({ 
  operation, 
  size = 'md', 
  className, 
  showText = true, 
  inline = false 
}: LoadingIndicatorProps) {
  if (operation === false) return null;

  const icon = getOperationIcon(operation, size);
  const message = getLoadingMessage(operation);

  if (inline) {
    return (
      <span className={cn('inline-flex items-center gap-1', className)}>
        {icon}
        {showText && (
          <span className={cn('text-muted-foreground', textSizeClasses[size])}>
            {message}
          </span>
        )}
      </span>
    );
  }

  return (
    <div className={cn('flex items-center justify-center gap-2 p-2', className)}>
      {icon}
      {showText && (
        <span className={cn('text-muted-foreground', textSizeClasses[size])}>
          {message}
        </span>
      )}
    </div>
  );
}

interface TaskLoadingOverlayProps {
  operation: LoadingOperation;
  className?: string;
}

export function TaskLoadingOverlay({ operation, className }: TaskLoadingOverlayProps) {
  if (operation === false) return null;

  return (
    <div className={cn(
      'absolute inset-0 bg-background/80 backdrop-blur-sm',
      'flex items-center justify-center',
      'rounded-lg border-2 border-dashed border-muted-foreground/30',
      'z-10',
      className
    )}>
      <LoadingIndicator operation={operation} size="lg" />
    </div>
  );
}

interface GlobalLoadingBarProps {
  isVisible: boolean;
  className?: string;
}

export function GlobalLoadingBar({ isVisible, className }: GlobalLoadingBarProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'fixed top-0 left-0 right-0 z-50',
      'h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-green-500',
      'animate-pulse',
      className
    )}>
      <div className="h-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-slide" />
    </div>
  );
}

interface LoadingStateDisplayProps {
  activeOperations: string[];
  className?: string;
}

export function LoadingStateDisplay({ activeOperations, className }: LoadingStateDisplayProps) {
  if (activeOperations.length === 0) return null;

  return (
    <div className={cn(
      'fixed bottom-4 right-4 z-40',
      'bg-background/95 backdrop-blur-sm',
      'border rounded-lg shadow-lg',
      'p-3 max-w-xs',
      className
    )}>
      <div className="text-sm font-medium text-foreground mb-2">
        Aktive KI-Operationen ({activeOperations.length})
      </div>
      <div className="space-y-1">
        {activeOperations.slice(0, 3).map((operation, index) => (
          <div key={index} className="flex items-center gap-2 text-xs text-muted-foreground">
            <Loader2 className="h-3 w-3 animate-spin" />
            <span className="truncate">{operation}</span>
          </div>
        ))}
        {activeOperations.length > 3 && (
          <div className="text-xs text-muted-foreground">
            +{activeOperations.length - 3} weitere...
          </div>
        )}
      </div>
    </div>
  );
}

// CSS for the sliding animation (add to globals.css)
export const loadingAnimationCSS = `
@keyframes slide {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-slide {
  animation: slide 2s ease-in-out infinite;
}
`;