/**
 * Progressive image loading component with WebP support
 */

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { getOptimizedImageSrc } from '@/lib/utils/lazyLoading';

interface ProgressiveImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  webpSrc?: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

export function ProgressiveImage({
  src,
  alt,
  className,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  webpSrc,
  width,
  height,
  quality = 75,
  priority = false,
  onLoad,
  onError
}: ProgressiveImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (priority) {
      // Load immediately for priority images
      loadImage();
    } else {
      // Use intersection observer for lazy loading
      if (imgRef.current && 'IntersectionObserver' in window) {
        observerRef.current = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                loadImage();
                observerRef.current?.unobserve(entry.target);
              }
            });
          },
          {
            rootMargin: '50px',
            threshold: 0.1
          }
        );

        observerRef.current.observe(imgRef.current);
      }
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [src, priority]);

  const loadImage = () => {
    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(img.src);
      setIsLoaded(true);
      onLoad?.();
    };
    
    img.onerror = () => {
      setIsError(true);
      onError?.();
    };

    // Try WebP first if supported and available
    if (webpSrc && supportsWebP()) {
      img.src = getOptimizedImageSrc(webpSrc, width, quality);
    } else {
      img.src = getOptimizedImageSrc(src, width, quality);
    }
  };

  const supportsWebP = (): boolean => {
    if (typeof window === 'undefined') return false;
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
  };

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <img
        ref={imgRef}
        src={currentSrc}
        alt={alt}
        width={width}
        height={height}
        className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0',
          isError && 'opacity-50'
        )}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
      />
      
      {/* Loading placeholder */}
      {!isLoaded && !isError && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
      
      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 bg-muted flex items-center justify-center">
          <span className="text-muted-foreground text-sm">
            Bild konnte nicht geladen werden
          </span>
        </div>
      )}
    </div>
  );
}

/**
 * Optimized avatar component with progressive loading
 */
interface ProgressiveAvatarProps {
  src?: string;
  alt: string;
  fallback: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ProgressiveAvatar({
  src,
  alt,
  fallback,
  size = 'md',
  className
}: ProgressiveAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base'
  };

  if (!src) {
    return (
      <div className={cn(
        'rounded-full bg-muted flex items-center justify-center font-medium',
        sizeClasses[size],
        className
      )}>
        {fallback}
      </div>
    );
  }

  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      className={cn(
        'rounded-full object-cover',
        sizeClasses[size],
        className
      )}
      width={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
      height={size === 'sm' ? 32 : size === 'md' ? 40 : 48}
      quality={90}
    />
  );
}