import React from 'react';
import { cn } from '@/lib/utils';

interface AIContentProps {
  content: string;
  className?: string;
  onSelectionChange?: (selectedText: string, range: Range) => void;
}

/**
 * Komponente für die Darstellung von AI-generierten Inhalten
 * mit verbesserter Formatierung und Interaktivität
 */
export function AIContent({ content, className, onSelectionChange }: AIContentProps) {
  const contentRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleSelection = () => {
      if (!onSelectionChange) return;
      
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const selectedText = selection.toString().trim();
      if (selectedText.length === 0) return;

      const range = selection.getRangeAt(0);
      if (contentRef.current && contentRef.current.contains(range.commonAncestorContainer)) {
        onSelectionChange(selectedText, range);
      }
    };

    const element = contentRef.current;
    if (element) {
      element.addEventListener('mouseup', handleSelection);
      element.addEventListener('touchend', handleSelection);
      
      return () => {
        element.removeEventListener('mouseup', handleSelection);
        element.removeEventListener('touchend', handleSelection);
      };
    }
  }, [onSelectionChange]);

  // Bereinige und verbessere den HTML-Content
  const processedContent = React.useMemo(() => {
    if (!content) return '';
    
    // Entferne potentielle Markdown-Code-Blöcke
    let processed = content.replace(/^```html\s*/, '').replace(/```\s*$/, '');
    
    // Stelle sicher, dass der Content nicht leer ist
    if (!processed.trim()) {
      return '<p class="text-slate-500 dark:text-slate-400 italic">Kein Inhalt verfügbar</p>';
    }
    
    return processed;
  }, [content]);

  // Client-seitiges Syntax-Highlighting mit highlight.js (dynamischer Import)
  React.useEffect(() => {
    const element = contentRef.current;
    if (!element) return;

    let cancelled = false;

    (async () => {
      try {
        const hljsModule = await import('highlight.js');
        const hljs = hljsModule.default || (hljsModule as any);

        // Vor dem Highlighting: Tailwind-Utility-Klassen entfernen, die Farben/Background erzwingen
        // Inline code: <code> ohne <pre>
        element.querySelectorAll('p code, li code, table code, blockquote code, pre code').forEach((node) => {
          const el = node as HTMLElement;
          el.classList.forEach(cls => {
            if (/^(text-|bg-|px-|py-|rounded|p-|m-|border|shadow|ring|outline|tracking|leading)/.test(cls)) {
              el.classList.remove(cls);
            }
          });
          // stelle sicher, dass Theme-Klassen wirken
          el.classList.add('hljs');
        });

        // Codeblöcke: <pre><code>
        element.querySelectorAll('pre').forEach((preNode) => {
          const pre = preNode as HTMLElement;
          pre.classList.forEach(cls => {
            if (/^(text-|bg-|px-|py-|rounded|p-|m-|border|shadow|ring|outline|tracking|leading|overflow)/.test(cls)) {
              pre.classList.remove(cls);
            }
          });
          // Basis-Randabstand; Hintergrund/Farben übernimmt das hljs-Theme auf dem <code>-Element
          pre.style.padding = '1rem';
          pre.style.margin = '1rem 0';
        });

        // Alle Codeblöcke hervorheben
        const codeBlocks = element.querySelectorAll('pre code');
        codeBlocks.forEach((codeEl) => {
          if (cancelled) return;
          const el = codeEl as HTMLElement;

          // Verhindere Doppel-Highlighting
          if (el.getAttribute('data-hljs-done') === '1') return;

          const classAttr = el.className || '';
          const hasLang = /language-\w+/.test(classAttr) || /lang-\w+/.test(classAttr);
          try {
            // Vollpaket kann highlightElement immer; fallback Auto falls keine class angegeben
            if (!hasLang) {
              const result = hljs.highlightAuto(el.textContent || '');
              el.innerHTML = result.value;
            }
            hljs.highlightElement(el);
            el.setAttribute('data-hljs-done', '1');
          } catch {
            // Fallback: Plaintext styling
            el.classList.add('hljs');
          }
        });
      } catch (err) {
        // highlight.js nicht verfügbar → ignorieren
      }
    })();

    return () => {
      cancelled = true;
    };
  }, [processedContent]);

  return (
    <div
      ref={contentRef}
      className={cn(
        // Base styles
        "ai-content prose prose-sm dark:prose-invert max-w-none",
        // Custom overrides for better integration
        "prose-headings:font-semibold prose-headings:text-slate-900 dark:prose-headings:text-slate-100",
        "prose-p:text-slate-700 dark:prose-p:text-slate-300 prose-p:leading-relaxed",
        "prose-li:text-slate-700 dark:prose-li:text-slate-300",
        "prose-strong:text-slate-900 dark:prose-strong:text-slate-100",
        // WICHTIG: Keine Code-Utilities setzen – das Highlight.js Theme übernimmt komplett
        "prose-blockquote:border-slate-300 dark:prose-blockquote:border-slate-600",
        "prose-blockquote:text-slate-600 dark:prose-blockquote:text-slate-400",
        // Selection styles
        "selection:bg-indigo-100 dark:selection:bg-indigo-900/50",
        // Interactive styles
        "cursor-text",
        className
      )}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

/**
 * Wrapper für AI-Content mit Loading-State
 */
interface AIContentWithLoadingProps extends AIContentProps {
  isLoading?: boolean;
  loadingText?: string;
}

export function AIContentWithLoading({ 
  isLoading, 
  loadingText = "Wird erstellt...", 
  ...props 
}: AIContentWithLoadingProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
          <span className="text-slate-500 dark:text-slate-400 text-sm">{loadingText}</span>
        </div>
      </div>
    );
  }

  return <AIContent {...props} />;
}

/**
 * Hilfsfunktion zum Dekodieren von HTML-Entitäten
 */
export function decodeHtmlEntities(text: string): string {
  if (typeof text !== 'string') return '';
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
}