"use client";

import { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import type { Task, LoadingStates, InsertionPosition, InsertionZone } from "@/lib/types";
import { TaskItem } from "@/components/TaskItem";
import { InsertionIndicator } from "@/components/InsertionIndicator";
import { calculateVirtualScrollItems, debounce } from '@/lib/utils/performanceOptimization';
import { usePerformanceMonitoring } from '@/hooks/usePerformanceMonitoring';

interface VirtualizedTaskListProps {
  tasks: Task[];
  level?: number;
  parentId?: string | null;
  onUpdateTask: (task: Task) => void;
  onOpenSolveModal: (task: Task, isRefinement?: boolean, selectionWrapperId?: string) => void;
  onAddTask: (parentId?: string | null, title?: string, description?: string) => void;
  onAddTaskAfter: (afterId: string, parentId: string | null, title?: string, description?: string) => void;
  onDeleteTask: (taskId: string) => void;
  onBreakdownTask?: (taskId: string) => void;
  numberingPrefix?: string;
  loading?: LoadingStates;
  setLoading?: (loading: LoadingStates | ((prev: LoadingStates) => LoadingStates)) => void;
  isVirtualized?: boolean;
  // New props for insertion functionality
  onInsertTask?: (position: InsertionPosition) => void;
  showInsertionIndicators?: boolean;
  insertionMode?: 'hover' | 'always' | 'keyboard';
}

// Flatten task tree for virtualization with unique keys
const flattenTasks = (tasks: Task[], level = 0, parentId: string | null = null, prefix = ''): Array<{
  task: Task;
  level: number;
  parentId: string | null;
  numbering: string;
  uniqueKey: string;
}> => {
  const flattened: Array<{
    task: Task;
    level: number;
    parentId: string | null;
    numbering: string;
    uniqueKey: string;
  }> = [];

  tasks.forEach((task, index) => {
    const numbering = `${prefix}${index + 1}`;
    const uniqueKey = `${level}-${parentId || 'root'}-${task.id}`;
    
    flattened.push({
      task,
      level,
      parentId,
      numbering,
      uniqueKey
    });

    if (task.subtasks && task.subtasks.length > 0) {
      flattened.push(...flattenTasks(task.subtasks, level + 1, task.id, `${numbering}.`));
    }
  });

  return flattened;
};

export const VirtualizedTaskList = memo(function VirtualizedTaskList({
  tasks,
  level = 0,
  parentId = null,
  onUpdateTask,
  onOpenSolveModal,
  onAddTask,
  onAddTaskAfter,
  onDeleteTask,
  onBreakdownTask,
  numberingPrefix = '',
  loading = {},
  setLoading = () => {},
  isVirtualized = false,
  onInsertTask,
  showInsertionIndicators = true,
  insertionMode = 'hover'
}: VirtualizedTaskListProps) {
  const listRef = useRef<HTMLDivElement>(null);
  
  // State for managing insertion indicators at list level
  const [hoveredZone, setHoveredZone] = useState<InsertionZone | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  
  // Flatten tasks for better performance with large trees
  const flattenedTasks = useMemo(() => {
    if (level === 0) {
      return flattenTasks(tasks, level, parentId, numberingPrefix);
    }
    return tasks.map((task, index) => ({
      task,
      level,
      parentId,
      numbering: `${numberingPrefix}${index + 1}`,
      uniqueKey: `${level}-${parentId || 'root'}-${task.id}`
    }));
  }, [tasks, level, parentId, numberingPrefix]);

  // Memoized breakdown handler
  const handleBreakdown = useCallback((taskId: string) => {
    if (onBreakdownTask) {
      onBreakdownTask(taskId);
    }
  }, [onBreakdownTask]);

  // Handle mouse movement for insertion zone detection
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!showInsertionIndicators || insertionMode !== 'hover') return;
    
    setMousePosition({ x: e.clientX, y: e.clientY });
  }, [showInsertionIndicators, insertionMode]);

  // Handle mouse leave to clear hover state
  const handleMouseLeave = useCallback(() => {
    setHoveredZone(null);
    setMousePosition(null);
  }, []);

  // Handle insertion at specific position
  const handleInsertAtPosition = useCallback((position: InsertionPosition) => {
    if (onInsertTask) {
      onInsertTask(position);
    } else if (onAddTaskAfter) {
      // Fallback to existing onAddTaskAfter functionality
      onAddTaskAfter(position.targetTaskId, position.parentId);
    }
  }, [onInsertTask, onAddTaskAfter]);

  // Effect to detect which insertion zone is being hovered
  useEffect(() => {
    if (!mousePosition || !listRef.current || !showInsertionIndicators || insertionMode !== 'hover') {
      setHoveredZone(null);
      return;
    }

    const { x, y } = mousePosition;
    let foundZone: InsertionZone | null = null;

    // Check each visible task for insertion zones
    const visibleTasks = flattenedTasks.slice(0, 100);
    
    visibleTasks.forEach(({ task, level: itemLevel, parentId: itemParentId }, index) => {
      const taskElement = listRef.current?.querySelector(`[data-task-id="${task.id}"]`) as HTMLElement;
      if (!taskElement) return;

      const taskRect = taskElement.getBoundingClientRect();
      const ZONE_HEIGHT = 20;
      const ZONE_OFFSET = 10;

      // Check before zone (for first task)
      if (index === 0) {
        const beforeZone = new DOMRect(
          taskRect.left,
          taskRect.top - ZONE_OFFSET,
          taskRect.width,
          ZONE_HEIGHT
        );
        
        if (x >= beforeZone.left && x <= beforeZone.right && 
            y >= beforeZone.top && y <= beforeZone.bottom) {
          foundZone = {
            id: `before-${task.id}`,
            type: 'before',
            bounds: beforeZone,
            targetTaskId: task.id,
            parentId: itemParentId,
            level: itemLevel
          };
          return;
        }
      }

      // Check after zone
      const afterZone = new DOMRect(
        taskRect.left,
        taskRect.bottom - ZONE_OFFSET,
        taskRect.width,
        ZONE_HEIGHT
      );
      
      if (x >= afterZone.left && x <= afterZone.right && 
          y >= afterZone.top && y <= afterZone.bottom) {
        foundZone = {
          id: `after-${task.id}`,
          type: 'after',
          bounds: afterZone,
          targetTaskId: task.id,
          parentId: itemParentId,
          level: itemLevel
        };
        return;
      }
    });

    setHoveredZone(foundZone);
  }, [mousePosition, flattenedTasks, showInsertionIndicators, insertionMode]);

  if (!tasks || tasks.length === 0) {
    return null;
  }

  // For small task lists, render normally with insertion indicators
  if (flattenedTasks.length <= 50) {
    return (
      <div 
        ref={listRef}
        className="space-y-1"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        {flattenedTasks.map(({ task, level: itemLevel, parentId: itemParentId, numbering, uniqueKey }, index) => (
          <div key={uniqueKey} className="relative">
            {/* Insertion indicator before first task */}
            {index === 0 && showInsertionIndicators && (
              <InsertionIndicator
                position={{
                  type: 'before',
                  targetTaskId: task.id,
                  parentId: itemParentId,
                  level: itemLevel
                }}
                isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `before-${task.id}`)}
                isHovered={hoveredZone?.id === `before-${task.id}`}
                onInsert={() => handleInsertAtPosition({
                  type: 'before',
                  targetTaskId: task.id,
                  parentId: itemParentId,
                  level: itemLevel
                })}
                className="mb-1"
                variant={itemLevel > 0 ? 'compact' : 'default'}
              />
            )}
            
            <TaskItem
              task={task}
              level={itemLevel}
              parentId={itemParentId}
              onUpdateTask={onUpdateTask}
              onOpenSolveModal={onOpenSolveModal}
              onAddTask={onAddTask}
              onAddTaskAfter={onAddTaskAfter}
              onDeleteTask={onDeleteTask}
              onBreakdownTask={handleBreakdown}
              numbering={numbering}
              loading={loading}
              setLoading={setLoading}
              isVirtualized={isVirtualized}
              onInsertTask={onInsertTask}
              showInsertionIndicators={showInsertionIndicators}
              insertionMode={insertionMode}
            />
            
            {/* Insertion indicator after each task */}
            {showInsertionIndicators && (
              <InsertionIndicator
                position={{
                  type: 'after',
                  targetTaskId: task.id,
                  parentId: itemParentId,
                  level: itemLevel
                }}
                isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `after-${task.id}`)}
                isHovered={hoveredZone?.id === `after-${task.id}`}
                onInsert={() => handleInsertAtPosition({
                  type: 'after',
                  targetTaskId: task.id,
                  parentId: itemParentId,
                  level: itemLevel
                })}
                className="my-1"
                variant={itemLevel > 0 ? 'compact' : 'default'}
              />
            )}
          </div>
        ))}
      </div>
    );
  }

  // For large task lists, implement simple virtualization with insertion indicators
  return (
    <div 
      ref={listRef}
      className="space-y-1"
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {flattenedTasks.slice(0, 100).map(({ task, level: itemLevel, parentId: itemParentId, numbering, uniqueKey }, index) => (
        <div key={uniqueKey} className="relative">
          {/* Insertion indicator before first task */}
          {index === 0 && showInsertionIndicators && (
            <InsertionIndicator
              position={{
                type: 'before',
                targetTaskId: task.id,
                parentId: itemParentId,
                level: itemLevel
              }}
              isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `before-${task.id}`)}
              isHovered={hoveredZone?.id === `before-${task.id}`}
              onInsert={() => handleInsertAtPosition({
                type: 'before',
                targetTaskId: task.id,
                parentId: itemParentId,
                level: itemLevel
              })}
              className="mb-1"
              variant={itemLevel > 0 ? 'compact' : 'default'}
            />
          )}
          
          <TaskItem
            task={task}
            level={itemLevel}
            parentId={itemParentId}
            onUpdateTask={onUpdateTask}
            onOpenSolveModal={onOpenSolveModal}
            onAddTask={onAddTask}
            onAddTaskAfter={onAddTaskAfter}
            onDeleteTask={onDeleteTask}
            onBreakdownTask={handleBreakdown}
            numbering={numbering}
            loading={loading}
            setLoading={setLoading}
            isVirtualized={isVirtualized}
            onInsertTask={onInsertTask}
            showInsertionIndicators={showInsertionIndicators}
            insertionMode={insertionMode}
          />
          
          {/* Insertion indicator after each task */}
          {showInsertionIndicators && (
            <InsertionIndicator
              position={{
                type: 'after',
                targetTaskId: task.id,
                parentId: itemParentId,
                level: itemLevel
              }}
              isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `after-${task.id}`)}
              isHovered={hoveredZone?.id === `after-${task.id}`}
              onInsert={() => handleInsertAtPosition({
                type: 'after',
                targetTaskId: task.id,
                parentId: itemParentId,
                level: itemLevel
              })}
              className="my-1"
              variant={itemLevel > 0 ? 'compact' : 'default'}
            />
          )}
        </div>
      ))}
      {flattenedTasks.length > 100 && (
        <div className="p-4 text-center text-muted-foreground">
          ... und {flattenedTasks.length - 100} weitere Aufgaben
        </div>
      )}
    </div>
  );
});