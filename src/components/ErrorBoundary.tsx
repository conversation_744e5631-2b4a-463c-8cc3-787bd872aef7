/**
 * Error boundary component for handling React errors
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

"use client";

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { ToastManager } from '@/lib/utils/toastManager';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Show error toast
    ToastManager.error({
      title: 'Anwendungsfehler',
      description: 'Ein unerwarteter Fehler ist aufgetreten. Die Seite wird automatisch wiederhergestellt.',
      duration: 8000
    });

    // Log error to external service (if configured)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService(error: Error, errorInfo: ErrorInfo) {
    // In a real application, you would send this to an error tracking service
    // like Sentry, LogRocket, or Bugsnag
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    // For now, just log to console
    console.error('Error logged:', errorData);
    
    // You could also store in localStorage for later transmission
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorData);
      // Keep only last 10 errors
      const recentErrors = existingErrors.slice(-10);
      localStorage.setItem('app_errors', JSON.stringify(recentErrors));
    } catch (e) {
      console.warn('Could not store error in localStorage:', e);
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportError = () => {
    const { error, errorInfo, errorId } = this.state;
    
    const errorReport = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Create mailto link with error details
    const subject = encodeURIComponent(`KI Projekt-Planer Fehler: ${errorId}`);
    const body = encodeURIComponent(`
Fehlerdetails:
${JSON.stringify(errorReport, null, 2)}

Bitte beschreiben Sie, was Sie getan haben, als der Fehler auftrat:
[Ihre Beschreibung hier]
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-slate-100 dark:bg-slate-900 flex items-center justify-center p-4">
          <Card className="max-w-2xl w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <AlertTriangle className="h-16 w-16 text-red-500" />
              </div>
              <CardTitle className="text-2xl text-red-600 dark:text-red-400">
                Oops! Etwas ist schiefgelaufen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center text-slate-600 dark:text-slate-400">
                <p className="mb-2">
                  Ein unerwarteter Fehler ist in der Anwendung aufgetreten.
                </p>
                <p className="text-sm">
                  Fehler-ID: <code className="bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded text-xs">
                    {this.state.errorId}
                  </code>
                </p>
              </div>

              {/* Error details (only in development) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                  <summary className="cursor-pointer font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Entwickler-Details anzeigen
                  </summary>
                  <div className="text-sm text-slate-600 dark:text-slate-400 space-y-2">
                    <div>
                      <strong>Fehler:</strong>
                      <pre className="mt-1 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs overflow-auto">
                        {this.state.error.message}
                      </pre>
                    </div>
                    {this.state.error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 p-2 bg-slate-100 dark:bg-slate-700 rounded text-xs overflow-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              {/* Action buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button onClick={this.handleRetry} className="flex items-center gap-2">
                  <RefreshCw size={16} />
                  Erneut versuchen
                </Button>
                <Button onClick={this.handleReload} variant="outline" className="flex items-center gap-2">
                  <RefreshCw size={16} />
                  Seite neu laden
                </Button>
                <Button onClick={this.handleGoHome} variant="outline" className="flex items-center gap-2">
                  <Home size={16} />
                  Zur Startseite
                </Button>
              </div>

              {/* Report error button */}
              <div className="text-center pt-4 border-t border-slate-200 dark:border-slate-700">
                <Button 
                  onClick={this.handleReportError} 
                  variant="ghost" 
                  size="sm"
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <Bug size={14} className="mr-2" />
                  Fehler melden
                </Button>
              </div>

              {/* Help text */}
              <div className="text-center text-sm text-slate-500 dark:text-slate-400">
                <p>
                  Falls das Problem weiterhin besteht, versuchen Sie:
                </p>
                <ul className="mt-2 space-y-1 text-xs">
                  <li>• Browser-Cache leeren</li>
                  <li>• Einen anderen Browser verwenden</li>
                  <li>• Die Seite in einem privaten/inkognito Fenster öffnen</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component to wrap components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook to manually trigger error boundary (for testing or manual error reporting)
 */
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    // This will trigger the error boundary
    throw error;
  };
}