"use client";

import { memo, useEffect, useMemo, useState, useCallback, useRef } from 'react';
import type { Task, InsertionPosition, InsertionZone, InsertionState } from "@/lib/types";
import { TaskItem } from "@/components/TaskItem";
import { MobileTaskItem } from "@/components/MobileTaskItem";
import { VirtualizedTaskList } from "@/components/VirtualizedTaskList";
import { InsertionIndicator } from "@/components/InsertionIndicator";
import { useRenderPerformance, usePerformanceMonitoring } from "@/hooks/usePerformanceMonitoring";
import { useInsertionZones } from "@/hooks/useInsertionZones";
import { useIsMobile } from "@/hooks/use-mobile";
import { debounceMouseEvent } from "@/lib/utils/debounce";
import { Plus, RefreshCw } from 'lucide-react';
import { showInsertionSuccess, showInsertionError, createInsertionRipple } from '@/lib/utils/insertionFeedback';
import { Button } from "@/components/ui/button";
import { insertionPerformanceMonitor } from '@/lib/utils/insertionPerformanceMonitor';

import type { LoadingStates } from "@/lib/types";

interface TaskListProps {
  tasks: Task[];
  level?: number;
  parentId?: string | null;
  onUpdateTask: (task: Task) => void;
  onOpenSolveModal: (task: Task, isRefinement?: boolean, selectionWrapperId?: string) => void;
  onAddTask: (parentId?: string | null, title?: string, description?: string) => void;
  onAddTaskAfter: (afterIdOrPosition: string | InsertionPosition, parentId?: string | null, title?: string, description?: string) => void;
  onDeleteTask: (taskId: string) => void;
  onBreakdownTask?: (taskId: string) => void;
  numberingPrefix?: string;
  loading?: LoadingStates;
  setLoading?: (loading: LoadingStates | ((prev: LoadingStates) => LoadingStates)) => void;
  // New props for insertion functionality
  onInsertTask?: (position: InsertionPosition) => void;
  showInsertionIndicators?: boolean;
  insertionMode?: 'hover' | 'always' | 'keyboard';
  insertionState?: InsertionState;
  onInsertionStateChange?: (updates: Partial<InsertionState>) => void;
  insertionLoading?: { [positionId: string]: boolean };
  // Mobile touch props
  onRefresh?: () => Promise<void>;
  enablePullToRefresh?: boolean;
}

export const TaskList = memo(function TaskListComponent({ 
  tasks, 
  level = 0, 
  parentId = null, 
  onUpdateTask, 
  onOpenSolveModal, 
  onAddTask,
  onAddTaskAfter,
  onDeleteTask,
  onBreakdownTask,
  numberingPrefix = '',
  loading = {},
  setLoading = () => {},
  onInsertTask,
  showInsertionIndicators = true,
  insertionMode = 'hover',
  insertionState,
  onInsertionStateChange,
  insertionLoading = {},
  onRefresh,
  enablePullToRefresh = true
}: TaskListProps) {
  const perf = usePerformanceMonitoring() as any;
  const startTiming = useCallback((label: string) => {
    const ret = perf.startTiming?.(label) ?? perf.startMeasurement?.(label);
    return typeof ret === 'function' ? ret : () => {};
  }, [perf]);
  const recordMemoryUsage = useCallback(() => {
    try {
      perf.recordMemoryUsage?.() ?? perf.recordMetric?.('memory', 0);
    } catch (_) {
      // Ignore errors in test environment
    }
  }, [perf]);
  useRenderPerformance('TaskList');
  const listRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  
  // State for managing insertion indicators at list level
  const [hoveredZone, setHoveredZone] = useState<InsertionZone | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);
  
  // Mobile touch states
  const [isPullToRefreshActive, setIsPullToRefreshActive] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const touchStartY = useRef<number>(0);
  const touchStartTime = useRef<number>(0);
  const pullThreshold = 80; // Distance needed to trigger refresh

  // Memoize task count calculation
  const taskCount = useMemo(() => {
    const count = (tasks: Task[]): number => {
      return tasks.reduce((total, task) => {
        return total + 1 + (task.subtasks ? count(task.subtasks) : 0);
      }, 0);
    };
    return count(tasks);
  }, [tasks]);

  // Determine if we should use virtualization
  const shouldVirtualize = useMemo(() => taskCount > 20, [taskCount]);

  // Debounced mouse movement handler for better performance
  const debouncedHandleMouseMove = useMemo(() => {
    return debounceMouseEvent<React.MouseEvent>((e) => {
      if (!showInsertionIndicators || insertionMode !== 'hover') return;
      
      setMousePosition({ x: e.clientX, y: e.clientY });
    }, 16); // ~60fps
  }, [showInsertionIndicators, insertionMode, insertionPerformanceMonitor]);

  // Handle mouse movement for insertion zone detection
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    debouncedHandleMouseMove(e);
  }, [debouncedHandleMouseMove]);

  // Handle mouse leave to clear hover state
  const handleMouseLeave = useCallback(() => {
    setHoveredZone(null);
    setMousePosition(null);
  }, []);

  // Handle insertion at specific position
  const handleInsertAtPosition = useCallback((position: InsertionPosition) => {
    try {
      if (onInsertTask) {
        onInsertTask(position);
      } else if (onAddTaskAfter) {
        // Fallback to existing onAddTaskAfter functionality
        onAddTaskAfter(position.targetTaskId, position.parentId);
      }
      
      // Show success feedback (non-blocking)
      showInsertionSuccess({
        position,
        showToast: true,
        playSound: true,
        hapticFeedback: true
      }).catch(error => {
        console.warn('Failed to show insertion success feedback:', error);
      });
      
    } catch (error) {
      // Show error feedback
      console.error('Insertion error:', error);
      showInsertionError({
        position,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler',
        retryAction: () => handleInsertAtPosition(position)
      });
    }
  }, [onInsertTask, onAddTaskAfter]);

  // Mobile touch handlers for pull-to-refresh
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !enablePullToRefresh || level > 0) return;
    
    const touch = e.touches[0];
    touchStartY.current = touch.clientY;
    touchStartTime.current = Date.now();
    
    // Only enable pull-to-refresh if we're at the top of the list
    const scrollTop = listRef.current?.scrollTop || 0;
    if (scrollTop === 0) {
      setIsPullToRefreshActive(true);
    }
  }, [isMobile, enablePullToRefresh, level]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPullToRefreshActive || !isMobile || level > 0) return;
    
    const touch = e.touches[0];
    const deltaY = touch.clientY - touchStartY.current;
    
    // Only allow pull down
    if (deltaY > 0) {
      e.preventDefault();
      const distance = Math.min(deltaY * 0.5, pullThreshold * 1.5); // Damping effect
      setPullDistance(distance);
    }
  }, [isPullToRefreshActive, isMobile, level, pullThreshold]);

  const handleTouchEnd = useCallback(async () => {
    if (!isPullToRefreshActive || !isMobile || level > 0) return;
    
    setIsPullToRefreshActive(false);
    
    if (pullDistance >= pullThreshold && onRefresh && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
        // Haptic feedback on successful refresh
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setPullDistance(0);
  }, [isPullToRefreshActive, isMobile, level, pullDistance, pullThreshold, onRefresh, isRefreshing]);

  // Haptic feedback helper
  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!isMobile) return;
    
    // Try modern Haptic API first
    if ('vibrate' in navigator) {
      const patterns = {
        light: 10,
        medium: 20,
        heavy: 50
      };
      navigator.vibrate(patterns[type]);
    }
  }, [isMobile]);

  // Effect to detect which insertion zone is being hovered
  useEffect(() => {
    if (!mousePosition || !listRef.current || !showInsertionIndicators || insertionMode !== 'hover') {
      setHoveredZone(null);
      return;
    }

    const { x, y } = mousePosition;
    let foundZone: InsertionZone | null = null;

    // Check each task for insertion zones
    tasks.forEach((task, index) => {
      const taskElement = listRef.current?.querySelector(`[data-task-id="${task.id}"]`) as HTMLElement;
      if (!taskElement) return;

      const taskRect = taskElement.getBoundingClientRect();
      const ZONE_HEIGHT = 20;
      const ZONE_OFFSET = 10;

      // Check before zone (for first task)
      if (index === 0) {
        const beforeZone = new DOMRect(
          taskRect.left,
          taskRect.top - ZONE_OFFSET,
          taskRect.width,
          ZONE_HEIGHT
        );
        
        if (x >= beforeZone.left && x <= beforeZone.right && 
            y >= beforeZone.top && y <= beforeZone.bottom) {
          foundZone = {
            id: `before-${task.id}`,
            type: 'before',
            bounds: beforeZone,
            targetTaskId: task.id,
            parentId: parentId,
            level: level
          };
          return;
        }
      }

      // Check after zone
      const afterZone = new DOMRect(
        taskRect.left,
        taskRect.bottom - ZONE_OFFSET,
        taskRect.width,
        ZONE_HEIGHT
      );
      
      if (x >= afterZone.left && x <= afterZone.right && 
          y >= afterZone.top && y <= afterZone.bottom) {
        foundZone = {
          id: `after-${task.id}`,
          type: 'after',
          bounds: afterZone,
          targetTaskId: task.id,
          parentId: parentId,
          level: level
        };
        return;
      }

      // Check between parent and child zone
      if (task.subtasks && task.subtasks.length > 0) {
        const firstSubtaskElement = taskElement.querySelector(`[data-task-id="${task.subtasks[0].id}"]`) as HTMLElement;
        if (firstSubtaskElement) {
          const firstSubtaskRect = firstSubtaskElement.getBoundingClientRect();
          const gapHeight = firstSubtaskRect.top - taskRect.bottom;
          
          if (gapHeight >= ZONE_HEIGHT) {
            const betweenZone = new DOMRect(
              taskRect.left + 20,
              taskRect.bottom + 5,
              taskRect.width - 20,
              Math.min(gapHeight - 10, ZONE_HEIGHT)
            );
            
            if (x >= betweenZone.left && x <= betweenZone.right && 
                y >= betweenZone.top && y <= betweenZone.bottom) {
              foundZone = {
                id: `between-${task.id}-${task.subtasks[0].id}`,
                type: 'between_parent_child',
                bounds: betweenZone,
                targetTaskId: task.id,
                parentId: task.id,
                level: level + 1
              };
              return;
            }
          }
        }
      }
    });

    setHoveredZone(foundZone);
  }, [mousePosition, tasks, parentId, level, showInsertionIndicators, insertionMode]);

  // Performance monitoring for task list rendering
  useEffect(() => {
    if (level === 0) { // Only monitor at root level
      const endTiming = startTiming('taskListRender');
      
      // Use setTimeout to measure after render
      setTimeout(() => {
        endTiming();
        recordMemoryUsage();
      }, 0);
    }
  }, [tasks, level, startTiming, recordMemoryUsage]);

  if (!tasks || tasks.length === 0) {
    return null;
  }

  // Use virtualized list for large task trees
  if (shouldVirtualize && level === 0) {
    return (
      <VirtualizedTaskList
        tasks={tasks}
        level={level}
        parentId={parentId}
        onUpdateTask={onUpdateTask}
        onOpenSolveModal={onOpenSolveModal}
        onAddTask={onAddTask}
        onAddTaskAfter={onAddTaskAfter}
        onDeleteTask={onDeleteTask}
        onBreakdownTask={onBreakdownTask}
        numberingPrefix={numberingPrefix}
        loading={loading}
        setLoading={setLoading}
        isVirtualized={true}
        onInsertTask={onInsertTask}
        showInsertionIndicators={showInsertionIndicators}
        insertionMode={insertionMode}
      />
    );
  }

  // Regular rendering for smaller lists
  return (
    <>
      {/* Enhanced hidden help text for screen readers */}
      {level === 0 && (
        <div id="task-list-help" className="sr-only">
          Task list with insertion capabilities. Use Tab to navigate between tasks and insertion points. 
          Focus on a task and use keyboard shortcuts to insert new tasks: 
          Enter to insert after current task, Shift+Enter to insert before current task, 
          Tab+Enter to insert as subtask of current task. 
          Use arrow keys to navigate between insertion points when in insertion mode. 
          Press Escape to cancel insertion mode. 
          Insertion indicators appear as plus buttons between tasks when hovering or in keyboard mode.
          {isMobile && enablePullToRefresh && ' On mobile: Pull down to refresh the task list.'}
        </div>
      )}
      
      {/* Pull-to-refresh indicator for mobile */}
      {isMobile && enablePullToRefresh && level === 0 && (isPullToRefreshActive || isRefreshing) && (
        <div 
          className="flex items-center justify-center py-4 transition-all duration-200"
          style={{
            transform: `translateY(${Math.max(0, pullDistance - 40)}px)`,
            opacity: pullDistance > 20 ? 1 : pullDistance / 20
          }}
        >
          <div className="flex items-center space-x-2 text-indigo-600 dark:text-indigo-400">
            <RefreshCw 
              className={`h-5 w-5 ${isRefreshing || pullDistance >= pullThreshold ? 'animate-spin' : ''}`} 
            />
            <span className="text-sm font-medium">
              {isRefreshing 
                ? 'Aktualisiere...' 
                : pullDistance >= pullThreshold 
                  ? 'Loslassen zum Aktualisieren' 
                  : 'Zum Aktualisieren herunterziehen'
              }
            </span>
          </div>
        </div>
      )}
      
      <div 
        ref={listRef}
        className={`relative ${isMobile ? 'touch-pan-y' : ''}`}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        role="list"
        aria-label={`Task list at level ${level + 1}${tasks.length > 0 ? ` with ${tasks.length} task${tasks.length === 1 ? '' : 's'}` : ''}`}
        aria-describedby={level === 0 ? "task-list-help" : undefined}
        aria-live="polite"
        aria-relevant="additions removals"
        style={{
          transform: isMobile && isPullToRefreshActive ? `translateY(${pullDistance}px)` : undefined,
          transition: isPullToRefreshActive ? 'none' : 'transform 0.2s ease-out'
        }}
      >
      {/* Insertion indicator before first task */}
      {tasks.length > 0 && showInsertionIndicators && (
        <InsertionIndicator
          position={{
            type: 'before',
            targetTaskId: tasks[0].id,
            parentId: parentId,
            level: level
          }}
          isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `before-${tasks[0].id}`)}
          isHovered={hoveredZone?.id === `before-${tasks[0].id}`}
          onInsert={() => handleInsertAtPosition({
            type: 'before',
            targetTaskId: tasks[0].id,
            parentId: parentId,
            level: level
          })}
          className="mb-1"
          variant={isMobile ? 'touch' : (level > 0 ? 'compact' : 'default')}
          touchFriendly={isMobile}
          onLongPress={() => handleInsertAtPosition({
            type: 'before',
            targetTaskId: tasks[0].id,
            parentId: parentId,
            level: level
          })}
        />
      )}
      
      {tasks.map((task, index) => (
        <div key={task.id} className="relative">
          {isMobile ? (
            <MobileTaskItem
              task={task}
              level={level}
              parentId={parentId}
              onUpdateTask={onUpdateTask}
              onOpenSolveModal={onOpenSolveModal}
              onAddTask={onAddTask}
              onAddTaskAfter={onAddTaskAfter}
              onDeleteTask={onDeleteTask}
              onBreakdownTask={onBreakdownTask}
              numbering={`${numberingPrefix}${index + 1}`}
              loading={loading}
              setLoading={setLoading}
              isVirtualized={false}
              onInsertTask={onInsertTask}
              showInsertionIndicators={showInsertionIndicators}
              insertionMode={insertionMode}
              insertionState={insertionState}
              onInsertionStateChange={onInsertionStateChange}
              insertionLoading={insertionLoading}
              enableSwipeActions={true}
              swipeThreshold={80}
            />
          ) : (
            <TaskItem
              task={task}
              level={level}
              parentId={parentId}
              onUpdateTask={onUpdateTask}
              onOpenSolveModal={onOpenSolveModal}
              onAddTask={onAddTask}
              onAddTaskAfter={onAddTaskAfter}
              onDeleteTask={onDeleteTask}
              onBreakdownTask={onBreakdownTask}
              numbering={`${numberingPrefix}${index + 1}`}
              loading={loading}
              setLoading={setLoading}
              isVirtualized={false}
              onInsertTask={onInsertTask}
              showInsertionIndicators={showInsertionIndicators}
              insertionMode={insertionMode}
              insertionState={insertionState}
              onInsertionStateChange={onInsertionStateChange}
              insertionLoading={insertionLoading}
            />
          )}
          
          {/* Insertion indicator after each task */}
          {showInsertionIndicators && (
            <InsertionIndicator
              position={{
                type: 'after',
                targetTaskId: task.id,
                parentId: parentId,
                level: level
              }}
              isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `after-${task.id}`)}
              isHovered={hoveredZone?.id === `after-${task.id}`}
              onInsert={() => handleInsertAtPosition({
                type: 'after',
                targetTaskId: task.id,
                parentId: parentId,
                level: level
              })}
              className="my-1"
              variant={isMobile ? 'touch' : (level > 0 ? 'compact' : 'default')}
              touchFriendly={isMobile}
              onLongPress={() => handleInsertAtPosition({
                type: 'after',
                targetTaskId: task.id,
                parentId: parentId,
                level: level
              })}
            />
          )}
          
          {/* Insertion indicator between parent and first subtask */}
          {showInsertionIndicators && task.subtasks && task.subtasks.length > 0 && (
            <InsertionIndicator
              position={{
                type: 'between_parent_child',
                targetTaskId: task.id,
                parentId: task.id,
                level: level + 1
              }}
              isVisible={insertionMode === 'always' || (insertionMode === 'hover' && hoveredZone?.id === `between-${task.id}-${task.subtasks[0].id}`)}
              isHovered={hoveredZone?.id === `between-${task.id}-${task.subtasks[0].id}`}
              onInsert={() => handleInsertAtPosition({
                type: 'between_parent_child',
                targetTaskId: task.id,
                parentId: task.id,
                level: level + 1
              })}
              className="ml-4 my-1"
              variant={isMobile ? 'touch' : 'compact'}
              touchFriendly={isMobile}
              onLongPress={() => handleInsertAtPosition({
                type: 'between_parent_child',
                targetTaskId: task.id,
                parentId: task.id,
                level: level + 1
              })}
            />
          )}
        </div>
      ))}
      </div>
    </>
  );
});
