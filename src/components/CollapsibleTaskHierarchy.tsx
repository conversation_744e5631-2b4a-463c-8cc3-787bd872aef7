"use client";

import { useState, useCallback, useRef } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import type { Task } from '@/lib/types';

interface CollapsibleTaskHierarchyProps {
  task: Task;
  level: number;
  isCollapsed?: boolean;
  onToggleCollapse?: (taskId: string, collapsed: boolean) => void;
  children: React.ReactNode;
  className?: string;
}

export function CollapsibleTaskHierarchy({
  task,
  level,
  isCollapsed = false,
  onToggleCollapse,
  children,
  className
}: CollapsibleTaskHierarchyProps) {
  const isMobile = useIsMobile();
  const [internalCollapsed, setInternalCollapsed] = useState(isCollapsed);
  const contentRef = useRef<HTMLDivElement>(null);
  
  const hasSubtasks = task.subtasks && task.subtasks.length > 0;
  const collapsed = onToggleCollapse ? isCollapsed : internalCollapsed;

  const handleToggle = useCallback(() => {
    const newCollapsed = !collapsed;
    
    if (onToggleCollapse) {
      onToggleCollapse(task.id, newCollapsed);
    } else {
      setInternalCollapsed(newCollapsed);
    }

    // Haptic feedback on mobile
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  }, [collapsed, onToggleCollapse, task.id, isMobile]);

  if (!hasSubtasks) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div className={cn("relative", className)}>
      {/* Main task content */}
      <div className="relative">
        {children}
        
        {/* Collapse/Expand button */}
        {hasSubtasks && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggle}
            className={cn(
              "absolute top-2 right-2 h-8 w-8 p-0 opacity-70 hover:opacity-100 transition-opacity",
              isMobile && "mobile-touch-target h-10 w-10"
            )}
            aria-label={collapsed ? "Unteraufgaben anzeigen" : "Unteraufgaben verbergen"}
            aria-expanded={!collapsed}
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>

      {/* Collapsible subtasks container */}
      {hasSubtasks && (
        <div
          ref={contentRef}
          className={cn(
            "overflow-hidden transition-all duration-300 ease-in-out",
            collapsed ? "max-h-0 opacity-0" : "max-h-[2000px] opacity-100",
            isMobile && "task-hierarchy-mobile"
          )}
          style={{
            transitionProperty: collapsed ? 'max-height, opacity' : 'max-height, opacity'
          }}
        >
          <div className={cn(
            "pt-2",
            level > 0 && "ml-4 pl-4 border-l-2 border-slate-200 dark:border-slate-700",
            isMobile && "ml-2 pl-3 border-l border-slate-300 dark:border-slate-600"
          )}>
            {/* Subtasks indicator */}
            {!collapsed && (
              <div className="mb-2 text-xs text-muted-foreground flex items-center gap-1">
                <span>{task.subtasks?.length} Unteraufgabe{task.subtasks?.length !== 1 ? 'n' : ''}</span>
              </div>
            )}
            
            {/* This is where subtasks would be rendered */}
            <div className="space-y-1">
              {/* Placeholder for subtask rendering - this would be handled by the parent component */}
            </div>
          </div>
        </div>
      )}

      {/* Collapsed indicator */}
      {collapsed && hasSubtasks && (
        <div 
          className={cn(
            "mt-2 text-xs text-muted-foreground flex items-center gap-2 cursor-pointer hover:text-foreground transition-colors",
            isMobile && "py-2"
          )}
          onClick={handleToggle}
        >
          <div className="flex -space-x-1">
            {task.subtasks?.slice(0, 3).map((_, index) => (
              <div
                key={index}
                className="w-2 h-2 rounded-full bg-muted-foreground/40 border border-background"
              />
            ))}
            {task.subtasks && task.subtasks.length > 3 && (
              <div className="w-2 h-2 rounded-full bg-muted-foreground/20 border border-background" />
            )}
          </div>
          <span>
            {task.subtasks?.length} versteckte Unteraufgabe{task.subtasks?.length !== 1 ? 'n' : ''}
          </span>
        </div>
      )}
    </div>
  );
}

// Hook for managing collapse state across multiple tasks
export function useCollapsibleTasks(initialCollapsedTasks: string[] = []) {
  const [collapsedTasks, setCollapsedTasks] = useState<Set<string>>(
    new Set(initialCollapsedTasks)
  );

  const toggleTaskCollapse = useCallback((taskId: string, collapsed: boolean) => {
    setCollapsedTasks(prev => {
      const newSet = new Set(prev);
      if (collapsed) {
        newSet.add(taskId);
      } else {
        newSet.delete(taskId);
      }
      return newSet;
    });
  }, []);

  const isTaskCollapsed = useCallback((taskId: string) => {
    return collapsedTasks.has(taskId);
  }, [collapsedTasks]);

  const collapseAll = useCallback((taskIds: string[]) => {
    setCollapsedTasks(new Set(taskIds));
  }, []);

  const expandAll = useCallback(() => {
    setCollapsedTasks(new Set());
  }, []);

  return {
    isTaskCollapsed,
    toggleTaskCollapse,
    collapseAll,
    expandAll,
    collapsedTaskIds: Array.from(collapsedTasks)
  };
}