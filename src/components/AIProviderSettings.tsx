"use client";

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { 
  Bot, 
  Key, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  EyeOff,
  TestTube,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  unifiedAIClient, 
  aiProviderRegistry, 
  AIProvider, 
  AIProviderConfig, 
  AIProviderCredentials, 
  AIProviderSettings 
} from '@/lib/ai';
import { GeminiProvider } from '@/lib/ai/providers/gemini';

interface AIProviderSettingsProps {
  className?: string;
}

export function AIProviderSettings({ className }: AIProviderSettingsProps) {
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [activeProviderId, setActiveProviderId] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null);
  const [credentials, setCredentials] = useState<AIProviderCredentials>({});
  const [settings, setSettings] = useState<AIProviderSettings>({});
  const [showApiKey, setShowApiKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<boolean | null>(null);
  const [isTesting, setIsTesting] = useState(false);

  // Load providers and active provider on mount
  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = useCallback(() => {
    const availableProviders = unifiedAIClient.getAvailableProviders();
    setProviders(availableProviders);
    
    const activeProvider = aiProviderRegistry.getActiveProvider();
    if (activeProvider) {
      setActiveProviderId(activeProvider.getId());
      setSelectedProvider(activeProvider);
      setCredentials(activeProvider.getSettings());
      setSettings(activeProvider.getSettings());
    } else if (availableProviders.length > 0) {
      // Select first provider if none is active
      const firstProvider = availableProviders[0];
      setSelectedProvider(firstProvider);
      setCredentials({});
      setSettings(firstProvider.getSettings());
    }
  }, []);

  const handleProviderSelect = useCallback((providerId: string) => {
    const provider = providers.find(p => p.getId() === providerId);
    if (provider) {
      setSelectedProvider(provider);
      setCredentials({});
      setSettings(provider.getSettings());
      setValidationResult(null);
    }
  }, [providers]);

  const handleCredentialChange = useCallback((key: string, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [key]: value
    }));
    setValidationResult(null);
  }, []);

  const handleSettingChange = useCallback((key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const validateCredentials = useCallback(async () => {
    if (!selectedProvider) return;

    setIsValidating(true);
    try {
      // Update provider with new credentials
      selectedProvider.updateCredentials(credentials);
      const isValid = await selectedProvider.validateCredentials();
      setValidationResult(isValid);
    } catch (error) {
      console.error('Credential validation failed:', error);
      setValidationResult(false);
    } finally {
      setIsValidating(false);
    }
  }, [selectedProvider, credentials]);

  const testProvider = useCallback(async () => {
    if (!selectedProvider) return;

    setIsTesting(true);
    try {
      // Update provider with current settings
      selectedProvider.updateCredentials(credentials);
      selectedProvider.updateSettings(settings);

      // Test with a simple task generation
      const response = await selectedProvider.generateTasks('Test-Projekt', 'Ein einfacher Test');
      
      if (response.success) {
        alert('Test erfolgreich! Der AI-Provider funktioniert korrekt.');
      } else {
        alert(`Test fehlgeschlagen: ${response.userFriendlyError || response.error}`);
      }
    } catch (error) {
      console.error('Provider test failed:', error);
      alert('Test fehlgeschlagen: Unbekannter Fehler');
    } finally {
      setIsTesting(false);
    }
  }, [selectedProvider, credentials, settings]);

  const saveConfiguration = useCallback(() => {
    if (!selectedProvider) return;

    try {
      // Update provider with new configuration
      selectedProvider.updateCredentials(credentials);
      selectedProvider.updateSettings(settings);

      // Set as active provider if configured
      if (selectedProvider.isConfigured()) {
        aiProviderRegistry.setActiveProvider(selectedProvider.getId());
        setActiveProviderId(selectedProvider.getId());
      }

      alert('Konfiguration gespeichert!');
    } catch (error) {
      console.error('Failed to save configuration:', error);
      alert('Fehler beim Speichern der Konfiguration');
    }
  }, [selectedProvider, credentials, settings]);

  const addGeminiProvider = useCallback(() => {
    const geminiProvider = new GeminiProvider();
    aiProviderRegistry.register(geminiProvider);
    loadProviders();
  }, [loadProviders]);

  if (providers.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI-Provider
          </CardTitle>
          <CardDescription>
            Keine AI-Provider verfügbar. Fügen Sie einen Provider hinzu, um AI-Funktionen zu nutzen.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={addGeminiProvider} className="w-full">
            <Bot className="h-4 w-4 mr-2" />
            Gemini Provider hinzufügen
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Provider Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI-Provider Auswahl
          </CardTitle>
          <CardDescription>
            Wählen Sie einen AI-Provider für die Aufgabengenerierung und Inhaltserstellung.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="provider-select">Aktiver Provider</Label>
            <Select
              value={selectedProvider?.getId() || ''}
              onValueChange={handleProviderSelect}
            >
              <SelectTrigger id="provider-select">
                <SelectValue placeholder="Provider auswählen..." />
              </SelectTrigger>
              <SelectContent>
                {providers.map((provider) => (
                  <SelectItem key={provider.getId()} value={provider.getId()}>
                    <div className="flex items-center gap-2">
                      <span>{provider.getName()}</span>
                      {provider.getId() === activeProviderId && (
                        <Badge variant="secondary" className="text-xs">Aktiv</Badge>
                      )}
                      {provider.isConfigured() ? (
                        <CheckCircle className="h-3 w-3 text-green-500" />
                      ) : (
                        <XCircle className="h-3 w-3 text-red-500" />
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedProvider && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                {selectedProvider.getDescription()}
              </p>
              <div className="flex flex-wrap gap-1">
                {selectedProvider.getSupportedFeatures().map((feature) => (
                  <Badge key={feature} variant="outline" className="text-xs">
                    {feature.replace('_', ' ')}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Provider Configuration */}
      {selectedProvider && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Provider Konfiguration
            </CardTitle>
            <CardDescription>
              Konfigurieren Sie die Zugangsdaten und Einstellungen für {selectedProvider.getName()}.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* API Key Configuration */}
            {selectedProvider.getConfig().apiKeyRequired && (
              <div className="space-y-2">
                <Label htmlFor="api-key">API-Schlüssel</Label>
                <div className="flex gap-2">
                  <div className="relative flex-1">
                    <Input
                      id="api-key"
                      type={showApiKey ? 'text' : 'password'}
                      value={credentials.apiKey || ''}
                      onChange={(e) => handleCredentialChange('apiKey', e.target.value)}
                      placeholder="Geben Sie Ihren API-Schlüssel ein..."
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      {showApiKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <Button
                    onClick={validateCredentials}
                    disabled={!credentials.apiKey || isValidating}
                    variant="outline"
                  >
                    {isValidating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                
                {validationResult !== null && (
                  <div className={cn(
                    "flex items-center gap-2 text-sm",
                    validationResult ? "text-green-600" : "text-red-600"
                  )}>
                    {validationResult ? (
                      <>
                        <CheckCircle className="h-4 w-4" />
                        API-Schlüssel ist gültig
                      </>
                    ) : (
                      <>
                        <XCircle className="h-4 w-4" />
                        API-Schlüssel ist ungültig
                      </>
                    )}
                  </div>
                )}
              </div>
            )}

            <Separator />

            {/* Provider Settings */}
            <div className="space-y-4">
              <h4 className="font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Erweiterte Einstellungen
              </h4>

              {/* Temperature Setting */}
              {settings.temperature !== undefined && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Kreativität (Temperature)</Label>
                    <span className="text-sm text-muted-foreground">
                      {settings.temperature}
                    </span>
                  </div>
                  <Slider
                    value={[settings.temperature]}
                    onValueChange={(value) => handleSettingChange('temperature', value[0])}
                    max={2}
                    min={0}
                    step={0.1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Konservativ</span>
                    <span>Kreativ</span>
                  </div>
                </div>
              )}

              {/* Max Tokens Setting */}
              {settings.maxTokens !== undefined && (
                <div className="space-y-2">
                  <Label htmlFor="max-tokens">Maximale Tokens</Label>
                  <Input
                    id="max-tokens"
                    type="number"
                    value={settings.maxTokens}
                    onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value))}
                    min={100}
                    max={8192}
                  />
                  <p className="text-xs text-muted-foreground">
                    Begrenzt die Länge der AI-Antworten (100-8192)
                  </p>
                </div>
              )}

              {/* Timeout Setting */}
              {settings.timeout !== undefined && (
                <div className="space-y-2">
                  <Label htmlFor="timeout">Timeout (Sekunden)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={Math.floor(settings.timeout / 1000)}
                    onChange={(e) => handleSettingChange('timeout', parseInt(e.target.value) * 1000)}
                    min={5}
                    max={120}
                  />
                  <p className="text-xs text-muted-foreground">
                    Maximale Wartezeit für AI-Anfragen (5-120 Sekunden)
                  </p>
                </div>
              )}
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                onClick={testProvider}
                disabled={!selectedProvider.isConfigured() || isTesting}
                variant="outline"
                className="flex-1"
              >
                {isTesting ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Testen
              </Button>
              
              <Button
                onClick={saveConfiguration}
                disabled={!credentials.apiKey}
                className="flex-1"
              >
                <Settings className="h-4 w-4 mr-2" />
                Speichern
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Provider Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Provider Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {providers.map((provider) => (
              <div key={provider.getId()} className="flex items-center justify-between p-2 rounded border">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{provider.getName()}</span>
                  {provider.getId() === activeProviderId && (
                    <Badge variant="default" className="text-xs">Aktiv</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {provider.isConfigured() ? (
                    <div className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm">Konfiguriert</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 text-red-600">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">Nicht konfiguriert</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}