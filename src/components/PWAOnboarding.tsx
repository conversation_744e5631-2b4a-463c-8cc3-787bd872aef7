/**
 * PWA Onboarding Component
 * 
 * Provides a guided onboarding experience for first-time PWA users,
 * explaining key features and helping them get started.
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Smartphone,
  Wifi,
  WifiOff,
  Download,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Star,
  Zap,
  Shield,
  Brain,
  FileDown,
  Settings,
  Play,
  X
} from 'lucide-react';

interface PWAOnboardingProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  onComplete?: () => void;
}

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export function PWAOnboarding({ isOpen, onOpenChange, onComplete }: PWAOnboardingProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Check if user has seen onboarding before
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('pwa-onboarding-completed');
    if (!hasSeenOnboarding && !isOpen) {
      // Show onboarding automatically for new users
      setTimeout(() => setIsVisible(true), 2000);
    }
  }, [isOpen]);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Willkommen zur PWA!',
      description: 'Entdecken Sie die Vorteile einer Progressive Web App',
      icon: <Star className="text-yellow-500" size={48} />,
      content: (
        <div className="text-center space-y-4">
          <div className="mx-auto w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Smartphone className="text-white" size={40} />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">KI Projekt-Planer als PWA</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Diese App wurde als Progressive Web App entwickelt und bietet Ihnen 
              alle Vorteile einer nativen App direkt im Browser.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="text-green-500 mx-auto mb-2" size={20} />
              <div className="font-medium">Offline-fähig</div>
            </div>
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Zap className="text-blue-500 mx-auto mb-2" size={20} />
              <div className="font-medium">Schnell</div>
            </div>
            <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <Shield className="text-purple-500 mx-auto mb-2" size={20} />
              <div className="font-medium">Sicher</div>
            </div>
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <Download className="text-orange-500 mx-auto mb-2" size={20} />
              <div className="font-medium">Installierbar</div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'offline',
      title: 'Offline arbeiten',
      description: 'Nutzen Sie die App auch ohne Internetverbindung',
      icon: <WifiOff className="text-red-500" size={48} />,
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="mx-auto w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
              <WifiOff className="text-red-500" size={32} />
            </div>
            <h3 className="text-lg font-semibold mb-2">Vollständig offline-fähig</h3>
            <p className="text-slate-600 dark:text-slate-300 mb-4">
              Arbeiten Sie an Ihren Projekten, auch wenn keine Internetverbindung verfügbar ist.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-600 dark:text-green-400 flex items-center gap-2">
                <CheckCircle size={16} />
                Offline verfügbar
              </h4>
              <ul className="text-sm space-y-1 text-slate-600 dark:text-slate-300">
                <li>• Projekte erstellen und bearbeiten</li>
                <li>• Aufgaben verwalten</li>
                <li>• Daten exportieren</li>
                <li>• App-Navigation</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600 dark:text-blue-400 flex items-center gap-2">
                <Wifi size={16} />
                Online-Features
              </h4>
              <ul className="text-sm space-y-1 text-slate-600 dark:text-slate-300">
                <li>• AI-Aufgabenerstellung</li>
                <li>• Inhalte generieren</li>
                <li>• Automatische Synchronisation</li>
                <li>• App-Updates</li>
              </ul>
            </div>
          </div>

          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Tipp:</strong> Alle offline erstellten Inhalte werden automatisch 
              synchronisiert, sobald Sie wieder online sind.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'installation',
      title: 'App installieren',
      description: 'Installieren Sie die App für die beste Erfahrung',
      icon: <Download className="text-blue-500" size={48} />,
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <div className="mx-auto w-20 h-20 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
              <Download className="text-blue-500" size={32} />
            </div>
            <h3 className="text-lg font-semibold mb-2">App installieren (optional)</h3>
            <p className="text-slate-600 dark:text-slate-300 mb-4">
              Installieren Sie die App auf Ihrem Gerät für schnelleren Zugriff und bessere Performance.
            </p>
          </div>

          <div className="space-y-3">
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Smartphone className="text-green-500" size={16} />
                Mobile Geräte
              </h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Tippen Sie auf "Zur Startseite hinzufügen" in Ihrem Browser-Menü 
                oder folgen Sie den Installationshinweisen.
              </p>
            </div>

            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Settings className="text-blue-500" size={16} />
                Desktop
              </h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Klicken Sie auf das Installations-Symbol in der Adressleiste 
                oder nutzen Sie das Browser-Menü.
              </p>
            </div>
          </div>

          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h4 className="font-medium mb-1 text-yellow-700 dark:text-yellow-300">
              Vorteile der Installation:
            </h4>
            <ul className="text-sm text-slate-600 dark:text-slate-300 space-y-1">
              <li>• Schnellerer App-Start</li>
              <li>• Eigenes App-Icon</li>
              <li>• Vollbild-Modus</li>
              <li>• Bessere Performance</li>
            </ul>
          </div>
        </div>
      ),
      action: {
        label: 'Installation überspringen',
        onClick: () => setCurrentStep(currentStep + 1)
      }
    },
    {
      id: 'features',
      title: 'Wichtige Features',
      description: 'Lernen Sie die wichtigsten PWA-Features kennen',
      icon: <Brain className="text-purple-500" size={48} />,
      content: (
        <div className="space-y-4">
          <div className="text-center mb-4">
            <h3 className="text-lg font-semibold mb-2">Wichtige Features im Überblick</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Diese Features machen Ihre Arbeit effizienter und flexibler.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <Brain className="text-purple-500 mb-2" size={20} />
              <h4 className="font-medium mb-1">AI-Integration</h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Nutzen Sie KI für Aufgabenerstellung und Inhalte (online)
              </p>
            </div>

            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <FileDown className="text-green-500 mb-2" size={20} />
              <h4 className="font-medium mb-1">Export & Backup</h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Exportieren Sie Projekte als PDF, CSV oder Markdown
              </p>
            </div>

            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Shield className="text-blue-500 mb-2" size={20} />
              <h4 className="font-medium mb-1">Datenschutz</h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Alle Daten bleiben lokal auf Ihrem Gerät
              </p>
            </div>

            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <Zap className="text-orange-500 mb-2" size={20} />
              <h4 className="font-medium mb-1">Performance</h4>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Intelligentes Caching für schnelle Ladezeiten
              </p>
            </div>
          </div>

          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Play className="text-indigo-500" size={16} />
              Schnellstart-Tipp
            </h4>
            <p className="text-sm text-slate-600 dark:text-slate-300">
              Beginnen Sie mit einem einfachen Projekt, um alle Features kennenzulernen. 
              Die App führt Sie durch den Prozess.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'complete',
      title: 'Bereit zum Start!',
      description: 'Sie sind bereit, die PWA zu nutzen',
      icon: <CheckCircle className="text-green-500" size={48} />,
      content: (
        <div className="text-center space-y-4">
          <div className="mx-auto w-24 h-24 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center">
            <CheckCircle className="text-white" size={40} />
          </div>
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">Alles bereit!</h3>
            <p className="text-slate-600 dark:text-slate-300">
              Sie können jetzt die volle Power der PWA nutzen. 
              Erstellen Sie Ihr erstes Projekt und entdecken Sie alle Möglichkeiten.
            </p>
          </div>

          <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg">
            <h4 className="font-medium mb-2">Nächste Schritte:</h4>
            <ul className="text-sm text-slate-600 dark:text-slate-300 space-y-1 text-left">
              <li>1. Erstellen Sie Ihr erstes Projekt</li>
              <li>2. Testen Sie die Offline-Funktionalität</li>
              <li>3. Nutzen Sie AI-Features (online)</li>
              <li>4. Exportieren Sie Ihre Ergebnisse</li>
            </ul>
          </div>

          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              <strong>Tipp:</strong> Nutzen Sie das Hilfe-Menü (?) für detaillierte 
              Informationen zu allen PWA-Features.
            </p>
          </div>
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    localStorage.setItem('pwa-onboarding-completed', 'true');
    localStorage.setItem('pwa-onboarding-completed-at', new Date().toISOString());
    onComplete?.();
    onOpenChange?.(false);
    setIsVisible(false);
  };

  const handleSkip = () => {
    localStorage.setItem('pwa-onboarding-skipped', 'true');
    onOpenChange?.(false);
    setIsVisible(false);
  };

  const isOpen_ = isOpen ?? isVisible;

  return (
    <Dialog open={isOpen_} onOpenChange={onOpenChange || setIsVisible}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              {currentStepData.icon}
              PWA Einführung
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSkip}
              className="text-slate-500 hover:text-slate-700"
            >
              <X size={16} />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-slate-600 dark:text-slate-300">
                Schritt {currentStep + 1} von {steps.length}
              </span>
              <Badge variant="outline">
                {Math.round(progress)}%
              </Badge>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Content */}
          <div className="space-y-4">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-2">{currentStepData.title}</h2>
              <p className="text-slate-600 dark:text-slate-300">
                {currentStepData.description}
              </p>
            </div>

            <div className="min-h-[300px]">
              {currentStepData.content}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="flex gap-2">
              {currentStep > 0 && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  className="gap-2"
                >
                  <ArrowLeft size={16} />
                  Zurück
                </Button>
              )}
              {currentStepData.action && (
                <Button
                  variant="ghost"
                  onClick={currentStepData.action.onClick}
                  className="text-slate-500"
                >
                  {currentStepData.action.label}
                </Button>
              )}
            </div>

            <div className="flex gap-2">
              <Button
                variant="ghost"
                onClick={handleSkip}
                className="text-slate-500"
              >
                Überspringen
              </Button>
              <Button onClick={handleNext} className="gap-2">
                {currentStep === steps.length - 1 ? 'Fertig' : 'Weiter'}
                {currentStep === steps.length - 1 ? (
                  <CheckCircle size={16} />
                ) : (
                  <ArrowRight size={16} />
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default PWAOnboarding;