"use client";

import { useState, useCallback, useRef } from 'react';
import { TaskItem } from './TaskItem';
import { useTouchGestures, SwipeAction } from '@/hooks/useTouchGestures';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Check, Edit, Trash2, MoreHorizontal } from 'lucide-react';
import type { Task, InsertionPosition, InsertionState, LoadingStates } from '@/lib/types';

interface MobileTaskItemProps {
  task: Task;
  level: number;
  parentId: string | null;
  onUpdateTask: (task: Task) => void;
  onOpenSolveModal: (task: Task, isRefinement?: boolean, wrapperId?: string) => void;
  onAddTask: (parentId?: string | null, title?: string, description?: string) => void;
  onAddTaskAfter: (afterId: string, parentId: string | null, title?: string, description?: string) => void;
  onDeleteTask: (taskId: string) => void;
  onBreakdownTask?: (taskId: string) => void;
  numbering: string;
  loading?: LoadingStates;
  setLoading?: (loading: LoadingStates | ((prev: LoadingStates) => LoadingStates)) => void;
  isVirtualized?: boolean;
  onInsertTask?: (position: InsertionPosition) => void;
  showInsertionIndicators?: boolean;
  insertionMode?: 'hover' | 'always' | 'keyboard';
  insertionState?: InsertionState;
  onInsertionStateChange?: (updates: Partial<InsertionState>) => void;
  insertionLoading?: { [positionId: string]: boolean };
  // Mobile-specific props
  enableSwipeActions?: boolean;
  swipeThreshold?: number;
}

export function MobileTaskItem(props: MobileTaskItemProps) {
  const {
    task,
    onUpdateTask,
    onDeleteTask,
    enableSwipeActions = true,
    swipeThreshold = 80,
    ...taskItemProps
  } = props;

  const isMobile = useIsMobile();
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [activeAction, setActiveAction] = useState<string | null>(null);
  const [isLongPressActive, setIsLongPressActive] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Define swipe actions
  const leftActions: SwipeAction[] = [
    {
      id: 'complete',
      label: 'Erledigt',
      color: 'bg-green-500',
      action: () => {
        const updatedTask = { ...task, completed: !task.completed };
        onUpdateTask(updatedTask);
      }
    }
  ];

  const rightActions: SwipeAction[] = [
    {
      id: 'edit',
      label: 'Bearbeiten',
      color: 'bg-blue-500',
      action: () => {
        const updatedTask = { ...task, isEditing: true };
        onUpdateTask(updatedTask);
      }
    },
    {
      id: 'delete',
      label: 'Löschen',
      color: 'bg-red-500',
      action: () => {
        onDeleteTask(task.id);
      }
    }
  ];

  const handleSwipeMove = useCallback((direction: 'left' | 'right', distance: number) => {
    if (!enableSwipeActions || !isMobile) return;

    const maxDistance = 200;
    const offset = direction === 'right' ? 
      Math.min(distance, maxDistance) : 
      -Math.min(distance, maxDistance);
    
    setSwipeOffset(offset);

    // Determine active action
    if (Math.abs(offset) > swipeThreshold) {
      if (offset > 0 && leftActions.length > 0) {
        setActiveAction(leftActions[0].id);
      } else if (offset < 0) {
        const actionIndex = Math.min(
          Math.floor(Math.abs(offset) / swipeThreshold) - 1,
          rightActions.length - 1
        );
        setActiveAction(rightActions[actionIndex]?.id || null);
      }
    } else {
      setActiveAction(null);
    }
  }, [enableSwipeActions, isMobile, swipeThreshold, leftActions, rightActions]);

  const handleSwipeEnd = useCallback(() => {
    if (!enableSwipeActions || !isMobile) return;

    // Execute action if threshold was met
    if (Math.abs(swipeOffset) > swipeThreshold && activeAction) {
      const action = [...leftActions, ...rightActions].find(a => a.id === activeAction);
      if (action) {
        action.action();
      }
    }

    // Reset swipe state
    setSwipeOffset(0);
    setActiveAction(null);
  }, [enableSwipeActions, isMobile, swipeOffset, swipeThreshold, activeAction, leftActions, rightActions]);

  const handleLongPress = useCallback(() => {
    setIsLongPressActive(true);
    // Could open a context menu or show additional options
    console.log('Long press detected on task:', task.title);
  }, [task.title]);

  const { touchHandlers, gestureState, triggerHapticFeedback } = useTouchGestures({
    onSwipeLeft: (distance) => handleSwipeMove('left', distance),
    onSwipeRight: (distance) => handleSwipeMove('right', distance),
    onLongPress: handleLongPress,
    swipeThreshold,
    enableHapticFeedback: true
  });

  // Reset swipe state when gesture ends
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    touchHandlers.onTouchEnd(e);
    handleSwipeEnd();
    setIsLongPressActive(false);
  }, [touchHandlers, handleSwipeEnd]);

  const enhancedTouchHandlers = {
    ...touchHandlers,
    onTouchEnd: handleTouchEnd
  };

  if (!isMobile || !enableSwipeActions) {
    // On desktop or when swipe is disabled, render normal TaskItem
    return <TaskItem {...props} />;
  }

  return (
    <div 
      ref={containerRef}
      className="relative overflow-hidden touch-pan-y"
      {...enhancedTouchHandlers}
    >
      {/* Left action background (complete) */}
      {swipeOffset > 0 && (
        <div 
          className="absolute left-0 top-0 h-full flex items-center justify-center transition-all duration-200 z-10"
          style={{ width: Math.min(swipeOffset, 100) }}
        >
          <div className={`${leftActions[0]?.color} text-white p-3 rounded-r-lg flex items-center space-x-2 h-full`}>
            <Check className="h-5 w-5" />
            {swipeOffset > 60 && (
              <span className="text-sm font-medium whitespace-nowrap">
                {leftActions[0]?.label}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Right actions background (edit, delete) */}
      {swipeOffset < 0 && (
        <div 
          className="absolute right-0 top-0 h-full flex items-center justify-center transition-all duration-200 z-10"
          style={{ width: Math.min(Math.abs(swipeOffset), 200) }}
        >
          <div className="flex h-full">
            {rightActions.map((action, index) => {
              const actionWidth = Math.abs(swipeOffset) / rightActions.length;
              const isActive = activeAction === action.id;
              return (
                <div 
                  key={action.id}
                  className={`${action.color} text-white flex items-center justify-center transition-all duration-200 ${
                    isActive ? 'scale-110' : ''
                  } h-full`}
                  style={{ width: actionWidth }}
                >
                  {action.id === 'edit' ? (
                    <Edit className="h-5 w-5" />
                  ) : (
                    <Trash2 className="h-5 w-5" />
                  )}
                  {actionWidth > 60 && (
                    <span className="text-sm font-medium ml-2 whitespace-nowrap">
                      {action.label}
                    </span>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Main TaskItem with transform */}
      <div
        className="relative z-20 transition-transform duration-200"
        style={{
          transform: `translateX(${swipeOffset}px)`,
          transition: gestureState.isActive ? 'none' : 'transform 0.2s ease-out'
        }}
      >
        <TaskItem {...taskItemProps} task={task} onUpdateTask={onUpdateTask} onDeleteTask={onDeleteTask} />
      </div>

      {/* Touch feedback indicator */}
      {gestureState.isActive && (
        <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs z-30">
          {activeAction ? 
            `${[...leftActions, ...rightActions].find(a => a.id === activeAction)?.label}` : 
            `${Math.round(Math.abs(swipeOffset))}px`
          }
        </div>
      )}

      {/* Long press indicator */}
      {isLongPressActive && (
        <div className="absolute inset-0 bg-indigo-500/20 border-2 border-indigo-500 rounded-lg z-30 pointer-events-none" />
      )}
    </div>
  );
}