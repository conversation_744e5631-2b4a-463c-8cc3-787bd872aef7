import React, { useRef, useEffect, useState } from 'react';
import { useInsertionZones } from '@/hooks/useInsertionZones';
import { calculateZoneBoundaries, detectInsertionZone } from '@/lib/utils/insertionZoneUtils';
import { InsertionIndicator } from './InsertionIndicator';
import type { Task, InsertionZone, InsertionPosition } from '@/lib/types';

interface InsertionZoneDemoProps {
  task: Task;
  level: number;
  parentId: string | null;
}

/**
 * Demo component showing insertion zone calculation in action
 * This demonstrates the core functionality implemented in task 2
 */
export function InsertionZoneDemo({ task, level, parentId }: InsertionZoneDemoProps) {
  const taskRef = useRef<HTMLDivElement>(null);
  const [hoveredZone, setHoveredZone] = useState<InsertionZone | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [insertedTasks, setInsertedTasks] = useState<string[]>([]);

  // Use the insertion zones hook
  const { zones, isCalculating, findZoneAtPoint } = useInsertionZones({
    task,
    level,
    parentId,
    taskElement: taskRef.current,
    enableCaching: true
  });

  // Handle mouse movement to detect zone hovering
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      
      if (zones.length > 0) {
        const zone = findZoneAtPoint(e.clientX, e.clientY);
        setHoveredZone(zone);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [zones, findZoneAtPoint]);

  // Handle task insertion
  const handleInsertTask = (position: InsertionPosition) => {
    const insertionId = `${position.type}-${position.targetTaskId}-${Date.now()}`;
    setInsertedTasks(prev => [...prev, insertionId]);
    console.log('Task inserted:', position);
  };

  return (
    <div className="insertion-zone-demo p-4 border rounded-lg bg-white dark:bg-gray-800">
      <h3 className="text-lg font-semibold mb-4">Insertion Zone Demo</h3>
      
      {/* Task representation */}
      <div
        ref={taskRef}
        data-task-id={task.id}
        className="task-demo p-4 border-2 border-dashed border-gray-300 rounded-lg mb-4 relative"
        style={{ paddingLeft: `${level * 20}px` }}
      >
        <div className="task-header">
          <h4 className="font-medium">{task.title}</h4>
          <p className="text-sm text-gray-600">{task.description}</p>
        </div>
        
        {/* Subtasks */}
        {task.subtasks && task.subtasks.length > 0 && (
          <div className="subtasks mt-4">
            {task.subtasks.map((subtask) => (
              <div
                key={subtask.id}
                data-task-id={subtask.id}
                className="subtask p-2 border border-gray-200 rounded mb-2 ml-4"
              >
                <h5 className="font-medium text-sm">{subtask.title}</h5>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Zone visualization */}
      <div className="zones-info">
        <h4 className="font-medium mb-2">Calculated Zones ({zones.length})</h4>
        
        {isCalculating && (
          <div className="text-blue-600 mb-2">Calculating zones...</div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-4">
          {zones.map((zone) => (
            <div
              key={zone.id}
              className={`zone-info p-2 border rounded text-xs ${
                hoveredZone?.id === zone.id 
                  ? 'bg-blue-100 border-blue-500' 
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="font-medium">{zone.type}</div>
              <div className="text-gray-600">
                Target: {zone.targetTaskId}
              </div>
              <div className="text-gray-600">
                Level: {zone.level}
              </div>
              <div className="text-gray-600">
                Bounds: {Math.round(zone.bounds.x)}, {Math.round(zone.bounds.y)}
              </div>
              <div className="text-gray-600">
                Size: {Math.round(zone.bounds.width)} × {Math.round(zone.bounds.height)}
              </div>
              {zone.metadata?.touchFriendly && (
                <div className="text-green-600 font-medium">Touch Friendly</div>
              )}
            </div>
          ))}
        </div>

        {/* Insertion Indicators Demo */}
        <div className="insertion-indicators-demo mb-4">
          <h5 className="font-medium mb-2">Insertion Indicators</h5>
          <div className="space-y-2">
            {zones.map((zone) => {
              const position: InsertionPosition = {
                type: zone.type,
                targetTaskId: zone.targetTaskId,
                parentId: zone.parentId,
                level: zone.level
              };
              
              return (
                <InsertionIndicator
                  key={zone.id}
                  position={position}
                  isVisible={true}
                  isHovered={hoveredZone?.id === zone.id}
                  onInsert={() => handleInsertTask(position)}
                  variant="default"
                  touchFriendly={zone.metadata?.touchFriendly || false}
                />
              );
            })}
          </div>
        </div>

        {/* Current hover state */}
        <div className="hover-info p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <h5 className="font-medium mb-2">Current Hover State</h5>
          <div className="text-sm">
            Mouse: ({mousePosition.x}, {mousePosition.y})
          </div>
          {hoveredZone ? (
            <div className="text-sm mt-2">
              <div className="text-green-600 font-medium">
                Hovering: {hoveredZone.type} zone
              </div>
              <div>Target Task: {hoveredZone.targetTaskId}</div>
              <div>Parent: {hoveredZone.parentId || 'None'}</div>
              <div>Level: {hoveredZone.level}</div>
              <div>Priority: {hoveredZone.metadata?.priority}</div>
            </div>
          ) : (
            <div className="text-sm text-gray-600 mt-2">
              Not hovering over any insertion zone
            </div>
          )}
        </div>

        {/* Inserted tasks log */}
        {insertedTasks.length > 0 && (
          <div className="inserted-tasks-log p-3 bg-green-100 dark:bg-green-900 rounded mt-4">
            <h5 className="font-medium mb-2">Inserted Tasks ({insertedTasks.length})</h5>
            <div className="text-sm space-y-1">
              {insertedTasks.slice(-5).map((taskId, index) => (
                <div key={index} className="text-green-700 dark:text-green-300">
                  ✓ {taskId}
                </div>
              ))}
              {insertedTasks.length > 5 && (
                <div className="text-green-600 dark:text-green-400">
                  ... and {insertedTasks.length - 5} more
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Visual zone overlays */}
      <div className="zone-overlays fixed inset-0 pointer-events-none z-50">
        {zones.map((zone) => (
          <div
            key={zone.id}
            className={`absolute border-2 ${
              hoveredZone?.id === zone.id
                ? 'border-blue-500 bg-blue-200 bg-opacity-30'
                : 'border-red-500 bg-red-200 bg-opacity-20'
            }`}
            style={{
              left: zone.bounds.x,
              top: zone.bounds.y,
              width: zone.bounds.width,
              height: zone.bounds.height,
              transition: 'all 0.2s ease'
            }}
          >
            <div className="absolute -top-6 left-0 text-xs bg-black text-white px-1 rounded">
              {zone.type}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Example usage component
 */
export function InsertionZoneExample() {
  const sampleTask: Task = {
    id: 'demo-task-1',
    title: 'Sample Task with Subtasks',
    description: 'This task demonstrates insertion zone calculation',
    content: '',
    status: 'To Do',
    assignees: [],
    subtasks: [
      {
        id: 'demo-subtask-1',
        title: 'First Subtask',
        description: 'This is the first subtask',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      },
      {
        id: 'demo-subtask-2',
        title: 'Second Subtask',
        description: 'This is the second subtask',
        content: '',
        status: 'In Progress',
        assignees: [],
        subtasks: []
      }
    ]
  };

  const simpleTask: Task = {
    id: 'demo-task-2',
    title: 'Simple Task without Subtasks',
    description: 'This task has no subtasks',
    content: '',
    status: 'Done',
    assignees: [],
    subtasks: []
  };

  return (
    <div className="insertion-zone-example p-6 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Insertion Zone Calculation Demo</h2>
        <p className="text-gray-600 mb-6">
          This demo shows the insertion zone calculation utilities in action. 
          Move your mouse around the task areas to see the zones being detected.
        </p>
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-4">Task with Subtasks</h3>
        <InsertionZoneDemo
          task={sampleTask}
          level={0}
          parentId={null}
        />
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-4">Simple Task</h3>
        <InsertionZoneDemo
          task={simpleTask}
          level={0}
          parentId={null}
        />
      </div>

      <div>
        <h3 className="text-xl font-semibold mb-4">Nested Task (Level 1)</h3>
        <InsertionZoneDemo
          task={simpleTask}
          level={1}
          parentId="parent-task"
        />
      </div>
    </div>
  );
}