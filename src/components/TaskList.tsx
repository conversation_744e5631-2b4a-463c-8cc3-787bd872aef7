"use client";

import React, { useCallback, useMemo, useState } from 'react';
import { TaskList as TaskListImpl } from './TaskListImpl';
import type { Task } from '@/lib/types';

interface WrapperProps {
  tasks: Task[];
  onTasksChange: (tasks: Task[]) => void;
  projectTitle: string;
}

// A thin wrapper to provide the simplified API used by tests
export default function TaskList({ tasks: initialTasks, onTasksChange }: WrapperProps) {
  const [tasks, setTasks] = useState<Task[]>(initialTasks || []);

  const updateState = useCallback((next: Task[]) => {
    setTasks(next);
    onTasksChange?.(next);
  }, [onTasksChange]);

  const genId = () => `task-${Math.random().toString(36).slice(2, 9)}-${Date.now()}`;

  const findAndUpdate = useCallback((list: Task[], id: string, updater: (t: Task) => Task): Task[] => {
    return list.map(t => {
      if (t.id === id) return updater({ ...t });
      if (t.subtasks && t.subtasks.length) {
        return { ...t, subtasks: findAndUpdate(t.subtasks, id, updater) };
      }
      return t;
    });
  }, []);

  const findAndRemove = useCallback((list: Task[], id: string): Task[] => {
    return list
      .filter(t => t.id !== id)
      .map(t => ({ ...t, subtasks: t.subtasks ? findAndRemove(t.subtasks, id) : [] }));
  }, []);

  const insertAfter = useCallback((list: Task[], targetId: string, newTask: Task): Task[] => {
    const result: Task[] = [];
    for (const t of list) {
      result.push(t);
      if (t.id === targetId) {
        result.push(newTask);
      }
      if (t.subtasks && t.subtasks.length) {
        t.subtasks = insertAfter(t.subtasks, targetId, newTask);
      }
    }
    return result;
  }, []);

  const onAddTask = useCallback((parentId?: string | null, title?: string, description?: string) => {
    const newTask: Task = {
      id: genId(),
      title: title || 'Neue Aufgabe',
      description: description || '',
      content: '',
      status: 'pending',
      assignees: [],
      subtasks: []
    } as any;

    if (!parentId) {
      updateState([newTask, ...tasks]);
      return;
    }
    const addToParent = (list: Task[]): Task[] => list.map(t => t.id === parentId
      ? { ...t, subtasks: [newTask, ...(t.subtasks || [])] }
      : { ...t, subtasks: t.subtasks ? addToParent(t.subtasks) : [] });
    updateState(addToParent(tasks));
  }, [tasks, updateState]);

  const onAddTaskAfter = useCallback((afterIdOrPosition: any, parentId?: string | null, title?: string, description?: string) => {
    const afterId = typeof afterIdOrPosition === 'string' ? afterIdOrPosition : afterIdOrPosition?.targetTaskId;
    const newTask: Task = {
      id: genId(),
      title: title || 'Neue Aufgabe',
      description: description || '',
      content: '',
      status: 'pending',
      assignees: [],
      subtasks: []
    } as any;
    const next = insertAfter(tasks, afterId, newTask);
    updateState(next);
  }, [tasks, insertAfter, updateState]);

  const onUpdateTask = useCallback((task: Task) => {
    const next = findAndUpdate(tasks, task.id, () => task);
    updateState(next);
  }, [tasks, findAndUpdate, updateState]);

  const onDeleteTask = useCallback((taskId: string) => {
    const next = findAndRemove(tasks, taskId);
    updateState(next);
  }, [tasks, findAndRemove, updateState]);

  const onOpenSolveModal = useCallback(() => {}, []);

  return (
    <TaskListImpl
      tasks={tasks}
      onUpdateTask={onUpdateTask}
      onOpenSolveModal={onOpenSolveModal}
      onAddTask={onAddTask}
      onAddTaskAfter={onAddTaskAfter}
      onDeleteTask={onDeleteTask}
      numberingPrefix=""
      loading={{}}
      setLoading={() => {}}
      showInsertionIndicators={false}
    />
  );
}

export * from './TaskListImpl';
