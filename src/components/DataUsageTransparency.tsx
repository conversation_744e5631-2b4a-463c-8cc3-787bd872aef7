'use client';

/**
 * Data Usage Transparency Component
 * Shows users what data is sent to AI providers and provides transparency controls
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Eye, 
  Shield, 
  AlertCircle, 
  CheckCircle, 
  ExternalLink,
  Clock,
  Database,
  Trash2
} from 'lucide-react';
import { DataManager } from '@/lib/security/dataManager';
import { DataSanitizer, type SanitizationOptions } from '@/lib/security/dataSanitization';

interface AIDataUsageEntry {
  timestamp: string;
  action: string;
  dataType: string;
  details: {
    provider?: string;
    sanitized?: boolean;
    removedItems?: string[];
    contentLength?: number;
    originalLength?: number;
  };
}

export function DataUsageTransparency() {
  const [usageLog, setUsageLog] = useState<AIDataUsageEntry[]>([]);
  const [sanitizationOptions, setSanitizationOptions] = useState<SanitizationOptions>(
    DataSanitizer.getDefaultOptions()
  );
  const [testContent, setTestContent] = useState('');
  const [sanitizationPreview, setSanitizationPreview] = useState<any>(null);

  useEffect(() => {
    loadUsageLog();
  }, []);

  const loadUsageLog = () => {
    const log = DataManager.getDataUsageLog();
    const aiRelatedEntries = log.filter((entry: any) => 
      entry.action.includes('ai_') || 
      entry.dataType.includes('ai') ||
      entry.details?.provider
    );
    setUsageLog(aiRelatedEntries);
  };

  const clearUsageLog = () => {
    DataManager.clearDataUsageLog();
    setUsageLog([]);
  };

  const testSanitization = () => {
    if (!testContent.trim()) return;

    const result = DataSanitizer.sanitizeForAI(testContent, sanitizationOptions);
    setSanitizationPreview({
      original: testContent,
      sanitized: result.sanitizedContent,
      removedItems: result.removedItems,
      wasModified: result.wasModified,
      report: DataSanitizer.createSanitizationReport(result)
    });
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('de-DE');
  };

  const getActionBadgeVariant = (action: string) => {
    if (action.includes('request')) return 'default';
    if (action.includes('error')) return 'destructive';
    if (action.includes('sanitize')) return 'secondary';
    return 'outline';
  };

  const getDataTypeIcon = (dataType: string) => {
    if (dataType.includes('task')) return '📝';
    if (dataType.includes('project')) return '📁';
    if (dataType.includes('ai')) return '🤖';
    return '📊';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Eye className="h-6 w-6" />
        <h2 className="text-2xl font-bold">Datentransparenz</h2>
      </div>

      {/* Overview */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Diese Übersicht zeigt Ihnen, welche Daten an KI-Anbieter gesendet werden. 
          Alle Übertragungen werden protokolliert und können hier eingesehen werden.
        </AlertDescription>
      </Alert>

      {/* Data Sanitization Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Datenbereinigung testen
          </CardTitle>
          <CardDescription>
            Testen Sie, wie Ihre Daten vor der KI-Übertragung bereinigt werden
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Testinhalt eingeben:</label>
            <textarea
              className="w-full h-24 p-3 border rounded-md resize-none"
              placeholder="Geben Sie hier Testtext ein (z.B. mit E-Mail-Adressen, Telefonnummern)..."
              value={testContent}
              onChange={(e) => setTestContent(e.target.value)}
            />
          </div>

          <Button onClick={testSanitization} disabled={!testContent.trim()}>
            Bereinigung testen
          </Button>

          {sanitizationPreview && (
            <div className="space-y-4 p-4 bg-muted rounded-lg">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Original:</h4>
                  <div className="p-3 bg-background rounded border text-sm">
                    {sanitizationPreview.original}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Bereinigt:</h4>
                  <div className="p-3 bg-background rounded border text-sm">
                    {sanitizationPreview.sanitized}
                  </div>
                </div>
              </div>

              {sanitizationPreview.removedItems.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Entfernte Elemente:</h4>
                  <div className="flex flex-wrap gap-2">
                    {sanitizationPreview.removedItems.map((item: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  {sanitizationPreview.report}
                </AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Usage Log */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                KI-Datenübertragungen
              </CardTitle>
              <CardDescription>
                Protokoll aller Datenübertragungen an KI-Anbieter
              </CardDescription>
            </div>
            <Button 
              onClick={clearUsageLog} 
              variant="outline" 
              size="sm"
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Log löschen
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {usageLog.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Noch keine KI-Datenübertragungen protokolliert</p>
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {usageLog.map((entry, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getDataTypeIcon(entry.dataType)}</span>
                        <div>
                          <div className="font-medium">{entry.action}</div>
                          <div className="text-sm text-muted-foreground">
                            {formatTimestamp(entry.timestamp)}
                          </div>
                        </div>
                      </div>
                      <Badge variant={getActionBadgeVariant(entry.action)}>
                        {entry.dataType}
                      </Badge>
                    </div>

                    {entry.details && (
                      <div className="space-y-2 text-sm">
                        {entry.details.provider && (
                          <div className="flex items-center gap-2">
                            <ExternalLink className="h-4 w-4" />
                            <span>Anbieter: {entry.details.provider}</span>
                          </div>
                        )}

                        {entry.details.sanitized && (
                          <div className="flex items-center gap-2 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span>Daten wurden bereinigt</span>
                          </div>
                        )}

                        {entry.details.removedItems && entry.details.removedItems.length > 0 && (
                          <div>
                            <span className="font-medium">Entfernte Elemente: </span>
                            <span>{entry.details.removedItems.length} Einträge</span>
                          </div>
                        )}

                        {entry.details.contentLength && (
                          <div>
                            <span className="font-medium">Übertragene Daten: </span>
                            <span>{entry.details.contentLength} Zeichen</span>
                            {entry.details.originalLength && entry.details.originalLength !== entry.details.contentLength && (
                              <span className="text-muted-foreground">
                                {' '}(ursprünglich {entry.details.originalLength} Zeichen)
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* Data Protection Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Datenschutzhinweise
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <div className="font-medium">Lokale Datenspeicherung</div>
                <div className="text-sm text-muted-foreground">
                  Alle Ihre Projekte und Aufgaben werden ausschließlich lokal auf Ihrem Gerät gespeichert.
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <div className="font-medium">Minimale Datenübertragung</div>
                <div className="text-sm text-muted-foreground">
                  Nur die für die KI-Verarbeitung notwendigen Informationen werden übertragen.
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <div className="font-medium">Automatische Bereinigung</div>
                <div className="text-sm text-muted-foreground">
                  Sensible Daten wie E-Mail-Adressen und Telefonnummern werden automatisch entfernt.
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <div className="font-medium">Vollständige Transparenz</div>
                <div className="text-sm text-muted-foreground">
                  Alle Datenübertragungen werden protokolliert und können hier eingesehen werden.
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Wichtiger Hinweis:</strong> Obwohl wir sensible Daten automatisch entfernen, 
              sollten Sie keine hochsensiblen Informationen (wie Passwörter, Kreditkartennummern oder 
              Sozialversicherungsnummern) in Ihre Projekte eingeben.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}