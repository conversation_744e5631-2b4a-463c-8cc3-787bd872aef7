/**
 * Lazy-loaded export components for better performance
 */

import { lazyWithPerformance, lazyWithRetry } from '@/lib/utils/lazyLoading';

// Lazy load export dialog components
export const LazyEnhancedExportDialog = lazyWithRetry(
  () => import('@/components/EnhancedExportDialog').then(mod => ({ default: mod.EnhancedExportDialog })),
  'EnhancedExportDialog'
);

export const LazyImportDialog = lazyWithRetry(
  () => import('@/components/ImportDialog').then(mod => ({ default: mod.ImportDialog })),
  'ImportDialog'
);

// Lazy load performance dashboard (non-critical)
export const LazyPerformanceDashboard = lazyWithPerformance(
  () => import('@/components/PerformanceDashboard').then(mod => ({ default: mod.PerformanceDashboard })),
  'PerformanceDashboard'
);

// Lazy load AI provider settings (settings are not critical for initial load)
export const LazyAIProviderSettings = lazyWithPerformance(
  () => import('@/components/AIProviderSettings').then(mod => ({ default: mod.AIProviderSettings })),
  'AIProviderSettings'
);

export const LazyAIProviderSettingsPage = lazyWithPerformance(
  () => import('@/components/AIProviderSettingsPage').then(mod => ({ default: mod.AIProviderSettingsPage })),
  'AIProviderSettingsPage'
);

// Lazy load mobile touch demo (demo component, not critical)
export const LazyMobileTouchDemo = lazyWithPerformance(
  () => import('@/components/MobileTouchDemo').then(mod => ({ default: mod.MobileTouchDemo })),
  'MobileTouchDemo'
);

// Lazy load solve task modal (only needed when user clicks solve)
export const LazySolveTaskModal = lazyWithPerformance(
  () => import('@/components/SolveTaskModal').then(mod => ({ default: mod.SolveTaskModal })),
  'SolveTaskModal'
);