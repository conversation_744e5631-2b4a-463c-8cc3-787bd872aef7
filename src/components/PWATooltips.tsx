/**
 * PWA Contextual Tooltips Component
 * 
 * Provides contextual help tooltips for PWA-specific features throughout the app
 */

import React, { useState, useEffect } from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  HelpCircle,
  Wifi,
  WifiOff,
  Download,
  RotateCw as Sync,
  Shield,
  Zap,
  Brain,
  FileDown,
  Smartphone,
  Globe,
  HardDrive,
  CheckCircle,
  AlertCircle,
  Info,
  X
} from 'lucide-react';

interface PWATooltipProps {
  type: 'offline-indicator' | 'install-prompt' | 'sync-status' | 'ai-offline' | 'cache-status' | 'data-storage' | 'performance' | 'export-enhanced';
  children: React.ReactNode;
  showBadge?: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

interface TooltipConfig {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  tips?: string[];
}

const tooltipConfigs: Record<string, TooltipConfig> = {
  'offline-indicator': {
    title: 'Offline-Status',
    description: 'Zeigt an, ob die App online oder offline ist. Offline können Sie weiterhin Projekte bearbeiten, aber AI-Features sind nicht verfügbar.',
    icon: <WifiOff className="text-red-500" size={16} />,
    color: 'red',
    tips: [
      'Alle Änderungen werden lokal gespeichert',
      'AI-Features sind nur online verfügbar',
      'Automatische Sync bei Verbindung'
    ]
  },
  'install-prompt': {
    title: 'App Installation',
    description: 'Installieren Sie die App auf Ihrem Gerät für bessere Performance und einfacheren Zugriff.',
    icon: <Download className="text-blue-500" size={16} />,
    color: 'blue',
    tips: [
      'Schnellerer App-Start',
      'Eigenes App-Icon',
      'Vollbild-Modus',
      'Offline-Funktionalität'
    ]
  },
  'sync-status': {
    title: 'Synchronisation',
    description: 'Zeigt den Status der Datensynchronisation an. Wartende Änderungen werden automatisch synchronisiert, wenn Sie online sind.',
    icon: <Sync className="text-blue-500" size={16} />,
    color: 'blue',
    tips: [
      'Automatische Synchronisation',
      'Warteschlange für Offline-Änderungen',
      'Konfliktauflösung bei Bedarf'
    ]
  },
  'ai-offline': {
    title: 'AI-Features offline',
    description: 'AI-Funktionen sind offline nicht verfügbar. Ihre Anfragen werden in eine Warteschlange eingereiht und verarbeitet, sobald Sie online sind.',
    icon: <Brain className="text-orange-500" size={16} />,
    color: 'orange',
    tips: [
      'AI-Anfragen werden gespeichert',
      'Automatische Verarbeitung bei Online-Status',
      'Benachrichtigung bei Fertigstellung'
    ]
  },
  'cache-status': {
    title: 'Cache-Performance',
    description: 'Zeigt die Cache-Effizienz an. Ein hoher Wert bedeutet, dass die App schneller lädt, da Inhalte aus dem lokalen Cache geladen werden.',
    icon: <Zap className="text-green-500" size={16} />,
    color: 'green',
    tips: [
      'Höhere Werte = bessere Performance',
      'Cache wird automatisch verwaltet',
      'Intelligente Vorab-Ladung'
    ]
  },
  'data-storage': {
    title: 'Lokale Datenspeicherung',
    description: 'Ihre Daten werden sicher auf Ihrem Gerät gespeichert. Kein automatischer Upload in die Cloud.',
    icon: <HardDrive className="text-purple-500" size={16} />,
    color: 'purple',
    tips: [
      'Daten bleiben auf Ihrem Gerät',
      'Verschlüsselte Speicherung',
      'Regelmäßige Backups empfohlen'
    ]
  },
  'performance': {
    title: 'Performance-Optimierung',
    description: 'Die App nutzt verschiedene Techniken für optimale Performance: Caching, Lazy Loading und intelligente Ressourcen-Verwaltung.',
    icon: <Globe className="text-indigo-500" size={16} />,
    color: 'indigo',
    tips: [
      'Intelligentes Caching',
      'Lazy Loading von Komponenten',
      'Optimierte Bundle-Größe'
    ]
  },
  'export-enhanced': {
    title: 'PWA-Enhanced Export',
    description: 'Export-Funktionen sind mit PWA-Metadaten erweitert und funktionieren auch offline.',
    icon: <FileDown className="text-green-500" size={16} />,
    color: 'green',
    tips: [
      'Funktioniert offline',
      'Erweiterte Metadaten',
      'Mehrere Formate verfügbar'
    ]
  }
};

export function PWATooltip({ type, children, showBadge = false, position = 'top' }: PWATooltipProps) {
  const config = tooltipConfigs[type];
  
  if (!config) {
    return <>{children}</>;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="relative inline-flex items-center">
            {children}
            {showBadge && (
              <Badge 
                variant="outline" 
                className={`ml-2 text-xs bg-${config.color}-50 dark:bg-${config.color}-900/20 text-${config.color}-700 dark:text-${config.color}-300 border-${config.color}-200 dark:border-${config.color}-700`}
              >
                PWA
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side={position} className="max-w-sm p-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              {config.icon}
              <h4 className="font-semibold">{config.title}</h4>
            </div>
            <p className="text-sm text-slate-600 dark:text-slate-300">
              {config.description}
            </p>
            {config.tips && (
              <div className="space-y-1">
                <h5 className="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide">
                  Tipps:
                </h5>
                <ul className="space-y-1">
                  {config.tips.map((tip, index) => (
                    <li key={index} className="text-xs text-slate-600 dark:text-slate-300 flex items-start gap-1">
                      <span className="text-green-500 mt-0.5">•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Contextual help overlay for complex PWA features
interface PWAHelpOverlayProps {
  isVisible: boolean;
  onClose: () => void;
  feature: 'first-install' | 'offline-transition' | 'sync-explanation';
}

export function PWAHelpOverlay({ isVisible, onClose, feature }: PWAHelpOverlayProps) {
  const [step, setStep] = useState(0);

  useEffect(() => {
    if (isVisible) {
      setStep(0);
    }
  }, [isVisible]);

  if (!isVisible) return null;

  const overlayConfigs = {
    'first-install': {
      title: 'App erfolgreich installiert!',
      steps: [
        {
          title: 'Willkommen in der installierten App',
          description: 'Die App läuft jetzt als eigenständige Anwendung mit verbesserter Performance.',
          icon: <CheckCircle className="text-green-500" size={24} />
        },
        {
          title: 'Offline-Funktionalität',
          description: 'Sie können die App jetzt auch ohne Internetverbindung nutzen.',
          icon: <WifiOff className="text-blue-500" size={24} />
        },
        {
          title: 'Automatische Updates',
          description: 'Die App aktualisiert sich automatisch im Hintergrund.',
          icon: <Sync className="text-purple-500" size={24} />
        }
      ]
    },
    'offline-transition': {
      title: 'Sie sind jetzt offline',
      steps: [
        {
          title: 'Weiterarbeiten möglich',
          description: 'Sie können weiterhin Projekte erstellen und bearbeiten.',
          icon: <CheckCircle className="text-green-500" size={24} />
        },
        {
          title: 'AI-Features pausiert',
          description: 'AI-Funktionen sind offline nicht verfügbar, werden aber gespeichert.',
          icon: <Brain className="text-orange-500" size={24} />
        },
        {
          title: 'Automatische Synchronisation',
          description: 'Alle Änderungen werden synchronisiert, sobald Sie wieder online sind.',
          icon: <Sync className="text-blue-500" size={24} />
        }
      ]
    },
    'sync-explanation': {
      title: 'Synchronisation läuft',
      steps: [
        {
          title: 'Daten werden abgeglichen',
          description: 'Ihre offline erstellten Inhalte werden mit dem Server synchronisiert.',
          icon: <Sync className="text-blue-500" size={24} />
        },
        {
          title: 'AI-Anfragen verarbeitet',
          description: 'Wartende AI-Anfragen werden jetzt verarbeitet.',
          icon: <Brain className="text-purple-500" size={24} />
        },
        {
          title: 'Cache aktualisiert',
          description: 'Der lokale Cache wird mit neuen Inhalten aktualisiert.',
          icon: <Zap className="text-green-500" size={24} />
        }
      ]
    }
  };

  const config = overlayConfigs[feature];
  const currentStep = config.steps[step];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{config.title}</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X size={16} />
          </Button>
        </div>

        <div className="space-y-4">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mb-3">
              {currentStep.icon}
            </div>
            <h4 className="font-semibold mb-2">{currentStep.title}</h4>
            <p className="text-sm text-slate-600 dark:text-slate-300">
              {currentStep.description}
            </p>
          </div>

          <div className="flex justify-center space-x-2">
            {config.steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === step ? 'bg-blue-500' : 'bg-slate-300 dark:bg-slate-600'
                }`}
              />
            ))}
          </div>

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setStep(Math.max(0, step - 1))}
              disabled={step === 0}
            >
              Zurück
            </Button>
            <Button
              onClick={() => {
                if (step < config.steps.length - 1) {
                  setStep(step + 1);
                } else {
                  onClose();
                }
              }}
            >
              {step === config.steps.length - 1 ? 'Fertig' : 'Weiter'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for managing PWA help state
export function usePWAHelp() {
  const [activeOverlay, setActiveOverlay] = useState<string | null>(null);
  const [hasSeenHelp, setHasSeenHelp] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const seenHelp = localStorage.getItem('pwa-help-seen');
    if (seenHelp) {
      setHasSeenHelp(JSON.parse(seenHelp));
    }
  }, []);

  const showHelp = (feature: string) => {
    if (!hasSeenHelp[feature]) {
      setActiveOverlay(feature);
    }
  };

  const hideHelp = () => {
    if (activeOverlay) {
      const newSeenHelp = { ...hasSeenHelp, [activeOverlay]: true };
      setHasSeenHelp(newSeenHelp);
      localStorage.setItem('pwa-help-seen', JSON.stringify(newSeenHelp));
    }
    setActiveOverlay(null);
  };

  const resetHelp = () => {
    setHasSeenHelp({});
    localStorage.removeItem('pwa-help-seen');
  };

  return {
    activeOverlay,
    showHelp,
    hideHelp,
    resetHelp,
    hasSeenHelp
  };
}

export default PWATooltip;