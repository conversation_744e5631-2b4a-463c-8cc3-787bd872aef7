'use client';

import { useState, useEffect } from 'react';
import { Clock, CheckCircle, XCircle, Loader2, Trash2 } from 'lucide-react';
import { offlineAwareAIClient } from '@/lib/ai/offlineAwareClient';
import { QueuedAIRequest } from '@/lib/ai/requestQueue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface AIRequestQueueStatusProps {
  className?: string;
}

export function AIRequestQueueStatus({ className }: AIRequestQueueStatusProps) {
  const [queuedRequests, setQueuedRequests] = useState<QueuedAIRequest[]>([]);
  const [completedRequests, setCompletedRequests] = useState<Array<{ request: QueuedAIRequest; success: boolean; error?: string }>>([]);
  const { toast } = useToast();

  useEffect(() => {
    // Initial load
    setQueuedRequests(offlineAwareAIClient.getQueuedRequests());

    // Listen for completed requests
    const unsubscribe = offlineAwareAIClient.onRequestCompleted((request, result) => {
      // Remove from queue
      setQueuedRequests(prev => prev.filter(req => req.id !== request.id));
      
      // Add to completed
      setCompletedRequests(prev => [
        { request, success: result.success, error: result.error },
        ...prev.slice(0, 4) // Keep only last 5 completed requests
      ]);

      // Show notification
      if (result.success) {
        toast({
          title: 'AI-Anfrage abgeschlossen',
          description: `${getRequestTypeLabel(request.type)} für "${request.taskTitle || 'Unbekannte Aufgabe'}" wurde erfolgreich verarbeitet.`,
        });
      } else {
        toast({
          variant: 'destructive',
          title: 'AI-Anfrage fehlgeschlagen',
          description: `${getRequestTypeLabel(request.type)} für "${request.taskTitle || 'Unbekannte Aufgabe'}" konnte nicht verarbeitet werden: ${result.error}`,
        });
      }
    });

    // Update queue status periodically
    const interval = setInterval(() => {
      setQueuedRequests(offlineAwareAIClient.getQueuedRequests());
    }, 5000);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [toast]);

  const getRequestTypeLabel = (type: string): string => {
    const labels = {
      'generateTasks': 'Projektzerlegung',
      'generateSubtasks': 'Aufgabenzerlegung',
      'generateTaskContent': 'Inhaltsgenerierung',
      'elaborateContent': 'Textverbesserung'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('de-DE', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const handleClearQueue = () => {
    offlineAwareAIClient.clearQueue();
    setQueuedRequests([]);
    toast({
      title: 'Warteschlange geleert',
      description: 'Alle wartenden AI-Anfragen wurden entfernt.',
    });
  };

  const handleClearCompleted = () => {
    setCompletedRequests([]);
  };

  if (queuedRequests.length === 0 && completedRequests.length === 0) {
    return null;
  }

  return (
    <Card className={cn("w-full max-w-md", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Clock className="h-4 w-4" />
          AI-Anfragen
          {queuedRequests.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              {queuedRequests.length} wartend
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Queued Requests */}
        {queuedRequests.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-muted-foreground">Warteschlange</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearQueue}
                className="h-6 px-2 text-xs"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Leeren
              </Button>
            </div>
            
            {queuedRequests.slice(0, 3).map((request) => (
              <div
                key={request.id}
                className="flex items-center gap-2 p-2 bg-muted/50 rounded-md text-xs"
              >
                <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">
                    {getRequestTypeLabel(request.type)}
                  </div>
                  <div className="text-muted-foreground truncate">
                    {request.taskTitle || 'Unbekannte Aufgabe'}
                  </div>
                </div>
                <div className="text-muted-foreground">
                  {formatTimestamp(request.timestamp)}
                </div>
              </div>
            ))}
            
            {queuedRequests.length > 3 && (
              <div className="text-xs text-muted-foreground text-center">
                +{queuedRequests.length - 3} weitere Anfragen
              </div>
            )}
          </div>
        )}

        {/* Completed Requests */}
        {completedRequests.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-muted-foreground">Kürzlich abgeschlossen</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearCompleted}
                className="h-6 px-2 text-xs"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Leeren
              </Button>
            </div>
            
            {completedRequests.slice(0, 3).map((item, index) => (
              <div
                key={`${item.request.id}-${index}`}
                className="flex items-center gap-2 p-2 bg-muted/30 rounded-md text-xs"
              >
                {item.success ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <XCircle className="h-3 w-3 text-red-500" />
                )}
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">
                    {getRequestTypeLabel(item.request.type)}
                  </div>
                  <div className="text-muted-foreground truncate">
                    {item.request.taskTitle || 'Unbekannte Aufgabe'}
                  </div>
                </div>
                <div className="text-muted-foreground">
                  {formatTimestamp(item.request.timestamp)}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}