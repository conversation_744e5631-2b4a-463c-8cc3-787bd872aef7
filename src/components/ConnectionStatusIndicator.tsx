'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, Signal } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { cn } from '@/lib/utils';

interface ConnectionStatusIndicatorProps {
  className?: string;
  showText?: boolean;
}

export function ConnectionStatusIndicator({ 
  className, 
  showText = false 
}: ConnectionStatusIndicatorProps) {
  const { isOnline, isSlowConnection, connectionType } = useNetworkStatus();
  
  // Prevent hydration mismatch by not rendering until client-side
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (!isClient) {
    // Return a placeholder during SSR to prevent hydration mismatch
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Wifi className="h-4 w-4 text-gray-400" />
        {showText && (
          <span className="text-sm font-medium text-gray-400">
            Verbindung...
          </span>
        )}
      </div>
    );
  }

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="h-4 w-4 text-red-500" />;
    }
    
    if (isSlowConnection) {
      return <Signal className="h-4 w-4 text-yellow-500" />;
    }
    
    return <Wifi className="h-4 w-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!isOnline) return 'Offline';
    if (isSlowConnection) return 'Langsame Verbindung';
    return 'Online';
  };

  const getStatusColor = () => {
    if (!isOnline) return 'text-red-500';
    if (isSlowConnection) return 'text-yellow-500';
    return 'text-green-500';
  };

  return (
    <div className={cn(
      "flex items-center gap-2",
      className
    )}>
      {getStatusIcon()}
      {showText && (
        <span className={cn(
          "text-sm font-medium",
          getStatusColor()
        )}>
          {getStatusText()}
        </span>
      )}
    </div>
  );
}