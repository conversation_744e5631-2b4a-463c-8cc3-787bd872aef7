"use client";

import React, { useState, useCallback, useMemo, useRef, useEffect, memo } from 'react';
import { Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { useDebouncedCallback } from '@/lib/utils/debounce';
import { useInsertionPerformanceMonitor } from '@/lib/utils/insertionPerformanceMonitor';
import type { InsertionPosition } from '@/lib/types';

/**
 * Get user-friendly description for insertion type
 */
const getInsertionTypeDescription = (type: InsertionPosition['type']): string => {
  switch (type) {
    case 'before':
      return 'before';
    case 'after':
      return 'after';
    case 'between_parent_child':
      return 'as subtask of';
    default:
      return type;
  }
};

/**
 * Get detailed description for screen readers
 */
const getDetailedInsertionDescription = (position: InsertionPosition): string => {
  switch (position.type) {
    case 'before':
      return `Insert a new task before the current task at level ${position.level + 1}. The new task will be positioned above the current task at the same hierarchy level.`;
    case 'after':
      return `Insert a new task after the current task at level ${position.level + 1}. The new task will be positioned below the current task at the same hierarchy level.`;
    case 'between_parent_child':
      return `Insert a new subtask under the current task at level ${position.level + 1}. The new task will become a child of the current task.`;
    default:
      return `Insert a new task at the ${position.type} position.`;
  }
};

/**
 * Announce message to screen readers with proper timing
 */
const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement with longer delay for complex messages
  const delay = message.length > 50 ? 3000 : 1500;
  setTimeout(() => {
    if (document.body.contains(announcement)) {
      document.body.removeChild(announcement);
    }
  }, delay);
};

export interface InsertionIndicatorProps {
  position: InsertionPosition;
  isVisible: boolean;
  isHovered?: boolean;
  onInsert: () => void;
  className?: string;
  touchFriendly?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'compact' | 'touch';
  onLongPress?: () => void;
  longPressDelay?: number;
}

/**
 * InsertionIndicator component for manual task insertion
 * Provides visual feedback and click handling for task insertion at specific positions
 * Optimized with memoization and performance monitoring
 */
export const InsertionIndicator: React.FC<InsertionIndicatorProps> = memo(({
  position,
  isVisible,
  isHovered = false,
  onInsert,
  className,
  touchFriendly = false,
  disabled = false,
  variant = 'default',
  onLongPress,
  longPressDelay = 500
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [animationPhase, setAnimationPhase] = useState<'entering' | 'visible' | 'success' | 'exiting'>('entering');
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartTimeRef = useRef<number>(0);
  const renderStartTimeRef = useRef<number>(0);
  const successTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isMobile = useIsMobile();
  const performanceMonitor = useInsertionPerformanceMonitor(`InsertionIndicator-${position.targetTaskId}`);

  // Start render timing
  renderStartTimeRef.current = performance.now();

  // Clear long press timer
  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  }, []);

  // Debounced click handler to prevent excessive calls
  const debouncedOnInsert = useDebouncedCallback(
    useCallback(() => {
      if (typeof onInsert === 'function') {
        // Trigger success animation
        setShowSuccessAnimation(true);
        setAnimationPhase('success');
        
        // Call the actual insert function
        onInsert();
        
        // Reset animation after completion
        successTimeoutRef.current = setTimeout(() => {
          setShowSuccessAnimation(false);
          setAnimationPhase('visible');
        }, 600);
      }
    }, [onInsert]),
    100,
    { leading: true, trailing: false }
  );

  // Handle click/touch events
  const handleClick = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (disabled) {
      announceToScreenReader('Task insertion is currently disabled.', 'assertive');
      return;
    }
    
    // Don't trigger click if this was a long press
    if (isLongPressing) {
      setIsLongPressing(false);
      return;
    }
    
    // Announce the insertion action to screen readers
    const insertionDescription = getInsertionTypeDescription(position.type);
    announceToScreenReader(`Inserting new task ${insertionDescription} current task.`, 'assertive');
    
    debouncedOnInsert();
  }, [disabled, isLongPressing, debouncedOnInsert, position]);

  // Handle keyboard events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled) {
      if (e.key === 'Enter' || e.key === ' ') {
        announceToScreenReader('Task insertion is currently disabled.', 'assertive');
      }
      return;
    }
    
    if (typeof onInsert !== 'function') return;
    
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.stopPropagation();
      
      // Announce the insertion action to screen readers
      const insertionDescription = getInsertionTypeDescription(position.type);
      announceToScreenReader(`Inserting new task ${insertionDescription} current task via keyboard.`, 'assertive');
      
      onInsert();
    } else if (e.key === 'Escape') {
      // Allow users to escape focus from insertion indicator
      e.preventDefault();
      const button = e.currentTarget as HTMLButtonElement;
      button.blur();
      announceToScreenReader('Insertion cancelled.', 'polite');
    }
  }, [onInsert, disabled, position]);

  const handleMouseDown = useCallback(() => {
    if (!disabled) {
      setIsPressed(true);
    }
  }, [disabled]);

  const handleMouseUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsPressed(false);
    clearLongPressTimer();
  }, [clearLongPressTimer]);

  // Touch event handlers for mobile
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled) return;
    
    e.preventDefault();
    setIsPressed(true);
    touchStartTimeRef.current = Date.now();
    
    // Start long press timer if long press handler is provided
    if (onLongPress && typeof onLongPress === 'function') {
      longPressTimerRef.current = setTimeout(() => {
        setIsLongPressing(true);
        onLongPress();
        
        // Provide haptic feedback if available
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
      }, longPressDelay);
    }
  }, [disabled, onLongPress, longPressDelay]);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (disabled) return;
    
    e.preventDefault();
    clearLongPressTimer();
    
    const touchDuration = Date.now() - touchStartTimeRef.current;
    
    // If it wasn't a long press, trigger normal click
    if (!isLongPressing && touchDuration < longPressDelay) {
      handleClick(e);
    }
    
    setIsPressed(false);
    setIsLongPressing(false);
  }, [disabled, clearLongPressTimer, isLongPressing, longPressDelay, handleClick]);

  const handleTouchCancel = useCallback(() => {
    clearLongPressTimer();
    setIsPressed(false);
    setIsLongPressing(false);
  }, [clearLongPressTimer]);

  // Animation phase management
  useEffect(() => {
    if (isVisible && animationPhase === 'entering') {
      // Small delay to ensure smooth entrance animation
      const timer = setTimeout(() => {
        setAnimationPhase('visible');
      }, 50);
      return () => clearTimeout(timer);
    } else if (!isVisible && animationPhase !== 'exiting') {
      setAnimationPhase('exiting');
    }
  }, [isVisible, animationPhase]);

  // Cleanup effect for long press timer and performance monitoring
  useEffect(() => {
    return () => {
      clearLongPressTimer();
      debouncedOnInsert.cancel();
      if (successTimeoutRef.current) {
        clearTimeout(successTimeoutRef.current);
      }
    };
  }, [clearLongPressTimer, debouncedOnInsert]);

  // Performance monitoring effect
  useEffect(() => {
    const renderTime = performance.now() - renderStartTimeRef.current;
    performanceMonitor.recordRender(renderTime);
  });

  // Calculate styles based on variant, state, and device type
  const indicatorStyles = useMemo(() => {
    const baseStyles = {
      default: {
        height: isMobile ? '32px' : '20px',
        buttonSize: isMobile ? '28px' : '20px',
        lineHeight: isMobile ? '3px' : '2px',
        padding: isMobile ? '4px' : '0px',
        minTouchTarget: '44px' // iOS/Android minimum touch target
      },
      compact: {
        height: isMobile ? '28px' : '16px',
        buttonSize: isMobile ? '24px' : '16px',
        lineHeight: isMobile ? '2px' : '1px',
        padding: isMobile ? '2px' : '0px',
        minTouchTarget: '44px'
      },
      touch: {
        height: '44px', // Always large for touch variant
        buttonSize: '32px',
        lineHeight: '4px',
        padding: '10px',
        minTouchTarget: '44px'
      }
    };

    // Override with touch-friendly sizes if touchFriendly prop is true
    const selectedStyle = baseStyles[variant] || baseStyles.default;
    
    if (touchFriendly || isMobile) {
      return {
        ...selectedStyle,
        height: Math.max(parseInt(selectedStyle.height), 32) + 'px',
        buttonSize: Math.max(parseInt(selectedStyle.buttonSize), 28) + 'px',
        minTouchTarget: '44px'
      };
    }

    return selectedStyle;
  }, [variant, isMobile, touchFriendly]);

  // Determine insertion type styling and animation
  const insertionTypeStyles = useMemo(() => {
    const baseStyles = {
      justification: 'flex-start',
      indentLevel: position.type === 'between_parent_child' ? 20 : 0,
      animationClass: ''
    };

    // Add entrance animation based on insertion type
    if (animationPhase === 'entering') {
      switch (position.type) {
        case 'before':
          baseStyles.animationClass = 'slide-in-top';
          break;
        case 'after':
          baseStyles.animationClass = 'slide-in-bottom';
          break;
        case 'between_parent_child':
          baseStyles.animationClass = 'slide-in-left';
          break;
      }
    }

    return baseStyles;
  }, [position.type, animationPhase]);

  // Animation and visibility classes
  const visibilityClasses = useMemo(() => {
    if (!isVisible || animationPhase === 'exiting') {
      return 'opacity-0 scale-95 pointer-events-none';
    }
    
    if (animationPhase === 'entering') {
      return 'opacity-0 scale-95';
    }
    
    if (animationPhase === 'success') {
      return 'opacity-100 scale-100';
    }
    
    if (isHovered || isPressed) {
      return 'opacity-100 scale-100';
    }
    
    return 'opacity-70 scale-95 hover:opacity-100 hover:scale-100';
  }, [isVisible, isHovered, isPressed, animationPhase]);

  // Button state classes
  const buttonStateClasses = useMemo(() => {
    if (disabled) {
      return 'cursor-not-allowed opacity-50';
    }
    
    if (showSuccessAnimation) {
      return 'bg-green-600 border-green-600 scale-110 shadow-lg ring-2 ring-green-600/30 success-animation';
    }
    
    if (isLongPressing) {
      return 'bg-primary border-primary scale-110 shadow-lg ring-2 ring-primary/30';
    }
    
    if (isPressed) {
      return 'bg-primary/90 border-primary/90 scale-95';
    }
    
    if (isHovered) {
      return 'bg-primary/80 border-primary/80 shadow-md';
    }
    
    return 'bg-primary/60 border-primary/60 hover:bg-primary/80 hover:border-primary/80 hover:shadow-md';
  }, [disabled, isPressed, isHovered, isLongPressing, showSuccessAnimation]);

  return (
    <div
      className={cn(
        // Base container styles
        'insertion-indicator relative flex items-center justify-center w-full',
        'transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)',
        visibilityClasses,
        insertionTypeStyles.animationClass,
        className
      )}
      style={{
        height: indicatorStyles.height,
        paddingLeft: `${insertionTypeStyles.indentLevel}px`,
        paddingTop: indicatorStyles.padding,
        paddingBottom: indicatorStyles.padding
      }}
      data-testid={`insertion-indicator-${position.type}-${position.targetTaskId}`}
      role="region"
      aria-label={`Task insertion zone: ${getInsertionTypeDescription(position.type)} position`}
      aria-live="polite"
      aria-atomic="true"
      aria-describedby={`insertion-zone-help-${position.targetTaskId}`}
    >
      {/* Left insertion line */}
      <div
        className={cn(
          'insertion-line flex-grow relative overflow-hidden',
          'transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)',
          isVisible ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          height: indicatorStyles.lineHeight,
          backgroundColor: showSuccessAnimation 
            ? 'hsl(142 76% 36%)' // green-600
            : isHovered || isPressed 
              ? 'hsl(var(--primary))' 
              : 'hsl(var(--primary) / 0.6)'
        }}
      >
        {/* Animated gradient effect on hover */}
        {(isHovered || isPressed) && !showSuccessAnimation && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent"
            style={{
              animation: 'shimmer 2s ease-in-out infinite'
            }}
          />
        )}
        
        {/* Success ripple effect */}
        {showSuccessAnimation && (
          <div
            className="absolute inset-0 bg-green-400/30 rounded-full"
            style={{
              animation: 'rippleEffect 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards'
            }}
          />
        )}
      </div>

      {/* Insertion button - centered */}
      <button
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchCancel={handleTouchCancel}
        disabled={disabled}
        className={cn(
          // Base button styles
          'insertion-button flex items-center justify-center',
          'border-2 rounded-full',
          'transition-all duration-200 ease-in-out',
          'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2',
          'active:scale-95',
          // Touch-friendly sizing and behavior
          (touchFriendly || isMobile) && 'touch-manipulation select-none',
          // Ensure minimum touch target size on mobile
          isMobile && 'min-w-[44px] min-h-[44px]',
          // State-based styles
          buttonStateClasses
        )}
        style={{
          width: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
          height: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
          marginLeft: '8px',
          marginRight: '8px',
          flexShrink: 0
        }}
        title={`Insert task ${getInsertionTypeDescription(position.type)} current task`}
        aria-label={`Insert new task ${getInsertionTypeDescription(position.type)} current task. ${onLongPress ? 'Long press for additional options.' : ''}`}
        role="button"
        aria-describedby={`insertion-help-${position.targetTaskId}`}
        aria-expanded={false}
        aria-haspopup={onLongPress ? 'menu' : false}
        aria-pressed={isPressed}
        aria-busy={disabled}
      >
        {showSuccessAnimation ? (
          // Success checkmark icon
          <svg 
            width={isMobile || touchFriendly ? 16 : 12} 
            height={isMobile || touchFriendly ? 16 : 12} 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="3" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            className="text-white transition-all duration-300 scale-110"
          >
            <polyline points="20,6 9,17 4,12" />
          </svg>
        ) : (
          <Plus 
            size={isMobile || touchFriendly ? 16 : 12} 
            className={cn(
              'text-white transition-transform duration-200',
              isLongPressing ? 'scale-125 rotate-45' : isPressed ? 'scale-90' : 'scale-100',
              isHovered && !isLongPressing ? 'rotate-90' : 'rotate-0'
            )}
          />
        )}
      </button>

      {/* Right insertion line */}
      <div
        className={cn(
          'insertion-line flex-grow relative overflow-hidden',
          'transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)',
          isVisible ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          height: indicatorStyles.lineHeight,
          backgroundColor: showSuccessAnimation 
            ? 'hsl(142 76% 36%)' // green-600
            : isHovered || isPressed 
              ? 'hsl(var(--primary))' 
              : 'hsl(var(--primary) / 0.6)'
        }}
      >
        {/* Animated gradient effect on hover */}
        {(isHovered || isPressed) && !showSuccessAnimation && (
          <div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent"
            style={{
              animation: 'shimmer 2s ease-in-out infinite'
            }}
          />
        )}
        
        {/* Success ripple effect */}
        {showSuccessAnimation && (
          <div
            className="absolute inset-0 bg-green-400/30 rounded-full"
            style={{
              animation: 'rippleEffect 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards'
            }}
          />
        )}
      </div>

      {/* Ripple effect for touch feedback */}
      {(isPressed || isLongPressing) && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            left: `${insertionTypeStyles.indentLevel + 8}px`
          }}
        >
          <div
            className={cn(
              'absolute rounded-full',
              isLongPressing 
                ? 'bg-primary/30 animate-pulse' 
                : 'bg-primary/20 animate-ping'
            )}
            style={{
              width: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
              height: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
              right: '0px',
              top: '50%',
              transform: 'translateY(-50%)'
            }}
          />
        </div>
      )}

      {/* Long press progress indicator */}
      {isPressed && onLongPress && !isLongPressing && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            left: `${insertionTypeStyles.indentLevel + 8}px`
          }}
        >
          <div
            className="absolute border-2 border-primary/40 rounded-full"
            style={{
              width: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
              height: Math.max(parseInt(indicatorStyles.buttonSize), isMobile ? 44 : 20),
              right: '0px',
              top: '50%',
              transform: 'translateY(-50%)',
              animation: `longPressProgress ${longPressDelay}ms linear forwards`
            }}
          />
        </div>
      )}

      {/* Hidden descriptions for screen readers */}
      <div
        id={`insertion-help-${position.targetTaskId}`}
        className="sr-only"
        aria-hidden="true"
      >
        {getDetailedInsertionDescription(position)}
        {onLongPress && '. Long press or hold for additional insertion options and settings.'}
      </div>
      
      <div
        id={`insertion-zone-help-${position.targetTaskId}`}
        className="sr-only"
        aria-hidden="true"
      >
        Insertion zone for {getInsertionTypeDescription(position.type)} position. 
        Use Tab to navigate to insertion button, then press Enter or Space to insert task.
        {isMobile && ' On touch devices, tap the plus button or long press for options.'}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.isVisible === nextProps.isVisible &&
    prevProps.isHovered === nextProps.isHovered &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.variant === nextProps.variant &&
    prevProps.touchFriendly === nextProps.touchFriendly &&
    prevProps.position.type === nextProps.position.type &&
    prevProps.position.targetTaskId === nextProps.position.targetTaskId &&
    prevProps.position.parentId === nextProps.position.parentId &&
    prevProps.position.level === nextProps.position.level &&
    prevProps.className === nextProps.className
  );
});

// CSS animations for enhanced visual feedback
const insertionIndicatorStyles = `
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  @keyframes longPressProgress {
    0% {
      border-color: transparent;
      transform: translateY(-50%) scale(1);
    }
    50% {
      border-color: hsl(var(--primary) / 0.6);
      transform: translateY(-50%) scale(1.1);
    }
    100% {
      border-color: hsl(var(--primary));
      transform: translateY(-50%) scale(1.2);
    }
  }
  
  @keyframes insertionSuccess {
    0% {
      transform: scale(1);
      background-color: hsl(var(--primary));
    }
    50% {
      transform: scale(1.2);
      background-color: hsl(142 76% 36%); /* green-600 */
      box-shadow: 0 0 20px hsl(142 76% 36% / 0.5);
    }
    100% {
      transform: scale(1);
      background-color: hsl(142 76% 36%);
      box-shadow: 0 0 10px hsl(142 76% 36% / 0.3);
    }
  }
  
  @keyframes slideInFromTop {
    0% {
      transform: translateY(-10px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes slideInFromBottom {
    0% {
      transform: translateY(10px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  @keyframes slideInFromLeft {
    0% {
      transform: translateX(-20px);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
    }
    50% {
      box-shadow: 0 0 15px hsl(var(--primary) / 0.6), 0 0 25px hsl(var(--primary) / 0.3);
    }
  }
  
  @keyframes lineExpand {
    0% {
      width: 0;
      opacity: 0;
    }
    100% {
      width: 100%;
      opacity: 1;
    }
  }
  
  @keyframes buttonBounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-3px);
    }
    60% {
      transform: translateY(-1px);
    }
  }
  
  @keyframes rippleEffect {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  .insertion-indicator {
    /* Ensure smooth transitions */
    will-change: opacity, transform;
    /* Animation timing for smooth appearance */
    animation-duration: 0.3s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    animation-fill-mode: both;
  }
  
  .insertion-indicator.slide-in-top {
    animation-name: slideInFromTop;
  }
  
  .insertion-indicator.slide-in-bottom {
    animation-name: slideInFromBottom;
  }
  
  .insertion-indicator.slide-in-left {
    animation-name: slideInFromLeft;
  }
  
  .insertion-line {
    /* Smooth line expansion animation */
    animation: lineExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    transform-origin: left center;
  }
  
  .insertion-button {
    /* Ensure button is always clickable */
    position: relative;
    z-index: 10;
    /* Prevent text selection on touch devices */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Prevent callout on iOS */
    -webkit-touch-callout: none;
    /* Prevent tap highlight on mobile */
    -webkit-tap-highlight-color: transparent;
    /* Enhanced transition for smooth interactions */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .insertion-button.success-animation {
    animation: insertionSuccess 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }
  
  .insertion-button.bounce-animation {
    animation: buttonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Enhanced hover effects for desktop */
  @media (hover: hover) and (pointer: fine) {
    .insertion-indicator:hover .insertion-line {
      box-shadow: 0 0 8px hsl(var(--primary) / 0.3);
      animation: pulseGlow 2s ease-in-out infinite;
    }
    
    .insertion-indicator:hover .insertion-button {
      box-shadow: 0 2px 8px hsl(var(--primary) / 0.4);
      transform: translateY(-1px);
    }
    
    .insertion-indicator:hover .insertion-button:not(.success-animation) {
      animation: buttonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  
  /* Touch-friendly adjustments */
  @media (hover: none) and (pointer: coarse) {
    .insertion-indicator {
      /* Larger touch targets on mobile */
      min-height: 44px;
      padding: 6px 0;
    }
    
    .insertion-button {
      /* Ensure minimum touch target size */
      min-width: 44px !important;
      min-height: 44px !important;
      /* Larger touch area */
      padding: 8px;
    }
    
    .insertion-line {
      /* Thicker line for better visibility on mobile */
      min-height: 3px;
    }
  }
  
  /* Tablet adjustments */
  @media (hover: none) and (pointer: coarse) and (min-width: 768px) {
    .insertion-indicator {
      min-height: 36px;
      padding: 4px 0;
    }
    
    .insertion-button {
      min-width: 36px !important;
      min-height: 36px !important;
      padding: 6px;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .insertion-line {
      border: 3px solid currentColor;
      background-color: transparent;
      min-height: 4px;
      box-shadow: 0 0 0 1px currentColor;
    }
    
    .insertion-button {
      border-width: 4px;
      background-color: ButtonFace;
      color: ButtonText;
      box-shadow: 0 0 0 2px currentColor;
      outline: 2px solid transparent;
    }
    
    .insertion-button:hover,
    .insertion-button:focus {
      background-color: Highlight;
      color: HighlightText;
      border-color: HighlightText;
      outline: 2px solid HighlightText;
      outline-offset: 2px;
    }
    
    .insertion-button:focus {
      box-shadow: 0 0 0 4px Highlight;
    }
    
    .insertion-button:active {
      background-color: ActiveText;
      color: Canvas;
      border-color: ActiveText;
    }
    
    .insertion-indicator:hover .insertion-line {
      border-color: Highlight;
      background-color: Highlight;
      box-shadow: 0 0 0 2px Highlight;
    }
    
    /* Ensure sufficient contrast for disabled state */
    .insertion-button:disabled {
      background-color: GrayText;
      color: Canvas;
      border-color: GrayText;
      opacity: 1;
    }
  }
  
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .insertion-indicator,
    .insertion-line,
    .insertion-button {
      transition: none !important;
      animation: none !important;
    }
    
    .insertion-button:active {
      transform: none !important;
    }
  }
  
  /* Dark mode adjustments */
  @media (prefers-color-scheme: dark) {
    .insertion-button {
      /* Better contrast in dark mode */
      border-color: hsl(var(--primary) / 0.8);
    }
  }
  
  /* Windows High Contrast mode support */
  @media (forced-colors: active) {
    .insertion-line {
      border: 2px solid ButtonText;
      background-color: ButtonText;
      forced-color-adjust: none;
      box-shadow: none;
    }
    
    .insertion-button {
      border: 2px solid ButtonText;
      background-color: ButtonFace;
      color: ButtonText;
      forced-color-adjust: none;
      box-shadow: none;
    }
    
    .insertion-button:hover,
    .insertion-button:focus {
      background-color: Highlight;
      color: HighlightText;
      border-color: HighlightText;
      outline: 2px solid HighlightText;
      outline-offset: 1px;
    }
    
    .insertion-button:active {
      background-color: ActiveText;
      color: Canvas;
      border-color: ActiveText;
    }
    
    .insertion-button:disabled {
      background-color: GrayText;
      color: GrayText;
      border-color: GrayText;
    }
    
    /* Remove animations and transitions in forced colors mode */
    .insertion-indicator,
    .insertion-line,
    .insertion-button {
      transition: none !important;
      animation: none !important;
    }
  }
`;

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleId = 'insertion-indicator-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = insertionIndicatorStyles;
    document.head.appendChild(style);
  }
}

export default InsertionIndicator;