"use client";

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Download, 
  FileText, 
  Database, 
  FileSpreadsheet, 
  FileImage,
  Smartphone,
  Clock,
  Shield,
  Info,
  CheckCircle
} from 'lucide-react';
import { 
  exportProject,
  getAvailableExportFormats,
  type ExportFormat,
  type ExportOptions,
  type PWAMetadata
} from '@/lib/export';
import type { Task } from '@/lib/types';

interface EnhancedExportDialogProps {
  project: {
    mainProject: string;
    mainProjectDescription?: string;
    tasks: Task[];
  };
  pwaMetadata?: PWAMetadata;
  libsLoaded?: boolean;
  trigger?: React.ReactNode;
}

const formatInfo: Record<ExportFormat, {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  features: string[];
  recommended?: boolean;
}> = {
  pdf: {
    icon: FileImage,
    title: 'PDF Document',
    description: 'Professional document with formatting and PWA metadata',
    features: ['Formatted layout', 'Offline indicators', 'Device info', 'Print-ready']
  },
  markdown: {
    icon: FileText,
    title: 'Markdown File',
    description: 'Human-readable format with PWA information',
    features: ['GitHub compatible', 'PWA metadata', 'Statistics', 'Version control friendly']
  },
  csv: {
    icon: FileSpreadsheet,
    title: 'CSV Spreadsheet',
    description: 'Tabular data with optional timestamps',
    features: ['Excel compatible', 'Timestamps', 'Flat structure', 'Data analysis ready']
  },
  json: {
    icon: Database,
    title: 'JSON Export',
    description: 'Structured data with full project information',
    features: ['Complete metadata', 'Hierarchical structure', 'Timestamps', 'API compatible'],
    recommended: true
  },
  backup: {
    icon: Shield,
    title: 'Full Backup',
    description: 'Complete app state including all projects and settings',
    features: ['All projects', 'App settings', 'Complete restore', 'Device info'],
    recommended: true
  }
};

export function EnhancedExportDialog({ 
  project, 
  pwaMetadata, 
  libsLoaded = true, 
  trigger 
}: EnhancedExportDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('json');
  const [includeTimestamps, setIncludeTimestamps] = useState(true);
  const [includeOfflineIndicators, setIncludeOfflineIndicators] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [exportResult, setExportResult] = useState<{ success: boolean; error?: string } | null>(null);

  const availableFormats = getAvailableExportFormats(libsLoaded);

  const handleExport = async () => {
    setIsExporting(true);
    setExportResult(null);

    try {
      const options: ExportOptions = {
        format: selectedFormat,
        project,
        libsLoaded,
        pwaMetadata,
        includeTimestamps,
        includeOfflineIndicators
      };

      const result = await exportProject(options);
      setExportResult(result);
      
      if (result.success) {
        // Auto-close dialog after successful export
        setTimeout(() => {
          setIsOpen(false);
        }, 2000);
      }
    } catch (error) {
      setExportResult({
        success: false,
        error: error instanceof Error ? error.message : 'Export failed'
      });
    } finally {
      setIsExporting(false);
    }
  };

  const resetDialog = () => {
    setExportResult(null);
    setIsExporting(false);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(resetDialog, 300);
  };

  const getFormatIcon = (format: ExportFormat) => {
    const IconComponent = formatInfo[format]?.icon || FileText;
    return <IconComponent className="h-4 w-4" />;
  };

  const isPWAFormat = (format: ExportFormat) => {
    return ['json', 'backup', 'pdf', 'markdown'].includes(format);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Export Project
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Project: {project.mainProject}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">Export Format</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availableFormats.map((format) => {
                const info = formatInfo[format];
                const isSelected = selectedFormat === format;
                
                return (
                  <Card 
                    key={format}
                    className={`cursor-pointer transition-all ${
                      isSelected 
                        ? 'ring-2 ring-primary border-primary' 
                        : 'hover:border-muted-foreground/50'
                    }`}
                    onClick={() => setSelectedFormat(format)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          {getFormatIcon(format)}
                          {info.title}
                        </div>
                        <div className="flex gap-1">
                          {info.recommended && (
                            <Badge variant="secondary" className="text-xs">
                              Recommended
                            </Badge>
                          )}
                          {isPWAFormat(format) && (
                            <Badge variant="outline" className="text-xs">
                              <Smartphone className="h-3 w-3 mr-1" />
                              PWA
                            </Badge>
                          )}
                        </div>
                      </CardTitle>
                      <CardDescription className="text-xs">
                        {info.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex flex-wrap gap-1">
                        {info.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          <Separator />

          {/* PWA Options */}
          {isPWAFormat(selectedFormat) && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                <Label className="text-sm font-medium">PWA Export Options</Label>
              </div>
              
              <div className="space-y-3 pl-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="timestamps"
                    checked={includeTimestamps}
                    onCheckedChange={setIncludeTimestamps}
                    disabled={isExporting}
                  />
                  <Label htmlFor="timestamps" className="text-sm flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    Include timestamps and creation dates
                  </Label>
                </div>

                {(selectedFormat === 'pdf' || selectedFormat === 'markdown') && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="offline-indicators"
                      checked={includeOfflineIndicators}
                      onCheckedChange={setIncludeOfflineIndicators}
                      disabled={isExporting}
                    />
                    <Label htmlFor="offline-indicators" className="text-sm flex items-center gap-2">
                      <Shield className="h-3 w-3" />
                      Show offline mode indicators
                    </Label>
                  </div>
                )}
              </div>

              {pwaMetadata && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <div className="text-sm">
                      <p className="font-medium">PWA Metadata Available:</p>
                      <ul className="mt-1 space-y-1 text-xs">
                        <li>• Offline Mode: {pwaMetadata.isOfflineOnly ? 'Enabled' : 'Disabled'}</li>
                        <li>• Created: {pwaMetadata.createdAt.toLocaleDateString()}</li>
                        <li>• Last Updated: {pwaMetadata.updatedAt.toLocaleDateString()}</li>
                        {pwaMetadata.installDate && (
                          <li>• Installed: {pwaMetadata.installDate.toLocaleDateString()}</li>
                        )}
                      </ul>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Library Status */}
          {!libsLoaded && (selectedFormat === 'pdf' || selectedFormat === 'csv') && (
            <Alert variant="destructive">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Required libraries for {selectedFormat.toUpperCase()} export are not loaded. 
                Please wait for libraries to load or refresh the page.
              </AlertDescription>
            </Alert>
          )}

          {/* Export Result */}
          {exportResult && (
            <Alert variant={exportResult.success ? "default" : "destructive"}>
              {exportResult.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <Info className="h-4 w-4" />
              )}
              <AlertDescription>
                {exportResult.success ? (
                  <div>
                    <p className="font-medium">Export completed successfully!</p>
                    <p className="text-sm mt-1">
                      Your {selectedFormat.toUpperCase()} file has been downloaded.
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="font-medium">Export failed</p>
                    <p className="text-sm mt-1">{exportResult.error}</p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={handleClose}>
              {exportResult?.success ? 'Close' : 'Cancel'}
            </Button>
            {!exportResult?.success && (
              <Button 
                onClick={handleExport}
                disabled={isExporting || (!libsLoaded && (selectedFormat === 'pdf' || selectedFormat === 'csv'))}
                className="gap-2"
              >
                {isExporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Export {selectedFormat.toUpperCase()}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}