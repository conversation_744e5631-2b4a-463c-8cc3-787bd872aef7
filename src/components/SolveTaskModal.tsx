
"use client";

import { useState, memo, useC<PERSON>back, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, <PERSON>r, Bot } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { Task, ContextStrategies } from '@/lib/types';
import { unifiedAIClient, contextBuilder } from '@/lib/ai';
import { decodeHtmlEntities } from '@/lib/utils/htmlParser';

// Real AI functions using Gemini API
const generateTaskContent = async (input: {
  taskName: string,
  taskDescription: string,
  context: string,
  additionalContext?: string,
  existingContent?: string
}): Promise<{ htmlContent: string, progress: string, error?: string }> => {
  const result = await unifiedAIClient.generateTaskContent(
    input.taskName,
    input.taskDescription,
    input.context,
    input.additionalContext
  );

  if (result.error) {
    return {
      htmlContent: `<p class="text-red-400">Fehler bei der Inhaltsgenerierung: ${result.error}</p>`,
      progress: 'Fehler bei der Inhaltsgenerierung.',
      error: result.error
    };
  }

  return {
    htmlContent: result.content,
    progress: 'Aufgabeninhalt erfolgreich generiert.'
  };
};

const refineSelectedText = async (input: {
  selectedText: string,
  context: string,
  fullContent: string
}): Promise<{ refinedText: string, error?: string }> => {
  const result = await unifiedAIClient.elaborateContent(
    input.fullContent,
    input.context,
    input.selectedText
  );

  if (result.error) {
    return {
      refinedText: input.selectedText, // Return original on error
      error: result.error
    };
  }

  return {
    refinedText: result.content
  };
};

const generateQuestions = async (context: string): Promise<{ questions: string[], error?: string }> => {
  const result = await unifiedAIClient.generateQuestions(context);

  if (result.error) {
    return {
      questions: [
        "Was sind die Hauptanforderungen für diese Aufgabe?",
        "Gibt es technische Einschränkungen zu beachten?",
        "Wie würde Erfolg für diese Aufgabe aussehen?"
      ],
      error: result.error
    };
  }

  return {
    questions: result.questions
  };
};


interface SolveTaskModalProps {
  modalId: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  task: Task | null;
  onUpdateTask: (task: Task) => void;
  setMotivation: (message: string) => void;
  findTask: (tasks: Task[], id: string) => Task | null;
  tasks: Task[];
  selectionWrapperId: string;
  mainProject: string;
  mainProjectDescription: string;
  contextStrategies: ContextStrategies;
  onSolveComplete?: (taskId: string) => void;
  handleSolve: (taskId: string, taskTitle: string, taskDescription: string, userAdditionalPrompt?: string, selectedHtml?: string, wrapperId?: string) => Promise<void>;
}

export const SolveTaskModal = memo(function SolveTaskModal({
  modalId,
  isOpen,
  onOpenChange,
  task,
  onUpdateTask,
  setMotivation,
  findTask,
  tasks,
  selectionWrapperId,
  mainProject,
  mainProjectDescription,
  contextStrategies,
  onSolveComplete,
  handleSolve,
}: SolveTaskModalProps) {
  const { toast } = useToast();
  const [step, setStep] = useState<'choice' | 'autopilot_prompt' | 'copilot_asking' | 'copilot_answering'>('choice');
  const [questions, setQuestions] = useState<string[]>([]);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [additionalPrompt, setAdditionalPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectionLoadingState, setSelectionLoadingState] = useState<string | null>(null);


  const exampleAdditionalPrompts = useMemo(() => [
    'Erkläre es einem 10-Jährigen.',
    'Fasse dich so kurz wie möglich.',
    'Erstelle eine Schritt-für-Schritt-Anleitung.',
    'Sei extrem motivierend und inspirierend.',
    'Welche Werkzeuge brauche ich dafür?',
    'Erstelle eine Vergleichstabelle.',
    'Was sind die häufigsten Fehler?',
    'Gib mir ein Code-Beispiel.',
    'Formuliere es als E-Mail.',
    'Was sind die wichtigsten Überlegungen?',
    'Erstelle einen Zeitplan.',
    'Welche Alternativen gibt es?',
    'Was sind die finanziellen Aspekte?',
    'Wie kann ich das sicher tun?',
    'Zerlege es in die kleinsten denkbaren Teile.',
    'Erstelle eine Checkliste.',
    'Was sind die Vor- und Nachteile?',
    'Wie messe ich den Erfolg?',
    'Welche Ressourcen benötige ich?',
    'Was sind mögliche Hindernisse?',
  ], []);

  const handleClose = useCallback(() => {
    onOpenChange(false);
    // Reset state for next time
    setTimeout(() => {
      setStep('choice');
      setQuestions([]);
      setAnswers({});
      setAdditionalPrompt('');
      setSelectionLoadingState(null);
    }, 300);
  }, [onOpenChange]);

  // Use the external handleSolve function from page.tsx

  const handleConfirmAutopilot = useCallback(async () => {
    if (!task) return;

    let selectedHtml = '';
    if (selectionWrapperId) {
      const currentTask = findTask(tasks, task.id);
      if (currentTask) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentTask.content;
        const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
        if (wrapper) {
          selectedHtml = decodeHtmlEntities(wrapper.innerHTML);
          // Enhanced loading indicator for selection-based operations
          wrapper.innerHTML = `
                  <div class="flex items-center justify-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded border-2 border-dashed border-blue-300 dark:border-blue-600">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span class="ml-2 text-sm text-blue-600 dark:text-blue-400">Text wird überarbeitet...</span>
                  </div>
                `;
          onUpdateTask({ ...currentTask, content: tempDiv.innerHTML });
        } else {
          // Handle case where wrapper is not found
          toast({
            variant: 'destructive',
            title: 'Fehler',
            description: 'Ausgewählter Text nicht mehr verfügbar. Bitte versuchen Sie es erneut.'
          });
          return;
        }
      }
    }

    // Create a comprehensive prompt that includes user instructions
    const finalPrompt = additionalPrompt
      ? `Bitte erstelle detaillierten Inhalt für diese Aufgabe mit folgenden spezifischen Anweisungen: ${additionalPrompt}`
      : 'Bitte erstelle umfassenden, detaillierten Inhalt für diese Aufgabe.';

    handleClose();
    await handleSolve(task.id, task.title, task.description, finalPrompt, selectedHtml, selectionWrapperId);
  }, [task, selectionWrapperId, findTask, onUpdateTask, toast, additionalPrompt, handleClose, handleSolve]);

  const handleCopilotSelect = useCallback(async () => {
    if (!task) return;
    setStep('copilot_asking');
    setIsLoading(true);

    let selectedHtml = '';
    if (selectionWrapperId) {
      const currentTask = findTask(tasks, task.id);
      if (currentTask) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentTask.content;
        const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
        if (wrapper) {
          selectedHtml = decodeHtmlEntities(wrapper.innerHTML);
        }
      }
    }

    try {
      // Build context for Co-pilot question generation
      const context = contextBuilder.buildCopilotContext(
        task,
        tasks,
        mainProject,
        mainProjectDescription
      );

      // Add selection context if applicable
      const fullContext = selectedHtml
        ? `${context}\n\nDer Benutzer möchte folgenden Textabschnitt verfeinern: "${selectedHtml}"`
        : context;

      const result = await generateQuestions(fullContext);

      if (result.error) {
        toast({
          variant: 'destructive',
          title: 'Warnung bei Fragengenerierung',
          description: result.error
        });
      }

      setQuestions(result.questions.length > 0 ? result.questions : ["Konnte keine spezifischen Fragen generieren. Bitte beschreiben Sie Ihr Ziel genauer."]);
      setStep('copilot_answering');
    } catch (e) {
      console.error(e);
      toast({
        variant: 'destructive',
        title: 'Fehler bei Fragengenerierung',
        description: e instanceof Error ? e.message : 'Unbekannter Fehler'
      });
      setStep('choice');
    } finally {
      setIsLoading(false);
    }
  }, [task, selectionWrapperId, findTask, tasks, mainProject, mainProjectDescription, contextBuilder, toast]);

  const handleConfirmCopilot = useCallback(async () => {
    if (!task) return;

    let selectedHtml = '';
    if (selectionWrapperId) {
      const currentTask = findTask(tasks, task.id);
      if (currentTask) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentTask.content;
        const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
        if (wrapper) {
          selectedHtml = decodeHtmlEntities(wrapper.innerHTML);
          // Enhanced loading indicator for selection-based operations
          wrapper.innerHTML = `
                  <div class="flex items-center justify-center p-3 bg-green-50 dark:bg-green-900/20 rounded border-2 border-dashed border-green-300 dark:border-green-600">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                    <span class="ml-2 text-sm text-green-600 dark:text-green-400">Text wird mit Co-pilot überarbeitet...</span>
                  </div>
                `;
          onUpdateTask({ ...currentTask, content: tempDiv.innerHTML });
        } else {
          // Handle case where wrapper is not found
          toast({
            variant: 'destructive',
            title: 'Fehler',
            description: 'Ausgewählter Text nicht mehr verfügbar. Bitte versuchen Sie es erneut.'
          });
          return;
        }
      }
    }

    handleClose();

    // Build comprehensive context with answers
    const answersString = Object.entries(answers)
      .filter(([_, a]) => a.trim()) // Only include non-empty answers
      .map(([q, a]) => `Frage: "${q}" - Antwort: "${a}"`)
      .join('\n');

    const copilotPrompt = answersString
      ? `Basierend auf den folgenden Antworten auf die Co-pilot Fragen:\n\n${answersString}\n\nBitte erstelle detaillierten Inhalt für diese Aufgabe.`
      : 'Bitte erstelle detaillierten Inhalt für diese Aufgabe basierend auf den verfügbaren Informationen.';

    await handleSolve(task.id, task.title, task.description, copilotPrompt, selectedHtml, selectionWrapperId);
  }, [task, selectionWrapperId, findTask, onUpdateTask, toast, answers, handleClose, handleSolve]);


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent onInteractOutside={(e) => e.preventDefault()} className="max-w-lg max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {selectionWrapperId ? 'Text überarbeiten' : 'Aufgabe lösen'}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Aufgabe: <span className="font-semibold text-primary">{task?.title}</span>
          </p>
          {selectionWrapperId && (
            <div className="text-xs bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                <span className="text-amber-700 dark:text-amber-300 font-medium">
                  Textauswahl-Modus aktiv
                </span>
              </div>
              <p className="text-amber-600 dark:text-amber-400 mt-1">
                Sie überarbeiten einen ausgewählten Textabschnitt aus der Aufgabe
              </p>
              {selectionLoadingState === selectionWrapperId && (
                <div className="flex items-center gap-2 mt-2 text-amber-600 dark:text-amber-400">
                  <div className="animate-spin rounded-full h-3 w-3 border border-amber-500 border-t-transparent"></div>
                  <span className="text-xs">Überarbeitung läuft...</span>
                </div>
              )}
            </div>
          )}
        </div>

        {step === 'choice' && (
          <div className="space-y-4 py-4">
            <p className="text-sm text-muted-foreground text-center">
              Wählen Sie, wie die KI diese Aufgabe angehen soll:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={handleCopilotSelect}
                className="p-4 rounded-lg bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors text-left"
              >
                <div className="flex items-center gap-3">
                  <User className="text-indigo-500" size={24} />
                  <div>
                    <p className="font-bold text-slate-800 dark:text-white">Co-Pilot</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">KI stellt Rückfragen für eine präzise Lösung.</p>
                  </div>
                </div>
              </button>
              <button
                onClick={() => setStep('autopilot_prompt')}
                className="p-4 rounded-lg bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors text-left"
              >
                <div className="flex items-center gap-3">
                  <Bot className="text-indigo-500" size={24} />
                  <div>
                    <p className="font-bold text-slate-800 dark:text-white">Autopilot</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400">KI trifft Annahmen und legt sofort los.</p>
                  </div>
                </div>
              </button>
            </div>
          </div>
        )}

        {step === 'autopilot_prompt' && (
          <div className="space-y-4 py-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Zusätzliche Anweisungen für die KI:
              </label>
              <Textarea
                value={additionalPrompt}
                onChange={(e) => setAdditionalPrompt(e.target.value)}
                placeholder="Optional: Beschreiben Sie, wie die KI die Aufgabe angehen soll..."
                rows={3}
                className="resize-none"
              />
            </div>

            <div>
              <p className="text-sm text-muted-foreground mb-3">
                Oder wählen Sie eine Anregung aus den beliebten Optionen:
              </p>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-1">
                {exampleAdditionalPrompts.map((prompt) => (
                  <Button
                    key={prompt}
                    variant="secondary"
                    size="sm"
                    className="rounded-full h-auto py-1 px-3 text-xs hover:bg-primary hover:text-primary-foreground transition-colors whitespace-nowrap"
                    onClick={() => {
                      const separator = additionalPrompt.trim() ? ' ' : '';
                      setAdditionalPrompt(prev => prev + separator + prompt);
                    }}
                  >
                    {prompt}
                  </Button>
                ))}
              </div>
            </div>

            {additionalPrompt && (
              <div className="p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground mb-1">Vorschau der Anweisungen:</p>
                <p className="text-sm font-medium">{additionalPrompt}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  className="mt-2 h-6 px-2 text-xs"
                  onClick={() => setAdditionalPrompt('')}
                >
                  Zurücksetzen
                </Button>
              </div>
            )}

            <DialogFooter className="flex-col sm:flex-row gap-2">
              <button onClick={handleClose} className="w-full sm:w-auto px-4 py-2 text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold rounded-lg hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">Abbrechen</button>
              <Button onClick={handleConfirmAutopilot} disabled={isLoading} className="w-full sm:w-auto">
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Sparkles className="mr-2 h-4 w-4" />
                {selectionWrapperId ? 'Text überarbeiten' : 'Autopilot starten'}
              </Button>
            </DialogFooter>
          </div>
        )}

        {(step === 'copilot_asking' || (step === 'copilot_answering' && isLoading)) && (
          <div className="text-center py-8">
            <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
            <p className="mt-4 text-muted-foreground">KI analysiert die Aufgabe und stellt Rückfragen...</p>
          </div>
        )}

        {step === 'copilot_answering' && !isLoading && (
          <div className="space-y-4 py-4">
            <p className="text-muted-foreground">Bitte beantworten Sie die folgenden Fragen der KI:</p>
            <div className="space-y-4 max-h-60 overflow-y-auto pr-2">
              {questions.map((q, i) => (
                <div key={i}>
                  <label className="block text-sm font-medium mb-1 break-words">{q}</label>
                  <Input type="text" onChange={(e) => setAnswers(prev => ({ ...prev, [q]: e.target.value }))} className="w-full" />
                </div>
              ))}
            </div>
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <button onClick={handleClose} className="w-full sm:w-auto px-4 py-2 text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold rounded-lg hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">Abbrechen</button>
              <Button onClick={handleConfirmCopilot} disabled={isLoading} className="w-full sm:w-auto">
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Sparkles className="mr-2 h-4 w-4" /> Aufgabe mit Antworten lösen
              </Button>
            </DialogFooter>
          </div>
        )}

      </DialogContent>
    </Dialog>
  );
});
