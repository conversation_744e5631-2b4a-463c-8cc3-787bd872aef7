'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { X, Download, Smartphone, Zap, Wifi, CheckCircle, Star, ArrowRight } from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface InstallationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [installationStep, setInstallationStep] = useState<'prompt' | 'installing' | 'success'>('prompt');
  const [onboardingProgress, setOnboardingProgress] = useState(0);

  // Installation benefits and steps
  const installationBenefits = [
    {
      icon: <Smartphone className="h-5 w-5 text-primary" />,
      title: "Native App Gefühl",
      description: "Läuft wie eine echte App auf Ihrem Gerät"
    },
    {
      icon: <Wifi className="h-5 w-5 text-primary" />,
      title: "Offline Verfügbar",
      description: "Arbeiten Sie auch ohne Internetverbindung"
    },
    {
      icon: <Zap className="h-5 w-5 text-primary" />,
      title: "Schneller Start",
      description: "Direkter Zugriff vom Homescreen"
    }
  ];

  const onboardingSteps: InstallationStep[] = [
    {
      id: 'welcome',
      title: 'Willkommen bei KI Projekt-Planer',
      description: 'Transformieren Sie Ihre Ideen in umsetzbare Projekte',
      icon: <Star className="h-6 w-6 text-primary" />,
      completed: onboardingProgress > 0
    },
    {
      id: 'benefits',
      title: 'Vorteile der App-Installation',
      description: 'Entdecken Sie die Vorteile einer installierten PWA',
      icon: <Zap className="h-6 w-6 text-primary" />,
      completed: onboardingProgress > 33
    },
    {
      id: 'install',
      title: 'Installation starten',
      description: 'Installieren Sie die App für das beste Erlebnis',
      icon: <Download className="h-6 w-6 text-primary" />,
      completed: onboardingProgress > 66
    }
  ];

  useEffect(() => {
    // Check if app is already installed
    const checkInstalled = () => {
      try {
        const isStandalone =
          typeof window !== 'undefined' &&
          typeof window.matchMedia === 'function' &&
          !!window.matchMedia('(display-mode: standalone)') &&
          (window.matchMedia('(display-mode: standalone)').matches ||
            ((window.navigator as any)?.standalone === true));
        if (isStandalone) {
          setIsInstalled(true);
          return;
        }
      } catch (_) {
        // In non-browser or test environments, safely ignore
      }
    };

    checkInstalled();

    // Check if user has seen onboarding
    const hasSeenOnboarding = localStorage.getItem('pwa-onboarding-seen') === 'true';
    const promptDismissedCount = parseInt(localStorage.getItem('pwa-prompt-dismissed-count') || '0');
    
    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const evt = e as any;
      const bip: BeforeInstallPromptEvent | null =
        (evt && (typeof evt.prompt === 'function' || evt.userChoice)) ? evt as BeforeInstallPromptEvent
        : (evt?.detail && (typeof evt.detail.prompt === 'function' || evt.detail.userChoice)) ? evt.detail as BeforeInstallPromptEvent
        : null;
      if (bip) {
        setDeferredPrompt(bip);
      }
      
      // Show onboarding for first-time users, otherwise show simple prompt
      const delay = process.env.NODE_ENV === 'test' ? 0 : 5000;
      setTimeout(() => {
        if (!isInstalled && promptDismissedCount < 3) {
          if (!hasSeenOnboarding) {
            setShowOnboarding(true);
          } else {
            setShowPrompt(true);
          }
        }
      }, delay);
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowPrompt(false);
      setShowOnboarding(false);
      setInstallationStep('success');
      setDeferredPrompt(null);
      
      // Show success message briefly
      setTimeout(() => {
        setInstallationStep('prompt');
      }, 3000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [isInstalled, onboardingProgress]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    setInstallationStep('installing');

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
        // Success state will be handled by appinstalled event
      } else {
        console.log('User dismissed the install prompt');
        setInstallationStep('prompt');
        handleDismiss();
      }
    } catch (error) {
      console.error('Error during installation:', error);
      setInstallationStep('prompt');
    } finally {
      setDeferredPrompt(null);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    setShowOnboarding(false);
    
    // Track dismissal count
    const currentCount = parseInt(localStorage.getItem('pwa-prompt-dismissed-count') || '0');
    localStorage.setItem('pwa-prompt-dismissed-count', (currentCount + 1).toString());
    
    // Don't show again for this session
    sessionStorage.setItem('pwa-prompt-dismissed', 'true');
  };

  const handleOnboardingNext = () => {
    if (onboardingProgress < 100) {
      setOnboardingProgress(prev => Math.min(prev + 33.33, 100));
    } else {
      // Complete onboarding and show install prompt
      localStorage.setItem('pwa-onboarding-seen', 'true');
      setShowOnboarding(false);
      setShowPrompt(true);
    }
  };

  const handleSkipOnboarding = () => {
    localStorage.setItem('pwa-onboarding-seen', 'true');
    setShowOnboarding(false);
    setShowPrompt(true);
  };

  // Don't show if already installed or dismissed this session
  if (isInstalled && installationStep !== 'success') {
    return null;
  }

  if (!showPrompt && !showOnboarding && installationStep !== 'success') {
    return null;
  }

  if (sessionStorage.getItem('pwa-prompt-dismissed') === 'true' && !showOnboarding) {
    return null;
  }

  // Success state after installation
  if (installationStep === 'success') {
    return (
      <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-green-500/20 bg-green-50 dark:bg-green-950 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-800 dark:text-green-200">
                  Installation erfolgreich!
                </h3>
                <p className="text-sm text-green-600 dark:text-green-300">
                  KI Projekt-Planer ist jetzt auf Ihrem Gerät installiert.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Onboarding flow
  if (showOnboarding) {
    const currentStep = onboardingSteps.find(step => 
      step.id === (onboardingProgress <= 33 ? 'welcome' : 
                   onboardingProgress <= 66 ? 'benefits' : 'install')
    );

    return (
      <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md border-2 border-primary/20 shadow-xl">
          <CardHeader>
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                Schritt {Math.floor(onboardingProgress / 33.33) + 1} von 3
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSkipOnboarding}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <Progress value={onboardingProgress} className="mt-2" />
          </CardHeader>
          
          <CardContent className="space-y-6">
            {currentStep && (
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  {currentStep.icon}
                </div>
                <div>
                  <CardTitle className="text-xl mb-2">{currentStep.title}</CardTitle>
                  <CardDescription className="text-base">
                    {currentStep.description}
                  </CardDescription>
                </div>
                
                {onboardingProgress <= 33 && (
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      Verwandeln Sie große Ideen in umsetzbare Aufgaben mit KI-Unterstützung.
                    </p>
                  </div>
                )}
                
                {onboardingProgress > 33 && onboardingProgress <= 66 && (
                  <div className="space-y-3">
                    {installationBenefits.map((benefit, index) => (
                      <div key={index} className="flex items-start gap-3 text-left">
                        {benefit.icon}
                        <div>
                          <h4 className="font-medium text-sm">{benefit.title}</h4>
                          <p className="text-xs text-muted-foreground">{benefit.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {onboardingProgress > 66 && (
                  <div className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      Bereit für die Installation? Die App wird direkt auf Ihrem Gerät verfügbar sein.
                    </p>
                  </div>
                )}
              </div>
            )}
            
            <div className="flex gap-2">
              {onboardingProgress < 100 ? (
                <>
                  <Button variant="outline" onClick={handleSkipOnboarding} className="flex-1">
                    Überspringen
                  </Button>
                  <Button onClick={handleOnboardingNext} className="flex-1">
                    Weiter
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="outline" onClick={handleSkipOnboarding} className="flex-1">
                    Später
                  </Button>
                  <Button onClick={handleInstallClick} className="flex-1" disabled={!deferredPrompt}>
                    <Download className="h-4 w-4 mr-2" />
                    Installieren
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Installing state
  if (installationStep === 'installing') {
    return (
      <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-primary/20 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <div>
                <h3 className="font-semibold">Installation läuft...</h3>
                <p className="text-sm text-muted-foreground">
                  Bitte warten Sie einen Moment.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Standard install prompt
  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
      <Card className="border-2 border-primary/20 shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Download className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">App installieren</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>
            Installieren Sie KI Projekt-Planer für ein besseres Erlebnis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-3 text-sm">
            <div className="flex flex-col items-center text-center">
              <Smartphone className="h-6 w-6 text-primary mb-1" />
              <span className="text-xs text-muted-foreground">Native App Gefühl</span>
            </div>
            <div className="flex flex-col items-center text-center">
              <Wifi className="h-6 w-6 text-primary mb-1" />
              <span className="text-xs text-muted-foreground">Offline Verfügbar</span>
            </div>
            <div className="flex flex-col items-center text-center">
              <Zap className="h-6 w-6 text-primary mb-1" />
              <span className="text-xs text-muted-foreground">Schneller Start</span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleInstallClick} className="flex-1" disabled={!deferredPrompt}>
              <Download className="h-4 w-4 mr-2" />
              Installieren
            </Button>
            <Button variant="outline" onClick={handleDismiss}>
              Später
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}