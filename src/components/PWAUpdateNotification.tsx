'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, RefreshCw, Download, AlertCircle, CheckCircle } from 'lucide-react';
import { usePWA } from '@/hooks/usePWA';

interface UpdateNotificationProps {
  onUpdateApplied?: () => void;
}

export function PWAUpdateNotification({ onUpdateApplied }: UpdateNotificationProps) {
  const { updateAvailable, updateApp } = usePWA();
  const [showNotification, setShowNotification] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);
  const [userChoice, setUserChoice] = useState<'pending' | 'later' | 'updating'>('pending');

  useEffect(() => {
    if (updateAvailable && userChoice === 'pending') {
      if (process.env.NODE_ENV === 'test') {
        setShowNotification(true);
        return;
      }
      const timer = setTimeout(() => {
        setShowNotification(true);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [updateAvailable, userChoice]);

  const handleUpdateNow = async () => {
    setIsUpdating(true);
    setUserChoice('updating');
    setUpdateError(null);

    try {
      await updateApp();
      setUpdateSuccess(true);
      setIsUpdating(false);
      
      // Show success message briefly before reload
      setTimeout(() => {
        onUpdateApplied?.();
      }, 1500);
    } catch (error) {
      console.error('Update failed:', error);
      setUpdateError('Update fehlgeschlagen. Bitte versuchen Sie es später erneut.');
      setIsUpdating(false);
      setUserChoice('pending');
    }
  };

  const handleUpdateLater = () => {
    setShowNotification(false);
    setUserChoice('later');
    
    // Don't show again for this session
    sessionStorage.setItem('pwa-update-dismissed', 'true');
    
    // Show again after 1 hour
    setTimeout(() => {
      if (updateAvailable) {
        setUserChoice('pending');
        sessionStorage.removeItem('pwa-update-dismissed');
      }
    }, 60 * 60 * 1000); // 1 hour
  };

  const handleDismiss = () => {
    setShowNotification(false);
    setUserChoice('later');
    sessionStorage.setItem('pwa-update-dismissed', 'true');
  };

  // Show updating state immediately regardless of notification state
  if (isUpdating) {
    return (
      <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-primary/20 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <div>
                <h3 className="font-semibold">Update wird installiert...</h3>
                <p className="text-sm text-muted-foreground">
                  Bitte schließen Sie die App nicht.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Don't show if no update available or user dismissed
  if (!updateAvailable || 
      !showNotification || 
      sessionStorage.getItem('pwa-update-dismissed') === 'true') {
    return null;
  }

  // Success state
  if (updateSuccess) {
    return (
      <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-green-500/20 bg-green-50 dark:bg-green-950 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-800 dark:text-green-200">
                  Update erfolgreich!
                </h3>
                <p className="text-sm text-green-600 dark:text-green-300">
                  Die App wird neu geladen...
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (updateError) {
    return (
      <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-red-500/20 bg-red-50 dark:bg-red-950 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <CardTitle className="text-lg text-red-800 dark:text-red-200">
                  Update fehlgeschlagen
                </CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-red-600 dark:text-red-300">
              {updateError}
            </p>
            <div className="flex gap-2">
              <Button onClick={handleUpdateNow} variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Erneut versuchen
              </Button>
              <Button onClick={handleDismiss} variant="ghost">
                Schließen
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Updating state
  if (isUpdating) {
    return (
      <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
        <Card className="border-2 border-primary/20 shadow-lg">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <div>
                <h3 className="font-semibold">Update wird installiert...</h3>
                <p className="text-sm text-muted-foreground">
                  Bitte schließen Sie die App nicht.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Main update notification
  return (
    <div className="fixed top-4 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96">
      <Card className="border-2 border-blue-500/20 bg-blue-50 dark:bg-blue-950 shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Download className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg text-blue-800 dark:text-blue-200">
                Update verfügbar
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                Neu
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription className="text-blue-600 dark:text-blue-300">
            Eine neue Version von KI Projekt-Planer ist verfügbar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Was ist neu:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Verbesserte Performance und Stabilität</li>
              <li>• Neue Features und Bugfixes</li>
              <li>• Optimierte Offline-Funktionalität</li>
            </ul>
          </div>
          
          <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              💡 Das Update wird im Hintergrund heruntergeladen und installiert. 
              Ihre Daten bleiben dabei erhalten.
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleUpdateNow} className="flex-1">
              <RefreshCw className="h-4 w-4 mr-2" />
              Jetzt aktualisieren
            </Button>
            <Button variant="outline" onClick={handleUpdateLater}>
              Später
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}