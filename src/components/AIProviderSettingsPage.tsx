"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  Settings, 
  Info, 
  Plus,
  ArrowLeft
} from 'lucide-react';
import { AIProviderSettings } from '@/components/AIProviderSettings';
import { unifiedAIClient } from '@/lib/ai';

interface AIProviderSettingsPageProps {
  onBack?: () => void;
}

export function AIProviderSettingsPage({ onBack }: AIProviderSettingsPageProps) {
  const [activeTab, setActiveTab] = useState('providers');

  const providerInfo = unifiedAIClient.getActiveProviderInfo();
  const isAnyProviderConfigured = unifiedAIClient.isAnyProviderConfigured();

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        {onBack && (
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Zurück
          </Button>
        )}
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Bot className="h-6 w-6" />
            AI-Provider Einstellungen
          </h1>
          <p className="text-muted-foreground">
            Konfigurieren Sie AI-Provider für Aufgabengenerierung und Inhaltserstellung
          </p>
        </div>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Aktueller Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="font-medium">
                {isAnyProviderConfigured ? 'AI-Funktionen verfügbar' : 'Kein AI-Provider konfiguriert'}
              </p>
              {providerInfo ? (
                <p className="text-sm text-muted-foreground">
                  Aktiver Provider: {providerInfo.name}
                </p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Konfigurieren Sie einen AI-Provider, um AI-Funktionen zu nutzen
                </p>
              )}
            </div>
            <Badge variant={isAnyProviderConfigured ? "default" : "secondary"}>
              {isAnyProviderConfigured ? "Aktiv" : "Inaktiv"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="providers" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            Provider
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Erweitert
          </TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <AIProviderSettings />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Erweiterte Einstellungen</CardTitle>
              <CardDescription>
                Zusätzliche Konfigurationsoptionen für AI-Provider
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Provider-Verwaltung</h4>
                <p className="text-sm text-muted-foreground">
                  Hier können Sie in Zukunft zusätzliche AI-Provider hinzufügen und verwalten.
                </p>
                <Button variant="outline" disabled>
                  <Plus className="h-4 w-4 mr-2" />
                  Neuen Provider hinzufügen
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Globale Einstellungen</h4>
                <p className="text-sm text-muted-foreground">
                  Einstellungen, die für alle AI-Provider gelten.
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Standard-Timeout</label>
                    <p className="text-xs text-muted-foreground">30 Sekunden</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Max. Wiederholungen</label>
                    <p className="text-xs text-muted-foreground">3 Versuche</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Datenschutz</h4>
                <p className="text-sm text-muted-foreground">
                  Alle AI-Anfragen werden direkt an den gewählten Provider gesendet. 
                  Keine Daten werden auf unseren Servern gespeichert.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Hilfe & Dokumentation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Unterstützte Provider</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• <strong>Google Gemini:</strong> Kostenloser API-Zugang verfügbar</li>
              <li>• <strong>Weitere Provider:</strong> Werden in zukünftigen Updates hinzugefügt</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">API-Schlüssel erhalten</h4>
            <p className="text-sm text-muted-foreground">
              Für Gemini: Besuchen Sie{' '}
              <a 
                href="https://makersuite.google.com/app/apikey" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Google AI Studio
              </a>
              {' '}um einen kostenlosen API-Schlüssel zu erhalten.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Fehlerbehebung</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Überprüfen Sie Ihren API-Schlüssel auf Tippfehler</li>
              <li>• Stellen Sie sicher, dass Ihr API-Schlüssel aktiv ist</li>
              <li>• Prüfen Sie Ihre Internetverbindung</li>
              <li>• Kontaktieren Sie den Support bei anhaltenden Problemen</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}