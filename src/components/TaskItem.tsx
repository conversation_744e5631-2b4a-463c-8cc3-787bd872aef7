
"use client";

import { useState, useEffect, useRef, useCallback, memo, useMemo } from 'react';
import type { Task, InsertionPosition, InsertionZone, InsertionState } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Loader2, ChevronDown, Sparkles, Trash2, Edit, Save, Plus, CornerDownRight, ListPlus, SplitSquareVertical, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAutoGrowTextarea } from '@/hooks/useAutoGrowTextarea';

import { useIsMobile } from '@/hooks/use-mobile';
import { TaskList } from './TaskList';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { AIContentWithLoading } from '@/components/ui/ai-content';

import { unifiedAIClient } from '@/lib/ai';
import { useAIButtonState } from '@/hooks/useAIButtonState';
import { cn } from '@/lib/utils';


import type { LoadingStates } from '@/lib/types';
import { LoadingIndicator, TaskLoadingOverlay } from '@/components/ui/loading-indicator';
// Icons als einfache SVG-Komponenten
const ChevronDownIcon = () => (
  <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

const ChevronUpIcon = () => (
  <svg className="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
  </svg>
);

const InformationCircleIcon = () => (
  <svg className="h-4 w-4 text-blue-500 hover:text-blue-600 cursor-help" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
  </svg>
);

interface TaskItemProps {
    task: Task;
    level: number;
    parentId: string | null;
    onUpdateTask: (task: Task) => void;
    onOpenSolveModal: (task: Task, isRefinement?: boolean, wrapperId?: string) => void;
    onAddTask: (parentId?: string | null, title?: string, description?: string) => void;
    onAddTaskAfter: (afterId: string, parentId: string | null, title?: string, description?: string) => void;
    onDeleteTask: (taskId: string) => void;
    onBreakdownTask?: (taskId: string) => void;
    numbering: string;
    loading?: LoadingStates;
    setLoading?: (loading: LoadingStates | ((prev: LoadingStates) => LoadingStates)) => void;
    isVirtualized?: boolean;
    // New props for insertion functionality
    onInsertTask?: (position: InsertionPosition) => void;
    showInsertionIndicators?: boolean;
    insertionMode?: 'hover' | 'always' | 'keyboard';
    insertionState?: InsertionState;
    onInsertionStateChange?: (updates: Partial<InsertionState>) => void;
    insertionLoading?: { [positionId: string]: boolean };
}

export const TaskItem = memo(function TaskItem({ 
    task, 
    level, 
    parentId, 
    onUpdateTask, 
    onOpenSolveModal,
    onAddTask,
    onAddTaskAfter,
    onDeleteTask,
    onBreakdownTask,
    numbering,
    loading = {},
    setLoading = () => {},
    isVirtualized = false,
    onInsertTask,
    showInsertionIndicators: showInsertionIndicatorsProp = true,
    insertionMode = 'hover',
    insertionState,
    onInsertionStateChange,
    insertionLoading = {}
}: TaskItemProps) {
    const { toast } = useToast();
    
    // AI button states for offline awareness
    const breakdownButtonState = useAIButtonState({ 
        operation: 'breakdown', 
        isLoading: loading?.[task.id] === 'breakdown' 
    });
    const solveButtonState = useAIButtonState({ 
        operation: 'solve', 
        isLoading: loading?.[task.id] === 'solve' 
    });
    
    // Editing states - using task interface properties if available
    const [isEditingTitle, setIsEditingTitle] = useState(task.isEditing || false);
    const [isEditingDescription, setIsEditingDescription] = useState(task.isDescriptionEditing || false);
    const [isAiContentEditing, setIsAiContentEditing] = useState(task.isAiContentEditing || false);

    const [tempTitle, setTempTitle] = useState(task.title);
    const [tempDescription, setTempDescription] = useState(task.description);
    const [tempAiContent, setTempAiContent] = useState(task.content);
    
    const [isAiContentVisible, setIsAiContentVisible] = useState(true);
    const [retryCount, setRetryCount] = useState(0);
    const maxRetries = 2;

    // Auto-growing textarea hooks
    const titleTextarea = useAutoGrowTextarea(tempTitle);
    const descriptionTextarea = useAutoGrowTextarea(tempDescription);
    const aiContentTextarea = useAutoGrowTextarea(tempAiContent);

    const aiContentRef = useRef<HTMLDivElement>(null);
    const aiContentContainerRef = useRef<HTMLDivElement>(null);
    const [aiContentMaxHeight, setAiContentMaxHeight] = useState<number>(0);
    const [popoverOpen, setPopoverOpen] = useState(false);
    const [selectionInfo, setSelectionInfo] = useState<{ range: Range | null, text: string }>({ range: null, text: '' });
    const [elaborationLoading, setElaborationLoading] = useState<string | null>(null); // wrapperId of element being elaborated

    // Task element ref for focus management
    const taskElementRef = useRef<HTMLDivElement>(null);
    
    // Mobile detection for touch-friendly behavior
    const isMobile = useIsMobile();

    // Get current loading state for this task - memoized for performance
    const currentLoading = useMemo(() => loading[task.id] || false, [loading, task.id]);
    const isLoading = useMemo(() => currentLoading !== false, [currentLoading]);
    
    // Abgeleitete Metriken für den KI-Content (Zeichen, Wörter, Tokens ~approx)
    const plainTextContent = useMemo(() => {
        if (!task.content) return '';
        return task.content
            .replace(/<[^>]*>/g, ' ') // HTML-Tags entfernen
            .replace(/\s+/g, ' ') // Whitespace normalisieren
            .trim();
    }, [task.content]);

    const characterCount = useMemo(() => plainTextContent.length, [plainTextContent]);
    const wordCount = useMemo(() => (plainTextContent ? plainTextContent.split(/\s+/).filter(Boolean).length : 0), [plainTextContent]);
    const tokenCountApprox = useMemo(() => Math.max(0, Math.ceil(characterCount / 4)), [characterCount]); // grobe Heuristik

    // ID für ARIA-Verknüpfung
    const aiPanelId = useMemo(() => `ai-content-panel-${task.id}`,[task.id]);



    // Insertion zones are now handled by TaskList

    const handleBreakdown = useCallback(async (isRetry: boolean = false) => {
        // Check if we can use AI before proceeding
        if (!breakdownButtonState.canUseAI) {
            toast({
                variant: 'destructive',
                title: 'Offline-Modus',
                description: breakdownButtonState.getOfflineMessage()
            });
            return;
        }

        if (!isRetry) {
            setRetryCount(0);
        }
        
        setLoading(prev => ({ ...prev, [task.id]: 'breakdown' }));
        
        try {
            // Use the real Gemini API to generate subtasks
            const result = await unifiedAIClient.generateSubtasks(
                task.title, 
                task.description || 'Keine Beschreibung verfügbar',
                task.content // Include existing AI content as context
            );
            
            if (result.error && !result.tasks?.length) {
                // Complete failure - offer retry if we haven't exceeded max retries
                if (retryCount < maxRetries) {
                    setRetryCount(prev => prev + 1);
                    toast({ 
                        variant: 'destructive', 
                        title: "KI-Zerlegung fehlgeschlagen", 
                        description: `${result.error} Versuche automatisch erneut... (${retryCount + 1}/${maxRetries})` 
                    });
                    
                    // Auto-retry after delay
                    setTimeout(() => handleBreakdown(true), 2000);
                    return;
                }
                
                toast({ 
                    variant: 'destructive', 
                    title: "KI-Zerlegung fehlgeschlagen", 
                    description: result.error 
                });
                setLoading(prev => ({ ...prev, [task.id]: false }));
                return;
            }
            
            if (result.error && result.tasks?.length) {
                // Partial success - show warning but proceed
                toast({ 
                    variant: 'destructive', 
                    title: "KI-Zerlegung teilweise fehlgeschlagen", 
                    description: `${result.error} Fallback-Aufgaben wurden erstellt.` 
                });
            }
            
            if (result.tasks && result.tasks.length > 0) {
                // Add the new subtasks to the existing ones
                const updatedTask = { 
                    ...task, 
                    subtasks: [...(task.subtasks || []), ...result.tasks] 
                };
                onUpdateTask(updatedTask);
                
                // Reset retry count on success
                setRetryCount(0);
                
                toast({ 
                    title: "Aufgabe erfolgreich zerlegt", 
                    description: `${result.tasks.length} Unteraufgaben erstellt` 
                });
            } else {
                toast({ 
                    variant: 'destructive', 
                    title: "Keine Unteraufgaben generiert", 
                    description: "Die KI konnte keine sinnvollen Unteraufgaben erstellen." 
                });
            }
        } catch (error) {
            console.error('Breakdown error:', error);
            
            // Offer retry on unexpected errors
            if (retryCount < maxRetries) {
                setRetryCount(prev => prev + 1);
                toast({ 
                    variant: 'destructive', 
                    title: "Unerwarteter Fehler", 
                    description: "Versuche automatisch erneut...",
                });
                
                // Auto-retry after delay
                setTimeout(() => handleBreakdown(true), 2000);
                return;
            }
            
            toast({ 
                variant: 'destructive', 
                title: "Fehler bei der Zerlegung", 
                description: error instanceof Error ? error.message : "Unbekannter Fehler aufgetreten" 
            });
        } finally {
            setLoading(prev => ({ ...prev, [task.id]: false }));
        }
    }, [task.id, task.title, task.description, task.content, retryCount, maxRetries, setLoading, onUpdateTask, toast, breakdownButtonState]);

    const handleTitleBlur = useCallback(() => {
        const updatedTask = { 
            ...task, 
            title: tempTitle,
            isEditing: false
        };
        onUpdateTask(updatedTask);
        setIsEditingTitle(false);
    }, [task, tempTitle, onUpdateTask]);

    const handleTitleKeyDown = useCallback((e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleTitleBlur();
        } else if (e.key === 'Escape') {
            setTempTitle(task.title);
            setIsEditingTitle(false);
            onUpdateTask({ ...task, isEditing: false });
        }
    }, [handleTitleBlur, task, setTempTitle, setIsEditingTitle, onUpdateTask]);

    const handleDescriptionBlur = useCallback(() => {
        const updatedTask = { 
            ...task, 
            description: tempDescription,
            isDescriptionEditing: false
        };
        onUpdateTask(updatedTask);
        setIsEditingDescription(false);
    }, [task, tempDescription, onUpdateTask]);

    const handleDescriptionKeyDown = useCallback((e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            handleDescriptionBlur();
        } else if (e.key === 'Escape') {
            setTempDescription(task.description);
            setIsEditingDescription(false);
            onUpdateTask({ ...task, isDescriptionEditing: false });
        }
    }, [handleDescriptionBlur, task, setTempDescription, setIsEditingDescription, onUpdateTask]);

    const handleAiContentSave = useCallback(() => {
        const updatedTask = { 
            ...task, 
            content: tempAiContent,
            isAiContentEditing: false
        };
        onUpdateTask(updatedTask);
        setIsAiContentEditing(false);
    }, [task, tempAiContent, onUpdateTask]);

    const handleSaveAndRework = useCallback(() => {
        // Save the current content first
        const updatedTask = { 
            ...task, 
            content: tempAiContent,
            isAiContentEditing: false
        };
        onUpdateTask(updatedTask);
        setIsAiContentEditing(false);
        
        // Then open the solve modal for refinement
        onOpenSolveModal(updatedTask, true);
    }, [task, tempAiContent, onUpdateTask, setIsAiContentEditing, onOpenSolveModal]);

    const handleAiContentKeyDown = useCallback((e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            setTempAiContent(task.content);
            setIsAiContentEditing(false);
            onUpdateTask({ ...task, isAiContentEditing: false });
        }
    }, [task, setTempAiContent, setIsAiContentEditing, onUpdateTask]);

    // Sync editing states with task properties
    useEffect(() => {
        setIsEditingTitle(task.isEditing || false);
        setIsEditingDescription(task.isDescriptionEditing || false);
        setIsAiContentEditing(task.isAiContentEditing || false);
    }, [task.isEditing, task.isDescriptionEditing, task.isAiContentEditing]);

    // Sync temp values when task changes
    useEffect(() => {
        setTempTitle(task.title);
        setTempDescription(task.description);
        setTempAiContent(task.content);
    }, [task.title, task.description, task.content]);

    // Auto-expand NUR wenn neuer Inhalt kommt oder Loading beginnt –
    // aber nicht, wenn der Nutzer manuell einklappt
    const previousContentRef = useRef<string | undefined>(task.content);
    useEffect(() => {
        // Inhalt hat sich geändert (z. B. nach KI-Generierung) -> automatisch öffnen
        if (task.content && task.content !== previousContentRef.current) {
            setIsAiContentVisible(true);
        }
        previousContentRef.current = task.content;
    }, [task.content]);

    useEffect(() => {
        // Während Ladevorgängen Panel sichtbar halten
        if (isLoading) {
            setIsAiContentVisible(true);
        }
    }, [isLoading]);

    // Persist visibility state in localStorage for better UX
    useEffect(() => {
        const savedVisibility = localStorage.getItem(`ai-content-visible-${task.id}`);
        if (savedVisibility !== null && savedVisibility !== 'undefined' && savedVisibility !== 'null') {
            try {
                const parsed = JSON.parse(savedVisibility);
                if (typeof parsed === 'boolean') {
                    setIsAiContentVisible(parsed);
                }
            } catch (error) {
                // Silently handle parsing errors and use default
                setIsAiContentVisible(true);
            }
        }
    }, [task.id]);

    const toggleAiContentVisibility = useCallback(() => {
        const newVisibility = !isAiContentVisible;
        setIsAiContentVisible(newVisibility);
        localStorage.setItem(`ai-content-visible-${task.id}`, JSON.stringify(newVisibility));
    }, [isAiContentVisible, task.id]);

    // Dynamische Höhe für den ausklappbaren Bereich, um Abschneiden zu vermeiden
    useEffect(() => {
        const updateMaxHeight = () => {
            const node = aiContentContainerRef.current;
            if (!node) return;
            const paddingBuffer = 32;
            setAiContentMaxHeight(node.scrollHeight + paddingBuffer);
        };

        const raf = requestAnimationFrame(updateMaxHeight);

        let resizeObserver: ResizeObserver | null = null;
        if (typeof ResizeObserver !== 'undefined' && aiContentContainerRef.current) {
            resizeObserver = new ResizeObserver(() => {
                updateMaxHeight();
            });
            resizeObserver.observe(aiContentContainerRef.current);
        }

        window.addEventListener('resize', updateMaxHeight);

        return () => {
            cancelAnimationFrame(raf);
            window.removeEventListener('resize', updateMaxHeight);
            if (resizeObserver && aiContentContainerRef.current) {
                try { resizeObserver.unobserve(aiContentContainerRef.current); } catch {}
                resizeObserver.disconnect();
            }
        };
    }, [task.content, isAiContentEditing, isAiContentVisible, characterCount, wordCount]);

    // Helper functions to start editing
    const startTitleEditing = useCallback(() => {
        setTempTitle(task.title);
        setIsEditingTitle(true);
        onUpdateTask({ ...task, isEditing: true });
    }, [task, onUpdateTask]);

    const startDescriptionEditing = useCallback(() => {
        setTempDescription(task.description);
        setIsEditingDescription(true);
        onUpdateTask({ ...task, isDescriptionEditing: true });
    }, [task, onUpdateTask]);

    const startAiContentEditing = useCallback(() => {
        setTempAiContent(task.content);
        setIsAiContentEditing(true);
        onUpdateTask({ ...task, isAiContentEditing: true });
    }, [task, onUpdateTask]);

    const handleSelection = useCallback(() => {
        const selection = window.getSelection();
        if (!selection || selection.isCollapsed) {
            setPopoverOpen(false);
            return;
        }

        // Check if selection is within the AI content area
        const aiContentElement = aiContentRef.current;
        if (!aiContentElement) {
            setPopoverOpen(false);
            return;
        }

        // Verify that the selection is within our AI content
        const range = selection.getRangeAt(0);
        const commonAncestor = range.commonAncestorContainer;
        
        // Check if the selection is within the AI content div
        const isWithinAiContent = aiContentElement.contains(commonAncestor) || 
                                 aiContentElement === commonAncestor;
        
        if (!isWithinAiContent) {
            setPopoverOpen(false);
            return;
        }

        const selectedText = selection.toString().trim();
        if (selectedText.length > 0 && selectedText.length <= 500) { // Limit selection length
            setSelectionInfo({ range: range.cloneRange(), text: selectedText });
            setPopoverOpen(true);
        } else {
            setPopoverOpen(false);
            if (selectedText.length > 500) {
                toast({ 
                    variant: 'destructive', 
                    title: "Auswahl zu lang", 
                    description: "Bitte wählen Sie maximal 500 Zeichen aus." 
                });
            }
        }
    }, [toast]);

    const handleCreateSubtaskFromSelection = useCallback(() => {
        onAddTask(task.id, selectionInfo.text);
        setPopoverOpen(false);
    }, [task.id, selectionInfo.text, onAddTask]);

    const handleCreateTaskAfterFromSelection = useCallback(() => {
        onAddTaskAfter(task.id, parentId, selectionInfo.text);
        setPopoverOpen(false);
    }, [task.id, parentId, selectionInfo.text, onAddTaskAfter]);

    const handleElaborateSelection = useCallback(async () => {
        const { range, text } = selectionInfo;
        if (!range || !text.trim()) return;

        const wrapperId = `wrapper-${crypto.randomUUID()}`;
        const wrapper = document.createElement('span');
        wrapper.id = wrapperId;
        wrapper.className = 'elaboration-wrapper';
        wrapper.style.backgroundColor = 'rgba(99, 102, 241, 0.1)';
        wrapper.style.borderRadius = '3px';
        wrapper.style.padding = '2px 4px';
        wrapper.style.position = 'relative';

        try {
            // Create loading indicator
            const loadingSpinner = document.createElement('span');
            loadingSpinner.className = 'elaboration-loading';
            loadingSpinner.innerHTML = '⟳';
            loadingSpinner.style.animation = 'spin 1s linear infinite';
            loadingSpinner.style.marginLeft = '4px';
            loadingSpinner.style.color = '#6366f1';

            // Extract and wrap the selected content
            const selectionContents = range.extractContents();
            wrapper.appendChild(selectionContents);
            wrapper.appendChild(loadingSpinner);
            range.insertNode(wrapper);

            // Update task content and set loading state
            const updatedContent = aiContentRef.current?.innerHTML || '';
            onUpdateTask({ ...task, content: updatedContent });
            setElaborationLoading(wrapperId);
            setLoading(prev => ({ ...prev, [task.id]: 'elaborate' }));
            setPopoverOpen(false);

            // Trigger AI elaboration through the solve modal
            onOpenSolveModal(task, true, wrapperId);

        } catch (error) {
            console.error("Fehler bei der Textauswahl-Verarbeitung:", error);
            setLoading(prev => ({ ...prev, [task.id]: false }));
            toast({ 
                variant: 'destructive', 
                title: "Fehler", 
                description: "Die Textauswahl konnte nicht verarbeitet werden." 
            });
        }
    }, [selectionInfo, task, onUpdateTask, setElaborationLoading, setLoading, onOpenSolveModal, toast]);

    const handleDeleteSelection = useCallback(() => {
        const { range } = selectionInfo;
        if (!range) return;

        try {
            range.deleteContents();
            const updatedContent = aiContentRef.current?.innerHTML || '';
            onUpdateTask({ ...task, content: updatedContent });
            setPopoverOpen(false);
        } catch (error) {
            console.error("Fehler beim Löschen der Auswahl:", error);
            toast({ 
                variant: 'destructive', 
                title: "Fehler", 
                description: "Die Auswahl konnte nicht gelöscht werden." 
            });
        }
    }, [selectionInfo, task, onUpdateTask, toast]);

    // Function to clean up elaboration loading state when AI operation completes
    const cleanupElaborationLoading = useCallback((wrapperId: string) => {
        if (elaborationLoading === wrapperId) {
            setElaborationLoading(null);
        }
        
        // Remove loading spinner from the wrapper
        const wrapper = document.getElementById(wrapperId);
        if (wrapper) {
            const loadingSpinner = wrapper.querySelector('.elaboration-loading');
            if (loadingSpinner) {
                loadingSpinner.remove();
            }
            // Reset wrapper styling
            wrapper.style.backgroundColor = '';
            wrapper.style.borderRadius = '';
            wrapper.style.padding = '';
        }
    }, [elaborationLoading]);

    // Expose cleanup function for parent component to call when AI operation completes
    useEffect(() => {
        // This effect can be used by parent components to trigger cleanup
        // when they detect that an AI operation has completed
    }, [cleanupElaborationLoading]);

    // Mouse handlers for insertion are now handled by TaskList

    // Insertion handling is now managed by TaskList

    // Insertion zones are managed by TaskList

    return (
        <>
            <style>{`
                @keyframes spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
                @keyframes wave {
                    0%, 60%, 100% {
                        transform: initial;
                        opacity: 0.4;
                    }
                    30% {
                        transform: translateY(-4px);
                        opacity: 1;
                    }
                }
                .dot-wave-1 {
                    animation: wave 1.4s linear infinite;
                }
                .dot-wave-2 {
                    animation: wave 1.4s linear infinite;
                    animation-delay: 0.2s;
                }
                .dot-wave-3 {
                    animation: wave 1.4s linear infinite;
                    animation-delay: 0.4s;
                }
                .elaboration-wrapper {
                    transition: all 0.2s ease;
                }
                .elaboration-wrapper:hover {
                    background-color: rgba(99, 102, 241, 0.15) !important;
                }
            `}</style>
            <div 
                ref={taskElementRef}
                className="relative group"
                style={{ paddingLeft: `${Math.min(level * 4, 16)}px` }}
                data-task-id={task.id}
                data-testid={task.id}
            >
                {/* Insertion indicators are now handled by TaskList to avoid duplicates */}

            <Card 
                tabIndex={0}
                role="listitem"
                aria-label={`Task: ${task.title}. Level ${level + 1}. ${task.completed ? 'Completed. ' : ''}${task.subtasks?.length ? `Has ${task.subtasks.length} subtask${task.subtasks.length === 1 ? '' : 's'}. ` : ''}${task.description ? 'Has description. ' : ''}`}
                aria-describedby={`task-description-${task.id}`}
                aria-expanded={task.subtasks && task.subtasks.length > 0 ? true : undefined}
                aria-level={level + 1}
                className={`mb-1 relative border-0 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 transition-all ${
                level === 0 ? 'bg-slate-200/90 dark:bg-slate-800/95' :
                level === 1 ? 'bg-slate-100/90 dark:bg-slate-700/95' :
                level === 2 ? 'bg-blue-50/70 dark:bg-slate-500/95' :
                level === 3 ? 'bg-green-50/80 dark:bg-slate-600/90' :
                level === 4 ? 'bg-yellow-50/80 dark:bg-slate-600/85' :
                level === 5 ? 'bg-pink-50/80 dark:bg-slate-600/80' :
                level === 6 ? 'bg-purple-50/80 dark:bg-slate-600/75' :
                'bg-orange-50/80 dark:bg-slate-600/70'
            }`}>
                
                {/* Enhanced hidden description for screen readers */}
                <div id={`task-description-${task.id}`} className="sr-only">
                  Task at level {level + 1} with numbering {numbering}. 
                  {task.completed && 'This task is completed. '}
                  {task.description && `Description: ${task.description}. `}
                  {task.content && 'Has AI-generated content. '}
                  {task.subtasks?.length ? `Contains ${task.subtasks.length} subtask${task.subtasks.length === 1 ? '' : 's'}. ` : ''}
                  Available actions: Edit task, generate AI content, add subtask, delete task. 
                  Use keyboard shortcuts to insert new tasks: Enter for after, Shift+Enter for before, Tab+Enter for subtask.
                  {showInsertionIndicatorsProp && ' Insertion indicators are available when hovering or using keyboard navigation.'}
                </div>

                <CardHeader className="bg-transparent border-0">
                    <div className="flex items-start justify-between">
                        <div className="flex-grow">
                            {isEditingTitle ? (
                                <Textarea
                                    ref={titleTextarea.textareaRef}
                                    value={tempTitle}
                                    onChange={(e) => setTempTitle(e.target.value)}
                                    onBlur={handleTitleBlur}
                                    onKeyDown={handleTitleKeyDown}
                                    autoFocus
                                    className="text-lg font-semibold resize-none overflow-hidden min-h-[2.5rem] border-none shadow-none p-0 focus-visible:ring-0 bg-transparent"
                                    placeholder="Aufgabentitel eingeben..."
                                />
                            ) : (
                                <h3 onClick={startTitleEditing} className="text-lg font-semibold cursor-pointer w-full hover:bg-white/20 dark:hover:bg-black/20 rounded p-1 -m-1 transition-colors text-slate-800 dark:text-slate-100">
                                    <span className="text-slate-600 dark:text-slate-300 mr-2 opacity-80">{numbering}</span>
                                    {task.title}
                                </h3>
                            )}
                        </div>
                        <div className="flex items-center space-x-2 flex-shrink-0 ml-4">
                            {currentLoading === 'breakdown' ? (
                                <div className="flex items-center justify-center p-2">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full dot-wave-1"></div>
                                        <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full dot-wave-2"></div>
                                        <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full dot-wave-3"></div>
                                    </div>
                                </div>
                            ) : currentLoading === 'solve' ? (
                                <div className="flex items-center justify-center p-2">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full dot-wave-1"></div>
                                        <div className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full dot-wave-2"></div>
                                        <div className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full dot-wave-3"></div>
                                    </div>
                                </div>
                            ) : currentLoading === 'elaborate' ? (
                                <div className="flex items-center justify-center p-2">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full dot-wave-1"></div>
                                        <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full dot-wave-2"></div>
                                        <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full dot-wave-3"></div>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <button 
                                        onClick={breakdownButtonState.canUseAI ? (onBreakdownTask ? () => onBreakdownTask(task.id) : () => handleBreakdown()) : () => {
                                            toast({
                                                variant: 'destructive',
                                                title: 'Offline-Modus',
                                                description: breakdownButtonState.getOfflineMessage()
                                            });
                                        }} 
                                        title={breakdownButtonState.buttonState.tooltip}
                                        disabled={!breakdownButtonState.buttonState.enabled}
                                        className={cn(
                                            "p-2 transition-colors disabled:opacity-50",
                                            breakdownButtonState.buttonState.variant === 'offline' 
                                                ? "text-slate-400 dark:text-slate-500 cursor-not-allowed"
                                                : "text-slate-600 dark:text-slate-300 hover:text-purple-600 dark:hover:text-purple-400"
                                        )}
                                    >
                                        <SplitSquareVertical size={18} />
                                    </button>
                                    <button 
                                        onClick={solveButtonState.canUseAI ? () => onOpenSolveModal(task) : () => {
                                            toast({
                                                variant: 'destructive',
                                                title: 'Offline-Modus',
                                                description: solveButtonState.getOfflineMessage()
                                            });
                                        }} 
                                        title={solveButtonState.buttonState.tooltip}
                                        disabled={!solveButtonState.buttonState.enabled}
                                        className={cn(
                                            "p-2 transition-colors disabled:opacity-50",
                                            solveButtonState.buttonState.variant === 'offline' 
                                                ? "text-slate-400 dark:text-slate-500 cursor-not-allowed"
                                                : "text-slate-600 dark:text-slate-300 hover:text-orange-600 dark:hover:text-orange-400"
                                        )}
                                    >
                                        <Sparkles size={18} />
                                    </button>
                                    <button 
                                        onClick={() => onDeleteTask(task.id)} 
                                        title="Löschen"
                                        disabled={isLoading}
                                        className="p-2 text-slate-600 dark:text-slate-300 hover:text-red-600 dark:hover:text-red-400 transition-colors disabled:opacity-50"
                                    >
                                        <Trash2 size={18} />
                                    </button>
                                </>
                            )}
                        </div>
                    </div>
                    <div className="mt-1 pl-8">
                        {isEditingDescription ? (
                            <Textarea
                                ref={descriptionTextarea.textareaRef}
                                value={tempDescription}
                                onChange={(e) => setTempDescription(e.target.value)}
                                onBlur={handleDescriptionBlur}
                                onKeyDown={handleDescriptionKeyDown}
                                autoFocus
                                className="text-sm resize-none overflow-hidden min-h-[2.5rem] border-0 bg-white/50 dark:bg-black/20 rounded"
                                placeholder="Beschreibung hinzufügen..."
                            />
                        ) : (
                            <p onClick={startDescriptionEditing} className="text-sm text-slate-600 dark:text-slate-300 cursor-pointer italic hover:bg-white/30 dark:hover:bg-black/20 rounded p-1 -m-1 transition-colors">
                                {task.description || "Beschreibung hinzufügen..."}
                            </p>
                        )}
                    </div>
                </CardHeader>
                {task.content && (
                    <CardContent>
                        <div className="mt-4 bg-slate-900 rounded-lg border border-slate-700 shadow-inner transition-all duration-200 ease-in-out hover:shadow-md">
                            <div 
                                className="flex justify-between items-center p-3 cursor-pointer bg-slate-800 rounded-t-lg hover:bg-slate-700 transition-all duration-200 ease-in-out select-none" 
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    toggleAiContentVisibility();
                                }}
                                role="button"
                                aria-expanded={isAiContentVisible}
                                aria-controls={aiPanelId}
                                tabIndex={0}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        toggleAiContentVisibility();
                                    }
                                }}
                            >
                                <h4 className="font-semibold text-sm text-blue-400 flex items-center gap-2 select-none">
                                    <Sparkles size={16} className={isLoading ? 'animate-pulse' : ''} /> 
                                    KI-Ausarbeitung
                                </h4>
                                <div className="flex items-center gap-2">
                                    {task.content && !isLoading && (
                                        <>
                                            <span className="text-xs text-slate-600 dark:text-slate-300 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded select-none">
                                                {characterCount} Zeichen
                                            </span>
                                            <span className="text-xs text-slate-600 dark:text-slate-300 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded select-none">
                                                {wordCount} Wörter
                                            </span>
                                            <span className="text-xs text-slate-600 dark:text-slate-300 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded select-none" title="ungefähre Tokenanzahl">
                                                ~{tokenCountApprox} Tokens
                                            </span>
                                            {typeof task.aiMetrics?.lastSolveMs === 'number' || typeof task.aiMetrics?.lastElaborateMs === 'number' ? (
                                                <span className="text-xs text-slate-600 dark:text-slate-300 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded select-none" title="Dauer der letzten KI-Antwort">
                                                    {Math.round(((task.aiMetrics?.lastElaborateMs ?? task.aiMetrics?.lastSolveMs) || 0) / 100) / 10}s
                                                </span>
                                            ) : null}
                                            {typeof task.aiMetrics?.lastBreakdownMs === 'number' ? (
                                                <span className="text-xs text-slate-600 dark:text-slate-300 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded select-none" title="Dauer der letzten Zerlegung">
                                                    Zerlegung: {Math.round((task.aiMetrics.lastBreakdownMs || 0) / 100) / 10}s
                                                </span>
                                            ) : null}
                                        </>
                                    )}
                                    <ChevronDown 
                                        size={20} 
                                        className={`text-slate-500 transform transition-all duration-300 ease-in-out ${
                                            isAiContentVisible ? 'rotate-180' : ''
                                        }`} 
                                    />
                                    
                                    {/* Debug Icon für AI-Metriken */}
                                    {task.aiMetrics && (
                                        <div className="relative group">
                                            <InformationCircleIcon />
                                            <div className="absolute bottom-full right-0 mb-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-3 text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none group-hover:pointer-events-auto z-50">
                                                <div className="font-semibold mb-2 text-gray-900 dark:text-gray-100">AI-Diagnose:</div>
                                                <div className="space-y-1 text-gray-700 dark:text-gray-300">
                                                    <div><span className="font-medium">Finish Reason:</span> {task.aiMetrics.lastFinishReason || 'N/A'}</div>
                                                    <div><span className="font-medium">Prompt Tokens:</span> {task.aiMetrics.promptTokens || 'N/A'}</div>
                                                    <div><span className="font-medium">Candidate Tokens:</span> {task.aiMetrics.candidateTokens || 'N/A'}</div>
                                                    <div><span className="font-medium">Total Tokens:</span> {task.aiMetrics.totalTokens || 'N/A'}</div>
                                                    <div><span className="font-medium">Content Chars:</span> {task.aiMetrics.lastContentChars || 'N/A'}</div>
                                                    <div><span className="font-medium">Likely Truncated:</span> {task.aiMetrics.likelyTruncated ? 'Ja' : 'Nein'}</div>
                                                    <div><span className="font-medium">Solve Time:</span> {task.aiMetrics.lastSolveMs ? `${(task.aiMetrics.lastSolveMs / 1000).toFixed(1)}s` : 'N/A'}</div>
                                                    <div><span className="font-medium">Elaborate Time:</span> {task.aiMetrics.lastElaborateMs ? `${(task.aiMetrics.lastElaborateMs / 1000).toFixed(1)}s` : 'N/A'}</div>
                                                    <div><span className="font-medium">Breakdown Time:</span> {task.aiMetrics.lastBreakdownMs ? `${(task.aiMetrics.lastBreakdownMs / 1000).toFixed(1)}s` : 'N/A'}</div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div 
                                id={aiPanelId}
                                className={`overflow-hidden transition-all duration-300 ease-in-out`}
                                style={{
                                    maxHeight: isAiContentVisible ? aiContentMaxHeight : 0,
                                    opacity: isAiContentVisible ? 1 : 0
                                }}
                            >
                                <div ref={aiContentContainerRef} className="p-4 border-t border-slate-700">
                                    {isAiContentEditing ? (
                                        <Textarea
                                            ref={aiContentTextarea.textareaRef}
                                            value={tempAiContent}
                                            onChange={(e) => setTempAiContent(e.target.value)}
                                            onKeyDown={handleAiContentKeyDown}
                                            className="w-full min-h-[12rem] resize-none overflow-hidden border-slate-600 bg-slate-700 text-white"
                                            placeholder="KI-Inhalt bearbeiten..."
                                        />
                                    ) : (
                                        <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                                            <PopoverTrigger asChild>
                                                <div
                                                    ref={aiContentRef}
                                                    onMouseUp={handleSelection}
                                                    onTouchEnd={handleSelection}
                                                    className="ai-content-display cursor-text"
                                                >
                                                    <AIContentWithLoading
                                                        content={task.content}
                                                        isLoading={loading[task.id] === 'elaborate' && !task.content}
                                                        loadingText="KI erstellt Inhalt..."
                                                        onSelectionChange={(selectedText, range) => {
                                                            // Handle selection for context menu
                                                        }}
                                                    />
                                                </div>
                                            </PopoverTrigger>
                                            <PopoverContent 
                                                className="w-64 p-2 bg-slate-800 border-slate-700 shadow-2xl"
                                                side="bottom"
                                                align="start"
                                            >
                                                <div className="w-full text-white">
                                                    <div className="mb-2 p-2 bg-slate-700 rounded text-xs">
                                                        <span className="font-medium">Ausgewählt:</span> "{selectionInfo.text.length > 50 ? selectionInfo.text.substring(0, 50) + '...' : selectionInfo.text}"
                                                    </div>
                                                    <Button 
                                                        onClick={handleCreateSubtaskFromSelection} 
                                                        variant="ghost" 
                                                        className="w-full justify-start gap-3 hover:bg-slate-700"
                                                    >
                                                        <CornerDownRight size={18} className="text-indigo-400" />
                                                        <div>
                                                            <p className="font-semibold text-sm">Unteraufgabe erstellen</p>
                                                            <p className="text-xs text-slate-400 text-left">Wandelt Text in Unterschritt um.</p>
                                                        </div>
                                                    </Button>
                                                    <Button 
                                                        onClick={handleCreateTaskAfterFromSelection} 
                                                        variant="ghost" 
                                                        className="w-full justify-start gap-3 hover:bg-slate-700"
                                                    >
                                                        <ListPlus size={18} className="text-indigo-400" />
                                                        <div>
                                                            <p className="font-semibold text-sm">Neue Aufgabe danach</p>
                                                            <p className="text-xs text-slate-400 text-left">Erstellt Aufgabe auf gleicher Ebene.</p>
                                                        </div>
                                                    </Button>
                                                    <Button 
                                                        onClick={handleElaborateSelection} 
                                                        variant="ghost" 
                                                        className="w-full justify-start gap-3 hover:bg-slate-700"
                                                        disabled={elaborationLoading !== null}
                                                    >
                                                        {elaborationLoading ? (
                                                            <Loader2 size={18} className="text-indigo-400 animate-spin" />
                                                        ) : (
                                                            <Sparkles size={18} className="text-indigo-400" />
                                                        )}
                                                        <div>
                                                            <p className="font-semibold text-sm">Ausarbeiten</p>
                                                            <p className="text-xs text-slate-400 text-left">
                                                                {elaborationLoading ? 'KI arbeitet...' : 'Lässt KI Text verfeinern.'}
                                                            </p>
                                                        </div>
                                                    </Button>
                                                    <Button 
                                                        onClick={handleDeleteSelection} 
                                                        variant="ghost" 
                                                        className="w-full justify-start gap-3 hover:bg-slate-700"
                                                    >
                                                        <Trash2 size={18} className="text-red-400" />
                                                        <div>
                                                            <p className="font-semibold text-sm text-red-400">Löschen</p>
                                                            <p className="text-xs text-slate-400 text-left">Entfernt den Text.</p>
                                                        </div>
                                                    </Button>
                                                </div>
                                            </PopoverContent>
                                        </Popover>
                                    )}
                                    <div className="flex justify-end mt-4 space-x-3">
                                        {isAiContentEditing ? (
                                            <>
                                                <Button variant="secondary" onClick={handleAiContentSave}><Save size={14} className="mr-2" />Speichern</Button>
                                                <Button onClick={handleSaveAndRework}><Sparkles size={14} className="mr-2" />Speichern & Überarbeiten</Button>
                                            </>
                                        ) : (
                                            <Button variant="link" onClick={startAiContentEditing}><Edit size={14} className="mr-2" />Bearbeiten</Button>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                )}
                <CardFooter className="flex-col items-start">
                    {!isVirtualized && task.subtasks && task.subtasks.length > 0 && (
                        <div className="mt-2 w-full">
                            <TaskList
                                tasks={task.subtasks}
                                level={level + 1}
                                parentId={task.id}
                                onUpdateTask={onUpdateTask}
                                onOpenSolveModal={onOpenSolveModal}
                                onAddTask={onAddTask}
                                onAddTaskAfter={onAddTaskAfter}
                                onDeleteTask={onDeleteTask}
                                numberingPrefix={`${numbering}.`}
                                loading={loading}
                                setLoading={setLoading}
                                onInsertTask={onInsertTask}
                                showInsertionIndicators={showInsertionIndicatorsProp}
                                insertionMode={insertionMode}
                                insertionState={insertionState}
                                onInsertionStateChange={onInsertionStateChange}
                                insertionLoading={insertionLoading}
                            />
                        </div>
                    )}
                    <Button variant="link" onClick={() => onAddTask(task.id)} className="mt-1"><Plus size={16} className="mr-2" />Unteraufgabe hinzufügen</Button>
                </CardFooter>
            </Card>
        </div>
        </>
    );
});
