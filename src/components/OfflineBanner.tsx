'use client';

import { useState } from 'react';
import { WifiOff, X, RefreshCw } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface OfflineBannerProps {
  className?: string;
}

export function OfflineBanner({ className }: OfflineBannerProps) {
  const { isOnline, isSlowConnection } = useNetworkStatus();
  const [isDismissed, setIsDismissed] = useState(false);

  // Show banner when offline or on very slow connection; allow dismiss
  if ((isOnline && !isSlowConnection) || isDismissed) {
    return null;
  }

  const handleRetry = () => {
    // Force a network check by trying to fetch a small resource
    fetch('/favicon.ico', { method: 'HEAD', cache: 'no-cache' })
      .then(() => {
        // If successful, the online event should fire automatically
        console.log('Connection restored');
      })
      .catch(() => {
        // Still offline, do nothing
        console.log('Still offline');
      });
  };

  return (
    <div className={cn(
      "bg-yellow-50 dark:bg-yellow-900/20 border-b border-yellow-200 dark:border-yellow-800",
      "px-4 py-3 flex items-center justify-between",
      className
    )}>
      <div className="flex items-center gap-3">
        <WifiOff className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
        <div className="flex-1">
          <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            {isSlowConnection ? 'Langsame Verbindung' : 'Offline-Modus aktiv'}
          </p>
          <p className="text-xs text-yellow-700 dark:text-yellow-300">
            {isSlowConnection
              ? 'Einige Features sind möglicherweise eingeschränkt.'
              : 'AI-Funktionen sind nicht verfügbar. Ihre Änderungen werden lokal gespeichert.'}
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRetry}
          className="text-yellow-700 dark:text-yellow-300 hover:bg-yellow-100 dark:hover:bg-yellow-800/30"
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Erneut versuchen
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsDismissed(true)}
          className="text-yellow-700 dark:text-yellow-300 hover:bg-yellow-100 dark:hover:bg-yellow-800/30"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}