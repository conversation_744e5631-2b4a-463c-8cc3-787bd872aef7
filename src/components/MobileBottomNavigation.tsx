"use client";

import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Home, 
  Plus, 
  FileDown, 
  Settings, 
  Eye, 
  EyeOff,
  Sparkles,
  MoreHorizontal 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileBottomNavigationProps {
  onAddTask: () => void;
  onExport: () => void;
  onToggleAIContent: () => void;
  onOpenSettings: () => void;
  isAIContentVisible: boolean;
  className?: string;
}

export function MobileBottomNavigation({
  onAddTask,
  onExport,
  onToggleAIContent,
  onOpenSettings,
  isAIContentVisible,
  className
}: MobileBottomNavigationProps) {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState<string>('home');

  const handleTabPress = useCallback((tabId: string, action?: () => void) => {
    setActiveTab(tabId);
    
    // Haptic feedback
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
    
    if (action) {
      action();
    }
  }, [isMobile]);

  if (!isMobile) {
    return null;
  }

  const navItems = [
    {
      id: 'home',
      label: 'Übersicht',
      icon: Home,
      action: () => {
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    },
    {
      id: 'ai-content',
      label: isAIContentVisible ? 'KI aus' : 'KI ein',
      icon: isAIContentVisible ? EyeOff : Eye,
      action: onToggleAIContent
    },
    {
      id: 'add',
      label: 'Hinzufügen',
      icon: Plus,
      action: onAddTask,
      primary: true
    },
    {
      id: 'export',
      label: 'Export',
      icon: FileDown,
      action: onExport
    },
    {
      id: 'more',
      label: 'Mehr',
      icon: MoreHorizontal,
      action: onOpenSettings
    }
  ];

  return (
    <nav className={cn("mobile-nav", className)}>
      <div className="flex items-center justify-around max-w-md mx-auto">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <Button
              key={item.id}
              variant={item.primary ? "default" : "ghost"}
              size="sm"
              onClick={() => handleTabPress(item.id, item.action)}
              className={cn(
                "flex flex-col items-center gap-1 h-auto py-2 px-3 min-w-0 flex-1 max-w-[80px]",
                "mobile-touch-target",
                item.primary && [
                  "bg-indigo-500 hover:bg-indigo-600 text-white",
                  "shadow-lg scale-110 -mt-2"
                ],
                !item.primary && isActive && [
                  "text-indigo-600 dark:text-indigo-400",
                  "bg-indigo-50 dark:bg-indigo-950/50"
                ],
                !item.primary && !isActive && [
                  "text-slate-600 dark:text-slate-400",
                  "hover:text-slate-900 dark:hover:text-slate-100"
                ]
              )}
              aria-label={item.label}
            >
              <Icon className={cn(
                "h-5 w-5",
                item.primary && "h-6 w-6"
              )} />
              <span className={cn(
                "text-xs font-medium leading-none",
                item.primary && "text-xs"
              )}>
                {item.label}
              </span>
            </Button>
          );
        })}
      </div>
    </nav>
  );
}

// Floating Action Button component for quick task creation
interface FloatingActionButtonProps {
  onClick: () => void;
  className?: string;
  disabled?: boolean;
}

export function FloatingActionButton({ 
  onClick, 
  className,
  disabled = false 
}: FloatingActionButtonProps) {
  const isMobile = useIsMobile();

  const handleClick = useCallback(() => {
    // Haptic feedback
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(20);
    }
    onClick();
  }, [isMobile, onClick]);

  if (!isMobile) {
    return null;
  }

  return (
    <Button
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        "fab",
        "bg-indigo-500 hover:bg-indigo-600 text-white",
        "flex items-center justify-center",
        "border-0 shadow-lg hover:shadow-xl",
        "transition-all duration-200",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      aria-label="Neue Aufgabe hinzufügen"
    >
      <Plus className="h-6 w-6" />
    </Button>
  );
}

// Mobile-specific quick actions menu
interface MobileQuickActionsProps {
  onAddTask: () => void;
  onAddSubtask: () => void;
  onBreakdownTask: () => void;
  onAIGenerate: () => void;
  isOpen: boolean;
  onClose: () => void;
}

export function MobileQuickActions({
  onAddTask,
  onAddSubtask,
  onBreakdownTask,
  onAIGenerate,
  isOpen,
  onClose
}: MobileQuickActionsProps) {
  const isMobile = useIsMobile();

  const handleAction = useCallback((action: () => void) => {
    // Haptic feedback
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(15);
    }
    action();
    onClose();
  }, [isMobile, onClose]);

  if (!isMobile || !isOpen) {
    return null;
  }

  const actions = [
    {
      label: 'Neue Aufgabe',
      icon: Plus,
      action: onAddTask,
      color: 'bg-blue-500'
    },
    {
      label: 'Unteraufgabe',
      icon: Plus,
      action: onAddSubtask,
      color: 'bg-green-500'
    },
    {
      label: 'KI Zerlegung',
      icon: Sparkles,
      action: onBreakdownTask,
      color: 'bg-purple-500'
    },
    {
      label: 'KI Generieren',
      icon: Sparkles,
      action: onAIGenerate,
      color: 'bg-orange-500'
    }
  ];

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />
      
      {/* Actions menu */}
      <div className="fixed bottom-20 right-4 z-50 flex flex-col gap-3">
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.label}
              onClick={() => handleAction(action.action)}
              className={cn(
                "h-12 w-12 rounded-full shadow-lg",
                action.color,
                "text-white hover:scale-110 transition-all duration-200",
                "animate-in slide-in-from-bottom-2 fade-in-0"
              )}
              style={{
                animationDelay: `${index * 50}ms`,
                animationFillMode: 'both'
              }}
              aria-label={action.label}
            >
              <Icon className="h-5 w-5" />
            </Button>
          );
        })}
      </div>
    </>
  );
}