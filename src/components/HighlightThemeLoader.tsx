"use client";

import { useEffect } from 'react';

export default function HighlightThemeLoader() {
  useEffect(() => {
    const apply = () => {
      const isDark = document.documentElement.classList.contains('dark');
      const light = document.getElementById('hljs-light') as HTMLLinkElement | null;
      const dark = document.getElementById('hljs-dark') as HTMLLinkElement | null;
      if (light) light.disabled = !!isDark;
      if (dark) dark.disabled = !isDark;
    };

    // initial
    apply();

    // observe class changes on html element
    const obs = new MutationObserver(apply);
    obs.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

    // also react to storage changes (theme toggle may write to localStorage)
    const onStorage = (e: StorageEvent) => {
      if (e.key === 'theme') setTimeout(apply, 0);
    };
    window.addEventListener('storage', onStorage);

    return () => {
      obs.disconnect();
      window.removeEventListener('storage', onStorage);
    };
  }, []);

  return null;
}


