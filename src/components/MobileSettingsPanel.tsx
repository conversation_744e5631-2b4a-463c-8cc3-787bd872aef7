"use client";

import { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Settings, 
  Smartphone, 
  Vibrate, 
  Eye, 
  Palette, 
  Download,
  Trash2,
  RefreshCw,
  Info,
  Bot,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
  MobileDialogTrigger,
} from '@/components/ui/mobile-dialog';
import { AIProviderSettings } from '@/components/AIProviderSettings';
import { LazyPerformanceDashboard } from '@/components/lazy/LazyExportComponents';
import { Suspense } from 'react';
import { LoadingIndicator } from '@/components/ui/loading-indicator';

interface MobileSettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  settings: {
    hapticFeedback: boolean;
    swipeActions: boolean;
    compactMode: boolean;
    autoCollapse: boolean;
    pullToRefresh: boolean;
    touchFeedback: boolean;
  };
  onSettingsChange: (settings: any) => void;
  onExportData: () => void;
  onImportData: () => void;
  onClearData: () => void;
  onResetApp: () => void;
}

export function MobileSettingsPanel({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  onExportData,
  onImportData,
  onClearData,
  onResetApp
}: MobileSettingsPanelProps) {
  const isMobile = useIsMobile();
  const [swipeSensitivity, setSwipeSensitivity] = useState(50);
  const [showPerformanceDashboard, setShowPerformanceDashboard] = useState(false);

  const handleSettingChange = useCallback((key: string, value: boolean | number) => {
    // Haptic feedback for setting changes
    if (isMobile && settings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
    
    onSettingsChange({
      ...settings,
      [key]: value
    });
  }, [isMobile, settings, onSettingsChange]);

  const handleHapticTest = useCallback(() => {
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate([50, 100, 50]);
    }
  }, [isMobile]);

  if (!isMobile) {
    return null;
  }

  return (
    <MobileDialog open={isOpen} onOpenChange={onClose}>
      <MobileDialogContent className="max-h-[85vh] overflow-y-auto">
        <MobileDialogHeader>
          <MobileDialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Mobile Einstellungen
          </MobileDialogTitle>
        </MobileDialogHeader>

        <div className="space-y-6">
          {/* Touch & Haptic Settings */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">Touch-Interaktionen</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="haptic-feedback">Haptisches Feedback</Label>
                  <p className="text-sm text-muted-foreground">
                    Vibrationen bei Touch-Aktionen
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id="haptic-feedback"
                    checked={settings.hapticFeedback}
                    onCheckedChange={(checked) => handleSettingChange('hapticFeedback', checked)}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleHapticTest}
                    disabled={!settings.hapticFeedback}
                    className="mobile-touch-target"
                  >
                    <Vibrate className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="swipe-actions">Wisch-Aktionen</Label>
                  <p className="text-sm text-muted-foreground">
                    Aufgaben durch Wischen bearbeiten
                  </p>
                </div>
                <Switch
                  id="swipe-actions"
                  checked={settings.swipeActions}
                  onCheckedChange={(checked) => handleSettingChange('swipeActions', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="pull-refresh">Pull-to-Refresh</Label>
                  <p className="text-sm text-muted-foreground">
                    Nach unten ziehen zum Aktualisieren
                  </p>
                </div>
                <Switch
                  id="pull-refresh"
                  checked={settings.pullToRefresh}
                  onCheckedChange={(checked) => handleSettingChange('pullToRefresh', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="touch-feedback">Touch-Feedback</Label>
                  <p className="text-sm text-muted-foreground">
                    Visuelle Rückmeldung bei Berührungen
                  </p>
                </div>
                <Switch
                  id="touch-feedback"
                  checked={settings.touchFeedback}
                  onCheckedChange={(checked) => handleSettingChange('touchFeedback', checked)}
                />
              </div>
            </div>

            {/* Swipe Sensitivity */}
            {settings.swipeActions && (
              <div className="space-y-2">
                <Label>Wisch-Empfindlichkeit</Label>
                <Slider
                  value={[swipeSensitivity]}
                  onValueChange={(value) => setSwipeSensitivity(value[0])}
                  max={100}
                  min={10}
                  step={10}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Niedrig</span>
                  <span>Hoch</span>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Display Settings */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">Anzeige</h3>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="compact-mode">Kompakter Modus</Label>
                  <p className="text-sm text-muted-foreground">
                    Weniger Abstände für mehr Inhalt
                  </p>
                </div>
                <Switch
                  id="compact-mode"
                  checked={settings.compactMode}
                  onCheckedChange={(checked) => handleSettingChange('compactMode', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-collapse">Auto-Einklappen</Label>
                  <p className="text-sm text-muted-foreground">
                    Große Aufgabenlisten automatisch einklappen
                  </p>
                </div>
                <Switch
                  id="auto-collapse"
                  checked={settings.autoCollapse}
                  onCheckedChange={(checked) => handleSettingChange('autoCollapse', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Performance Monitoring */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">Performance</h3>
            </div>
            
            <Button
              variant="outline"
              onClick={() => setShowPerformanceDashboard(true)}
              className="w-full mobile-touch-target flex items-center gap-2"
            >
              <Activity className="h-4 w-4" />
              Performance Dashboard öffnen
            </Button>
          </div>

          <Separator />

          {/* AI Provider Settings */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">KI-Provider</h3>
            </div>
            
            <AIProviderSettings />
          </div>

          <Separator />

          {/* Data Management */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Download className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">Daten-Verwaltung</h3>
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                onClick={onExportData}
                className="mobile-touch-target flex flex-col gap-1 h-auto py-3"
              >
                <Download className="h-4 w-4" />
                <span className="text-xs">Exportieren</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={onImportData}
                className="mobile-touch-target flex flex-col gap-1 h-auto py-3"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="text-xs">Importieren</span>
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                onClick={onClearData}
                className="mobile-touch-target flex flex-col gap-1 h-auto py-3 text-orange-600 border-orange-200 hover:bg-orange-50"
              >
                <Trash2 className="h-4 w-4" />
                <span className="text-xs">Daten löschen</span>
              </Button>
              
              <Button
                variant="outline"
                onClick={onResetApp}
                className="mobile-touch-target flex flex-col gap-1 h-auto py-3 text-red-600 border-red-200 hover:bg-red-50"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="text-xs">App zurücksetzen</span>
              </Button>
            </div>
          </div>

          <Separator />

          {/* App Info */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-muted-foreground" />
              <h3 className="font-medium">App-Informationen</h3>
            </div>
            
            <div className="text-sm text-muted-foreground space-y-1">
              <p>Version: 1.0.0</p>
              <p>PWA-Modus: {typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches ? 'Aktiv' : 'Inaktiv'}</p>
              <p>Touch-Gerät: {isMobile ? 'Ja' : 'Nein'}</p>
            </div>
          </div>
        </div>
      </MobileDialogContent>

      {/* Performance Dashboard Modal */}
      <MobileDialog open={showPerformanceDashboard} onOpenChange={setShowPerformanceDashboard}>
        <MobileDialogContent className="max-h-[90vh] overflow-y-auto">
          <MobileDialogHeader>
            <MobileDialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance Dashboard
            </MobileDialogTitle>
          </MobileDialogHeader>
          <div className="mt-4">
            <Suspense fallback={
              <div className="flex items-center justify-center py-8">
                <LoadingIndicator />
                <span className="ml-2 text-sm text-muted-foreground">
                  Performance Dashboard wird geladen...
                </span>
              </div>
            }>
              <LazyPerformanceDashboard />
            </Suspense>
          </div>
        </MobileDialogContent>
      </MobileDialog>
    </MobileDialog>
  );
}

// Quick settings toggle component for the bottom nav
interface QuickSettingsToggleProps {
  setting: keyof MobileSettingsPanelProps['settings'];
  value: boolean;
  onChange: (value: boolean) => void;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

export function QuickSettingsToggle({
  setting,
  value,
  onChange,
  icon: Icon,
  label
}: QuickSettingsToggleProps) {
  const isMobile = useIsMobile();

  const handleToggle = useCallback(() => {
    if (isMobile && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
    onChange(!value);
  }, [isMobile, value, onChange]);

  return (
    <Button
      variant={value ? "default" : "outline"}
      size="sm"
      onClick={handleToggle}
      className={cn(
        "flex items-center gap-2 mobile-touch-target",
        value && "bg-indigo-500 hover:bg-indigo-600"
      )}
      aria-label={`${label} ${value ? 'deaktivieren' : 'aktivieren'}`}
    >
      <Icon className="h-4 w-4" />
      <span className="text-xs">{label}</span>
    </Button>
  );
}