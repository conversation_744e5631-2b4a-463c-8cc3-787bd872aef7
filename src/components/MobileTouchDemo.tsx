"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { Trash2, Edit, Check, MoreHorizontal, RefreshCw } from 'lucide-react';

interface SwipeAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}

interface MobileTouchDemoProps {
  onRefresh?: () => Promise<void>;
}

export function MobileTouchDemo({ onRefresh }: MobileTouchDemoProps) {
  const isMobile = useIsMobile();
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  const [activeAction, setActiveAction] = useState<string | null>(null);
  const [isPullToRefreshActive, setIsPullToRefreshActive] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const touchStartX = useRef<number>(0);
  const touchStartY = useRef<number>(0);
  const touchStartTime = useRef<number>(0);
  const cardRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const swipeThreshold = 80;
  const pullThreshold = 80;

  const leftActions: SwipeAction[] = [
    {
      id: 'complete',
      label: 'Erledigt',
      icon: <Check className="h-4 w-4" />,
      color: 'bg-green-500',
      action: () => {
        triggerHapticFeedback('medium');
        console.log('Task completed');
      }
    }
  ];

  const rightActions: SwipeAction[] = [
    {
      id: 'edit',
      label: 'Bearbeiten',
      icon: <Edit className="h-4 w-4" />,
      color: 'bg-blue-500',
      action: () => {
        triggerHapticFeedback('light');
        console.log('Edit task');
      }
    },
    {
      id: 'delete',
      label: 'Löschen',
      icon: <Trash2 className="h-4 w-4" />,
      color: 'bg-red-500',
      action: () => {
        triggerHapticFeedback('heavy');
        console.log('Delete task');
      }
    }
  ];

  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!isMobile) return;
    
    if ('vibrate' in navigator) {
      const patterns = {
        light: 10,
        medium: 20,
        heavy: 50
      };
      navigator.vibrate(patterns[type]);
    }
  }, [isMobile]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;
    
    const touch = e.touches[0];
    touchStartX.current = touch.clientX;
    touchStartY.current = touch.clientY;
    touchStartTime.current = Date.now();
    setIsSwipeActive(true);
    
    // Check if we're at the top for pull-to-refresh
    const scrollTop = containerRef.current?.scrollTop || 0;
    if (scrollTop === 0) {
      setIsPullToRefreshActive(true);
    }
  }, [isMobile]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStartX.current;
    const deltaY = touch.clientY - touchStartY.current;
    
    // Determine if this is a horizontal swipe or vertical pull
    if (Math.abs(deltaX) > Math.abs(deltaY) && isSwipeActive) {
      // Horizontal swipe for actions
      e.preventDefault();
      const offset = Math.max(-200, Math.min(200, deltaX));
      setSwipeOffset(offset);
      
      // Determine active action
      if (Math.abs(offset) > swipeThreshold) {
        if (offset > 0 && leftActions.length > 0) {
          setActiveAction(leftActions[0].id);
        } else if (offset < 0) {
          const actionIndex = Math.min(
            Math.floor(Math.abs(offset) / swipeThreshold) - 1,
            rightActions.length - 1
          );
          setActiveAction(rightActions[actionIndex]?.id || null);
        }
        triggerHapticFeedback('light');
      } else {
        setActiveAction(null);
      }
    } else if (isPullToRefreshActive && deltaY > 0) {
      // Vertical pull for refresh
      e.preventDefault();
      const distance = Math.min(deltaY * 0.5, pullThreshold * 1.5);
      setPullDistance(distance);
    }
  }, [isMobile, isSwipeActive, isPullToRefreshActive, leftActions, rightActions, swipeThreshold, pullThreshold, triggerHapticFeedback]);

  const handleTouchEnd = useCallback(async () => {
    if (!isMobile) return;
    
    // Handle swipe actions
    if (isSwipeActive && Math.abs(swipeOffset) > swipeThreshold && activeAction) {
      const action = [...leftActions, ...rightActions].find(a => a.id === activeAction);
      if (action) {
        action.action();
      }
    }
    
    // Handle pull-to-refresh
    if (isPullToRefreshActive && pullDistance >= pullThreshold && onRefresh && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
        triggerHapticFeedback('medium');
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    // Reset states
    setIsSwipeActive(false);
    setIsPullToRefreshActive(false);
    setSwipeOffset(0);
    setPullDistance(0);
    setActiveAction(null);
  }, [isMobile, isSwipeActive, swipeOffset, swipeThreshold, activeAction, leftActions, rightActions, isPullToRefreshActive, pullDistance, pullThreshold, onRefresh, isRefreshing, triggerHapticFeedback]);

  if (!isMobile) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-lg">Mobile Touch Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Diese Demo ist nur auf mobilen Geräten verfügbar. Öffnen Sie die App auf einem Smartphone oder Tablet, um die Touch-Interaktionen zu testen.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div ref={containerRef} className="space-y-4">
      {/* Pull-to-refresh indicator */}
      {(isPullToRefreshActive || isRefreshing) && (
        <div 
          className="flex items-center justify-center py-4 transition-all duration-200"
          style={{
            transform: `translateY(${Math.max(0, pullDistance - 40)}px)`,
            opacity: pullDistance > 20 ? 1 : pullDistance / 20
          }}
        >
          <div className="flex items-center space-x-2 text-indigo-600 dark:text-indigo-400">
            <RefreshCw 
              className={`h-5 w-5 ${isRefreshing || pullDistance >= pullThreshold ? 'animate-spin' : ''}`} 
            />
            <span className="text-sm font-medium">
              {isRefreshing 
                ? 'Aktualisiere...' 
                : pullDistance >= pullThreshold 
                  ? 'Loslassen zum Aktualisieren' 
                  : 'Zum Aktualisieren herunterziehen'
              }
            </span>
          </div>
        </div>
      )}

      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-lg">Mobile Touch Interaktionen</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Testen Sie die Touch-Gesten:
          </p>
          <ul className="text-sm space-y-2 text-muted-foreground">
            <li>• <strong>Nach unten ziehen:</strong> Liste aktualisieren</li>
            <li>• <strong>Nach rechts wischen:</strong> Aufgabe als erledigt markieren</li>
            <li>• <strong>Nach links wischen:</strong> Bearbeiten oder löschen</li>
            <li>• <strong>Lange drücken:</strong> Zusätzliche Optionen</li>
          </ul>
        </CardContent>
      </Card>

      {/* Demo task card with swipe actions */}
      <div className="relative overflow-hidden">
        {/* Left action (complete) */}
        {swipeOffset > 0 && (
          <div 
            className="absolute left-0 top-0 h-full flex items-center justify-center transition-all duration-200"
            style={{ width: Math.min(swipeOffset, 100) }}
          >
            <div className={`${leftActions[0]?.color} text-white p-3 rounded-r-lg flex items-center space-x-2`}>
              {leftActions[0]?.icon}
              {swipeOffset > 60 && <span className="text-sm font-medium">{leftActions[0]?.label}</span>}
            </div>
          </div>
        )}

        {/* Right actions (edit, delete) */}
        {swipeOffset < 0 && (
          <div 
            className="absolute right-0 top-0 h-full flex items-center justify-center transition-all duration-200"
            style={{ width: Math.min(Math.abs(swipeOffset), 200) }}
          >
            <div className="flex">
              {rightActions.map((action, index) => {
                const actionWidth = Math.abs(swipeOffset) / rightActions.length;
                const isActive = activeAction === action.id;
                return (
                  <div 
                    key={action.id}
                    className={`${action.color} text-white p-3 flex items-center justify-center transition-all duration-200 ${
                      isActive ? 'scale-110' : ''
                    }`}
                    style={{ width: actionWidth }}
                  >
                    {action.icon}
                    {actionWidth > 60 && (
                      <span className="text-sm font-medium ml-2">{action.label}</span>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Main task card */}
        <Card 
          ref={cardRef}
          className="transition-transform duration-200 touch-pan-y"
          style={{
            transform: `translateX(${swipeOffset}px) translateY(${isPullToRefreshActive ? pullDistance : 0}px)`,
            transition: isSwipeActive || isPullToRefreshActive ? 'none' : 'transform 0.2s ease-out'
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h3 className="font-medium text-base mb-1">Demo Aufgabe</h3>
                <p className="text-sm text-muted-foreground">
                  Wischen Sie diese Karte nach links oder rechts, um Aktionen auszulösen.
                </p>
              </div>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Touch feedback indicator */}
      {(isSwipeActive || isPullToRefreshActive) && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-3 py-2 rounded-lg text-sm z-50">
          {activeAction ? `${[...leftActions, ...rightActions].find(a => a.id === activeAction)?.label} aktiviert` : 
           isPullToRefreshActive ? `Ziehen: ${Math.round(pullDistance)}px` :
           `Wischen: ${Math.round(Math.abs(swipeOffset))}px`}
        </div>
      )}
    </div>
  );
}