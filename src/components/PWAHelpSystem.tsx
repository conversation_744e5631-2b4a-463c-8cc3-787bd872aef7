/**
 * PWA Help System Component
 * 
 * Provides comprehensive in-app help explaining PWA features and offline capabilities
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  HelpCircle,
  Smartphone,
  Wifi,
  WifiOff,
  Download,
  RotateCw as Sync,
  Shield,
  Zap,
  CheckCircle,
  AlertCircle,
  Info,
  ArrowRight,
  Star,
  Settings,
  FileDown,
  Upload,
  Brain,
  Globe,
  HardDrive
} from 'lucide-react';

interface PWAHelpSystemProps {
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
  trigger?: React.ReactNode;
}

export function PWAHelpSystem({ isOpen, onOpenChange, trigger }: PWAHelpSystemProps) {
  const [activeTab, setActiveTab] = useState('overview');

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="gap-2">
      <HelpCircle size={16} />
      PWA Hilfe
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="text-blue-500" size={24} />
            Progressive Web App (PWA) Funktionen
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Überblick</TabsTrigger>
            <TabsTrigger value="offline">Offline</TabsTrigger>
            <TabsTrigger value="installation">Installation</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="troubleshooting">Hilfe</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Star className="text-yellow-500" size={20} />
                Was ist eine Progressive Web App?
              </h3>
              <p className="text-slate-600 dark:text-slate-300">
                Diese App ist als Progressive Web App (PWA) entwickelt und bietet Ihnen die Vorteile 
                einer nativen App direkt im Browser. Sie können die App installieren, offline nutzen 
                und von verbesserter Performance profitieren.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="text-green-500" size={20} />
                    <h4 className="font-semibold">Vorteile</h4>
                  </div>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Offline-Funktionalität</li>
                    <li>• Schnelle Ladezeiten</li>
                    <li>• App-ähnliche Bedienung</li>
                    <li>• Automatische Updates</li>
                    <li>• Plattformübergreifend</li>
                  </ul>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="text-blue-500" size={20} />
                    <h4 className="font-semibold">Funktionen</h4>
                  </div>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Projekte offline erstellen</li>
                    <li>• Aufgaben bearbeiten</li>
                    <li>• Daten exportieren</li>
                    <li>• AI-Features (online)</li>
                    <li>• Automatische Synchronisation</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Zap className="text-orange-500" size={20} />
                  Schnellstart
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">1</span>
                    <span>App installieren (optional)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">2</span>
                    <span>Projekt erstellen und offline arbeiten</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">3</span>
                    <span>Bei Internetverbindung AI-Features nutzen</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="offline" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <WifiOff className="text-red-500" size={20} />
                Offline-Funktionalität
              </h3>
              <p className="text-slate-600 dark:text-slate-300">
                Die App funktioniert auch ohne Internetverbindung. Ihre Daten werden lokal gespeichert 
                und automatisch synchronisiert, sobald Sie wieder online sind.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-semibold text-green-600 dark:text-green-400 flex items-center gap-2">
                    <CheckCircle size={16} />
                    Offline verfügbar
                  </h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={14} />
                      Projekte erstellen und bearbeiten
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={14} />
                      Aufgaben hinzufügen, bearbeiten, löschen
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={14} />
                      Daten exportieren (PDF, CSV, Markdown)
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={14} />
                      Lokale Datenspeicherung
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={14} />
                      App-Navigation und -bedienung
                    </li>
                  </ul>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-red-600 dark:text-red-400 flex items-center gap-2">
                    <AlertCircle size={16} />
                    Nur online verfügbar
                  </h4>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <AlertCircle className="text-red-500" size={14} />
                      AI-Aufgabenerstellung
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertCircle className="text-red-500" size={14} />
                      AI-Inhalte generieren
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertCircle className="text-red-500" size={14} />
                      Aufgaben zerlegen (AI)
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertCircle className="text-red-500" size={14} />
                      Daten-Synchronisation
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertCircle className="text-red-500" size={14} />
                      App-Updates
                    </li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Sync className="text-blue-500" size={20} />
                  Automatische Synchronisation
                </h4>
                <p className="text-sm text-slate-600 dark:text-slate-300 mb-3">
                  Wenn Sie wieder online gehen, werden alle Änderungen automatisch synchronisiert:
                </p>
                <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                  <li>• Wartende AI-Anfragen werden verarbeitet</li>
                  <li>• Lokale Änderungen werden gesichert</li>
                  <li>• App-Updates werden heruntergeladen</li>
                  <li>• Cache wird aktualisiert</li>
                </ul>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="installation" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Download className="text-blue-500" size={20} />
                App Installation
              </h3>
              <p className="text-slate-600 dark:text-slate-300">
                Sie können die App wie eine native App auf Ihrem Gerät installieren. 
                Dies bietet bessere Performance und einfacheren Zugriff.
              </p>

              <div className="space-y-4">
                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Smartphone className="text-green-500" size={20} />
                    Mobile Installation (Android/iOS)
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start gap-2">
                      <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">1</span>
                      <div>
                        <strong>Android (Chrome):</strong> Tippen Sie auf "Zur Startseite hinzufügen" 
                        in der Browser-Adressleiste oder im Menü
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">2</span>
                      <div>
                        <strong>iOS (Safari):</strong> Tippen Sie auf das Teilen-Symbol und wählen Sie 
                        "Zum Home-Bildschirm hinzufügen"
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">3</span>
                      <div>
                        Die App erscheint als Icon auf Ihrem Startbildschirm
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Settings className="text-blue-500" size={20} />
                    Desktop Installation
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start gap-2">
                      <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">1</span>
                      <div>
                        Klicken Sie auf das Installations-Symbol in der Adressleiste (Chrome, Edge)
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">2</span>
                      <div>
                        Oder nutzen Sie das Browser-Menü: "App installieren" oder "Zur Startseite hinzufügen"
                      </div>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mt-0.5">3</span>
                      <div>
                        Die App wird als eigenständige Anwendung installiert
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Star className="text-yellow-500" size={20} />
                    Vorteile der Installation
                  </h4>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Schnellerer Start ohne Browser-Overhead</li>
                    <li>• Eigenes App-Icon auf dem Desktop/Startbildschirm</li>
                    <li>• Vollbild-Modus ohne Browser-UI</li>
                    <li>• Bessere Integration ins Betriebssystem</li>
                    <li>• Automatische Updates im Hintergrund</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Zap className="text-purple-500" size={20} />
                PWA-spezifische Features
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <HardDrive className="text-purple-500" size={20} />
                    Lokale Datenspeicherung
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                    Ihre Daten werden sicher auf Ihrem Gerät gespeichert:
                  </p>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• IndexedDB für strukturierte Daten</li>
                    <li>• LocalStorage als Backup</li>
                    <li>• Automatische Datenmigration</li>
                    <li>• Verschlüsselte Speicherung</li>
                  </ul>
                </div>

                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Shield className="text-green-500" size={20} />
                    Datenschutz & Sicherheit
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                    Ihre Privatsphäre ist geschützt:
                  </p>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Daten bleiben auf Ihrem Gerät</li>
                    <li>• Keine automatische Cloud-Sync</li>
                    <li>• Verschlüsselte lokale Speicherung</li>
                    <li>• Transparente AI-Datennutzung</li>
                  </ul>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <FileDown className="text-blue-500" size={20} />
                    Export & Import
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                    Flexible Datenportabilität:
                  </p>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• PDF-Export mit PWA-Metadaten</li>
                    <li>• JSON-Backup mit vollständigen Daten</li>
                    <li>• CSV für Tabellenkalkulationen</li>
                    <li>• Markdown für Dokumentation</li>
                  </ul>
                </div>

                <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Brain className="text-orange-500" size={20} />
                    Intelligente AI-Integration
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                    AI-Features mit Offline-Bewusstsein:
                  </p>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Automatische Offline-Erkennung</li>
                    <li>• AI-Anfragen in Warteschlange</li>
                    <li>• Mehrere AI-Provider unterstützt</li>
                    <li>• Kontextuelle Hilfe</li>
                  </ul>
                </div>
              </div>

              <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Globe className="text-indigo-500" size={20} />
                  Performance-Optimierungen
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium mb-1">Caching-Strategien:</h5>
                    <ul className="space-y-1 text-slate-600 dark:text-slate-300">
                      <li>• Cache-First für statische Assets</li>
                      <li>• Network-First für API-Aufrufe</li>
                      <li>• Stale-While-Revalidate für Inhalte</li>
                    </ul>
                  </div>
                  <div>
                    <h5 className="font-medium mb-1">Optimierungen:</h5>
                    <ul className="space-y-1 text-slate-600 dark:text-slate-300">
                      <li>• Lazy Loading für Komponenten</li>
                      <li>• Code Splitting für AI-Module</li>
                      <li>• Intelligentes Preloading</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="troubleshooting" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <AlertCircle className="text-red-500" size={20} />
                Problemlösung & FAQ
              </h3>

              <div className="space-y-4">
                <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 text-red-700 dark:text-red-300">
                    Häufige Probleme
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div>
                      <strong>Problem:</strong> App lädt nicht offline
                      <br />
                      <strong>Lösung:</strong> Stellen Sie sicher, dass Sie die App mindestens einmal 
                      online besucht haben. Der Service Worker muss erst installiert werden.
                    </div>
                    <div>
                      <strong>Problem:</strong> AI-Features funktionieren nicht
                      <br />
                      <strong>Lösung:</strong> Überprüfen Sie Ihre Internetverbindung. 
                      AI-Features sind nur online verfügbar.
                    </div>
                    <div>
                      <strong>Problem:</strong> Daten sind verschwunden
                      <br />
                      <strong>Lösung:</strong> Prüfen Sie den Browser-Speicher. 
                      Exportieren Sie regelmäßig Backups Ihrer Projekte.
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 text-blue-700 dark:text-blue-300">
                    Browser-Kompatibilität
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h5 className="font-medium mb-1 text-green-600 dark:text-green-400">
                        Vollständig unterstützt:
                      </h5>
                      <ul className="space-y-1 text-slate-600 dark:text-slate-300">
                        <li>• Chrome 67+ (Desktop & Mobile)</li>
                        <li>• Firefox 63+ (Desktop & Mobile)</li>
                        <li>• Safari 11.1+ (Desktop & Mobile)</li>
                        <li>• Edge 79+ (Desktop & Mobile)</li>
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium mb-1 text-yellow-600 dark:text-yellow-400">
                        Eingeschränkt unterstützt:
                      </h5>
                      <ul className="space-y-1 text-slate-600 dark:text-slate-300">
                        <li>• Internet Explorer (nicht empfohlen)</li>
                        <li>• Ältere Browser-Versionen</li>
                        <li>• Einige mobile Browser</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 text-green-700 dark:text-green-300">
                    Tipps für beste Performance
                  </h4>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Installieren Sie die App für bessere Performance</li>
                    <li>• Löschen Sie regelmäßig den Browser-Cache bei Problemen</li>
                    <li>• Nutzen Sie die neueste Browser-Version</li>
                    <li>• Exportieren Sie wichtige Projekte als Backup</li>
                    <li>• Schließen Sie andere Browser-Tabs für mehr Speicher</li>
                  </ul>
                </div>

                <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h4 className="font-semibold mb-2 text-yellow-700 dark:text-yellow-300">
                    Speicher-Management
                  </h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300 mb-2">
                    Die App nutzt lokalen Browser-Speicher. Bei Speicherproblemen:
                  </p>
                  <ul className="space-y-1 text-sm text-slate-600 dark:text-slate-300">
                    <li>• Exportieren Sie wichtige Projekte</li>
                    <li>• Löschen Sie nicht benötigte Projekte</li>
                    <li>• Leeren Sie den Browser-Cache</li>
                    <li>• Prüfen Sie verfügbaren Speicherplatz</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center pt-4 border-t">
          <Badge variant="outline" className="text-xs">
            PWA Version 2.0
          </Badge>
          <div className="flex gap-2">
            {activeTab !== 'troubleshooting' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const tabs = ['overview', 'offline', 'installation', 'features', 'troubleshooting'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex < tabs.length - 1) {
                    setActiveTab(tabs[currentIndex + 1]);
                  }
                }}
                className="gap-2"
              >
                Weiter
                <ArrowRight size={14} />
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default PWAHelpSystem;