"use client";

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Upload, FileText, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { 
  importProject, 
  validateImportFile,
  type ImportOptions,
  type ImportResult,
  type ImportFormat 
} from '@/lib/export';

interface ImportDialogProps {
  onImportComplete?: (result: ImportResult) => void;
  trigger?: React.ReactNode;
}

export function ImportDialog({ onImportComplete, trigger }: ImportDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importFormat, setImportFormat] = useState<ImportFormat>('json');
  const [mergeStrategy, setMergeStrategy] = useState<'replace' | 'merge' | 'append'>('append');
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [filePreview, setFilePreview] = useState<{
    name: string;
    size: string;
    type: string;
    detectedFormat?: ImportFormat;
  } | null>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      setSelectedFile(null);
      setFilePreview(null);
      setValidationError(null);
      return;
    }

    // Validate file
    const validation = validateImportFile(file);
    if (!validation.valid) {
      setValidationError(validation.error || 'Invalid file');
      setSelectedFile(null);
      setFilePreview(null);
      return;
    }

    setSelectedFile(file);
    setValidationError(null);
    
    // Create file preview
    const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
    const preview = {
      name: file.name,
      size: `${sizeInMB} MB`,
      type: file.type || 'Unknown',
      detectedFormat: detectFormatFromFile(file)
    };
    
    setFilePreview(preview);
    
    // Auto-set format if detected
    if (preview.detectedFormat) {
      setImportFormat(preview.detectedFormat);
    }
  }, []);

  const detectFormatFromFile = (file: File): ImportFormat | undefined => {
    const extension = file.name.toLowerCase().split('.').pop();
    switch (extension) {
      case 'json':
        return file.name.toLowerCase().includes('backup') ? 'backup' : 'json';
      case 'md':
      case 'markdown':
        return 'markdown';
      case 'csv':
        return 'csv';
      default:
        return undefined;
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setIsImporting(true);
    setImportProgress(0);
    setImportResult(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const options: ImportOptions = {
        format: importFormat,
        mergeStrategy,
        validateData: true,
        preserveIds: false
      };

      const result = await importProject(selectedFile, options);
      
      clearInterval(progressInterval);
      setImportProgress(100);
      setImportResult(result);
      
      if (result.success && onImportComplete) {
        onImportComplete(result);
      }
    } catch (error) {
      setImportResult({
        success: false,
        error: error instanceof Error ? error.message : 'Import failed',
        imported: { projects: 0, tasks: 0 },
        warnings: []
      });
    } finally {
      setIsImporting(false);
    }
  };

  const resetDialog = () => {
    setSelectedFile(null);
    setFilePreview(null);
    setValidationError(null);
    setImportResult(null);
    setImportProgress(0);
    setIsImporting(false);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(resetDialog, 300); // Reset after dialog animation
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="gap-2">
            <Upload className="h-4 w-4" />
            Import Project
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Import Project Data
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Selection */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="file-input" className="text-sm font-medium">
                Select File to Import
              </Label>
              <Input
                id="file-input"
                type="file"
                accept=".json,.md,.markdown,.csv,.txt"
                onChange={handleFileSelect}
                disabled={isImporting}
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Supported formats: JSON, Backup, Markdown, CSV (max 50MB)
              </p>
            </div>

            {validationError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{validationError}</AlertDescription>
              </Alert>
            )}

            {filePreview && (
              <div className="p-3 border rounded-lg bg-muted/50">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{filePreview.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {filePreview.size} • {filePreview.type}
                    </p>
                  </div>
                  {filePreview.detectedFormat && (
                    <Badge variant="secondary">
                      {filePreview.detectedFormat.toUpperCase()}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Import Options */}
          <div className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Import Format</Label>
              <Select 
                value={importFormat} 
                onValueChange={(value: ImportFormat) => setImportFormat(value)}
                disabled={isImporting}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON Export</SelectItem>
                  <SelectItem value="backup">Full Backup</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-sm font-medium mb-3 block">
                Merge Strategy
              </Label>
              <RadioGroup 
                value={mergeStrategy} 
                onValueChange={(value: 'replace' | 'merge' | 'append') => setMergeStrategy(value)}
                disabled={isImporting}
                className="space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="append" id="append" />
                  <Label htmlFor="append" className="text-sm">
                    <span className="font-medium">Append</span> - Create new project (recommended)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="merge" id="merge" />
                  <Label htmlFor="merge" className="text-sm">
                    <span className="font-medium">Merge</span> - Add to existing project with same name
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="replace" id="replace" />
                  <Label htmlFor="replace" className="text-sm">
                    <span className="font-medium">Replace</span> - Overwrite existing project (destructive)
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>

          {/* Import Progress */}
          {isImporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Importing...</span>
                <span>{importProgress}%</span>
              </div>
              <Progress value={importProgress} className="h-2" />
            </div>
          )}

          {/* Import Result */}
          {importResult && (
            <div className="space-y-3">
              <Alert variant={importResult.success ? "default" : "destructive"}>
                {importResult.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {importResult.success ? (
                    <div>
                      <p className="font-medium">Import completed successfully!</p>
                      <p className="text-sm mt-1">
                        Imported {importResult.imported.projects} project(s) with {importResult.imported.tasks} task(s)
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="font-medium">Import failed</p>
                      <p className="text-sm mt-1">{importResult.error}</p>
                    </div>
                  )}
                </AlertDescription>
              </Alert>

              {importResult.warnings.length > 0 && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    <p className="font-medium">Warnings:</p>
                    <ul className="text-sm mt-1 space-y-1">
                      {importResult.warnings.map((warning, index) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={handleClose}>
              {importResult?.success ? 'Close' : 'Cancel'}
            </Button>
            {!importResult?.success && (
              <Button 
                onClick={handleImport}
                disabled={!selectedFile || isImporting}
                className="gap-2"
              >
                {isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4" />
                    Import
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}