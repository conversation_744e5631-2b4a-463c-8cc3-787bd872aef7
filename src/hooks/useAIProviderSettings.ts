"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  unifiedAIClient, 
  aiProviderRegistry, 
  AIProvider, 
  AIProviderConfig, 
  AIProviderCredentials, 
  AIProviderSettings 
} from '@/lib/ai';
import { GeminiProvider } from '@/lib/ai/providers/gemini';

export interface AIProviderState {
  providers: AIProvider[];
  activeProviderId: string | null;
  isAnyConfigured: boolean;
  activeProviderInfo: {
    id: string;
    name: string;
    description: string;
  } | null;
}

export interface UseAIProviderSettingsReturn {
  // State
  state: AIProviderState;
  
  // Provider management
  getProvider: (id: string) => AIProvider | null;
  setActiveProvider: (id: string) => boolean;
  addGeminiProvider: () => void;
  
  // Configuration
  updateProviderCredentials: (providerId: string, credentials: AIProviderCredentials) => void;
  updateProviderSettings: (providerId: string, settings: AIProviderSettings) => void;
  validateProviderCredentials: (providerId: string) => Promise<boolean>;
  
  // Testing
  testProvider: (providerId: string) => Promise<boolean>;
  
  // Utilities
  refresh: () => void;
  isProviderConfigured: (providerId: string) => boolean;
}

export function useAIProviderSettings(): UseAIProviderSettingsReturn {
  const [state, setState] = useState<AIProviderState>({
    providers: [],
    activeProviderId: null,
    isAnyConfigured: false,
    activeProviderInfo: null
  });

  // Load current state
  const loadState = useCallback(() => {
    const providers = unifiedAIClient.getAvailableProviders();
    const activeProvider = aiProviderRegistry.getActiveProvider();
    const isAnyConfigured = unifiedAIClient.isAnyProviderConfigured();
    const activeProviderInfo = unifiedAIClient.getActiveProviderInfo();

    setState({
      providers,
      activeProviderId: activeProvider?.getId() || null,
      isAnyConfigured,
      activeProviderInfo
    });
  }, []);

  // Load state on mount
  useEffect(() => {
    loadState();
  }, [loadState]);

  // Provider management
  const getProvider = useCallback((id: string): AIProvider | null => {
    return aiProviderRegistry.getProvider(id);
  }, []);

  const setActiveProvider = useCallback((id: string): boolean => {
    const success = aiProviderRegistry.setActiveProvider(id);
    if (success) {
      loadState();
    }
    return success;
  }, [loadState]);

  const addGeminiProvider = useCallback(() => {
    // Check if Gemini provider already exists
    const existingGemini = state.providers.find(p => p.getId() === 'gemini');
    if (existingGemini) {
      return;
    }

    // Create and register new Gemini provider
    const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || '';
    const geminiProvider = new GeminiProvider({ apiKey: geminiApiKey });
    
    aiProviderRegistry.register(geminiProvider);
    loadState();
  }, [state.providers, loadState]);

  // Configuration
  const updateProviderCredentials = useCallback((providerId: string, credentials: AIProviderCredentials) => {
    const provider = getProvider(providerId);
    if (provider) {
      provider.updateCredentials(credentials);
      loadState();
    }
  }, [getProvider, loadState]);

  const updateProviderSettings = useCallback((providerId: string, settings: AIProviderSettings) => {
    const provider = getProvider(providerId);
    if (provider) {
      provider.updateSettings(settings);
      loadState();
    }
  }, [getProvider, loadState]);

  const validateProviderCredentials = useCallback(async (providerId: string): Promise<boolean> => {
    const provider = getProvider(providerId);
    if (!provider) {
      return false;
    }

    try {
      const isValid = await provider.validateCredentials();
      loadState(); // Refresh state after validation
      return isValid;
    } catch (error) {
      console.error('Credential validation failed:', error);
      return false;
    }
  }, [getProvider, loadState]);

  // Testing
  const testProvider = useCallback(async (providerId: string): Promise<boolean> => {
    const provider = getProvider(providerId);
    if (!provider || !provider.isConfigured()) {
      return false;
    }

    try {
      // Test with a simple task generation
      const response = await provider.generateTasks('Test-Projekt', 'Ein einfacher Test der AI-Funktionalität');
      return response.success;
    } catch (error) {
      console.error('Provider test failed:', error);
      return false;
    }
  }, [getProvider]);

  // Utilities
  const refresh = useCallback(() => {
    loadState();
  }, [loadState]);

  const isProviderConfigured = useCallback((providerId: string): boolean => {
    const provider = getProvider(providerId);
    return provider ? provider.isConfigured() : false;
  }, [getProvider]);

  return {
    state,
    getProvider,
    setActiveProvider,
    addGeminiProvider,
    updateProviderCredentials,
    updateProviderSettings,
    validateProviderCredentials,
    testProvider,
    refresh,
    isProviderConfigured
  };
}

// Hook for getting current AI provider status
export function useAIProviderStatus() {
  const [isConfigured, setIsConfigured] = useState(false);
  const [activeProvider, setActiveProvider] = useState<string | null>(null);

  useEffect(() => {
    const checkStatus = () => {
      const configured = unifiedAIClient.isAnyProviderConfigured();
      const info = unifiedAIClient.getActiveProviderInfo();
      
      setIsConfigured(configured);
      setActiveProvider(info?.name || null);
    };

    checkStatus();
    
    // Check status periodically
    const interval = setInterval(checkStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  return {
    isConfigured,
    activeProvider
  };
}