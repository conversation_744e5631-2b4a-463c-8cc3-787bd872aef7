import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import type { Task, InsertionZone } from '@/lib/types';
import { debounce, debounceResize, debounceScroll } from '@/lib/utils/debounce';
import { useInsertionPerformanceMonitor } from '@/lib/utils/insertionPerformanceMonitor';
import { zoneCacheUtils } from '@/lib/utils/insertionZoneCache';

interface UseInsertionZonesOptions {
  task: Task;
  level: number;
  parentId: string | null;
  taskElement: HTMLElement | null;
  enableCaching?: boolean;
  cacheTimeout?: number;
}

interface ZoneCache {
  zones: InsertionZone[];
  timestamp: number;
  elementBounds: DOMRect;
}

// Global cache for insertion zones to optimize performance
const zoneCache = new Map<string, ZoneCache>();
const CACHE_TIMEOUT = 5000; // 5 seconds cache timeout
const ZONE_HEIGHT = 20; // Height of insertion zones in pixels
const ZONE_OFFSET = 10; // Offset from task boundaries
const TOUCH_ZONE_HEIGHT = 44; // Minimum touch target height for mobile
const TOUCH_ZONE_OFFSET = 15; // Larger offset for touch devices

/**
 * Custom hook to calculate insertion zones for a task
 * Provides hover zones for task insertion with caching optimization and performance monitoring
 */
export function useInsertionZones({
  task,
  level,
  parentId,
  taskElement,
  enableCaching = true,
  cacheTimeout = CACHE_TIMEOUT
}: UseInsertionZonesOptions) {
  const [zones, setZones] = useState<InsertionZone[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);
  const calculationTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCalculationRef = useRef<number>(0);
  const performanceMonitor = useInsertionPerformanceMonitor(`useInsertionZones-${task.id}`);
  
  // Detect if we're on a touch device
  const isTouchDevice = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  // Generate cache key based on task properties and DOM state
  const cacheKey = useMemo(() => {
    if (!taskElement) return null;
    const bounds = taskElement.getBoundingClientRect();
    return zoneCacheUtils.generateKey(task.id, level, bounds, task.subtasks?.length || 0);
  }, [task.id, level, taskElement, task.subtasks?.length]);

  /**
   * Calculate insertion zones for the current task with performance monitoring
   */
  const calculateZones = useCallback((): InsertionZone[] => {
    if (!taskElement) return [];

    // Start performance monitoring
    const operationId = performanceMonitor.startZoneCalculation(task.id, level);
    setIsCalculating(true);
    
    try {
      const taskRect = taskElement.getBoundingClientRect();
      const zones: InsertionZone[] = [];
      
      // Use touch-friendly dimensions if on touch device
      const zoneHeight = isTouchDevice ? TOUCH_ZONE_HEIGHT : ZONE_HEIGHT;
      const zoneOffset = isTouchDevice ? TOUCH_ZONE_OFFSET : ZONE_OFFSET;
      const zoneExpansion = isTouchDevice ? 10 : 0; // Expand zone width on touch devices

      // Zone 1: Before task (above the task)
      zones.push({
        id: `before-${task.id}`,
        type: 'before',
        bounds: new DOMRect(
          taskRect.left - zoneExpansion,
          taskRect.top - zoneOffset,
          taskRect.width + (zoneExpansion * 2),
          zoneHeight
        ),
        targetTaskId: task.id,
        parentId: parentId,
        level: level,
        metadata: {
          isVisible: true,
          priority: 1,
          touchFriendly: isTouchDevice
        }
      });

      // Zone 2: After task (below the task, but before subtasks if any)
      const afterZoneTop = task.subtasks && task.subtasks.length > 0 
        ? taskRect.bottom // If has subtasks, place after zone right after task header
        : taskRect.bottom - zoneOffset; // If no subtasks, place with offset

      zones.push({
        id: `after-${task.id}`,
        type: 'after',
        bounds: new DOMRect(
          taskRect.left - zoneExpansion,
          afterZoneTop,
          taskRect.width + (zoneExpansion * 2),
          zoneHeight
        ),
        targetTaskId: task.id,
        parentId: parentId,
        level: level,
        metadata: {
          isVisible: true,
          priority: 2,
          touchFriendly: isTouchDevice
        }
      });

      // Zone 3: Between parent and first subtask (if subtasks exist)
      if (task.subtasks && task.subtasks.length > 0) {
        // Find the first subtask element
        const firstSubtaskElement = taskElement.querySelector(`[data-task-id="${task.subtasks[0].id}"]`) as HTMLElement;
        
        if (firstSubtaskElement) {
          const firstSubtaskRect = firstSubtaskElement.getBoundingClientRect();
          const gapHeight = firstSubtaskRect.top - taskRect.bottom;
          const minRequiredHeight = isTouchDevice ? TOUCH_ZONE_HEIGHT : ZONE_HEIGHT;
          
          // Only create zone if there's sufficient gap between parent and first subtask
          if (gapHeight >= minRequiredHeight) {
            zones.push({
              id: `between-${task.id}-${task.subtasks[0].id}`,
              type: 'between_parent_child',
              bounds: new DOMRect(
                taskRect.left + (isTouchDevice ? 10 : 20), // Less indent on touch for easier access
                taskRect.bottom + 5,
                taskRect.width - (isTouchDevice ? 10 : 20),
                Math.min(gapHeight - 10, zoneHeight)
              ),
              targetTaskId: task.id,
              parentId: task.id, // Parent is the current task for this insertion type
              level: level + 1, // One level deeper than current task
              metadata: {
                isVisible: true,
                priority: 3,
                touchFriendly: isTouchDevice
              }
            });
          }
        }
      }

      // End performance monitoring
      performanceMonitor.endZoneCalculation(operationId, 1, level, false);
      lastCalculationRef.current = Date.now();
      
      return zones;
    } catch (error) {
      console.error('Error calculating insertion zones:', error);
      performanceMonitor.endZoneCalculation(operationId, 1, level, false);
      return [];
    } finally {
      setIsCalculating(false);
    }
  }, [task, level, parentId, taskElement, isTouchDevice, performanceMonitor]);

  /**
   * Get zones from cache if available and valid with performance tracking
   */
  const getCachedZones = useCallback((): InsertionZone[] | null => {
    if (!enableCaching || !cacheKey || !taskElement) return null;

    const elementBounds = taskElement.getBoundingClientRect();
    const cached = zoneCacheUtils.get(cacheKey, elementBounds);
    
    if (cached) {
      // Record cache hit
      performanceMonitor.endZoneCalculation('cache-hit', 1, level, true);
      return cached;
    }

    return null;
  }, [enableCaching, cacheKey, taskElement, performanceMonitor, level]);

  /**
   * Cache calculated zones with performance tracking
   */
  const cacheZones = useCallback((zones: InsertionZone[]) => {
    if (!enableCaching || !cacheKey || !taskElement) return;

    const elementBounds = taskElement.getBoundingClientRect();
    zoneCacheUtils.set(cacheKey, zones, elementBounds);
  }, [enableCaching, cacheKey, taskElement]);

  /**
   * Update zones with caching logic and throttling
   */
  const updateZones = useCallback(() => {
    if (!taskElement) {
      setZones([]);
      return;
    }

    // Throttle calculations to prevent excessive recalculation
    const now = Date.now();
    const timeSinceLastCalculation = now - lastCalculationRef.current;
    
    if (timeSinceLastCalculation < 16) { // Less than one frame
      // Schedule for next frame
      if (calculationTimeoutRef.current) {
        clearTimeout(calculationTimeoutRef.current);
      }
      calculationTimeoutRef.current = setTimeout(updateZones, 16);
      return;
    }

    // Try to get from cache first
    const cachedZones = getCachedZones();
    if (cachedZones) {
      setZones(cachedZones);
      return;
    }

    // Calculate new zones
    const newZones = calculateZones();
    setZones(newZones);
    
    // Cache the results
    cacheZones(newZones);
  }, [taskElement, getCachedZones, calculateZones, cacheZones]);

  /**
   * Debounced zone recalculation for performance with different delays for different events
   */
  const debouncedUpdateZones = useMemo(() => {
    return debounce(updateZones, 50, { leading: false, trailing: true });
  }, [updateZones]);

  const debouncedUpdateZonesResize = useMemo(() => {
    return debounceResize(() => {
      // Clear cache on resize as element positions may have changed
      if (cacheKey) {
        zoneCacheUtils.delete(cacheKey);
      }
      updateZones();
    }, 150);
  }, [updateZones, cacheKey]);

  const debouncedUpdateZonesScroll = useMemo(() => {
    return debounceScroll(() => {
      // Clear cache on scroll as element positions may have changed
      if (cacheKey) {
        zoneCacheUtils.delete(cacheKey);
      }
      updateZones();
    }, true); // Use RAF for scroll
  }, [updateZones, cacheKey]);

  // Effect to calculate zones when dependencies change
  useEffect(() => {
    updateZones();
  }, [updateZones]);

  // Effect to handle window resize and scroll events with optimized debouncing
  useEffect(() => {
    window.addEventListener('resize', debouncedUpdateZonesResize);
    window.addEventListener('scroll', debouncedUpdateZonesScroll, { passive: true });

    return () => {
      window.removeEventListener('resize', debouncedUpdateZonesResize);
      window.removeEventListener('scroll', debouncedUpdateZonesScroll);
      
      // Cleanup debounced functions
      debouncedUpdateZones.cancel();
      debouncedUpdateZonesResize();
      
      // Clear any pending calculation timeout
      if (calculationTimeoutRef.current) {
        clearTimeout(calculationTimeoutRef.current);
      }
    };
  }, [debouncedUpdateZonesResize, debouncedUpdateZonesScroll, debouncedUpdateZones]);

  /**
   * Find zone at specific coordinates
   */
  const findZoneAtPoint = useCallback((x: number, y: number): InsertionZone | null => {
    for (const zone of zones) {
      const { bounds } = zone;
      if (x >= bounds.left && 
          x <= bounds.right && 
          y >= bounds.top && 
          y <= bounds.bottom) {
        return zone;
      }
    }
    return null;
  }, [zones]);

  /**
   * Get zones filtered by type
   */
  const getZonesByType = useCallback((type: InsertionZone['type']): InsertionZone[] => {
    return zones.filter(zone => zone.type === type);
  }, [zones]);

  /**
   * Clear cache for this task
   */
  const clearCache = useCallback(() => {
    if (cacheKey) {
      zoneCacheUtils.delete(cacheKey);
    }
  }, [cacheKey]);

  /**
   * Force recalculation of zones (bypassing cache)
   */
  const forceRecalculate = useCallback(() => {
    clearCache();
    updateZones();
  }, [clearCache, updateZones]);

  return {
    zones,
    isCalculating,
    findZoneAtPoint,
    getZonesByType,
    clearCache,
    forceRecalculate,
    // Utility functions for external use
    updateZones: debouncedUpdateZones,
    // Performance monitoring
    getPerformanceMetrics: () => performanceMonitor.getComponentMetrics(),
    recordHoverEvent: () => performanceMonitor.recordHoverEvent()
  };
}

/**
 * Utility function to clear all cached zones
 * Useful for cleanup or when major layout changes occur
 */
export function clearAllZoneCache(): void {
  zoneCache.clear();
}

/**
 * Utility function to get cache statistics
 * Useful for debugging and performance monitoring
 */
export function getZoneCacheStats(): {
  size: number;
  keys: string[];
  oldestEntry: number | null;
  newestEntry: number | null;
} {
  const keys = Array.from(zoneCache.keys());
  const timestamps = Array.from(zoneCache.values()).map(cache => cache.timestamp);
  
  return {
    size: zoneCache.size,
    keys,
    oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : null,
    newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : null
  };
}

/**
 * Enhanced zone calculation for touch devices
 * Increases zone sizes and adjusts positioning for better touch interaction
 */
export function calculateTouchFriendlyZones(
  task: Task,
  level: number,
  parentId: string | null,
  taskElement: HTMLElement
): InsertionZone[] {
  if (!taskElement) return [];

  const taskRect = taskElement.getBoundingClientRect();
  const zones: InsertionZone[] = [];

  // Touch-friendly before zone
  zones.push({
    id: `touch-before-${task.id}`,
    type: 'before',
    bounds: new DOMRect(
      taskRect.left - 15,
      taskRect.top - TOUCH_ZONE_OFFSET,
      taskRect.width + 30,
      TOUCH_ZONE_HEIGHT
    ),
    targetTaskId: task.id,
    parentId: parentId,
    level: level,
    metadata: {
      isVisible: true,
      priority: 1,
      touchFriendly: true
    }
  });

  // Touch-friendly after zone
  zones.push({
    id: `touch-after-${task.id}`,
    type: 'after',
    bounds: new DOMRect(
      taskRect.left - 15,
      taskRect.bottom - TOUCH_ZONE_OFFSET,
      taskRect.width + 30,
      TOUCH_ZONE_HEIGHT
    ),
    targetTaskId: task.id,
    parentId: parentId,
    level: level,
    metadata: {
      isVisible: true,
      priority: 2,
      touchFriendly: true
    }
  });

  // Touch-friendly between parent and child zone
  if (task.subtasks && task.subtasks.length > 0) {
    const firstSubtaskElement = taskElement.querySelector(`[data-task-id="${task.subtasks[0].id}"]`) as HTMLElement;
    
    if (firstSubtaskElement) {
      const firstSubtaskRect = firstSubtaskElement.getBoundingClientRect();
      const gapHeight = firstSubtaskRect.top - taskRect.bottom;
      
      if (gapHeight >= TOUCH_ZONE_HEIGHT) {
        zones.push({
          id: `touch-between-${task.id}-${task.subtasks[0].id}`,
          type: 'between_parent_child',
          bounds: new DOMRect(
            taskRect.left + 10,
            taskRect.bottom + 5,
            taskRect.width - 10,
            Math.min(gapHeight - 10, TOUCH_ZONE_HEIGHT)
          ),
          targetTaskId: task.id,
          parentId: task.id,
          level: level + 1,
          metadata: {
            isVisible: true,
            priority: 3,
            touchFriendly: true
          }
        });
      }
    }
  }

  return zones;
}