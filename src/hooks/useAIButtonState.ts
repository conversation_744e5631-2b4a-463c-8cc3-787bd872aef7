import { useState, useEffect } from 'react';
import { useNetworkStatus } from './useNetworkStatus';
import { offlineAwareAIClient, AIButtonState } from '@/lib/ai/offlineAwareClient';

export interface UseAIButtonStateOptions {
  operation?: string;
  isLoading?: boolean;
}

export function useAIButtonState(options: UseAIButtonStateOptions = {}) {
  const { operation = 'default', isLoading = false } = options;
  const networkStatus = useNetworkStatus();
  const [buttonState, setButtonState] = useState<AIButtonState>({
    enabled: true,
    loading: false,
    tooltip: 'AI-Unterstützung verfügbar',
    variant: 'default'
  });

  useEffect(() => {
    const newState = offlineAwareAIClient.getAIButtonState(isLoading);
    setButtonState(newState);
  }, [networkStatus.isOnline, isLoading]);

  const getOfflineMessage = () => {
    return offlineAwareAIClient.getOfflineMessage(operation);
  };

  const canUseAI = () => {
    return networkStatus.isOnline && !isLoading;
  };

  return {
    buttonState,
    canUseAI: canUseAI(),
    isOnline: networkStatus.isOnline,
    isLoading,
    getOfflineMessage,
    networkStatus
  };
}