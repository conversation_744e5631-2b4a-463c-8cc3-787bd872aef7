import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    if (typeof window === 'undefined') {
      setIsMobile(false)
      return
    }
    const supportsMatchMedia = typeof window.matchMedia === 'function'
    const mql = supportsMatchMedia ? window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`) : null
    const onChange = () => {
      try {
        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
      } catch (_) {
        setIsMobile(false)
      }
    }
    if (mql && typeof mql.addEventListener === 'function') {
      mql.addEventListener("change", onChange)
    } else if (mql && typeof (mql as any).addListener === 'function') {
      ;(mql as any).addListener(onChange)
    }
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => {
      if (mql && typeof mql.removeEventListener === 'function') {
        mql.removeEventListener("change", onChange)
      } else if (mql && typeof (mql as any).removeListener === 'function') {
        ;(mql as any).removeListener(onChange)
      }
    }
  }, [])

  return !!isMobile
}
