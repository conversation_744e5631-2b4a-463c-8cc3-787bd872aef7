import { useState, useEffect, useCallback, useRef } from 'react';
import type { Task, InsertionPosition, InsertionState } from '@/lib/types';

export interface KeyboardShortcuts {
  insertAfter: string; // Default: 'Enter'
  insertBefore: string; // Default: 'Shift+Enter'
  insertSubtask: string; // Default: 'Tab+Enter'
  insertBetweenParentChild: string; // Default: 'Ctrl+Enter'
}

export interface FocusedTaskInfo {
  taskId: string;
  task: Task;
  level: number;
  parentId: string | null;
  element: HTMLElement;
}

export interface KeyboardInsertionOptions {
  tasks: Task[];
  shortcuts?: Partial<KeyboardShortcuts>;
  onInsertTask: (position: InsertionPosition) => void;
  enabled?: boolean;
  debugMode?: boolean;
  insertionState?: InsertionState;
  onInsertionStateChange?: (updates: Partial<InsertionState>) => void;
}

const DEFAULT_SHORTCUTS: KeyboardShortcuts = {
  insertAfter: 'Enter',
  insertBefore: 'Shift+Enter',
  insertSubtask: 'Tab+Enter',
  insertBetweenParentChild: 'Ctrl+Enter'
};

/**
 * Announce message to screen readers with enhanced timing and priority
 */
const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  // Check if we're in a browser environment
  if (typeof document === 'undefined') return;
  
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement with appropriate delay
  const delay = priority === 'assertive' ? 2000 : 1500;
  setTimeout(() => {
    if (document.body.contains(announcement)) {
      document.body.removeChild(announcement);
    }
  }, delay);
};

/**
 * Custom hook for keyboard-based task insertion
 * Provides focus management and keyboard shortcuts for task insertion
 */
export function useKeyboardInsertion({
  tasks,
  shortcuts = {},
  onInsertTask,
  enabled = true,
  debugMode = false,
  insertionState,
  onInsertionStateChange
}: KeyboardInsertionOptions) {
  const [focusedTask, setFocusedTask] = useState<FocusedTaskInfo | null>(null);
  const [availableInsertionPoints, setAvailableInsertionPoints] = useState<InsertionPosition[]>([]);
  const [currentInsertionIndex, setCurrentInsertionIndex] = useState(0);
  
  // Use insertion state if provided, otherwise fall back to local state
  const keyboardMode = insertionState?.keyboardMode ?? false;
  const setKeyboardMode = useCallback((mode: boolean) => {
    if (onInsertionStateChange) {
      onInsertionStateChange({ keyboardMode: mode });
    }
  }, [onInsertionStateChange]);
  
  // Refs for managing focus and event listeners
  const focusTimeoutRef = useRef<NodeJS.Timeout>();
  const lastKeyPressRef = useRef<number>(0);
  
  // Merge default shortcuts with provided shortcuts
  const activeShortcuts: KeyboardShortcuts = { ...DEFAULT_SHORTCUTS, ...shortcuts };

  /**
   * Find task by ID recursively
   */
  const findTaskById = useCallback((taskId: string, taskList: Task[] = tasks, level = 0, parentId: string | null = null): { task: Task; level: number; parentId: string | null } | null => {
    for (const task of taskList) {
      if (task.id === taskId) {
        return { task, level, parentId };
      }
      if (task.subtasks && task.subtasks.length > 0) {
        const found = findTaskById(taskId, task.subtasks, level + 1, task.id);
        if (found) return found;
      }
    }
    return null;
  }, [tasks]);

  /**
   * Get all focusable task elements in the DOM
   */
  const getFocusableTaskElements = useCallback((): HTMLElement[] => {
    // Check if we're in a browser environment
    if (typeof document === 'undefined') return [];
    
    const elements = document.querySelectorAll('[data-task-id]') as NodeListOf<HTMLElement>;
    return Array.from(elements).filter(el => {
      // Filter out elements that are not actually focusable or visible
      const rect = el.getBoundingClientRect();
      return rect.width > 0 && rect.height > 0 && !el.hasAttribute('disabled');
    });
  }, []);

  /**
   * Calculate available insertion points for the currently focused task
   */
  const calculateInsertionPoints = useCallback((taskInfo: FocusedTaskInfo): InsertionPosition[] => {
    const points: InsertionPosition[] = [];
    const { taskId, level, parentId, task } = taskInfo;

    // Always allow insertion after current task (same level)
    points.push({
      type: 'after',
      targetTaskId: taskId,
      parentId: parentId,
      level: level
    });

    // Allow insertion before current task (same level)
    points.push({
      type: 'before',
      targetTaskId: taskId,
      parentId: parentId,
      level: level
    });

    // Allow insertion as subtask if task can have subtasks
    points.push({
      type: 'between_parent_child',
      targetTaskId: taskId,
      parentId: taskId,
      level: level + 1
    });

    return points;
  }, []);

  /**
   * Update focused task information
   */
  const updateFocusedTask = useCallback((taskId: string) => {
    const taskInfo = findTaskById(taskId);
    if (!taskInfo) {
      if (debugMode) console.log('Task not found:', taskId);
      return;
    }

    const element = document.querySelector(`[data-task-id="${taskId}"]`) as HTMLElement;
    if (!element) {
      if (debugMode) console.log('Task element not found:', taskId);
      return;
    }

    const focusedTaskInfo: FocusedTaskInfo = {
      taskId,
      task: taskInfo.task,
      level: taskInfo.level,
      parentId: taskInfo.parentId,
      element
    };

    setFocusedTask(focusedTaskInfo);
    
    // Calculate available insertion points
    const insertionPoints = calculateInsertionPoints(focusedTaskInfo);
    setAvailableInsertionPoints(insertionPoints);
    setCurrentInsertionIndex(0);

    // Announce available insertion points to screen readers with enhanced descriptions
    const insertionTypesText = insertionPoints.map((point, index) => {
      const description = point.type === 'before' ? 'before current task' :
                         point.type === 'after' ? 'after current task' :
                         'as subtask of current task';
      return `${index + 1}: ${description}`;
    }).join(', ');
    
    const shortcutHints = [
      'Enter for after',
      'Shift+Enter for before', 
      'Tab+Enter for subtask',
      'Arrow keys to navigate insertion points'
    ].join(', ');
    
    announceToScreenReader(
      `Task focused: "${taskInfo.task.title}" at level ${taskInfo.level + 1}. ` +
      `Available insertion points: ${insertionTypesText}. ` +
      `Keyboard shortcuts: ${shortcutHints}.`,
      'polite'
    );

    if (debugMode) {
      console.log('Focused task updated:', {
        taskId,
        level: taskInfo.level,
        parentId: taskInfo.parentId,
        insertionPoints: insertionPoints.length
      });
    }
  }, [findTaskById, calculateInsertionPoints, debugMode]);

  /**
   * Check if a key combination matches a shortcut
   */
  const matchesShortcut = useCallback((event: KeyboardEvent, shortcut: string): boolean => {
    const keys = shortcut.split('+').map(k => k.trim().toLowerCase());
    const pressedKeys: string[] = [];

    // Add modifier keys
    if (event.ctrlKey) pressedKeys.push('ctrl');
    if (event.shiftKey) pressedKeys.push('shift');
    if (event.altKey) pressedKeys.push('alt');
    if (event.metaKey) pressedKeys.push('meta');

    // Add the main key
    pressedKeys.push(event.key.toLowerCase());

    // Check if all required keys are pressed
    return keys.every(key => pressedKeys.includes(key)) && 
           keys.length === pressedKeys.length;
  }, []);

  /**
   * Handle keyboard shortcuts for task insertion
   */
  const handleKeyboardShortcut = useCallback((event: KeyboardEvent) => {
    if (!enabled || !focusedTask) return;

    // Prevent shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || 
        target.tagName === 'TEXTAREA' || 
        target.contentEditable === 'true' ||
        target.closest('[contenteditable="true"]')) {
      return;
    }

    let insertionPosition: InsertionPosition | null = null;

    // Check each shortcut
    if (matchesShortcut(event, activeShortcuts.insertAfter)) {
      insertionPosition = {
        type: 'after',
        targetTaskId: focusedTask.taskId,
        parentId: focusedTask.parentId,
        level: focusedTask.level
      };
    } else if (matchesShortcut(event, activeShortcuts.insertBefore)) {
      insertionPosition = {
        type: 'before',
        targetTaskId: focusedTask.taskId,
        parentId: focusedTask.parentId,
        level: focusedTask.level
      };
    } else if (matchesShortcut(event, activeShortcuts.insertSubtask)) {
      insertionPosition = {
        type: 'between_parent_child',
        targetTaskId: focusedTask.taskId,
        parentId: focusedTask.taskId,
        level: focusedTask.level + 1
      };
    } else if (matchesShortcut(event, activeShortcuts.insertBetweenParentChild)) {
      // This is the same as insertSubtask for now, but could be different in the future
      insertionPosition = {
        type: 'between_parent_child',
        targetTaskId: focusedTask.taskId,
        parentId: focusedTask.taskId,
        level: focusedTask.level + 1
      };
    }

    if (insertionPosition) {
      event.preventDefault();
      event.stopPropagation();
      
      // Announce the insertion action to screen readers with enhanced feedback
      const insertionTypeText = insertionPosition.type === 'before' ? 'before current task' :
                               insertionPosition.type === 'after' ? 'after current task' :
                               'as subtask of current task';
      
      const shortcutUsed = Object.entries(activeShortcuts).find(([_, shortcut]) => 
        matchesShortcut(event, shortcut)
      )?.[0] || 'unknown';
      
      announceToScreenReader(
        `Inserting new task ${insertionTypeText} using ${shortcutUsed} shortcut. ` +
        `New task will be created at level ${insertionPosition.level + 1}.`,
        'assertive'
      );
      
      if (debugMode) {
        console.log('Keyboard insertion triggered:', insertionPosition);
      }
      
      onInsertTask(insertionPosition);
      setKeyboardMode(true);
      
      // Reset keyboard mode after a short delay with announcement
      setTimeout(() => {
        setKeyboardMode(false);
        announceToScreenReader('Task insertion completed. Focus returned to normal navigation.', 'polite');
      }, 2000);
    }
  }, [enabled, focusedTask, activeShortcuts, matchesShortcut, onInsertTask, debugMode]);

  /**
   * Handle arrow key navigation between insertion points
   */
  const handleArrowKeyNavigation = useCallback((event: KeyboardEvent) => {
    if (!enabled || !keyboardMode || availableInsertionPoints.length === 0) return;

    // Only handle arrow keys when in keyboard mode
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      event.preventDefault();
      
      const direction = event.key === 'ArrowUp' ? -1 : 1;
      const newIndex = Math.max(0, Math.min(
        availableInsertionPoints.length - 1,
        currentInsertionIndex + direction
      ));
      
      setCurrentInsertionIndex(newIndex);
      
      // Announce the current insertion point to screen readers
      const currentPoint = availableInsertionPoints[newIndex];
      if (currentPoint) {
        const insertionTypeText = currentPoint.type === 'before' ? 'before current task' :
                                 currentPoint.type === 'after' ? 'after current task' :
                                 'as subtask of current task';
        announceToScreenReader(
          `Insertion point ${newIndex + 1} of ${availableInsertionPoints.length}: ${insertionTypeText} ` +
          `at level ${currentPoint.level + 1}. Press Enter to insert, Escape to cancel.`,
          'assertive'
        );
      }
      
      if (debugMode) {
        console.log('Arrow navigation:', {
          direction,
          newIndex,
          totalPoints: availableInsertionPoints.length
        });
      }
    } else if (event.key === 'Enter' && keyboardMode) {
      // Execute insertion at current index
      event.preventDefault();
      const insertionPoint = availableInsertionPoints[currentInsertionIndex];
      if (insertionPoint) {
        const insertionTypeText = insertionPoint.type === 'before' ? 'before current task' :
                                 insertionPoint.type === 'after' ? 'after current task' :
                                 'as subtask of current task';
        announceToScreenReader(
          `Inserting new task ${insertionTypeText} at level ${insertionPoint.level + 1}. Task insertion completed.`,
          'assertive'
        );
        onInsertTask(insertionPoint);
        setKeyboardMode(false);
      }
    } else if (event.key === 'Escape' && keyboardMode) {
      // Cancel keyboard mode
      event.preventDefault();
      announceToScreenReader('Insertion mode cancelled. Focus returned to normal navigation.', 'polite');
      setKeyboardMode(false);
    }
  }, [enabled, keyboardMode, availableInsertionPoints, currentInsertionIndex, onInsertTask, debugMode]);

  /**
   * Handle focus events on task elements
   */
  const handleFocusEvent = useCallback((event: FocusEvent) => {
    if (!enabled) return;

    const target = event.target as HTMLElement;
    const taskElement = target.closest('[data-task-id]') as HTMLElement;
    
    if (taskElement) {
      const taskId = taskElement.getAttribute('data-task-id');
      if (taskId) {
        // Debounce focus updates to avoid excessive recalculation
        clearTimeout(focusTimeoutRef.current);
        focusTimeoutRef.current = setTimeout(() => {
          updateFocusedTask(taskId);
        }, 100);
      }
    }
  }, [enabled, updateFocusedTask]);

  /**
   * Handle click events to update focus
   */
  const handleClickEvent = useCallback((event: MouseEvent) => {
    if (!enabled) return;

    const target = event.target as HTMLElement;
    const taskElement = target.closest('[data-task-id]') as HTMLElement;
    
    if (taskElement) {
      const taskId = taskElement.getAttribute('data-task-id');
      if (taskId) {
        updateFocusedTask(taskId);
      }
    }
  }, [enabled, updateFocusedTask]);

  /**
   * Ensure task elements have proper focus management attributes
   */
  const ensureFocusManagement = useCallback(() => {
    const taskElements = getFocusableTaskElements();
    
    taskElements.forEach((element, index) => {
      // Ensure all task elements are focusable
      if (!element.hasAttribute('tabindex')) {
        element.setAttribute('tabindex', '0');
      }
      
      // Add role if not present
      if (!element.hasAttribute('role')) {
        element.setAttribute('role', 'listitem');
      }
      
      // Add enhanced aria-label if not present
      const taskId = element.getAttribute('data-task-id');
      const existingLabel = element.getAttribute('aria-label');
      if (taskId && !existingLabel) {
        const taskInfo = findTaskById(taskId);
        if (taskInfo) {
          const subtaskCount = taskInfo.task.subtasks?.length || 0;
          const subtaskText = subtaskCount > 0 ? ` Has ${subtaskCount} subtask${subtaskCount === 1 ? '' : 's'}.` : '';
          const completedText = taskInfo.task.completed ? ' Completed.' : '';
          element.setAttribute(
            'aria-label', 
            `Task: ${taskInfo.task.title}. Level ${taskInfo.level + 1}.${subtaskText}${completedText}`
          );
        }
      }
      
      // Add keyboard navigation hints
      const existingDescription = element.getAttribute('aria-describedby');
      if (!existingDescription) {
        element.setAttribute('aria-describedby', 'keyboard-insertion-help');
      }
    });
    
    // Create or update global keyboard help with enhanced instructions
    let helpElement = document.getElementById('keyboard-insertion-help');
    if (!helpElement) {
      helpElement = document.createElement('div');
      helpElement.id = 'keyboard-insertion-help';
      helpElement.className = 'sr-only';
      helpElement.textContent = 
        'Keyboard insertion shortcuts: Enter to insert after current task, Shift+Enter to insert before, ' +
        'Tab+Enter to insert as subtask. Use arrow keys to navigate between insertion points when in insertion mode. ' +
        'Press Escape to cancel insertion mode. Focus on a task first to enable insertion shortcuts.';
      document.body.appendChild(helpElement);
    }
  }, [getFocusableTaskElements, findTaskById]);

  /**
   * Navigate to next/previous focusable task
   */
  const navigateToTask = useCallback((direction: 'next' | 'previous') => {
    const focusableElements = getFocusableTaskElements();
    if (focusableElements.length === 0) return;

    let currentIndex = -1;
    if (focusedTask) {
      currentIndex = focusableElements.findIndex(el => 
        el.getAttribute('data-task-id') === focusedTask.taskId
      );
    }

    const nextIndex = direction === 'next' 
      ? (currentIndex + 1) % focusableElements.length
      : (currentIndex - 1 + focusableElements.length) % focusableElements.length;

    const nextElement = focusableElements[nextIndex];
    const taskId = nextElement.getAttribute('data-task-id');
    
    if (taskId) {
      updateFocusedTask(taskId);
      nextElement.focus();
      
      if (debugMode) {
        console.log('Navigated to task:', { direction, taskId, index: nextIndex });
      }
    }
  }, [focusedTask, getFocusableTaskElements, updateFocusedTask, debugMode]);

  // Set up event listeners and focus management
  useEffect(() => {
    if (!enabled || typeof document === 'undefined') return;

    // Ensure proper focus management attributes
    ensureFocusManagement();

    // Keyboard event listeners
    document.addEventListener('keydown', handleKeyboardShortcut);
    document.addEventListener('keydown', handleArrowKeyNavigation);
    
    // Focus event listeners
    document.addEventListener('focusin', handleFocusEvent);
    document.addEventListener('click', handleClickEvent);

    return () => {
      if (typeof document !== 'undefined') {
        document.removeEventListener('keydown', handleKeyboardShortcut);
        document.removeEventListener('keydown', handleArrowKeyNavigation);
        document.removeEventListener('focusin', handleFocusEvent);
        document.removeEventListener('click', handleClickEvent);
      }
      
      // Clear timeout
      if (focusTimeoutRef.current) {
        clearTimeout(focusTimeoutRef.current);
      }
      
      // Clean up global help element
      const helpElement = document.getElementById('keyboard-insertion-help');
      if (helpElement) {
        document.body.removeChild(helpElement);
      }
    };
  }, [enabled, handleKeyboardShortcut, handleArrowKeyNavigation, handleFocusEvent, handleClickEvent, ensureFocusManagement]);

  // Update insertion points when tasks change
  useEffect(() => {
    if (focusedTask) {
      const insertionPoints = calculateInsertionPoints(focusedTask);
      setAvailableInsertionPoints(insertionPoints);
      setCurrentInsertionIndex(0);
    }
  }, [focusedTask, calculateInsertionPoints, tasks]);

  return {
    // State
    focusedTask,
    keyboardMode,
    availableInsertionPoints,
    currentInsertionIndex,
    
    // Actions
    updateFocusedTask,
    navigateToTask,
    setKeyboardMode,
    
    // Utilities
    activeShortcuts,
    
    // For debugging
    ...(debugMode && {
      debug: {
        focusableElements: getFocusableTaskElements(),
        lastKeyPress: lastKeyPressRef.current
      }
    })
  };
}

/**
 * Utility function to check if keyboard shortcuts are available
 */
export function getAvailableShortcuts(): KeyboardShortcuts {
  return { ...DEFAULT_SHORTCUTS };
}

/**
 * Utility function to format shortcut for display
 */
export function formatShortcutForDisplay(shortcut: string): string {
  return shortcut
    .split('+')
    .map(key => {
      switch (key.toLowerCase()) {
        case 'ctrl': return '⌃';
        case 'shift': return '⇧';
        case 'alt': return '⌥';
        case 'meta': return '⌘';
        case 'enter': return '↵';
        case 'tab': return '⇥';
        default: return key.toUpperCase();
      }
    })
    .join('');
}

/**
 * Hook for managing keyboard insertion in a specific component
 * Provides a simpler interface for components that only need basic keyboard insertion
 */
export function useSimpleKeyboardInsertion(
  onInsertAfter: () => void,
  onInsertBefore?: () => void,
  onInsertSubtask?: () => void,
  enabled = true
) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Prevent shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || 
        target.tagName === 'TEXTAREA' || 
        target.contentEditable === 'true') {
      return;
    }

    if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.altKey) {
      event.preventDefault();
      onInsertAfter();
    } else if (event.key === 'Enter' && event.shiftKey) {
      event.preventDefault();
      onInsertBefore?.();
    } else if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault();
      onInsertSubtask?.();
    }
  }, [enabled, onInsertAfter, onInsertBefore, onInsertSubtask]);

  useEffect(() => {
    if (!enabled || typeof document === 'undefined') return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      if (typeof document !== 'undefined') {
        document.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, [enabled, handleKeyDown]);

  return {
    handleKeyDown
  };
}