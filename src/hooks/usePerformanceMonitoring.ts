import { useEffect, useCallback, useRef } from 'react';
import { performanceMonitor } from '@/lib/utils/performanceMonitor';

/**
 * Hook for integrating performance monitoring into React components
 */
export function usePerformanceMonitoring() {
  const timingRef = useRef<Map<string, () => void>>(new Map());

  // Start timing for a specific operation
  const startTiming = useCallback((label: string) => {
    const endTiming = performanceMonitor.startTiming(label);
    timingRef.current.set(label, endTiming);
    return endTiming;
  }, []);

  // End timing for a specific operation
  const endTiming = useCallback((label: string) => {
    const endFn = timingRef.current.get(label);
    if (endFn) {
      endFn();
      timingRef.current.delete(label);
    }
  }, []);

  // Record cache hit rate
  const recordCacheHitRate = useCallback((hits: number, total: number) => {
    performanceMonitor.recordCacheHitRate(hits, total);
  }, []);

  // Record memory usage
  const recordMemoryUsage = useCallback(() => {
    performanceMonitor.recordMemoryUsage();
  }, []);

  // Get current metrics
  const getMetrics = useCallback(() => {
    return performanceMonitor.getLatestMetrics();
  }, []);

  // Get performance report
  const generateReport = useCallback(() => {
    return performanceMonitor.generatePerformanceReport();
  }, []);

  // Enable automatic performance reporting
  useEffect(() => {
    performanceMonitor.enableAutoReporting(60000); // Report every minute
    
    return () => {
      performanceMonitor.disableAutoReporting();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // End any ongoing timings
      timingRef.current.forEach(endFn => endFn());
      timingRef.current.clear();
    };
  }, []);

  return {
    startTiming,
    endTiming,
    recordCacheHitRate,
    recordMemoryUsage,
    getMetrics,
    generateReport
  };
}

/**
 * Hook for monitoring component render performance
 */
export function useRenderPerformance(componentName: string) {
  const renderStartTime = useRef<number>(0);

  useEffect(() => {
    renderStartTime.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartTime.current;
    
    // Only log significant render times (>16ms for 60fps)
    if (renderTime > 16) {
      console.debug(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      
      // Record specific component render times
      if (componentName === 'TaskList') {
        const endTiming = performanceMonitor.startTiming('taskListRender');
        setTimeout(endTiming, 0);
      }
    }
  });

  return renderStartTime.current;
}

/**
 * Hook for monitoring async operations
 */
export function useAsyncPerformance() {
  const measureAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    label: string
  ): Promise<T> => {
    const endTiming = performanceMonitor.startTiming(label);
    
    try {
      const result = await operation();
      return result;
    } finally {
      endTiming();
    }
  }, []);

  return { measureAsync };
}