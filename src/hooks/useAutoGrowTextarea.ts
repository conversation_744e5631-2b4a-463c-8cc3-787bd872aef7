'use client';

import { useRef, useEffect, useCallback, RefObject } from 'react';

interface UseAutoGrowTextareaReturn {
  textareaRef: RefObject<HTMLTextAreaElement>;
  adjustHeight: () => void;
}

export function useAutoGrowTextarea(value: string): UseAutoGrowTextareaReturn {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Function to adjust textarea height
  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';
    
    // Set height to scrollHeight to fit content
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, []);

  // Adjust height when value changes
  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  // Adjust height on initial render
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Set initial styles for auto-growing behavior
    textarea.style.resize = 'none';
    textarea.style.overflow = 'hidden';
    textarea.style.minHeight = '2.5rem'; // Minimum height for usability
    
    // Initial height adjustment
    adjustHeight();

    // Add input event listener for real-time adjustment
    const handleInput = () => {
      adjustHeight();
    };

    textarea.addEventListener('input', handleInput);

    // Cleanup
    return () => {
      textarea.removeEventListener('input', handleInput);
    };
  }, [adjustHeight]);

  return {
    textareaRef,
    adjustHeight,
  };
}