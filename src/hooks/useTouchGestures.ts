import { useCallback, useRef, useState } from 'react';
import { useIsMobile } from './use-mobile';

export interface SwipeAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  color?: string;
  action: () => void;
}

export interface TouchGestureOptions {
  onSwipeLeft?: (distance: number) => void;
  onSwipeRight?: (distance: number) => void;
  onSwipeUp?: (distance: number) => void;
  onSwipeDown?: (distance: number) => void;
  onLongPress?: () => void;
  onTap?: () => void;
  swipeThreshold?: number;
  longPressThreshold?: number;
  enableHapticFeedback?: boolean;
}

export interface TouchGestureState {
  isActive: boolean;
  direction: 'left' | 'right' | 'up' | 'down' | null;
  distance: number;
  isLongPress: boolean;
}

export function useTouchGestures(options: TouchGestureOptions = {}) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onLongPress,
    onTap,
    swipeThreshold = 50,
    longPressThreshold = 500,
    enableHapticFeedback = true
  } = options;

  const isMobile = useIsMobile();
  const [gestureState, setGestureState] = useState<TouchGestureState>({
    isActive: false,
    direction: null,
    distance: 0,
    isLongPress: false
  });

  const touchStartX = useRef<number>(0);
  const touchStartY = useRef<number>(0);
  const touchStartTime = useRef<number>(0);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const triggerHapticFeedback = useCallback((type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!isMobile || !enableHapticFeedback) return;
    
    if ('vibrate' in navigator) {
      const patterns = {
        light: 10,
        medium: 20,
        heavy: 50
      };
      navigator.vibrate(patterns[type]);
    }
  }, [isMobile, enableHapticFeedback]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile) return;
    
    const touch = e.touches[0];
    touchStartX.current = touch.clientX;
    touchStartY.current = touch.clientY;
    touchStartTime.current = Date.now();
    
    setGestureState({
      isActive: true,
      direction: null,
      distance: 0,
      isLongPress: false
    });

    // Start long press timer
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        setGestureState(prev => ({ ...prev, isLongPress: true }));
        onLongPress();
        triggerHapticFeedback('medium');
      }, longPressThreshold);
    }
  }, [isMobile, onLongPress, longPressThreshold, triggerHapticFeedback]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !gestureState.isActive) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchStartX.current;
    const deltaY = touch.clientY - touchStartY.current;
    
    // Clear long press timer on movement
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    // Determine primary direction and distance
    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);
    
    let direction: 'left' | 'right' | 'up' | 'down' | null = null;
    let distance = 0;

    if (absX > absY) {
      // Horizontal swipe
      direction = deltaX > 0 ? 'right' : 'left';
      distance = absX;
    } else {
      // Vertical swipe
      direction = deltaY > 0 ? 'down' : 'up';
      distance = absY;
    }

    setGestureState(prev => ({
      ...prev,
      direction,
      distance
    }));

    // Call appropriate callback during movement
    if (distance > swipeThreshold) {
      switch (direction) {
        case 'left':
          onSwipeLeft?.(distance);
          break;
        case 'right':
          onSwipeRight?.(distance);
          break;
        case 'up':
          onSwipeUp?.(distance);
          break;
        case 'down':
          onSwipeDown?.(distance);
          break;
      }
    }
  }, [isMobile, gestureState.isActive, swipeThreshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown]);

  const handleTouchEnd = useCallback(() => {
    if (!isMobile) return;
    
    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    const { direction, distance, isLongPress } = gestureState;
    const touchDuration = Date.now() - touchStartTime.current;

    // Handle tap if no significant movement and not a long press
    if (distance < swipeThreshold && touchDuration < longPressThreshold && !isLongPress && onTap) {
      onTap();
      triggerHapticFeedback('light');
    }

    // Reset state
    setGestureState({
      isActive: false,
      direction: null,
      distance: 0,
      isLongPress: false
    });
  }, [isMobile, gestureState, swipeThreshold, longPressThreshold, onTap, triggerHapticFeedback]);

  const touchHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onTouchCancel: handleTouchEnd // Handle touch cancel same as touch end
  };

  return {
    touchHandlers,
    gestureState,
    triggerHapticFeedback,
    isMobile
  };
}

// Hook for pull-to-refresh functionality
export function usePullToRefresh(onRefresh: () => Promise<void>, options: {
  threshold?: number;
  enabled?: boolean;
} = {}) {
  const { threshold = 80, enabled = true } = options;
  const isMobile = useIsMobile();
  
  const [isPullActive, setIsPullActive] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const touchStartY = useRef<number>(0);
  const scrollElement = useRef<HTMLElement | null>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!isMobile || !enabled) return;
    
    const touch = e.touches[0];
    touchStartY.current = touch.clientY;
    
    // Only enable pull-to-refresh if we're at the top of the scroll container
    const element = scrollElement.current || e.currentTarget as HTMLElement;
    const scrollTop = element.scrollTop;
    
    if (scrollTop === 0) {
      setIsPullActive(true);
    }
  }, [isMobile, enabled]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPullActive || !isMobile) return;
    
    const touch = e.touches[0];
    const deltaY = touch.clientY - touchStartY.current;
    
    // Only allow pull down
    if (deltaY > 0) {
      e.preventDefault();
      const distance = Math.min(deltaY * 0.5, threshold * 1.5); // Damping effect
      setPullDistance(distance);
    }
  }, [isPullActive, isMobile, threshold]);

  const handleTouchEnd = useCallback(async () => {
    if (!isPullActive || !isMobile) return;
    
    setIsPullActive(false);
    
    if (pullDistance >= threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
        // Haptic feedback on successful refresh
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setPullDistance(0);
  }, [isPullActive, isMobile, pullDistance, threshold, onRefresh, isRefreshing]);

  const pullToRefreshHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd,
    onTouchCancel: handleTouchEnd
  };

  const setScrollElement = useCallback((element: HTMLElement | null) => {
    scrollElement.current = element;
  }, []);

  return {
    pullToRefreshHandlers,
    isPullActive,
    pullDistance,
    isRefreshing,
    threshold,
    setScrollElement,
    isMobile
  };
}