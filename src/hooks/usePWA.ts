'use client';

import { useEffect, useState, useCallback } from 'react';

interface PWAUpdateInfo {
  version?: string;
  releaseNotes?: string[];
  size?: number;
}

export function usePWA() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<PWAUpdateInfo>({});
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Check for updates manually
  const checkForUpdates = useCallback(async () => {
    if (registration) {
      try {
        await registration.update();
        console.log('Manual update check completed');
      } catch (error) {
        console.error('Manual update check failed:', error);
      }
    }
  }, [registration]);

  useEffect(() => {
    // Check if app is installed
    const checkInstalled = () => {
      if (typeof window !== 'undefined') {
        let isStandalone = false;
        try {
          if (typeof window.matchMedia === 'function') {
            const mm = window.matchMedia('(display-mode: standalone)');
            isStandalone = !!mm && !!mm.matches;
          }
          if (!isStandalone) {
            isStandalone = ((window.navigator as any)?.standalone === true);
          }
        } catch (_) {
          // ignore in non-browser environments
        }
        setIsInstalled(isStandalone);
      }
    };

    // Check online status
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine);
    };

    // Register service worker with enhanced update detection
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const reg = await navigator.serviceWorker.register('/sw.js', {
            updateViaCache: 'none' // Always check for updates
          });
          
          setRegistration(reg);

          // Check for updates immediately
          await reg.update();
          
          // Enhanced update detection
          const handleUpdateFound = () => {
            const newWorker = reg.installing;
            if (newWorker) {
              console.log('New service worker found, installing...');
              
              newWorker.addEventListener('statechange', () => {
                console.log('Service worker state changed:', newWorker.state);
                
                if (newWorker.state === 'installed') {
                  if (navigator.serviceWorker.controller) {
                    // New update available
                    console.log('New content available, update ready');
                    setUpdateAvailable(true);
                    setUpdateInfo({
                      version: 'Latest',
                      releaseNotes: [
                        'Verbesserte Performance und Stabilität',
                        'Neue Features und Bugfixes',
                        'Optimierte Offline-Funktionalität'
                      ]
                    });
                    
                    // Dispatch custom event
                    window.dispatchEvent(new CustomEvent('sw-update-available', {
                      detail: { registration: reg, newWorker }
                    }));
                  } else {
                    // First install
                    console.log('Content cached for offline use');
                  }
                }
              });
            }
          };

          // Listen for updates
          reg.addEventListener('updatefound', handleUpdateFound);

          // Check if there's already a waiting service worker
          if (reg.waiting) {
            console.log('Service worker already waiting');
            setUpdateAvailable(true);
          }

          // Listen for controlling service worker changes
          navigator.serviceWorker.addEventListener('controllerchange', () => {
            console.log('Controller changed, reloading page');
            if (isUpdating) {
              window.location.reload();
            }
          });

          // Periodic update checks (every 30 minutes)
          const updateInterval = setInterval(async () => {
            try {
              await reg.update();
            } catch (error) {
              console.error('Periodic update check failed:', error);
            }
          }, 30 * 60 * 1000);

          // Cleanup interval on unmount
          return () => clearInterval(updateInterval);

          console.log('Service Worker registered successfully');
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      }
    };

    // Listen for custom update events
    const handleUpdateAvailable = (event: CustomEvent) => {
      console.log('Update available event received:', event.detail);
      setUpdateAvailable(true);
    };

    // Listen for app focus to check for updates
    const handleVisibilityChange = () => {
      if (!document.hidden && registration) {
        checkForUpdates();
      }
    };

    checkInstalled();
    updateOnlineStatus();
    registerServiceWorker();

    // Event listeners
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    window.addEventListener('sw-update-available', handleUpdateAvailable as EventListener);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
      window.removeEventListener('sw-update-available', handleUpdateAvailable as EventListener);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [checkForUpdates, isUpdating, registration]);

  const updateApp = async () => {
    if (!registration) {
      throw new Error('Service Worker not registered');
    }

    setIsUpdating(true);

    try {
      if (registration.waiting) {
        // Tell the waiting service worker to skip waiting
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        
        // Wait for the controller to change
        return new Promise<void>((resolve) => {
          const handleControllerChange = () => {
            navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
            resolve();
          };
          navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
        });
      } else {
        // Force update check
        await registration.update();
        throw new Error('No update available');
      }
    } catch (error) {
      setIsUpdating(false);
      throw error;
    }
  };

  // Get update size estimate (mock implementation)
  const getUpdateSize = useCallback((): string => {
    // In a real implementation, this could be calculated from the service worker cache
    return '< 1 MB';
  }, []);

  return {
    isInstalled,
    isOnline,
    updateAvailable,
    updateInfo,
    isUpdating,
    updateApp,
    checkForUpdates,
    getUpdateSize,
    registration
  };
}