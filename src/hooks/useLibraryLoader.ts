'use client';

import { useState, useEffect, useCallback } from 'react';

export interface LibraryConfig {
  id: string;
  src: string;
  name: string;
}

interface UseLibraryLoaderReturn {
  libsLoaded: boolean;
  loadingError: string | null;
  reloadLibraries: () => Promise<void>;
}

export function useLibraryLoader(libraries: LibraryConfig[]): UseLibraryLoaderReturn {
  const [libsLoaded, setLibsLoaded] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Load a single library with retry mechanism
  const loadLibrary = useCallback(async (library: LibraryConfig, retries = 3): Promise<boolean> => {
    return new Promise((resolve) => {
      // Check if library is already loaded
      if (document.querySelector(`script[src="${library.src}"]`)) {
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = library.src;
      script.id = library.id;
      script.async = true;

      const attemptLoad = (attemptsLeft: number) => {
        script.onload = () => {
          resolve(true);
        };

        script.onerror = () => {
          if (attemptsLeft > 0) {
            // Remove failed script and retry
            script.remove();
            setTimeout(() => {
              const newScript = document.createElement('script');
              newScript.src = library.src;
              newScript.id = library.id;
              newScript.async = true;
              
              newScript.onload = script.onload;
              newScript.onerror = () => attemptLoad(attemptsLeft - 1);
              
              document.head.appendChild(newScript);
            }, 1000); // Wait 1 second before retry
          } else {
            resolve(false);
          }
        };
      };

      attemptLoad(retries);
      document.head.appendChild(script);
    });
  }, []);

  // Load all libraries
  const loadLibraries = useCallback(async () => {
    setLoadingError(null);
    setLibsLoaded(false);

    try {
      const loadPromises = libraries.map(library => loadLibrary(library));
      const results = await Promise.all(loadPromises);
      
      const failedLibraries = libraries.filter((_, index) => !results[index]);
      
      if (failedLibraries.length > 0) {
        const failedNames = failedLibraries.map(lib => lib.name).join(', ');
        setLoadingError(`Failed to load libraries: ${failedNames}`);
        setLibsLoaded(false);
      } else {
        setLibsLoaded(true);
      }
    } catch (error) {
      setLoadingError(`Error loading libraries: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLibsLoaded(false);
    }
  }, [libraries, loadLibrary]);

  // Reload libraries function
  const reloadLibraries = useCallback(async () => {
    // Remove existing scripts
    libraries.forEach(library => {
      const existingScript = document.getElementById(library.id);
      if (existingScript) {
        existingScript.remove();
      }
    });
    
    await loadLibraries();
  }, [libraries, loadLibraries]);

  // Load libraries on mount and when libraries change
  useEffect(() => {
    loadLibraries();
  }, [loadLibraries]);

  return {
    libsLoaded,
    loadingError,
    reloadLibraries,
  };
}