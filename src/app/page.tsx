"use client";

import { useState, useRef, useEffect, useCallback, useMemo, memo } from 'react';
import dynamic from 'next/dynamic';
import {
  BrainCircuit,
  ChevronDown,
  Eye,
  EyeOff,
  FileDown,
  Heart,
  HelpCircle,
  Info,
  Moon,
  Sparkles,
  Sun,
  X,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useKeyboardInsertion } from '@/hooks/useKeyboardInsertion';
import { MotivationManager, useMotivation, getMotivationClasses } from '@/lib/utils/motivationManager';
import { ToastManager, useEnhancedToast } from '@/lib/utils/toastManager';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { TaskList } from '@/components/TaskList';
import { MobileBottomNavigation, FloatingActionButton } from '@/components/MobileBottomNavigation';
import { MobileSettingsPanel } from '@/components/MobileSettingsPanel';
import { MobileTouchDemo } from '@/components/MobileTouchDemo';
import { useIsMobile } from '@/hooks/use-mobile';
import { ConnectionStatusIndicator } from '@/components/ConnectionStatusIndicator';
import { OfflineBanner } from '@/components/OfflineBanner';
import { AIRequestQueueStatus } from '@/components/AIRequestQueueStatus';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { offlineAwareAIClient } from '@/lib/ai/offlineAwareClient';
import type { Project, Task, ContextStrategies, LoadingStates, InsertionPosition, InsertionState, InsertionResult, InsertionZone } from '@/lib/types';
import { unifiedAIClient, contextBuilder } from '@/lib/ai';
import { SolveTaskModal } from '@/components/SolveTaskModal';
import { exportToPdf } from '@/lib/export/pdf';
import { exportToCsv } from '@/lib/export/csv';
import { exportToMarkdown } from '@/lib/export/markdown';
import { validateInsertionPosition } from '@/lib/utils/insertionValidation';
import { usePerformanceMonitoring, useAsyncPerformance } from '@/hooks/usePerformanceMonitoring';
import { handleInsertionValidation } from '@/lib/utils/insertionErrorHandler';
import { InsertionIntegration, TaskOperationIntegration, ExportIntegration } from '@/lib/utils/insertionIntegration';
import { usePWA } from '@/hooks/usePWA';
import { usePWAIntegration } from '@/hooks/usePWAIntegration';
import { serviceWorkerManager } from '@/lib/pwa/serviceWorker';
import { preloadCriticalResources } from '@/lib/utils/lazyLoading';
import { initializeSmartPreloading } from '@/lib/ai/lazyAI';
import { initializePerformanceOptimizations } from '@/lib/utils/performanceOptimization';

// Dynamically import PWA components to avoid SSR issues
const PWAInstallPrompt = dynamic(() => import('@/components/PWAInstallPrompt').then(mod => ({ default: mod.PWAInstallPrompt })), {
  ssr: false,
  loading: () => null
});

const PWAUpdateNotification = dynamic(() => import('@/components/PWAUpdateNotification').then(mod => ({ default: mod.PWAUpdateNotification })), {
  ssr: false,
  loading: () => null
});

const PWAHelpSystem = dynamic(() => import('@/components/PWAHelpSystem'), {
  ssr: false,
  loading: () => null
});

const PWAOnboarding = dynamic(() => import('@/components/PWAOnboarding'), {
  ssr: false,
  loading: () => null
});

// Import PWA Tooltips statically for now to avoid complex dynamic import issues
// const PWATooltips = dynamic(() => import('@/components/PWATooltips'), { ssr: false });

// Lazy load non-critical components
const LazyEnhancedExportDialog = dynamic(() => import('@/components/lazy/LazyExportComponents').then(mod => ({ default: mod.LazyEnhancedExportDialog })), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted h-8 w-32 rounded" />
});

const LazyImportDialog = dynamic(() => import('@/components/lazy/LazyExportComponents').then(mod => ({ default: mod.LazyImportDialog })), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted h-8 w-32 rounded" />
});

const LazySolveTaskModal = dynamic(() => import('@/components/lazy/LazyExportComponents').then(mod => ({ default: mod.LazySolveTaskModal })), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-muted h-64 w-full rounded" />
});

function Home() {
  const { toast } = useToast();
  const enhancedToast = useEnhancedToast();
  const motivation = useMotivation();
  const taskContainerRef = useRef<HTMLDivElement>(null);
  const { isOnline, updateAvailable, updateApp } = usePWA();
  const networkStatus = useNetworkStatus();
  const { startTiming, endTiming, recordMemoryUsage } = usePerformanceMonitoring();
  const { measureAsync } = useAsyncPerformance();
  
  // PWA Integration - using simplified version to avoid errors
  const pwaIntegration = usePWAIntegration();
  
  // PWA Help System
  const [showPWAHelp, setShowPWAHelp] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [mainProject, setMainProject] = useState('');
  const [mainProjectDescription, setMainProjectDescription] = useState('');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Service Worker state
  const [swRegistration, setSwRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [swUpdateAvailable, setSwUpdateAvailable] = useState(false);
  const [swInstalling, setSwInstalling] = useState(false);
  const [motivationState, setMotivationState] = useState(
    MotivationManager.getCurrentState()
  );
  const [theme, setTheme] = useState('dark');
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isExamplesVisible, setIsExamplesVisible] = useState(true);
  // New state management for enhanced functionality
  const [loading, setLoading] = useState<LoadingStates>({});
  const [contextStrategies, setContextStrategies] = useState<ContextStrategies>({
    strategy1: false,
    strategy2: true, // Default enabled as per requirements
    strategy3: false,
  });
  const [projectJustStarted, setProjectJustStarted] = useState(false);
  const [libsLoaded, setLibsLoaded] = useState(false);

  // Insertion state management
  const [insertionState, setInsertionState] = useState<InsertionState>({
    activeInsertionPoint: null,
    hoveredZone: null,
    keyboardMode: false,
    insertionHistory: [],
    lastCalculatedZones: new Map(),
    zoneCalculationCache: new Map()
  });

  // Loading states for insertion operations
  const [insertionLoading, setInsertionLoading] = useState<{
    [positionId: string]: boolean;
  }>({});

  // Mobile-specific state
  const isMobile = useIsMobile();
  const [mobileSettingsOpen, setMobileSettingsOpen] = useState(false);
  const [mobileSettings, setMobileSettings] = useState({
    hapticFeedback: true,
    swipeActions: true,
    compactMode: false,
    autoCollapse: true,
    pullToRefresh: true,
    touchFeedback: true
  });
  const [isAIContentVisible, setIsAIContentVisible] = useState(true);

  // Theme initialization - separate from client check to avoid hydration issues
  useEffect(() => {
    // Only run on client side to avoid hydration mismatch
    if (typeof window === 'undefined') return;

    // Initialize theme from localStorage or system preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      setTheme(savedTheme);
      document.documentElement.classList.toggle('dark', savedTheme === 'dark');
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const initialTheme = prefersDark ? 'dark' : 'light';
      setTheme(initialTheme);
      document.documentElement.classList.toggle('dark', initialTheme === 'dark');
    }
  }, []);

  // Performance optimization - preload critical resources and initialize smart preloading
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Preload critical resources
    preloadCriticalResources();

    // Initialize smart preloading based on user behavior
    const cleanupPreloading = initializeSmartPreloading();

    // Initialize performance optimizations
    const cleanupOptimizations = initializePerformanceOptimizations();

    return () => {
      cleanupPreloading?.();
      cleanupOptimizations?.();
    };
  }, []);

  // Service Worker registration and lifecycle management
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const registerServiceWorker = async () => {
      try {
        setSwInstalling(true);
        const registration = await serviceWorkerManager.register();
        
        if (registration) {
          setSwRegistration(registration);
          console.log('Service Worker registered successfully');
          
          // Show success toast
          toast({
            title: 'App bereit für Offline-Nutzung',
            description: 'Die App wurde erfolgreich für die Offline-Nutzung konfiguriert.',
          });
        }
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        toast({
          variant: 'destructive',
          title: 'Offline-Modus nicht verfügbar',
          description: 'Die App konnte nicht für die Offline-Nutzung konfiguriert werden.',
        });
      } finally {
        setSwInstalling(false);
      }
    };

    // Register service worker after a short delay to not block initial page load
    const timer = setTimeout(registerServiceWorker, 1000);

    return () => clearTimeout(timer);
  }, [toast]);

  // Process AI request queue when coming back online
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleOnline = async () => {
      console.log('Connection restored, processing AI request queue...');
      const queueLength = offlineAwareAIClient.getQueueLength();
      
      if (queueLength > 0) {
        toast({
          title: 'Verbindung wiederhergestellt',
          description: `${queueLength} wartende AI-Anfragen werden verarbeitet...`,
        });
        
        try {
          await offlineAwareAIClient.processQueue();
        } catch (error) {
          console.error('Error processing AI request queue:', error);
        }
      }
    };

    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [toast]);

  // Listen for service worker updates
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleSwUpdate = (event: CustomEvent) => {
      console.log('Service Worker update available');
      setSwUpdateAvailable(true);
      
      toast({
        title: 'App-Update verfügbar',
        description: 'Eine neue Version der App ist verfügbar.',
      });
    };

    const handleSwControllerChange = () => {
      console.log('Service Worker controller changed');
      toast({
        title: 'App aktualisiert',
        description: 'Die App wurde erfolgreich aktualisiert.',
      });
    };

    // Listen for custom update events
    window.addEventListener('sw-update-available', handleSwUpdate as EventListener);
    
    // Listen for controller changes (when update is applied)
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('controllerchange', handleSwControllerChange);
    }

    return () => {
      window.removeEventListener('sw-update-available', handleSwUpdate as EventListener);
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('controllerchange', handleSwControllerChange);
      }
    };
  }, [toast]);

  // Handle service worker update
  const handleSwUpdate = useCallback(async () => {
    if (!swRegistration) return;

    try {
      await serviceWorkerManager.forceUpdate();
      setSwUpdateAvailable(false);
    } catch (error) {
      console.error('Failed to update service worker:', error);
      toast({
        variant: 'destructive',
        title: 'Update fehlgeschlagen',
        description: 'Das App-Update konnte nicht angewendet werden.',
      });
    }
  }, [swRegistration, toast]);

  // Auto-scroll effect when project is started
  useEffect(() => {
    if (projectJustStarted && taskContainerRef.current) {
      const timer = setTimeout(() => {
        taskContainerRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
        setProjectJustStarted(false); // Reset the flag after scrolling
      }, 500); // Small delay to ensure tasks are rendered

      return () => clearTimeout(timer);
    }
  }, [projectJustStarted]);

  // Subscribe to motivation state changes
  useEffect(() => {
    const unsubscribe = MotivationManager.subscribe((state) => {
      setMotivationState(state);
    });

    return unsubscribe;
  }, []);

  const [solveModals, setSolveModals] = useState<Array<{
    id: string;
    isOpen: boolean;
    task: Task | null;
    isRefinement: boolean;
    selectionWrapperId: string;
  }>>([]);

  // Insertion state management functions - define before using in hooks
  const updateInsertionState = useCallback((updates: Partial<InsertionState>) => {
    setInsertionState(prev => ({ ...prev, ...updates }));
  }, []);

  // Helper functions for different insertion types - define before handleAddTaskAfter
  const insertTaskBefore = useCallback((tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
    if (!parentId) {
      // Insert at root level
      const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
      if (targetIndex !== -1) {
        const newTasks = [...tasks];
        newTasks.splice(targetIndex, 0, newTask);
        return newTasks;
      }
      // If not found at root, search recursively
      return tasks.map(task => ({
        ...task,
        subtasks: insertTaskBefore(task.subtasks || [], targetTaskId, newTask, parentId)
      }));
    } else {
      // Insert within specific parent's subtasks
      return tasks.map(task => {
        if (task.id === parentId) {
          const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
          if (targetIndex !== -1) {
            const newSubtasks = [...(task.subtasks || [])];
            newSubtasks.splice(targetIndex, 0, newTask);
            return { ...task, subtasks: newSubtasks };
          }
        }
        if (task.subtasks) {
          return { ...task, subtasks: insertTaskBefore(task.subtasks, targetTaskId, newTask, parentId) };
        }
        return task;
      });
    }
  }, []);

  const insertTaskAfter = useCallback((tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
    if (!parentId) {
      // Insert at root level
      const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
      if (targetIndex !== -1) {
        const newTasks = [...tasks];
        newTasks.splice(targetIndex + 1, 0, newTask);
        return newTasks;
      }
      // If not found at root, search recursively
      return tasks.map(task => ({
        ...task,
        subtasks: insertTaskAfter(task.subtasks || [], targetTaskId, newTask, parentId)
      }));
    } else {
      // Insert within specific parent's subtasks
      return tasks.map(task => {
        if (task.id === parentId) {
          const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
          if (targetIndex !== -1) {
            const newSubtasks = [...(task.subtasks || [])];
            newSubtasks.splice(targetIndex + 1, 0, newTask);
            return { ...task, subtasks: newSubtasks };
          }
        }
        if (task.subtasks) {
          return { ...task, subtasks: insertTaskAfter(task.subtasks, targetTaskId, newTask, parentId) };
        }
        return task;
      });
    }
  }, []);

  const insertTaskBetweenParentChild = useCallback((tasks: Task[], parentTaskId: string, newTask: Task): Task[] => {
    return tasks.map(task => {
      if (task.id === parentTaskId) {
        // Insert the new task at the beginning of the parent's subtasks
        // This places it "between" the parent and its first child
        return {
          ...task,
          subtasks: [newTask, ...(task.subtasks || [])]
        };
      }
      if (task.subtasks) {
        return {
          ...task,
          subtasks: insertTaskBetweenParentChild(task.subtasks, parentTaskId, newTask)
        };
      }
      return task;
    });
  }, []);

  // Define handleAddTaskAfter before using it in hooks
  const handleAddTaskAfter = useCallback((
    afterIdOrPosition: string | InsertionPosition,
    parentId: string | null = null,
    title = 'Neue Aufgabe (zum Bearbeiten klicken)',
    description = 'Beschreibung hinzufügen...'
  ) => {
    const newTask: Task = {
      id: crypto.randomUUID(),
      title,
      description,
      subtasks: [],
      content: '',
      status: 'To Do',
      assignees: []
    };

    // Generate position ID for loading state tracking
    const positionId = typeof afterIdOrPosition === 'object'
      ? `${afterIdOrPosition.type}-${afterIdOrPosition.targetTaskId}`
      : `after-${afterIdOrPosition}`;

    // Set loading state for this insertion operation
    setInsertionLoading(prev => ({ ...prev, [positionId]: true }));

    setTasks(prevTasks => {
      // Handle InsertionPosition parameter with validation
      if (typeof afterIdOrPosition === 'object') {
        const position = afterIdOrPosition;

        console.log('Inserting task at position:', position);

        // Simple direct insertion based on position type
        let updatedTasks: Task[];

        switch (position.type) {
          case 'before':
            updatedTasks = insertTaskBefore(prevTasks, position.targetTaskId, newTask, position.parentId);
            break;

          case 'after':
            updatedTasks = insertTaskAfter(prevTasks, position.targetTaskId, newTask, position.parentId);
            break;

          case 'between_parent_child':
            updatedTasks = insertTaskBetweenParentChild(prevTasks, position.targetTaskId, newTask);
            break;

          default:
            console.warn('Unknown insertion type:', position.type);
            updatedTasks = prevTasks;
        }

        // Clear loading state after successful insertion
        setTimeout(() => {
          setInsertionLoading(prev => ({ ...prev, [positionId]: false }));
        }, 300);

        return updatedTasks;
      }

      // Handle legacy string parameter (afterId)
      const afterId = afterIdOrPosition as string;

      if (afterId === '__FIRST__') {
        // Insert at the beginning
        const result = [newTask, ...prevTasks];

        // Clear loading state
        setTimeout(() => {
          setInsertionLoading(prev => ({ ...prev, [positionId]: false }));
        }, 300);

        return result;
      }

      // Insert after specific task
      const insertAfterTask = (taskList: Task[]): Task[] => {
        const result: Task[] = [];

        for (const task of taskList) {
          result.push(task);

          if (task.id === afterId) {
            if (parentId === task.id) {
              // Insert as first subtask
              task.subtasks = [newTask, ...task.subtasks];
            } else {
              // Insert after this task at the same level
              result.push(newTask);
            }
          } else if (task.subtasks.length > 0) {
            // Recursively check subtasks
            task.subtasks = insertAfterTask(task.subtasks);
          }
        }

        return result;
      };

      const result = insertAfterTask(prevTasks);

      // Clear loading state
      setTimeout(() => {
        setInsertionLoading(prev => ({ ...prev, [positionId]: false }));
      }, 300);

      return result;
    });
  }, [enhancedToast, setInsertionLoading]);

  // Keyboard insertion hook for task management
  const keyboardInsertion = useKeyboardInsertion({
    tasks,
    onInsertTask: handleAddTaskAfter,
    enabled: tasks.length > 0, // Only enable when there are tasks
    debugMode: false, // Set to true for debugging
    insertionState,
    onInsertionStateChange: updateInsertionState
  });

  const exampleProjects = [
    'Ich möchte eine App entwickeln',
    'Ich möchte Trading lernen',
    'Ich möchte meine Musik über Social-Media promoten',
    'Ich möchte ein Startup gründen im Bereich Trading',
    'Ich möchte meine App vermarkten',
    'Ich möchte Schwimmen autodidaktisch lernen',
    'Ich möchte React lernen',
    'Ich möchte Englisch lernen',
    'Ich möchte mathematische Ableitungen praktisch einsetzen',
    'Ich möchte ein Grundstück in Deutschland kaufen',
    'Ich möchte ein Haus bauen',
    'Ich möchte gute Selbstenwicklungsbücher entdecken',
    'Ich möchte lernen, wie man mit Frauen flirtet',
    'Ich möchte mich auf ein Marathon vorbereiten',
  ];

  const handleStartProject = useCallback(async () => {
    if (!mainProject) return;
    console.log('Starting project with:', mainProject);
    setIsLoading(true);
    MotivationManager.setMessage('project_starting');

    try {
      console.log('Calling unifiedAIClient.generateTasks...');
      const result = await measureAsync(
        () => unifiedAIClient.generateTasks(mainProject, mainProjectDescription),
        'aiResponse'
      );
      console.log('Result received:', result);

      // Handle partial success or complete failure
      if (result.error) {
        const isCompleteFailure = !result.tasks || result.tasks.length === 0;

        if (isCompleteFailure) {
          enhancedToast.aiError({
            operation: 'breakdown',
            title: 'Projektstart fehlgeschlagen',
            description: result.error,
            retryAction: handleStartProject
          });
          MotivationManager.setMessage('api_error');
        } else {
          enhancedToast.aiWarning({
            operation: 'breakdown',
            title: 'Projekt teilweise erstellt',
            description: result.error
          });
          MotivationManager.setMessage('project_started');
        }
      } else {
        enhancedToast.aiSuccess({
          operation: 'breakdown',
          title: 'Projekt erfolgreich erstellt',
          description: `${result.tasks.length} Hauptaufgaben wurden generiert.`
        });
        MotivationManager.setMessage('project_started');
      }

      const newTasks: Task[] = result.tasks.map((task) => ({
        id: crypto.randomUUID(),
        title: task.title,
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: [],
        description: task.description,
      }));

      setTasks(newTasks);
      setIsExamplesVisible(false);
      setProjectJustStarted(true);

    } catch (error) {
      console.error('Unexpected error in handleStartProject:', error);

      enhancedToast.error({
        title: 'Unerwarteter Fehler',
        description: 'Ein unerwarteter Fehler beim Projektstart ist aufgetreten.'
      });
      MotivationManager.setMessage('general_error');
    } finally {
      setIsLoading(false);
    }
  }, [mainProject, mainProjectDescription, enhancedToast]);

  const handleUpdateTask = useCallback((updatedTask: Task) => {
    setTasks(prevTasks => {
      // Use enhanced task editing that handles insertion compatibility
      const { updatedTasks, updatedHistory } = InsertionIntegration.handleTaskEditingWithInsertion(
        updatedTask,
        prevTasks,
        insertionState.insertionHistory
      );

      // Update insertion state with any changes to history
      if (updatedHistory !== insertionState.insertionHistory) {
        updateInsertionState({ insertionHistory: updatedHistory });
      }

      return updatedTasks;
    });
  }, [insertionState.insertionHistory, updateInsertionState]);

  const findTask = useCallback((
    tasksToSearch: Task[],
    id: string
  ): Task | null => {
    for (const task of tasksToSearch) {
      if (task.id === id) return task;
      if (task.subtasks) {
        const found = findTask(task.subtasks, id);
        if (found) return found;
      }
    }
    return null;
  }, []);

  // Simple solve function like in original code
  const handleSolve = useCallback(async (taskId: string, taskTitle: string, taskDescription: string, userAdditionalPrompt = '', selectedHtml = '', wrapperId = '') => {
    const isSelectionBasedElaboration = !!selectedHtml && !!wrapperId;

    // Set loading state
    setLoading(prev => ({ ...prev, [taskId]: isSelectionBasedElaboration ? 'elaborate' : 'solve' }));

    try {
      const startedAt = performance.now();
      // Build context using the context builder
      const context = contextBuilder.buildContextForAI(taskId, {
        strategies: contextStrategies,
        mainProject,
        mainProjectDescription,
        tasks,
        selectedHtml: isSelectionBasedElaboration ? selectedHtml : undefined
      });

      if (isSelectionBasedElaboration) {
        // Handle selection-based elaboration
        const currentTask = findTask(tasks, taskId);
        if (!currentTask) {
          throw new Error('Aufgabe nicht gefunden');
        }

        const elaborateResult = await unifiedAIClient.elaborateContent(
          currentTask.content || '',
          context,
          selectedHtml
        );

        if (elaborateResult.error) {
          throw new Error(elaborateResult.error);
        }

        // Update the wrapper content
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentTask.content || '';
        const wrapper = tempDiv.querySelector(`#${wrapperId}`);
        if (wrapper) {
          wrapper.innerHTML = elaborateResult.content;
          // Remove wrapper while keeping content
          const parent = wrapper.parentNode;
          while (wrapper.firstChild) {
            parent?.insertBefore(wrapper.firstChild, wrapper);
          }
          parent?.removeChild(wrapper);
        }

        const elapsed = Math.max(0, Math.round(performance.now() - startedAt));
        const updatedTask = { 
          ...currentTask, 
          content: tempDiv.innerHTML,
          aiMetrics: { ...(currentTask.aiMetrics || {}), lastElaborateMs: elapsed }
        };
        handleUpdateTask(updatedTask);

        toast({
          title: 'Text überarbeitet',
          description: 'Der ausgewählte Text wurde erfolgreich überarbeitet.'
        });
      } else {
        // Handle normal task content generation
        const generateResult = await unifiedAIClient.generateTaskContent(
          taskTitle,
          taskDescription || 'Keine Beschreibung verfügbar',
          context,
          userAdditionalPrompt
        );

        if (generateResult.error) {
          throw new Error(generateResult.error);
        }

        const currentTask = findTask(tasks, taskId);
        if (currentTask) {
          const elapsed = Math.max(0, Math.round(performance.now() - startedAt));
          const updatedTask = { 
            ...currentTask, 
            content: generateResult.content,
            aiMetrics: { ...(currentTask.aiMetrics || {}), lastSolveMs: elapsed }
          };
          handleUpdateTask(updatedTask);
        }

        toast({
          title: 'Inhalt generiert',
          description: 'Die KI hat einen Vorschlag erarbeitet.'
        });
      }
    } catch (error) {
      console.error('Solve error:', error);
      toast({
        variant: 'destructive',
        title: 'Fehler bei der KI-Generierung',
        description: error instanceof Error ? error.message : 'Unbekannter Fehler aufgetreten'
      });
    } finally {
      // Reset loading state when operation is complete
      setLoading(prev => ({ ...prev, [taskId]: false }));
    }
  }, [tasks, contextStrategies, mainProject, mainProjectDescription, contextBuilder, findTask, handleUpdateTask, toast, setLoading]);

  // Direct autopilot execution without modal
  const handleAutopilotSolve = useCallback(async (task: Task, selectionWrapperId = '') => {
    const isSelectionBasedElaboration = !!selectionWrapperId;

    // Set loading state for the task
    console.log('Setting solve loading for task:', task.id);
    setLoading(prev => ({ ...prev, [task.id]: 'solve' }));

    try {
      const startedAt = performance.now();
      // Build context using the context builder
      const context = contextBuilder.buildContextForAI(task.id, {
        strategies: contextStrategies,
        mainProject,
        mainProjectDescription,
        tasks,
        selectedHtml: isSelectionBasedElaboration ? selectionWrapperId : undefined
      });

      let result;
      if (isSelectionBasedElaboration) {
        // Handle selection-based elaboration
        const currentTask = findTask(tasks, task.id);
        if (!currentTask) {
          throw new Error('Aufgabe nicht gefunden');
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentTask.content;
        const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
        if (!wrapper) {
          throw new Error('Ausgewählter Text nicht mehr verfügbar.');
        }

        const selectedHtml = wrapper.innerHTML;
        const elaborateResult = await unifiedAIClient.elaborateContent(
          currentTask.content,
          context,
          selectedHtml
        );

        if (elaborateResult.error) {
          throw new Error(elaborateResult.error);
        }

        // Replace wrapper content with improved text
        wrapper.innerHTML = elaborateResult.content;
        const parent = wrapper.parentNode;
        while (wrapper.firstChild) {
          parent?.insertBefore(wrapper.firstChild, wrapper);
        }
        parent?.removeChild(wrapper);

        const elapsed = Math.max(0, Math.round(performance.now() - startedAt));
        const updatedTask = { 
          ...currentTask, 
          content: tempDiv.innerHTML,
          aiMetrics: { ...(currentTask.aiMetrics || {}), lastElaborateMs: elapsed }
        };
        handleUpdateTask(updatedTask);

        toast({
          title: 'Text überarbeitet',
          description: 'Der ausgewählte Text wurde erfolgreich überarbeitet.'
        });
      } else {
        // Handle normal task content generation
        const generateResult = await unifiedAIClient.generateTaskContent(
          task.title,
          task.description || 'Keine Beschreibung verfügbar',
          context
        );

        if (generateResult.error) {
          throw new Error(generateResult.error);
        }

        const elapsed = Math.max(0, Math.round(performance.now() - startedAt));
        const likelyTruncated = /<h[23][^>]*>\s*\d+\.\d+(?:\.\d+)?\s*<\/h[23]>\s*$/.test(generateResult.content.trim());
        const updatedTask = { 
          ...task, 
          content: generateResult.content,
          aiMetrics: { 
            ...(task.aiMetrics || {}), 
            lastSolveMs: elapsed,
            lastFinishReason: (generateResult as any)._diag?.finishReason,
            promptTokens: (generateResult as any)._diag?.promptTokens,
            candidateTokens: (generateResult as any)._diag?.candidateTokens,
            totalTokens: (generateResult as any)._diag?.totalTokens,
            lastContentChars: generateResult.content.length,
            likelyTruncated
          }
        };
        handleUpdateTask(updatedTask);

        toast({
          title: 'Inhalt generiert',
          description: 'Die KI hat einen Vorschlag erarbeitet.'
        });
      }
    } catch (error) {
      console.error('Autopilot solve error:', error);
      toast({
        variant: 'destructive',
        title: 'Fehler bei der KI-Generierung',
        description: error instanceof Error ? error.message : 'Unbekannter Fehler aufgetreten'
      });
    } finally {
      // Reset loading state when operation is complete
      console.log('Resetting solve loading for task:', task.id);
      setLoading(prev => ({ ...prev, [task.id]: false }));
    }
  }, [tasks, contextStrategies, mainProject, mainProjectDescription, contextBuilder, findTask, handleUpdateTask, toast, setLoading]);

  const handleOpenSolveModal = useCallback((
    task: Task,
    isRefinement = false,
    selectionWrapperId = ''
  ) => {
    // Check for direct autopilot request
    if (selectionWrapperId === 'autopilot-direct') {
      // Direct autopilot execution like in original code
      handleSolve(task.id, task.title, task.description || '', 'Führe die Aufgabe im Autopilot-Modus aus. Wenn dir für eine spezifische Antwort Details fehlen, triff plausible, kreative Annahmen, die zum Gesamtziel passen.');
      return;
    }

    // Create a new modal for this task (for copilot)
    const modalId = `modal-${task.id}-${Date.now()}`;

    setSolveModals(prev => [...prev, {
      id: modalId,
      isOpen: true,
      task,
      isRefinement,
      selectionWrapperId,
    }]);
  }, [handleSolve]);

  const handleCloseSolveModal = useCallback((modalId: string) => {
    setSolveModals(prev => prev.filter(modal => modal.id !== modalId));
  }, []);

  // Helper function to find task level in hierarchy
  const findTaskLevel = useCallback((tasksToSearch: Task[], id: string, currentLevel: number = 0): number | null => {
    for (const task of tasksToSearch) {
      if (task.id === id) return currentLevel;
      if (task.subtasks) {
        const level = findTaskLevel(task.subtasks, id, currentLevel + 1);
        if (level !== null) return level;
      }
    }
    return null;
  }, []);

  // Helper function to get task hierarchy path
  const getTaskHierarchyPath = useCallback((tasksToSearch: Task[], id: string): string[] => {
    const path: string[] = [];

    const findPath = (taskList: Task[], currentPath: string[]): boolean => {
      for (const task of taskList) {
        const newPath = [...currentPath, task.title];

        if (task.id === id) {
          path.push(...newPath);
          return true;
        }

        if (task.subtasks && findPath(task.subtasks, newPath)) {
          return true;
        }
      }
      return false;
    };

    findPath(tasksToSearch, []);
    return path;
  }, []);

  // Additional insertion state management functions

  const handleAddTask = useCallback((parentId = null, title = 'Neue Aufgabe (zum Bearbeiten klicken)', description = 'Beschreibung hinzufügen...') => {
    const newTask: Task = {
      id: crypto.randomUUID(),
      title,
      description,
      subtasks: [],
      content: '',
      status: 'To Do',
      assignees: []
    };

    setTasks(prevTasks => {
      if (!parentId) {
        return [...prevTasks, newTask];
      } else {
        const addSubtask = (tasks: Task[]): Task[] => {
          return tasks.map(task => {
            if (task.id === parentId) {
              return { ...task, subtasks: [...(task.subtasks || []), newTask] };
            }
            if (task.subtasks) {
              return { ...task, subtasks: addSubtask(task.subtasks) };
            }
            return task;
          });
        };
        return addSubtask(prevTasks);
      }
    });
  }, []);

  // Additional insertion state management functions

  const setActiveInsertionPoint = useCallback((position: InsertionPosition | null) => {
    updateInsertionState({ activeInsertionPoint: position });
  }, [updateInsertionState]);

  const setHoveredZone = useCallback((zone: InsertionZone | null) => {
    updateInsertionState({ hoveredZone: zone });
  }, [updateInsertionState]);

  const addToInsertionHistory = useCallback((position: InsertionPosition) => {
    setInsertionState(prev => ({
      ...prev,
      insertionHistory: [position, ...prev.insertionHistory.slice(0, 9)] // Keep last 10 insertions
    }));
  }, []);

  const clearInsertionHistory = useCallback(() => {
    updateInsertionState({ insertionHistory: [] });
  }, [updateInsertionState]);


  const handleDeleteTask = useCallback((targetId: string) => {
    // Use enhanced deletion that handles insertion state cleanup
    const { updatedTasks, updatedHistory } = TaskOperationIntegration.handleTaskDeletionWithInsertion(
      targetId,
      tasks,
      insertionState.insertionHistory
    );

    setTasks(updatedTasks);

    // Update insertion state with cleaned history and clear related state
    setInsertionState(prev => ({
      ...prev,
      activeInsertionPoint: prev.activeInsertionPoint?.targetTaskId === targetId ? null : prev.activeInsertionPoint,
      hoveredZone: prev.hoveredZone?.targetTaskId === targetId ? null : prev.hoveredZone,
      insertionHistory: updatedHistory,
      lastCalculatedZones: new Map([...prev.lastCalculatedZones].filter(([key]) => !key.includes(targetId))),
      zoneCalculationCache: new Map([...prev.zoneCalculationCache].filter(([key]) => !key.includes(targetId)))
    }));

    // Show toast notification for successful deletion
    enhancedToast.success({
      title: 'Aufgabe gelöscht',
      description: 'Die Aufgabe und alle zugehörigen Einfügepunkte wurden entfernt.'
    });
  }, [tasks, insertionState.insertionHistory, enhancedToast]);

  const handleBreakdownTask = useCallback(async (taskId: string) => {
    const task = findTask(tasks, taskId);
    if (!task) return;

    // Set loading state for this specific task
    setLoading(prev => ({ ...prev, [taskId]: 'breakdown' }));
    MotivationManager.setMessage('task_breakdown_starting');

    try {
      const startedAt = performance.now();
      // Use enhanced breakdown that preserves insertion context
      const result = await TaskOperationIntegration.enhancedBreakdownTask(
        taskId,
        tasks,
        insertionState.insertionHistory,
        {
          useContextFromParent: true,
          contextStrategies,
          mainProject,
          mainProjectDescription
        }
      );

      // Handle different error scenarios
      if (result.error) {
        const hasSubtasks = result.newSubtasks && result.newSubtasks.length > 0;

        if (!hasSubtasks) {
          enhancedToast.aiError({
            operation: 'breakdown',
            taskTitle: task.title,
            description: result.error,
            retryAction: () => handleBreakdownTask(taskId)
          });
          MotivationManager.setMessage('task_breakdown_error');
          return;
        } else {
          enhancedToast.aiWarning({
            operation: 'breakdown',
            taskTitle: task.title,
            description: result.error
          });
          MotivationManager.setMessage('task_breakdown_success');
        }
      } else {
        enhancedToast.aiSuccess({
          operation: 'breakdown',
          taskTitle: task.title,
          description: `${result.newSubtasks.length} Unteraufgaben erstellt`
        });
        MotivationManager.setMessage('task_breakdown_success');
      }

      if (result.newSubtasks && result.newSubtasks.length > 0) {
        // Update tasks using recursive approach to avoid duplication
        setTasks(prevTasks => {
          const updateRecursively = (tasks: Task[]): Task[] => {
            return tasks.map((currentTask) => {
              if (currentTask.id === taskId) {
                const elapsed = Math.max(0, Math.round(performance.now() - startedAt));
                return {
                  ...currentTask,
                  subtasks: [...(currentTask.subtasks || []), ...result.newSubtasks],
                  aiMetrics: { ...(currentTask.aiMetrics || {}), lastBreakdownMs: elapsed }
                };
              }
              if (currentTask.subtasks?.length > 0) {
                return { ...currentTask, subtasks: updateRecursively(currentTask.subtasks) };
              }
              return currentTask;
            });
          };
          return updateRecursively(prevTasks);
        });

        // Update insertion history to track breakdown-generated tasks
        const newInsertionPositions: InsertionPosition[] = result.newSubtasks.map((subtask, index) => ({
          type: 'between_parent_child' as const,
          targetTaskId: taskId,
          parentId: taskId,
          level: (findTaskLevel(tasks, taskId) || 0) + 1,
          context: {
            siblingCount: (task.subtasks?.length || 0) + result.newSubtasks.length,
            insertionIndex: (task.subtasks?.length || 0) + index,
            hierarchyPath: getTaskHierarchyPath(tasks, taskId)
          }
        }));

        updateInsertionState({
          insertionHistory: [...insertionState.insertionHistory, ...newInsertionPositions]
        });
      }

    } catch (error) {
      console.error('Unexpected error in handleBreakdownTask:', error);

      enhancedToast.error({
        title: 'Unerwarteter Fehler',
        description: 'Ein unerwarteter Fehler bei der Aufgabenzerlegung ist aufgetreten.'
      });
      MotivationManager.setMessage('general_error');
    } finally {
      // Clear loading state for this task
      setLoading(prev => ({ ...prev, [taskId]: false }));
    }
  }, [tasks, insertionState.insertionHistory, contextStrategies, mainProject, mainProjectDescription, enhancedToast, updateInsertionState]);

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme);
      document.documentElement.classList.toggle('dark', newTheme === 'dark');
    }
  }, [theme]);

  const handleStrategyChange = useCallback((strategy: keyof ContextStrategies, enabled: boolean) => {
    setContextStrategies(prev => ({
      ...prev,
      [strategy]: enabled
    }));
  }, []);

  // Project data for exports
  const projectData = useMemo(() => {
    // Enhance export data with insertion metadata if needed
    const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
      tasks,
      insertionState.insertionHistory,
      false // Set to true if you want to include insertion metadata in exports
    );

    // Validate task structure before export
    const validation = ExportIntegration.validateTaskStructureForExport(enhancedTasks);
    if (!validation.isValid) {
      console.warn('Task structure validation issues:', validation.issues);
    }

    return {
      mainProject,
      mainProjectDescription,
      tasks: enhancedTasks
    };
  }, [mainProject, mainProjectDescription, tasks, insertionState.insertionHistory]);

  // Mobile-specific handlers
  const handleMobileRefresh = useCallback(async () => {
    // Refresh task list or reload data
    if (mobileSettings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    // Simulate refresh action
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Aktualisiert",
      description: "Die Aufgabenliste wurde aktualisiert."
    });
  }, [mobileSettings.hapticFeedback, toast]);

  const handleMobileAddTask = useCallback(() => {
    handleAddTask();
    if (mobileSettings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(20);
    }
  }, [handleAddTask, mobileSettings.hapticFeedback]);

  const handleMobileExport = useCallback(() => {
    // Open export options
    if (mobileSettings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(15);
    }
    
    // For now, just export as PDF
    exportToPdf(projectData);
  }, [mobileSettings.hapticFeedback, projectData]);

  const handleToggleAIContent = useCallback(() => {
    setIsAIContentVisible(prev => !prev);
    if (mobileSettings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  }, [mobileSettings.hapticFeedback]);

  const handleOpenMobileSettings = useCallback(() => {
    setMobileSettingsOpen(true);
    if (mobileSettings.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(15);
    }
  }, [mobileSettings.hapticFeedback]);

  const handleMobileSettingsChange = useCallback((newSettings: typeof mobileSettings) => {
    setMobileSettings(newSettings);
    // Save to localStorage
    localStorage.setItem('mobileSettings', JSON.stringify(newSettings));
  }, []);

  const handleMobileExportData = useCallback(() => {
    exportToPdf(projectData);
    setMobileSettingsOpen(false);
  }, [projectData]);

  const handleMobileImportData = useCallback(() => {
    // Implement import functionality
    toast({
      title: "Import",
      description: "Import-Funktionalität wird bald verfügbar sein."
    });
  }, [toast]);

  const handleMobileClearData = useCallback(() => {
    if (confirm('Alle Daten löschen? Diese Aktion kann nicht rückgängig gemacht werden.')) {
      setTasks([]);
      setMainProject('');
      setMainProjectDescription('');
      localStorage.removeItem('tasks');
      localStorage.removeItem('mainProject');
      toast({
        title: "Daten gelöscht",
        description: "Alle Projektdaten wurden entfernt."
      });
    }
  }, [toast]);

  const handleMobileResetApp = useCallback(() => {
    if (confirm('App komplett zurücksetzen? Alle Daten und Einstellungen gehen verloren.')) {
      // Clear all data and settings
      localStorage.clear();
      window.location.reload();
    }
  }, []);



  return (
    <div className="bg-slate-100 dark:bg-slate-900 min-h-screen font-sans text-slate-900 dark:text-slate-100 transition-colors duration-300" suppressHydrationWarning>
      <div className="container mx-auto p-2 sm:p-4 md:p-8 pb-20 sm:pb-8" suppressHydrationWarning>
        {/* Offline Banner */}
        <OfflineBanner />
        
        <header className="relative text-center mb-6 sm:mb-8">
          <div className="absolute top-0 right-0 hidden sm:flex items-center gap-2">
            <ConnectionStatusIndicator showText={true} className="mr-2" />
            
            {/* PWA Integration Status */}
            {pwaIntegration.isInitialized && (
              <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                PWA
              </div>
            )}
            
            <a href="https://paypal.me/VeniceWaveRecords" target="_blank" rel="noopener noreferrer" className="p-2 block rounded-full bg-slate-200 dark:bg-slate-700 text-red-500 dark:text-red-400 hover:bg-slate-300 dark:hover:bg-slate-600 hover:text-red-500 transition-colors">
              <Heart size={24} />
            </a>
            <button onClick={() => setIsHelpModalOpen(true)} aria-label="Hilfe anzeigen" title="Hilfe anzeigen" className="p-2 rounded-full text-slate-600 dark:text-slate-300 hover:!text-indigo-500 dark:hover:!text-indigo-400 transition-colors mobile-touch-target">
              <HelpCircle size={24} />
            </button>
            
            {/* PWA Help Button */}
            {pwaIntegration.isInitialized && (
              <button 
                onClick={() => setShowPWAHelp(true)} 
                aria-label="PWA Hilfe" 
                title="PWA Features & Hilfe" 
                className="p-2 rounded-full text-slate-600 dark:text-slate-300 hover:!text-blue-500 dark:hover:!text-blue-400 transition-colors mobile-touch-target"
              >
                <Info size={24} />
              </button>
            )}
            <button onClick={toggleTheme} aria-label="Toggle Dark Mode" title="Theme wechseln" className="p-2 rounded-full text-slate-600 dark:text-slate-300 hover:!text-indigo-500 dark:hover:!text-indigo-400 transition-colors mobile-touch-target">
              {theme === 'light' ? <Moon size={24} /> : <Sun size={24} />}
            </button>
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-slate-800 dark:text-white pt-12 sm:pt-0">
            <BrainCircuit className="inline-block mr-2 sm:mr-3 text-indigo-500" size={36} />
            <span className="hidden sm:inline">KI Projekt-Planer</span>
            <span className="sm:hidden">KI Planer</span>
          </h1>
          <div className="sm:hidden flex justify-center items-center gap-3 mt-4">
            <ConnectionStatusIndicator className="mr-2" />
            <a href="https://paypal.me/VeniceWaveRecords" target="_blank" rel="noopener noreferrer" className="p-3 block rounded-full bg-slate-200 dark:bg-slate-700 text-red-500 dark:text-red-400 hover:bg-slate-300 dark:hover:bg-slate-600 hover:text-red-500 transition-colors mobile-touch-target">
              <Heart size={20} />
            </a>
            <button onClick={() => setIsHelpModalOpen(true)} aria-label="Hilfe anzeigen" title="Hilfe anzeigen" className="p-3 rounded-full text-slate-600 dark:text-slate-300 hover:!text-indigo-500 dark:hover:!text-indigo-400 transition-colors mobile-touch-target">
              <HelpCircle size={20} />
            </button>
            <button onClick={toggleTheme} aria-label="Toggle Dark Mode" title="Theme wechseln" className="p-3 rounded-full text-slate-600 dark:text-slate-300 hover:!text-indigo-500 dark:hover:!text-indigo-400 transition-colors mobile-touch-target">
              {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
            </button>
          </div>
          <p className={`mt-3 sm:mt-4 text-base sm:text-lg px-4 sm:px-0 ${getMotivationClasses(motivationState.type)}`}>
            {motivationState.message}
          </p>
        </header>

        <main>
          <div className="bg-white dark:bg-slate-800 p-4 sm:p-6 rounded-lg sm:rounded-xl shadow-lg mb-6 sm:mb-8 border border-slate-200 dark:border-slate-700 mobile-spacing">
            <h2 className="text-xl sm:text-2xl font-semibold mb-3 sm:mb-4 text-slate-800 dark:text-slate-100">Ihr großes Ziel</h2>
            <div className="flex flex-col gap-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <Textarea
                  value={mainProject}
                  onChange={(e) => setMainProject(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleStartProject();
                    }
                  }}
                  placeholder="z.B. eine Weltreise planen, eine App entwickeln..."
                  className="flex-grow p-3 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                />
                <Button onClick={handleStartProject} disabled={isLoading} className="hidden sm:flex">
                  {isLoading ? 'Analysiere...' : 'Projekt starten'}
                </Button>
              </div>
              <div>
                <Textarea
                  value={mainProjectDescription}
                  onChange={(e) => setMainProjectDescription(e.target.value)}
                  placeholder="Optionale Beschreibung: Fügen Sie hier weitere Details zu Ihrem Ziel hinzu..."
                  className="w-full p-3 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition"
                  rows={4}
                />
              </div>
              <Button onClick={handleStartProject} disabled={isLoading} className="sm:hidden">
                {isLoading ? 'Analysiere...' : 'Projekt starten'}
              </Button>
            </div>
            <div className="mt-4 pt-4 border-t border-dashed border-slate-200 dark:border-slate-700">
              <Button onClick={() => setIsExamplesVisible(!isExamplesVisible)} variant="link" className="mx-auto flex items-center gap-1">
                {isExamplesVisible ? <EyeOff size={16} /> : <Eye size={16} />}
                {isExamplesVisible ? 'Beispiele verbergen' : 'Beispiele anzeigen'}
              </Button>
            </div>
            {isExamplesVisible && (
              <div className="text-center mt-4">
                <div className="flex flex-wrap gap-2 justify-center p-1">
                  {exampleProjects.map((example) => (
                    <Button
                      key={example}
                      onClick={() => setMainProject(example)}
                      variant="secondary"
                      size="sm"
                      className="rounded-full bg-slate-300 dark:bg-slate-600 text-slate-700 dark:text-slate-300 hover:bg-blue-500 hover:text-white dark:hover:bg-blue-500 dark:hover:text-white transition-colors"
                    >
                      {example}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div id="task-container" ref={taskContainerRef}>
            <TaskList
              tasks={tasks}
              onUpdateTask={handleUpdateTask}
              onOpenSolveModal={handleOpenSolveModal}
              onAddTask={handleAddTask}
              onAddTaskAfter={handleAddTaskAfter}
              onDeleteTask={handleDeleteTask}
              onBreakdownTask={handleBreakdownTask}
              loading={loading}
              setLoading={setLoading}
              onInsertTask={handleAddTaskAfter}
              showInsertionIndicators={true}
              insertionMode="always"
              insertionState={insertionState}
              onInsertionStateChange={updateInsertionState}
              insertionLoading={insertionLoading}
              onRefresh={isMobile ? handleMobileRefresh : undefined}
              enablePullToRefresh={isMobile && mobileSettings.pullToRefresh}
            />
          </div>

          {/* PWA Integration Status Panel */}
          {pwaIntegration.isInitialized && (
            <div className="mt-8 bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-300">
                  PWA Status
                </h3>
                <Button
                  onClick={async () => {
                    try {
                      const success = await pwaIntegration.testWorkflow();
                      toast({
                        title: success ? 'PWA Test erfolgreich' : 'PWA Test fehlgeschlagen',
                        description: success ? 'Alle PWA-Funktionen arbeiten korrekt zusammen.' : 'Es gab Probleme bei der PWA-Integration.',
                        variant: success ? 'default' : 'destructive'
                      });
                    } catch (error) {
                      toast({
                        title: 'PWA Test fehlgeschlagen',
                        description: error instanceof Error ? error.message : 'Unbekannter Fehler',
                        variant: 'destructive'
                      });
                    }
                  }}
                  variant="outline"
                  size="sm"
                  className="text-slate-600 dark:text-slate-400"
                >
                  <Info size={16} className="mr-1" />
                  Test
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${pwaIntegration.state.isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span>Online: {pwaIntegration.state.isOnline ? 'Ja' : 'Nein'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${pwaIntegration.state.serviceWorkerReady ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span>Service Worker: {pwaIntegration.state.serviceWorkerReady ? 'Aktiv' : 'Inaktiv'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${pwaIntegration.state.isInstalled ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                    <span>Installiert: {pwaIntegration.state.isInstalled ? 'Ja' : 'Nein'}</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-slate-600 dark:text-slate-400">
                    Ladezeit: {Math.round(pwaIntegration.state.performance.loadTime)}ms
                  </div>
                  <div className="text-slate-600 dark:text-slate-400">
                    Cache-Rate: {Math.round(pwaIntegration.state.performance.cacheHitRate)}%
                  </div>
                  <div className="text-slate-600 dark:text-slate-400">
                    Warteschlange: {pwaIntegration.state.dataSync.pendingChanges}
                  </div>
                </div>
              </div>
              {pwaIntegration.state.updateAvailable && (
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-blue-700 dark:text-blue-300 text-sm">
                      App-Update verfügbar
                    </span>
                    <Button
                      onClick={async () => {
                        try {
                          await pwaIntegration.applyUpdate();
                          toast({
                            title: 'Update angewendet',
                            description: 'Die App wurde erfolgreich aktualisiert.'
                          });
                        } catch (error) {
                          toast({
                            title: 'Update fehlgeschlagen',
                            description: 'Das Update konnte nicht angewendet werden.',
                            variant: 'destructive'
                          });
                        }
                      }}
                      size="sm"
                      variant="outline"
                    >
                      Aktualisieren
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Insertion History Panel - Development/Debug Feature */}
          {tasks.length > 0 && insertionState.insertionHistory.length > 0 && (
            <div className="mt-8 bg-white dark:bg-slate-800 p-4 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-300">
                  Einfügungshistorie ({insertionState.insertionHistory.length})
                </h3>
                <Button
                  onClick={clearInsertionHistory}
                  variant="outline"
                  size="sm"
                  className="text-slate-600 dark:text-slate-400"
                >
                  <X size={16} className="mr-1" />
                  Löschen
                </Button>
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {insertionState.insertionHistory.slice(0, 5).map((position, index) => {
                  const targetTask = findTask(tasks, position.targetTaskId);
                  return (
                    <div
                      key={`${position.targetTaskId}-${position.type}-${index}`}
                      className="flex items-center gap-2 p-2 bg-slate-50 dark:bg-slate-700 rounded text-sm"
                    >
                      <span className="text-slate-500 dark:text-slate-400 font-mono">
                        {position.type}
                      </span>
                      <span className="text-slate-700 dark:text-slate-300">
                        {targetTask?.title || 'Unbekannte Aufgabe'}
                      </span>
                      <span className="text-slate-500 dark:text-slate-400 text-xs ml-auto">
                        Level {position.level}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {tasks.length > 0 && (
            <div className="mt-12 text-center">
              <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-300 mb-4">Projekt exportieren</h3>
              <div className="flex justify-center items-center gap-4">
                <Button onClick={async () => {
                  MotivationManager.setMessage('export_starting');
                  try {
                    await measureAsync(
                      () => Promise.resolve(exportToPdf(projectData)),
                      'export'
                    );
                    enhancedToast.exportSuccess('PDF');
                    MotivationManager.setMessage('export_success');
                  } catch (error) {
                    enhancedToast.exportError('PDF', () => exportToPdf(projectData));
                    MotivationManager.setMessage('export_error');
                  }
                }}>
                  <FileDown size={18} className="mr-2" /> PDF
                </Button>
                <Button onClick={() => {
                  MotivationManager.setMessage('export_starting');
                  try {
                    exportToMarkdown(projectData);
                    enhancedToast.exportSuccess('Markdown');
                    MotivationManager.setMessage('export_success');
                  } catch (error) {
                    enhancedToast.exportError('Markdown', () => exportToMarkdown(projectData));
                    MotivationManager.setMessage('export_error');
                  }
                }}>
                  <FileDown size={18} className="mr-2" /> Markdown
                </Button>
                <Button onClick={() => {
                  MotivationManager.setMessage('export_starting');
                  try {
                    exportToCsv(projectData);
                    enhancedToast.exportSuccess('CSV');
                    MotivationManager.setMessage('export_success');
                  } catch (error) {
                    enhancedToast.exportError('CSV', () => exportToCsv(projectData));
                    MotivationManager.setMessage('export_error');
                  }
                }}>
                  <FileDown size={18} className="mr-2" /> CSV
                </Button>
              </div>
            </div>
          )}

        </main>

        <footer className="text-center mt-16 text-sm text-slate-500 dark:text-slate-400 space-y-3">
          <p className="text-lg text-slate-600 dark:text-slate-400">Gemeinsam verwandeln wir Ideen in Imperien.</p>
          <p className="flex items-center justify-center gap-2 text-slate-500 dark:text-slate-500">
            Entwickelt mit <Heart size={16} className="text-red-500 inline-block" /> von Murat Eren & künstlicher Intelligenz.
          </p>
        </footer>
      </div>

      <Dialog open={isHelpModalOpen} onOpenChange={setIsHelpModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="text-indigo-500 dark:text-indigo-400" /> Anleitung
            </DialogTitle>
          </DialogHeader>
          <div className="text-slate-600 dark:text-slate-300 space-y-6">
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">KI-Kontext Strategien</h4>
              <div className="space-y-3 mb-6 p-4 bg-slate-100 dark:bg-slate-700 rounded-lg">
                <p className="text-sm text-slate-600 dark:text-slate-400">Konfigurieren Sie, wie viel Kontext die KI bei der Aufgabenlösung erhält:</p>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="strategy1"
                      checked={contextStrategies.strategy1}
                      onCheckedChange={(checked) => handleStrategyChange('strategy1', !!checked)}
                    />
                    <label htmlFor="strategy1" className="text-sm">Aufgabenpfad einbeziehen</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="strategy2"
                      checked={contextStrategies.strategy2}
                      onCheckedChange={(checked) => handleStrategyChange('strategy2', !!checked)}
                    />
                    <label htmlFor="strategy2" className="text-sm">Intelligente Projektzusammenfassung (empfohlen)</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="strategy3"
                      checked={contextStrategies.strategy3}
                      onCheckedChange={(checked) => handleStrategyChange('strategy3', !!checked)}
                    />
                    <label htmlFor="strategy3" className="text-sm">Vollständigen Projektbaum einbeziehen</label>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">1. Projekt starten</h4>
              <p>Geben Sie Ihr großes Ziel in das Haupt-Eingabefeld ein. Fügen Sie optional eine detailliertere Beschreibung hinzu. Klicken Sie dann auf <span className="font-semibold text-slate-800 dark:text-white">"Projekt starten"</span>. Die KI erstellt für Sie die ersten großen Aufgabenblöcke.</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">2. Aufgaben zerlegen (<ChevronDown size={16} className="inline-block" />)</h4>
              <p>Jede große Idee besteht aus kleinen Schritten. Klicken Sie auf den <ChevronDown size={16} className="inline-block" />-Pfeil neben einer Aufgabe, um sie von der KI in detaillierte Unteraufgaben zerlegen zu lassen. Dies können Sie beliebig oft wiederholen, um tief ins Detail zu gehen.</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">3. Aufgaben ausarbeiten lassen (<Sparkles size={16} className="inline-block" />)</h4>
              <p>Wenn Sie für eine Aufgabe einen konkreten Plan, eine Anleitung oder einen Text benötigen, klicken Sie auf das <Sparkles size={16} className="inline-block" />-Symbol. Daraufhin öffnet sich ein Fenster, in dem Sie der KI <span className="font-semibold text-slate-800 dark:text-white">optionale Zusatzanweisungen</span> geben können, um das Ergebnis noch besser zu machen.</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">4. Interaktiv arbeiten & bearbeiten</h4>
              <p>Diese App ist dynamisch. Sie können jederzeit Titel, Beschreibungen und sogar die von der KI generierten Texte bearbeiten. Klicken Sie einfach auf den entsprechenden Text. Nach der Bearbeitung eines KI-Textes haben Sie die Wahl: Entweder nur Ihre Änderungen <span className="font-semibold text-slate-800 dark:text-white">speichern</span> oder die KI bitten, Ihre Version zu <span className="font-semibold text-slate-800 dark:text-white">überarbeiten</span>.</p>
            </div>
            <div>
              <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">5. Exportieren (<FileDown size={16} className="inline-block" />)</h4>
              <p>Wenn Ihr Plan fertig ist, können Sie ihn ganz einfach als PDF, Markdown-Datei oder CSV-Tabelle exportieren, um ihn zu teilen oder in anderen Programmen weiterzuverwenden.</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      {/* Render multiple solve modals */}
      {solveModals.map((modal) => (
        <SolveTaskModal
          key={modal.id}
          modalId={modal.id}
          isOpen={modal.isOpen}
          onOpenChange={(isOpen) => {
            if (!isOpen) {
              handleCloseSolveModal(modal.id);
            }
          }}
          task={modal.task}
          onUpdateTask={handleUpdateTask}
          setMotivation={(message: string) => MotivationManager.setCustomMessage(message)}
          findTask={findTask}
          tasks={tasks}
          isRefinement={modal.isRefinement}
          selectionWrapperId={modal.selectionWrapperId}
          mainProject={mainProject}
          mainProjectDescription={mainProjectDescription}
          contextStrategies={contextStrategies}
          onSolveComplete={(taskId) => {
            // Reset loading state when AI operation is actually complete
            setLoading(prev => ({ ...prev, [taskId]: false }));
          }}
          handleSolve={handleSolve}
        />
      ))}
      
      {/* PWA Components */}
      <PWAInstallPrompt />
      <PWAUpdateNotification />
      
      {/* Mobile PWA Prompt */}
      {isMobile && <MobilePWAPrompt />}
      
      <PWAHelpSystem 
        isOpen={showPWAHelp} 
        onOpenChange={setShowPWAHelp} 
      />
      <PWAOnboarding 
        isOpen={showOnboarding}
        onOpenChange={setShowOnboarding}
        onComplete={() => {
          toast({
            title: 'PWA Einführung abgeschlossen',
            description: 'Sie können jetzt alle PWA-Features nutzen!'
          });
        }}
      />
      
      {/* Update notification */}
      {updateAvailable && (
        <div className="fixed top-4 right-4 z-50">
          <div className="bg-primary text-primary-foreground p-4 rounded-lg shadow-lg">
            <p className="text-sm mb-2">App-Update verfügbar</p>
            <Button onClick={updateApp} size="sm" variant="secondary">
              Jetzt aktualisieren
            </Button>
          </div>
        </div>
      )}
      
      {/* Offline indicator */}
      {!isOnline && (
        <div className="fixed bottom-20 left-4 right-4 z-40">
          <div className="bg-orange-500 text-white p-3 rounded-lg shadow-lg text-center">
            <p className="text-sm">Offline-Modus - AI-Funktionen nicht verfügbar</p>
          </div>
        </div>
      )}

      {/* AI Request Queue Status */}
      <div className="fixed bottom-4 right-4 z-40">
        <AIRequestQueueStatus />
      </div>

      {/* Mobile Navigation Components */}
      {isMobile && (
        <>
          <MobileBottomNavigation
            onAddTask={handleMobileAddTask}
            onExport={handleMobileExport}
            onToggleAIContent={handleToggleAIContent}
            onOpenSettings={handleOpenMobileSettings}
            isAIContentVisible={isAIContentVisible}
          />
          
          <FloatingActionButton
            onClick={handleMobileAddTask}
            disabled={isLoading}
          />
          
          <MobileSettingsPanel
            isOpen={mobileSettingsOpen}
            onClose={() => setMobileSettingsOpen(false)}
            settings={mobileSettings}
            onSettingsChange={handleMobileSettingsChange}
            onExportData={handleMobileExportData}
            onImportData={handleMobileImportData}
            onClearData={handleMobileClearData}
            onResetApp={handleMobileResetApp}
          />
        </>
      )}
    </div>
  );
}

export default function Page() {
  return <Home />;
}
