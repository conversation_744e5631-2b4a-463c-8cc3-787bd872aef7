@tailwind base;
@tailwind components;
@tailwind utilities;

/* Loading animations */
@keyframes slide {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-slide {
  animation: slide 2s ease-in-out infinite;
}

/* Enhanced insertion animations for cross-browser compatibility */
@keyframes insertionSlideInTop {
  0% {
    transform: translateY(-10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes insertionSlideInBottom {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes insertionSlideInLeft {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes insertionSuccess {
  0% {
    transform: scale(1);
    background-color: hsl(var(--primary));
  }
  50% {
    transform: scale(1.2);
    background-color: hsl(142 76% 36%);
    box-shadow: 0 0 20px hsl(142 76% 36% / 0.5);
  }
  100% {
    transform: scale(1);
    background-color: hsl(142 76% 36%);
    box-shadow: 0 0 10px hsl(142 76% 36% / 0.3);
  }
}

@keyframes insertionPulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% {
    box-shadow: 0 0 15px hsl(var(--primary) / 0.6), 0 0 25px hsl(var(--primary) / 0.3);
  }
}

@keyframes insertionLineExpand {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes insertionButtonBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

@keyframes insertionRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes insertionShimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Insertion indicator utility classes with browser prefixes */
.insertion-slide-in-top {
  -webkit-animation: insertionSlideInTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionSlideInTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionSlideInTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionSlideInTop 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-slide-in-bottom {
  -webkit-animation: insertionSlideInBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionSlideInBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionSlideInBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionSlideInBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-slide-in-left {
  -webkit-animation: insertionSlideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionSlideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionSlideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionSlideInLeft 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-success {
  -webkit-animation: insertionSuccess 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionSuccess 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionSuccess 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionSuccess 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-pulse-glow {
  -webkit-animation: insertionPulseGlow 2s ease-in-out infinite;
  -moz-animation: insertionPulseGlow 2s ease-in-out infinite;
  -o-animation: insertionPulseGlow 2s ease-in-out infinite;
  animation: insertionPulseGlow 2s ease-in-out infinite;
}

.insertion-line-expand {
  -webkit-animation: insertionLineExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionLineExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionLineExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionLineExpand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-button-bounce {
  -webkit-animation: insertionButtonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-animation: insertionButtonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  -o-animation: insertionButtonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: insertionButtonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.insertion-ripple {
  -webkit-animation: insertionRipple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -moz-animation: insertionRipple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  -o-animation: insertionRipple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  animation: insertionRipple 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.insertion-shimmer {
  -webkit-animation: insertionShimmer 2s ease-in-out infinite;
  -moz-animation: insertionShimmer 2s ease-in-out infinite;
  -o-animation: insertionShimmer 2s ease-in-out infinite;
  animation: insertionShimmer 2s ease-in-out infinite;
}

/* Cross-browser compatibility for insertion indicators */
.insertion-indicator {
  /* Ensure hardware acceleration */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  
  /* Smooth transitions */
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Prevent flickering */
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
  
  /* Optimize for animations */
  will-change: opacity, transform;
}

.insertion-button {
  /* Hardware acceleration */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  
  /* Smooth transitions */
  -webkit-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Prevent flickering */
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
}

.insertion-line {
  /* Hardware acceleration */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  
  /* Smooth transitions */
  -webkit-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -moz-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .insertion-indicator,
  .insertion-button,
  .insertion-line,
  .insertion-slide-in-top,
  .insertion-slide-in-bottom,
  .insertion-slide-in-left,
  .insertion-success,
  .insertion-pulse-glow,
  .insertion-line-expand,
  .insertion-button-bounce,
  .insertion-ripple,
  .insertion-shimmer {
    -webkit-animation: none !important;
    -moz-animation: none !important;
    -o-animation: none !important;
    animation: none !important;
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -o-transition: none !important;
    transition: none !important;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 206 78% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 294 60% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 256 15% 11%;
    --foreground: 210 40% 98%;
    --card: 256 15% 13%;
    --card-foreground: 210 40% 98%;
    --popover: 256 15% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 206 78% 58%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 256 15% 18%;
    --secondary-foreground: 210 40% 98%;
    --muted: 256 15% 18%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 294 60% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 256 15% 20%;
    --input: 256 15% 20%;
    --ring: 206 78% 58%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* AI Content Styling Enhancements */
.ai-content {
  /* Verbesserte Typografie */
  line-height: 1.7;
  font-feature-settings: "kern" 1, "liga" 1;
  color: rgb(203 213 225); /* slate-300 */
}

.dark .ai-content {
  color: rgb(203 213 225); /* slate-300 */
}

/* Überschriften */
.ai-content h1,
.ai-content h2 {
  color: rgb(96 165 250); /* blue-400 */
  font-weight: bold;
  scroll-margin-top: 2rem;
  position: relative;
}

.ai-content h3,
.ai-content h4 {
  color: rgb(147 197 253); /* blue-300 */
  font-weight: 600;
  scroll-margin-top: 2rem;
  position: relative;
}

.ai-content h5,
.ai-content h6 {
  color: rgb(203 213 225); /* slate-300 */
  font-weight: 600;
  scroll-margin-top: 2rem;
  position: relative;
}

/* Listen-Styling */
.ai-content ul,
.ai-content ol {
  padding-left: 1.5rem;
  color: rgb(203 213 225); /* slate-300 */
}

.ai-content li {
  margin-bottom: 0.5rem;
  color: rgb(203 213 225); /* slate-300 */
}

.ai-content li::marker {
  color: rgb(96 165 250); /* blue-400 */
}

/* Absätze */
.ai-content p {
  color: rgb(203 213 225); /* slate-300 */
  margin-bottom: 1rem;
}

/* Starker Text */
.ai-content strong {
  color: rgb(226 232 240); /* slate-200 */
  font-weight: 600;
}

/* Hervorgehobener Text */
.ai-content em {
  color: rgb(147 197 253); /* blue-300 */
  font-style: italic;
}

/* Code-Blöcke */
.ai-content pre {
  position: relative;
  border-radius: 0.5rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
  padding: 1rem;
  margin: 1rem 0;
}

/* Light Mode – atom-one-light ähnliche Farben */
.ai-content pre,
.ai-content pre code {
  background: #fafafa !important;
  color: #1f2937 !important; /* slate-800 */
}
.ai-content pre code .hljs-comment,
.ai-content pre code .hljs-quote { color: #6b7280; }
.ai-content pre code .hljs-keyword,
.ai-content pre code .hljs-selector-tag,
.ai-content pre code .hljs-subst { color: #0ea5e9; }
.ai-content pre code .hljs-number,
.ai-content pre code .hljs-literal,
.ai-content pre code .hljs-variable { color: #d97706; }
.ai-content pre code .hljs-string,
.ai-content pre code .hljs-doctag,
.ai-content pre code .hljs-template-variable { color: #059669; }
.ai-content pre code .hljs-attr,
.ai-content pre code .hljs-attribute,
.ai-content pre code .hljs-builtin-name { color: #7c3aed; }
.ai-content pre code .hljs-title,
.ai-content pre code .hljs-section { color: #2563eb; }

/* Dark Mode – monokai ähnliche Farben */
.dark .ai-content pre,
.dark .ai-content pre code {
  background: #0b1220 !important;
  color: #e5e7eb !important;
}
.dark .ai-content pre code .hljs-comment,
.dark .ai-content pre code .hljs-quote { color: #9ca3af; }
.dark .ai-content pre code .hljs-keyword,
.dark .ai-content pre code .hljs-selector-tag,
.dark .ai-content pre code .hljs-subst { color: #60a5fa; }
.dark .ai-content pre code .hljs-number,
.dark .ai-content pre code .hljs-literal,
.dark .ai-content pre code .hljs-variable { color: #fbbf24; }
.dark .ai-content pre code .hljs-string,
.dark .ai-content pre code .hljs-doctag,
.dark .ai-content pre code .hljs-template-variable { color: #34d399; }
.dark .ai-content pre code .hljs-attr,
.dark .ai-content pre code .hljs-attribute,
.dark .ai-content pre code .hljs-builtin-name { color: #a78bfa; }
.dark .ai-content pre code .hljs-title,
.dark .ai-content pre code .hljs-section { color: #93c5fd; }

/* Ausarbeitung-Container: im Light-Mode weiß, im Dark-Mode dunkel */
.ai-content-display {
  background: transparent;
}
.dark .ai-content-display {
  background: transparent;
}

.ai-content code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 0.875em;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  /* Inline-Code neutral, kein gelber Zwangs-Style */
  background-color: rgba(148, 163, 184, 0.15); /* slate-400/15 */
  color: inherit;
}

/* Tabellen */
.ai-content table {
  border-collapse: collapse;
  margin: 1.5rem 0;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  border: 1px solid rgb(71 85 105); /* slate-600 */
  background-color: rgba(30 41 59 / 0.5); /* slate-800/50 */
}

.ai-content th,
.ai-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid rgb(71 85 105); /* slate-600 */
  color: rgb(203 213 225); /* slate-300 */
}

.ai-content th {
  background-color: rgb(51 65 85); /* slate-700 */
  font-weight: 600;
  font-size: 0.875rem;
  color: rgb(226 232 240); /* slate-200 */
}

/* Blockquotes */
.ai-content blockquote {
  position: relative;
  font-style: italic;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  background-color: rgba(30 41 59 / 0.3); /* slate-800/30 */
  border-left: 4px solid rgb(100 116 139); /* slate-500 */
  color: rgb(148 163 184); /* slate-400 */
}

/* Hervorhebungsboxen */
.ai-content .highlight-box {
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  position: relative;
}

.ai-content .highlight-box::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  border-radius: 2px 0 0 2px;
}

/* Info Box */
.ai-content .info-box {
  background-color: rgba(30 41 59 / 0.3); /* slate-800/30 */
  border-left: 4px solid rgb(96 165 250); /* blue-400 */
  color: rgb(147 197 253); /* blue-300 */
}

/* Warning Box */
.ai-content .warning-box {
  background-color: rgba(30 41 59 / 0.3); /* slate-800/30 */
  border-left: 4px solid rgb(251 191 36); /* amber-400 */
  color: rgb(252 211 77); /* amber-300 */
}

/* Success Box */
.ai-content .success-box {
  background-color: rgba(30 41 59 / 0.3); /* slate-800/30 */
  border-left: 4px solid rgb(74 222 128); /* green-400 */
  color: rgb(134 239 172); /* green-300 */
}

/* Animationen für bessere UX */
.ai-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Selection Styling */
.ai-content ::selection {
  background-color: rgb(199 210 254); /* indigo-200 */
  color: rgb(30 27 75); /* indigo-950 */
}

.dark .ai-content ::selection {
  background-color: rgb(67 56 202 / 0.5); /* indigo-700/50 */
  color: rgb(224 231 255); /* indigo-100 */
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Touch-friendly button sizing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Improved spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
    margin: 0.5rem 0;
  }
  
  /* Better text sizing for mobile */
  .mobile-text {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
  }
  
  /* Collapsible task hierarchies */
  .task-hierarchy-mobile {
    padding-left: 1rem;
    border-left: 2px solid rgb(148 163 184 / 0.3);
    margin-left: 0.5rem;
  }
  
  /* Mobile modal optimizations */
  .mobile-modal {
    margin: 0;
    max-height: 90vh;
    border-radius: 1rem 1rem 0 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(0);
  }
  
  /* Mobile form improvements */
  .mobile-form-input {
    font-size: 16px; /* Prevents zoom */
    padding: 0.75rem;
    border-radius: 0.5rem;
  }
  
  /* Swipe action backgrounds */
  .swipe-action-left {
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1));
  }
  
  .swipe-action-right {
    background: linear-gradient(-90deg, transparent, rgba(239, 68, 68, 0.1));
  }
  
  /* Pull-to-refresh styling */
  .pull-to-refresh {
    transform-origin: center top;
    transition: transform 0.2s ease-out;
  }
  
  /* Mobile navigation improvements */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgb(226 232 240);
    padding: 0.5rem;
    z-index: 50;
  }
  
  .dark .mobile-nav {
    background: rgba(15, 23, 42, 0.95);
    border-top-color: rgb(51 65 85);
  }
  
  /* Floating action button */
  .fab {
    position: fixed;
    bottom: 5rem;
    right: 1rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 40;
    transition: all 0.2s ease;
  }
  
  .fab:active {
    transform: scale(0.95);
  }
}

/* Responsive Anpassungen */
@media (max-width: 640px) {
  .ai-content {
    font-size: 0.875rem;
  }
  
  .ai-content h1 { font-size: 1.5rem; }
  .ai-content h2 { font-size: 1.25rem; }
  .ai-content h3 { font-size: 1.125rem; }
  
  .ai-content table {
    font-size: 0.75rem;
  }
  
  .ai-content th,
  .ai-content td {
    padding: 0.5rem;
  }
  
  /* Mobile task list improvements */
  .task-list-mobile {
    padding: 0 0.5rem;
  }
  
  .task-item-mobile {
    margin-bottom: 0.75rem;
    border-radius: 0.75rem;
    padding: 1rem;
  }
  
  /* Improved button spacing for mobile */
  .button-group-mobile {
    gap: 0.75rem;
    flex-wrap: wrap;
  }
  
  .button-group-mobile > * {
    min-height: 44px;
    flex: 1;
    min-width: 0;
  }
}

/* Landscape mobile optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-modal {
    max-height: 80vh;
  }
  
  .fab {
    bottom: 1rem;
  }
  
  .mobile-nav {
    padding: 0.25rem 0.5rem;
  }
}

/* High DPI mobile screens */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
  .task-item-mobile {
    border-width: 0.5px;
  }
}

/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .mobile-nav {
    padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
  }
  
  .fab {
    bottom: max(5rem, calc(5rem + env(safe-area-inset-bottom)));
  }
}

/* Reduced motion for mobile */
@media (prefers-reduced-motion: reduce) and (max-width: 768px) {
  .pull-to-refresh,
  .swipe-action-left,
  .swipe-action-right,
  .fab {
    transition: none !important;
    animation: none !important;
  }
}
