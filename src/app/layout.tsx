import type { Metadata, Viewport } from "next";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import HighlightThemeLoader from '@/components/HighlightThemeLoader';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "KI Projekt-Planer",
  description: "AI-powered project planning application - Transform ideas into actionable tasks",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "KI Projekt-Planer",
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: "KI Projekt-Planer",
    title: "KI Projekt-Planer",
    description: "AI-powered project planning application",
  },
  twitter: {
    card: "summary",
    title: "KI Projekt-Planer",
    description: "AI-powered project planning application",
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Source+Code+Pro:wght@400;500;600&display=swap" rel="stylesheet" />
        
        {/* PWA Meta Tags */}
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-16x16.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="KI Projekt-Planer" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-tap-highlight" content="no" />
        
        {/* Highlighting Styles werden lokal in globals.css definiert (light + dark scoped) */}

        {/* Entferne fremde Attribut-Injektionen durch Browser-Extensions vor der Hydration */}
        <Script id="sanitize-ext-attrs" strategy="beforeInteractive">
          {`
          (function(){
            try {
              // Erweiterte Liste von Browser-Extension-Attributen
              var ATTRS = [
                'bis_skin_checked', 'data-new-gr-c-s-check-loaded', 'data-gr-ext-installed',
                'data-adblock', 'data-extension', 'data-ext-id', 'data-darkreader-mode',
                'data-darkreader-scheme', 'data-lastpass', 'data-1password', 'data-bitwarden',
                'data-dashlane', 'data-honey', 'data-grammarly', 'data-translate',
                'data-extension-id', 'data-ext-name', 'data-plugin-id'
              ];
              
              // Funktion zum Entfernen der Attribute
              function removeExtensionAttrs() {
                ATTRS.forEach(function(name){
                  document.querySelectorAll('['+name+']').forEach(function(el){ 
                    el.removeAttribute(name); 
                  });
                });
              }
              
              // Sofort ausführen
              removeExtensionAttrs();
              
              // Auch nach DOM-Änderungen ausführen
              if (typeof MutationObserver !== 'undefined') {
                var observer = new MutationObserver(function(mutations) {
                  var shouldClean = false;
                  mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && ATTRS.includes(mutation.attributeName)) {
                      shouldClean = true;
                    }
                  });
                  if (shouldClean) {
                    removeExtensionAttrs();
                  }
                });
                
                observer.observe(document.documentElement, {
                  attributes: true,
                  subtree: true,
                  attributeFilter: ATTRS
                });
              }
            } catch(_) {}
          })();
          `}
        </Script>
      </head>
      <body className="font-body antialiased" suppressHydrationWarning>
        {/* highlight.js Themes per Link (wechselseitig aktiviert) */}
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-light.min.css" />
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/monokai.min.css" />
        <HighlightThemeLoader />
        {children}
        <Toaster />
      </body>
    </html>
  );
}
