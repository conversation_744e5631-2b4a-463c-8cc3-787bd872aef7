/**
 * Unit tests for PWA Service Worker functionality
 * Tests caching strategies, offline functionality, and lifecycle management
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { serviceWorkerManager } from '@/lib/pwa/serviceWorker';

// Mock service worker registration
const mockRegistration = {
  installing: null,
  waiting: null,
  active: null,
  scope: '/',
  update: vi.fn(),
  unregister: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  postMessage: vi.fn()
};

// Mock navigator.serviceWorker
const mockServiceWorker = {
  register: vi.fn(),
  getRegistration: vi.fn(),
  controller: null,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

// Mock service worker instance
const mockWorker = {
  state: 'installing',
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  postMessage: vi.fn()
};

describe('PWA Service Worker', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock navigator.serviceWorker
    Object.defineProperty(navigator, 'serviceWorker', {
      value: mockServiceWorker,
      writable: true
    });

    // Mock window events
    Object.defineProperty(window, 'addEventListener', {
      value: vi.fn(),
      writable: true
    });

    Object.defineProperty(window, 'dispatchEvent', {
      value: vi.fn(),
      writable: true
    });

    // Reset registration mock
    mockRegistration.installing = null;
    mockRegistration.waiting = null;
    mockRegistration.active = null;
    mockRegistration.update.mockResolvedValue(undefined);
    mockRegistration.unregister.mockResolvedValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Service Worker Support Detection', () => {
    it('should detect service worker support correctly', () => {
      expect(serviceWorkerManager.isSupported()).toBe(true);
    });

    it('should handle missing service worker support', () => {
      // @ts-ignore - Testing runtime behavior
      delete navigator.serviceWorker;
      expect(serviceWorkerManager.isSupported()).toBe(false);
    });
  });

  describe('Service Worker Registration', () => {
    it('should register service worker successfully', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      const registration = await serviceWorkerManager.register();

      expect(mockServiceWorker.register).toHaveBeenCalledWith('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });
      expect(registration).toBe(mockRegistration);
    });

    it('should handle registration failure gracefully', async () => {
      const error = new Error('Registration failed');
      mockServiceWorker.register.mockRejectedValue(error);

      const registration = await serviceWorkerManager.register();

      expect(registration).toBeNull();
    });

    it('should return null when service worker is not supported', async () => {
      // @ts-ignore - Testing runtime behavior
      delete navigator.serviceWorker;

      const registration = await serviceWorkerManager.register();

      expect(registration).toBeNull();
      expect(mockServiceWorker.register).not.toHaveBeenCalled();
    });

    it('should set up lifecycle listeners after registration', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      expect(mockRegistration.addEventListener).toHaveBeenCalledWith(
        'updatefound',
        expect.any(Function)
      );
    });

    it('should check for immediate updates after registration', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      expect(mockRegistration.update).toHaveBeenCalled();
    });
  });

  describe('Service Worker Lifecycle Events', () => {
    it('should handle updatefound event', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      mockRegistration.installing = mockWorker;

      await serviceWorkerManager.register();

      // Simulate updatefound event
      const updateFoundHandler = mockRegistration.addEventListener.mock.calls
        .find(call => call[0] === 'updatefound')?.[1];
      
      expect(updateFoundHandler).toBeDefined();
      
      if (updateFoundHandler) {
        updateFoundHandler();
        expect(mockWorker.addEventListener).toHaveBeenCalledWith(
          'statechange',
          expect.any(Function)
        );
      }
    });

    it('should dispatch custom events for service worker states', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      mockRegistration.installing = mockWorker;

      await serviceWorkerManager.register();

      // Get the statechange handler
      const updateFoundHandler = mockRegistration.addEventListener.mock.calls
        .find(call => call[0] === 'updatefound')?.[1];
      
      if (updateFoundHandler) {
        updateFoundHandler();
        
        const stateChangeHandler = mockWorker.addEventListener.mock.calls
          .find(call => call[0] === 'statechange')?.[1];

        if (stateChangeHandler) {
          // Test installed state
          mockWorker.state = 'installed';
          mockServiceWorker.controller = mockWorker;
          stateChangeHandler();

          expect(window.dispatchEvent).toHaveBeenCalledWith(
            expect.objectContaining({
              type: 'sw-update-available'
            })
          );
        }
      }
    });

    it('should handle first install vs update scenarios', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      mockRegistration.installing = mockWorker;
      mockServiceWorker.controller = null; // First install

      await serviceWorkerManager.register();

      const updateFoundHandler = mockRegistration.addEventListener.mock.calls
        .find(call => call[0] === 'updatefound')?.[1];
      
      if (updateFoundHandler) {
        updateFoundHandler();
        
        const stateChangeHandler = mockWorker.addEventListener.mock.calls
          .find(call => call[0] === 'statechange')?.[1];

        if (stateChangeHandler) {
          mockWorker.state = 'installed';
          stateChangeHandler();

          // Should dispatch installed event, not update-available
          expect(window.dispatchEvent).toHaveBeenCalledWith(
            expect.objectContaining({
              type: 'sw-installed'
            })
          );
        }
      }
    });
  });

  describe('Service Worker Updates', () => {
    it('should check for updates successfully', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      await serviceWorkerManager.register();

      const result = await serviceWorkerManager.checkForUpdates();

      expect(result).toBe(true);
      expect(mockRegistration.update).toHaveBeenCalled();
    });

    it('should handle update check failure', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      mockRegistration.update.mockRejectedValue(new Error('Update failed'));
      
      await serviceWorkerManager.register();

      const result = await serviceWorkerManager.checkForUpdates();

      expect(result).toBe(false);
    });

    it('should return false when no registration exists', async () => {
      const result = await serviceWorkerManager.checkForUpdates();
      expect(result).toBe(false);
    });

    it('should force update and reload when waiting worker exists', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);
      mockRegistration.waiting = mockWorker;
      
      await serviceWorkerManager.register();

      // Mock location.reload
      const mockReload = vi.fn();
      Object.defineProperty(window, 'location', {
        value: { reload: mockReload },
        writable: true
      });

      await serviceWorkerManager.forceUpdate();

      expect(mockWorker.postMessage).toHaveBeenCalledWith({
        type: 'SKIP_WAITING'
      });
    });
  });

  describe('Service Worker Unregistration', () => {
    it('should unregister service worker successfully', async () => {
      mockServiceWorker.getRegistration.mockResolvedValue(mockRegistration);

      const result = await serviceWorkerManager.unregister();

      expect(result).toBe(true);
      expect(mockRegistration.unregister).toHaveBeenCalled();
    });

    it('should handle unregistration when no registration exists', async () => {
      mockServiceWorker.getRegistration.mockResolvedValue(null);

      const result = await serviceWorkerManager.unregister();

      expect(result).toBe(false);
    });

    it('should handle unregistration failure', async () => {
      mockServiceWorker.getRegistration.mockResolvedValue(mockRegistration);
      mockRegistration.unregister.mockRejectedValue(new Error('Unregister failed'));

      const result = await serviceWorkerManager.unregister();

      expect(result).toBe(false);
    });

    it('should return false when service worker is not supported', async () => {
      // @ts-ignore - Testing runtime behavior
      delete navigator.serviceWorker;

      const result = await serviceWorkerManager.unregister();

      expect(result).toBe(false);
    });
  });

  describe('Offline Functionality', () => {
    it('should handle waiting service worker on registration', async () => {
      mockRegistration.waiting = mockWorker;
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      expect(window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'sw-update-available'
        })
      );
    });

    it('should handle controller change events', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      expect(mockServiceWorker.addEventListener).toHaveBeenCalledWith(
        'controllerchange',
        expect.any(Function)
      );
    });
  });

  describe('Caching Strategies', () => {
    it('should validate service worker scope configuration', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      expect(mockServiceWorker.register).toHaveBeenCalledWith('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });
    });

    it('should handle cache update policies correctly', async () => {
      mockServiceWorker.register.mockResolvedValue(mockRegistration);

      await serviceWorkerManager.register();

      // Verify updateViaCache is set to 'none' for always checking updates
      const registerCall = mockServiceWorker.register.mock.calls[0];
      expect(registerCall[1].updateViaCache).toBe('none');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      mockServiceWorker.register.mockRejectedValue(new Error('Database error'));

      const result = await serviceWorkerManager.register();

      expect(result).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Service Worker registration failed:',
        expect.any(Error)
      );

      consoleErrorSpy.mockRestore();
    });

    it('should handle network errors during registration', async () => {
      const networkError = new Error('Network error');
      networkError.name = 'NetworkError';
      
      mockServiceWorker.register.mockRejectedValue(networkError);

      const result = await serviceWorkerManager.register();

      expect(result).toBeNull();
    });

    it('should handle quota exceeded errors', async () => {
      const quotaError = new Error('Quota exceeded');
      quotaError.name = 'QuotaExceededError';
      
      mockServiceWorker.register.mockRejectedValue(quotaError);

      const result = await serviceWorkerManager.register();

      expect(result).toBeNull();
    });
  });
});