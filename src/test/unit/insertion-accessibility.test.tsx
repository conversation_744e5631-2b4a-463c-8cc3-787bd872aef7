import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { InsertionIndicator } from '@/components/InsertionIndicator';
import { TaskList } from '@/components/TaskList';
import { TaskItem } from '@/components/TaskItem';
import { useKeyboardInsertion } from '@/hooks/useKeyboardInsertion';
import type { Task, InsertionPosition } from '@/lib/types';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { it } from 'node:test';
import { describe } from 'node:test';
import { beforeEach } from 'node:test';
import { describe } from 'node:test';

// Mock dependencies
vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: () => false
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

vi.mock('@/lib/ai', () => ({
  geminiClient: {
    generateSubtasks: vi.fn()
  }
}));

describe('Insertion Accessibility Features', () => {
  const mockTask: Task = {
    id: 'test-task-1',
    title: 'Test Task',
    description: 'Test Description',
    content: '',
    subtasks: [],
    completed: false
  };

  const mockInsertionPosition: InsertionPosition = {
    type: 'after',
    targetTaskId: 'test-task-1',
    parentId: null,
    level: 0
  };

  const mockOnInsert = vi.fn();
  const mockOnUpdateTask = vi.fn();
  const mockOnOpenSolveModal = vi.fn();
  const mockOnAddTask = vi.fn();
  const mockOnAddTaskAfter = vi.fn();
  const mockOnDeleteTask = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('InsertionIndicator Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Insert new task after current task'));
      expect(button).toHaveAttribute('role', 'button');
      expect(button).toHaveAttribute('aria-describedby', 'insertion-help-test-task-1');

      const region = screen.getByRole('region');
      expect(region).toHaveAttribute('aria-label', 'Task insertion zone: after position');
      expect(region).toHaveAttribute('aria-live', 'polite');
      expect(region).toHaveAttribute('aria-atomic', 'true');
    });

    it('should have proper ARIA attributes for different insertion types', () => {
      const beforePosition: InsertionPosition = {
        type: 'before',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      };

      render(
        <InsertionIndicator
          position={beforePosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Insert new task before current task'));

      const region = screen.getByRole('region');
      expect(region).toHaveAttribute('aria-label', 'Task insertion zone: before position');
    });

    it('should have proper ARIA attributes for subtask insertion', () => {
      const subtaskPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'test-task-1',
        parentId: 'test-task-1',
        level: 1
      };

      render(
        <InsertionIndicator
          position={subtaskPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Insert new task as subtask of current task'));

      const region = screen.getByRole('region');
      expect(region).toHaveAttribute('aria-label', 'Task insertion zone: as subtask of position');
    });

    it('should have hidden description for screen readers', () => {
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const description = document.getElementById('insertion-help-test-task-1');
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass('sr-only');
      expect(description.textContent).toMatch(/Insert.*task.*after/i);
    });

    it('should be keyboard accessible', async () => {
      const user = userEvent.setup();
      
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      // Test Enter key
      await user.click(button);
      await user.keyboard('{Enter}');
      expect(mockOnInsert).toHaveBeenCalled();

      // Test Space key
      mockOnInsert.mockClear();
      await user.keyboard(' ');
      expect(mockOnInsert).toHaveBeenCalled();
    });

    it('should have proper focus management', async () => {
      const user = userEvent.setup();
      
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      await user.tab();
      expect(button).toHaveFocus();
      expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-primary/50');
    });

    it('should support long press with proper ARIA attributes', () => {
      const mockOnLongPress = vi.fn();
      
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-haspopup', 'menu');
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Long press for additional options'));
    });
  });

  describe('TaskList Accessibility', () => {
    it('should have proper list role and ARIA attributes', () => {
      render(
        <TaskList
          tasks={[mockTask]}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
        />
      );

      const list = screen.getByRole('list');
      expect(list).toHaveAttribute('aria-label', expect.stringContaining('Task list at level 1'));
      expect(list).toHaveAttribute('aria-describedby', 'task-list-help');

      const helpText = document.getElementById('task-list-help');
      expect(helpText).toBeInTheDocument();
      expect(helpText).toHaveClass('sr-only');
      expect(helpText.textContent).toMatch(/Task list.*insertion/i);
    });

    it('should not duplicate help text for nested lists', () => {
      const taskWithSubtasks: Task = {
        ...mockTask,
        subtasks: [{
          id: 'subtask-1',
          title: 'Subtask',
          description: '',
          content: '',
          subtasks: [],
          completed: false
        }]
      };

      render(
        <TaskList
          tasks={[taskWithSubtasks]}
          level={1}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
        />
      );

      const lists = screen.getAllByRole('list');
      const nestedList = lists.find(list => 
        list.getAttribute('aria-label')?.includes('Task list at level 2')
      );
      
      if (nestedList) {
        expect(nestedList).not.toHaveAttribute('aria-describedby');
      }
    });
  });

  describe('TaskItem Accessibility', () => {
    it('should have proper listitem role and ARIA attributes', () => {
      render(
        <TaskItem
          task={mockTask}
          level={0}
          parentId={null}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
          numbering="1"
        />
      );

      const listItem = screen.getByRole('listitem');
      expect(listItem).toHaveAttribute('aria-label', expect.stringContaining('Task: Test Task. Level 1'));
      expect(listItem).toHaveAttribute('aria-describedby', 'task-description-test-task-1');
      expect(listItem).toHaveAttribute('tabIndex', '0');

      const description = document.getElementById('task-description-test-task-1');
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass('sr-only');
      expect(description.textContent).toMatch(/Task.*level/i);
    });

    it('should include subtask information in ARIA label', () => {
      const taskWithSubtasks: Task = {
        ...mockTask,
        subtasks: [
          { id: 'sub1', title: 'Sub1', description: '', content: '', subtasks: [], completed: false },
          { id: 'sub2', title: 'Sub2', description: '', content: '', subtasks: [], completed: false }
        ]
      };

      render(
        <TaskItem
          task={taskWithSubtasks}
          level={0}
          parentId={null}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
          numbering="1"
        />
      );

      const listItems = screen.getAllByRole('listitem');
      const listItem = listItems[0]; // Get the main task item
      expect(listItem).toHaveAttribute('aria-label', expect.stringContaining('Has 2 subtasks'));

      const description = document.getElementById('task-description-test-task-1');
      expect(description.textContent).toMatch(/Contains.*2.*subtask/i);
    });

    it('should be keyboard focusable', async () => {
      const user = userEvent.setup();
      
      render(
        <TaskItem
          task={mockTask}
          level={0}
          parentId={null}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
          numbering="1"
        />
      );

      const listItem = screen.getByRole('listitem');
      
      await user.tab();
      expect(listItem).toHaveFocus();
      expect(listItem).toHaveClass('focus:outline-none', 'focus:ring-2');
    });
  });

  describe('Keyboard Insertion Accessibility', () => {
    // Mock component to test the hook
    const TestComponent: React.FC<{ tasks: Task[] }> = ({ tasks }) => {
      const { focusedTask, keyboardMode, availableInsertionPoints } = useKeyboardInsertion({
        tasks,
        onInsertTask: mockOnInsert,
        enabled: true
      });

      return (
        <div>
          <div data-testid="focused-task">
            {focusedTask ? focusedTask.taskId : 'none'}
          </div>
          <div data-testid="keyboard-mode">
            {keyboardMode ? 'active' : 'inactive'}
          </div>
          <div data-testid="insertion-points">
            {availableInsertionPoints.length}
          </div>
          {tasks.map(task => (
            <div key={task.id} data-task-id={task.id} tabIndex={0}>
              {task.title}
            </div>
          ))}
        </div>
      );
    };

    it('should create global keyboard help element', () => {
      render(<TestComponent tasks={[mockTask]} />);

      const helpElement = document.getElementById('keyboard-insertion-help');
      expect(helpElement).toBeInTheDocument();
      expect(helpElement).toHaveClass('sr-only');
      expect(helpElement.textContent).toMatch(/Enter.*insert/i);
    });

    it('should announce focus changes to screen readers', async () => {
      const user = userEvent.setup();
      
      // Mock the announceToScreenReader function
      const mockAnnounce = vi.fn();
      
      render(<TestComponent tasks={[mockTask]} />);

      const taskElement = screen.getByText('Test Task');
      await user.click(taskElement);

      // Check that the task is focused
      await waitFor(() => {
        expect(screen.getByTestId('focused-task')).toHaveTextContent('test-task-1');
      });
    });

    it('should manage focus attributes on task elements', async () => {
      render(<TestComponent tasks={[mockTask]} />);

      // Wait for the hook to initialize and set up focus management
      await waitFor(() => {
        const helpElement = document.getElementById('keyboard-insertion-help');
        expect(helpElement).toBeInTheDocument();
      });

      const taskElement = screen.getByText('Test Task').closest('[data-task-id]');
      expect(taskElement).toHaveAttribute('tabindex', '0');
      expect(taskElement).toHaveAttribute('data-task-id', 'test-task-1');
      
      // The aria-describedby might be set by the hook's ensureFocusManagement function
      // Let's check if it exists or if the element has the proper attributes
      const hasAriaDescribedBy = taskElement?.hasAttribute('aria-describedby');
      if (hasAriaDescribedBy) {
        expect(taskElement).toHaveAttribute('aria-describedby', 'keyboard-insertion-help');
      }
    });

    it('should clean up global help element on unmount', () => {
      const { unmount } = render(<TestComponent tasks={[mockTask]} />);

      const helpElement = document.getElementById('keyboard-insertion-help');
      expect(helpElement).toBeInTheDocument();

      unmount();

      const helpElementAfterUnmount = document.getElementById('keyboard-insertion-help');
      expect(helpElementAfterUnmount).not.toBeInTheDocument();
    });
  });

  describe('High Contrast Mode Support', () => {
    it('should apply high contrast styles when prefers-contrast is high', () => {
      // Mock CSS media query
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(prefers-contrast: high)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Check that the component renders without errors in high contrast mode
      expect(button).toHaveClass('insertion-button');
    });

    it('should apply forced colors mode styles', () => {
      // Mock CSS media query for forced colors
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(forced-colors: active)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      
      // Check that the component renders without errors in forced colors mode
      expect(button).toHaveClass('insertion-button');
    });
  });

  describe('Screen Reader Announcements', () => {
    it('should create announcement elements for screen readers', () => {
      // This test would need to be more sophisticated to actually test the announcements
      // For now, we just verify the component structure supports announcements
      render(
        <InsertionIndicator
          position={mockInsertionPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const region = screen.getByRole('region');
      expect(region).toHaveAttribute('aria-live', 'polite');
      expect(region).toHaveAttribute('aria-atomic', 'true');
    });
  });
});