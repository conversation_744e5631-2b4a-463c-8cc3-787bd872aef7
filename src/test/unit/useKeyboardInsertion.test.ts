import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useKeyboardInsertion, formatShortcutForDisplay, getAvailableShortcuts } from '@/hooks/useKeyboardInsertion';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock DOM methods
Object.defineProperty(window, 'getSelection', {
  writable: true,
  value: vi.fn(() => ({
    isCollapsed: true,
    toString: () => '',
    getRangeAt: () => ({
      cloneRange: () => ({})
    })
  }))
});

// Mock document.querySelector and querySelectorAll
const mockQuerySelector = vi.fn();
const mockQuerySelectorAll = vi.fn();
Object.defineProperty(document, 'querySelector', {
  writable: true,
  value: mockQuerySelector
});
Object.defineProperty(document, 'querySelectorAll', {
  writable: true,
  value: mockQuerySelectorAll
});

// Mock addEventListener and removeEventListener
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();
Object.defineProperty(document, 'addEventListener', {
  writable: true,
  value: mockAddEventListener
});
Object.defineProperty(document, 'removeEventListener', {
  writable: true,
  value: mockRemoveEventListener
});

describe('useKeyboardInsertion', () => {
  const mockTasks: Task[] = [
    {
      id: 'task-1',
      title: 'Task 1',
      description: 'Description 1',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: [
        {
          id: 'task-1-1',
          title: 'Subtask 1.1',
          description: 'Subtask description',
          content: '',
          status: 'To Do',
          assignees: [],
          subtasks: []
        }
      ]
    },
    {
      id: 'task-2',
      title: 'Task 2',
      description: 'Description 2',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    }
  ];

  const mockOnInsertTask = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockQuerySelector.mockReturnValue(null);
    mockQuerySelectorAll.mockReturnValue([]);
  });

  afterEach(() => {
    // Clean up any event listeners
    act(() => {
      // Trigger cleanup
    });
  });

  describe('Hook initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(result.current.focusedTask).toBeNull();
      expect(result.current.keyboardMode).toBe(false);
      expect(result.current.availableInsertionPoints).toEqual([]);
      expect(result.current.currentInsertionIndex).toBe(0);
    });

    it('should set up event listeners when enabled', () => {
      renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function));
      expect(mockAddEventListener).toHaveBeenCalledWith('focusin', expect.any(Function));
      expect(mockAddEventListener).toHaveBeenCalledWith('click', expect.any(Function));
    });

    it('should not set up event listeners when disabled', () => {
      renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: false
        })
      );

      expect(mockAddEventListener).not.toHaveBeenCalled();
    });
  });

  describe('Focus management', () => {
    it('should update focused task when updateFocusedTask is called', () => {
      // Mock DOM element for task
      const mockElement = {
        getAttribute: vi.fn().mockReturnValue('task-1'),
        getBoundingClientRect: vi.fn().mockReturnValue({
          top: 0,
          left: 0,
          width: 100,
          height: 50
        })
      };
      mockQuerySelector.mockReturnValue(mockElement);

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      expect(result.current.focusedTask).toEqual({
        taskId: 'task-1',
        task: mockTasks[0],
        level: 0,
        parentId: null,
        element: mockElement
      });
    });

    it('should calculate insertion points for focused task', () => {
      const mockElement = {
        getAttribute: vi.fn().mockReturnValue('task-1'),
        getBoundingClientRect: vi.fn().mockReturnValue({
          top: 0,
          left: 0,
          width: 100,
          height: 50
        })
      };
      mockQuerySelector.mockReturnValue(mockElement);

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      expect(result.current.availableInsertionPoints).toHaveLength(3);
      expect(result.current.availableInsertionPoints).toEqual([
        {
          type: 'after',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0
        },
        {
          type: 'before',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0
        },
        {
          type: 'between_parent_child',
          targetTaskId: 'task-1',
          parentId: 'task-1',
          level: 1
        }
      ]);
    });
  });

  describe('Keyboard shortcuts', () => {
    it('should use default shortcuts when none provided', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(result.current.activeShortcuts).toEqual({
        insertAfter: 'Enter',
        insertBefore: 'Shift+Enter',
        insertSubtask: 'Tab+Enter',
        insertBetweenParentChild: 'Ctrl+Enter'
      });
    });

    it('should merge custom shortcuts with defaults', () => {
      const customShortcuts = {
        insertAfter: 'Space',
        insertBefore: 'Shift+Space'
      };

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          shortcuts: customShortcuts,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(result.current.activeShortcuts).toEqual({
        insertAfter: 'Space',
        insertBefore: 'Shift+Space',
        insertSubtask: 'Tab+Enter',
        insertBetweenParentChild: 'Ctrl+Enter'
      });
    });
  });

  describe('Task navigation', () => {
    it('should navigate to next task', () => {
      const mockElements = [
        { 
          getAttribute: vi.fn().mockReturnValue('task-1'), 
          focus: vi.fn(),
          getBoundingClientRect: vi.fn().mockReturnValue({ width: 100, height: 50 }),
          hasAttribute: vi.fn().mockReturnValue(false)
        },
        { 
          getAttribute: vi.fn().mockReturnValue('task-2'), 
          focus: vi.fn(),
          getBoundingClientRect: vi.fn().mockReturnValue({ width: 100, height: 50 }),
          hasAttribute: vi.fn().mockReturnValue(false)
        }
      ];
      mockQuerySelectorAll.mockReturnValue(mockElements);
      mockQuerySelector.mockImplementation((selector) => {
        if (selector.includes('task-1')) return mockElements[0];
        if (selector.includes('task-2')) return mockElements[1];
        return null;
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Set initial focus
      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      // Navigate to next task
      act(() => {
        result.current.navigateToTask('next');
      });

      expect(mockElements[1].focus).toHaveBeenCalled();
    });

    it('should navigate to previous task', () => {
      const mockElements = [
        { 
          getAttribute: vi.fn().mockReturnValue('task-1'), 
          focus: vi.fn(),
          getBoundingClientRect: vi.fn().mockReturnValue({ width: 100, height: 50 }),
          hasAttribute: vi.fn().mockReturnValue(false)
        },
        { 
          getAttribute: vi.fn().mockReturnValue('task-2'), 
          focus: vi.fn(),
          getBoundingClientRect: vi.fn().mockReturnValue({ width: 100, height: 50 }),
          hasAttribute: vi.fn().mockReturnValue(false)
        }
      ];
      mockQuerySelectorAll.mockReturnValue(mockElements);
      mockQuerySelector.mockImplementation((selector) => {
        if (selector.includes('task-1')) return mockElements[0];
        if (selector.includes('task-2')) return mockElements[1];
        return null;
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Set initial focus to second task
      act(() => {
        result.current.updateFocusedTask('task-2');
      });

      // Navigate to previous task
      act(() => {
        result.current.navigateToTask('previous');
      });

      expect(mockElements[0].focus).toHaveBeenCalled();
    });
  });

  describe('Keyboard mode', () => {
    it('should enter keyboard mode when setKeyboardMode is called', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.setKeyboardMode(true);
      });

      expect(result.current.keyboardMode).toBe(true);
    });

    it('should exit keyboard mode when setKeyboardMode is called with false', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: mockTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.setKeyboardMode(true);
      });

      act(() => {
        result.current.setKeyboardMode(false);
      });

      expect(result.current.keyboardMode).toBe(false);
    });
  });
});

describe('Utility functions', () => {
  describe('getAvailableShortcuts', () => {
    it('should return default shortcuts', () => {
      const shortcuts = getAvailableShortcuts();
      expect(shortcuts).toEqual({
        insertAfter: 'Enter',
        insertBefore: 'Shift+Enter',
        insertSubtask: 'Tab+Enter',
        insertBetweenParentChild: 'Ctrl+Enter'
      });
    });
  });

  describe('formatShortcutForDisplay', () => {
    it('should format simple shortcuts', () => {
      expect(formatShortcutForDisplay('Enter')).toBe('↵');
      expect(formatShortcutForDisplay('Tab')).toBe('⇥');
    });

    it('should format shortcuts with modifiers', () => {
      expect(formatShortcutForDisplay('Ctrl+Enter')).toBe('⌃↵');
      expect(formatShortcutForDisplay('Shift+Enter')).toBe('⇧↵');
      expect(formatShortcutForDisplay('Alt+Tab')).toBe('⌥⇥');
      expect(formatShortcutForDisplay('Meta+Space')).toBe('⌘SPACE');
    });

    it('should format complex shortcuts', () => {
      expect(formatShortcutForDisplay('Ctrl+Shift+Enter')).toBe('⌃⇧↵');
      expect(formatShortcutForDisplay('Tab+Enter')).toBe('⇥↵');
    });
  });
});