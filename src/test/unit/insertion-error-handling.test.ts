/**
 * Comprehensive error handling tests for insertion functionality
 * Tests edge cases, error recovery, and graceful degradation
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useInsertionZones } from '@/hooks/useInsertionZones';
import { useKeyboardInsertion } from '@/hooks/useKeyboardInsertion';
import { 
  validateInsertionPosition,
  createInsertionValidator,
  TaskInsertionValidator
} from '@/lib/utils/insertionValidation';
import { 
  calculateZoneBoundaries,
  detectInsertionZone,
  optimizeZoneCalculations
} from '@/lib/utils/insertionZoneUtils';
import { zoneCacheUtils } from '@/lib/utils/insertionZoneCache';
import type { Task, InsertionPosition, InsertionZone } from '@/lib/types';

// Mock console methods to test error logging
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
const mockConsoleWarn = vi.spyOn(console, 'warn').mockImplementation(() => {});

beforeEach(() => {
  vi.clearAllMocks();
  mockConsoleError.mockClear();
  mockConsoleWarn.mockClear();
});

// Test data
const createTestTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
  id,
  title,
  description: `Description for ${title}`,
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks
});

const validTasks: Task[] = [
  createTestTask('task-1', 'Task 1', [
    createTestTask('task-1-1', 'Subtask 1.1')
  ]),
  createTestTask('task-2', 'Task 2')
];

describe('Insertion Zone Error Handling', () => {
  describe('DOM element access errors', () => {
    it('should handle null task elements gracefully', () => {
      const { result } = renderHook(() => useInsertionZones({
        task: validTasks[0],
        level: 0,
        parentId: null,
        taskElement: null
      }));

      expect(result.current.zones).toEqual([]);
      expect(result.current.isCalculating).toBe(false);
      expect(() => result.current.findZoneAtPoint(0, 0)).not.toThrow();
    });

    it('should handle getBoundingClientRect failures', () => {
      const faultyElement = {
        getBoundingClientRect: vi.fn().mockImplementation(() => {
          throw new Error('DOM access failed');
        }),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(faultyElement, validTasks[0], 0, null);
      }).not.toThrow();

      // Should log error but continue execution
      expect(mockConsoleError).toHaveBeenCalled();
    });

    it('should handle querySelector failures', () => {
      const elementWithFaultyQuery = {
        getBoundingClientRect: vi.fn().mockReturnValue(new DOMRect(0, 0, 100, 50)),
        querySelector: vi.fn().mockImplementation(() => {
          throw new Error('Query failed');
        })
      } as unknown as HTMLElement;

      const zones = calculateZoneBoundaries(elementWithFaultyQuery, validTasks[0], 0, null);
      
      // Should return basic zones even if subtask query fails
      expect(Array.isArray(zones)).toBe(true);
      expect(zones.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle invalid DOMRect values', () => {
      const elementWithInvalidRect = {
        getBoundingClientRect: vi.fn().mockReturnValue({
          left: NaN,
          top: Infinity,
          width: -100,
          height: null
        } as any),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(elementWithInvalidRect, validTasks[0], 0, null);
      }).not.toThrow();
    });
  });

  describe('Zone calculation edge cases', () => {
    it('should handle tasks with circular references', () => {
      const circularTask: Task = createTestTask('circular', 'Circular Task');
      // Create circular reference (this shouldn't happen in practice)
      circularTask.subtasks = [circularTask];

      const mockElement = {
        getBoundingClientRect: vi.fn().mockReturnValue(new DOMRect(0, 0, 100, 50)),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(mockElement, circularTask, 0, null);
      }).not.toThrow();
    });

    it('should handle extremely deep nesting', () => {
      let deepTask = createTestTask('deep-0', 'Deep Task 0');
      let currentTask = deepTask;

      // Create 100 levels of nesting
      for (let i = 1; i < 100; i++) {
        const nextTask = createTestTask(`deep-${i}`, `Deep Task ${i}`);
        currentTask.subtasks = [nextTask];
        currentTask = nextTask;
      }

      const mockElement = {
        getBoundingClientRect: vi.fn().mockReturnValue(new DOMRect(0, 0, 100, 50)),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(mockElement, deepTask, 99, `deep-98`);
      }).not.toThrow();
    });

    it('should handle tasks with malformed subtask arrays', () => {
      const malformedTask = {
        ...validTasks[0],
        subtasks: [null, undefined, 'invalid', { id: 'partial' }] as any
      };

      const mockElement = {
        getBoundingClientRect: vi.fn().mockReturnValue(new DOMRect(0, 0, 100, 50)),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(mockElement, malformedTask, 0, null);
      }).not.toThrow();
    });

    it('should handle zone detection with invalid coordinates', () => {
      const validZones: InsertionZone[] = [
        {
          id: 'test-zone',
          type: 'before',
          bounds: new DOMRect(0, 0, 100, 20),
          targetTaskId: 'task-1',
          parentId: null,
          level: 0,
          metadata: { isVisible: true, priority: 1, touchFriendly: false }
        }
      ];

      const invalidCoordinates = [
        [NaN, 0],
        [0, Infinity],
        [-Infinity, -Infinity],
        [null as any, undefined as any]
      ];

      invalidCoordinates.forEach(([x, y]) => {
        expect(() => {
          detectInsertionZone(x, y, validZones);
        }).not.toThrow();
      });
    });
  });

  describe('Zone optimization error handling', () => {
    it('should handle optimization with invalid zones', () => {
      const invalidZones = [
        null,
        undefined,
        { id: 'incomplete' },
        { 
          id: 'invalid-bounds',
          type: 'before',
          bounds: null,
          targetTaskId: 'task-1'
        }
      ] as any;

      expect(() => {
        optimizeZoneCalculations(invalidZones);
      }).not.toThrow();
    });

    it('should handle memory pressure during optimization', () => {
      // Create a large number of zones to test memory handling
      const manyZones: InsertionZone[] = Array.from({ length: 10000 }, (_, i) => ({
        id: `zone-${i}`,
        type: 'before',
        bounds: new DOMRect(i, i, 100, 20),
        targetTaskId: `task-${i}`,
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      }));

      expect(() => {
        optimizeZoneCalculations(manyZones);
      }).not.toThrow();
    });
  });

  describe('Cache error handling', () => {
    beforeEach(() => {
      zoneCacheUtils.clear();
    });

    it('should handle cache corruption gracefully', () => {
      // Manually corrupt the cache
      const corruptedData = { invalid: 'data' };
      (zoneCacheUtils as any).cache.set('corrupted-key', corruptedData);

      expect(() => {
        zoneCacheUtils.get('corrupted-key');
      }).not.toThrow();
    });

    it('should handle cache storage failures', () => {
      const originalSet = Map.prototype.set;
      Map.prototype.set = vi.fn().mockImplementation(() => {
        throw new Error('Storage failed');
      });

      const zones: InsertionZone[] = [
        {
          id: 'test-zone',
          type: 'before',
          bounds: new DOMRect(0, 0, 100, 20),
          targetTaskId: 'task-1',
          parentId: null,
          level: 0,
          metadata: { isVisible: true, priority: 1, touchFriendly: false }
        }
      ];

      expect(() => {
        zoneCacheUtils.set('test-key', zones, new DOMRect(0, 0, 100, 50));
      }).not.toThrow();

      // Restore original method
      Map.prototype.set = originalSet;
    });

    it('should handle cache retrieval failures', () => {
      const originalGet = Map.prototype.get;
      Map.prototype.get = vi.fn().mockImplementation(() => {
        throw new Error('Retrieval failed');
      });

      expect(() => {
        zoneCacheUtils.get('any-key');
      }).not.toThrow();

      // Should return null on failure
      const result = zoneCacheUtils.get('any-key');
      expect(result).toBeNull();

      // Restore original method
      Map.prototype.get = originalGet;
    });
  });
});

describe('Keyboard Insertion Error Handling', () => {
  const mockOnInsertTask = vi.fn();

  beforeEach(() => {
    mockOnInsertTask.mockClear();
  });

  describe('Event listener errors', () => {
    it('should handle addEventListener failures', () => {
      const originalAddEventListener = document.addEventListener;
      document.addEventListener = vi.fn().mockImplementation(() => {
        throw new Error('Event listener failed');
      });

      expect(() => {
        renderHook(() => useKeyboardInsertion({
          tasks: validTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        }));
      }).not.toThrow();

      // Restore original method
      document.addEventListener = originalAddEventListener;
    });

    it('should handle removeEventListener failures', () => {
      const originalRemoveEventListener = document.removeEventListener;
      document.removeEventListener = vi.fn().mockImplementation(() => {
        throw new Error('Remove listener failed');
      });

      const { unmount } = renderHook(() => useKeyboardInsertion({
        tasks: validTasks,
        onInsertTask: mockOnInsertTask,
        enabled: true
      }));

      expect(() => {
        unmount();
      }).not.toThrow();

      // Restore original method
      document.removeEventListener = originalRemoveEventListener;
    });

    it('should handle callback errors during event handling', () => {
      const faultyCallback = vi.fn().mockImplementation(() => {
        throw new Error('Callback failed');
      });

      const { result } = renderHook(() => useKeyboardInsertion({
        tasks: validTasks,
        onInsertTask: faultyCallback,
        enabled: true
      }));

      // Should not crash when callback fails
      expect(() => {
        act(() => {
          result.current.updateFocusedTask('task-1');
        });
      }).not.toThrow();
    });
  });

  describe('Focus management errors', () => {
    it('should handle missing DOM elements during focus updates', () => {
      const originalQuerySelector = document.querySelector;
      document.querySelector = vi.fn().mockReturnValue(null);

      const { result } = renderHook(() => useKeyboardInsertion({
        tasks: validTasks,
        onInsertTask: mockOnInsertTask,
        enabled: true
      }));

      expect(() => {
        act(() => {
          result.current.updateFocusedTask('nonexistent-task');
        });
      }).not.toThrow();

      expect(result.current.focusedTask).toBeNull();

      // Restore original method
      document.querySelector = originalQuerySelector;
    });

    it('should handle focus() method failures', () => {
      const mockElement = {
        getAttribute: vi.fn().mockReturnValue('task-1'),
        getBoundingClientRect: vi.fn().mockReturnValue(new DOMRect(0, 0, 100, 50)),
        focus: vi.fn().mockImplementation(() => {
          throw new Error('Focus failed');
        })
      };

      const originalQuerySelector = document.querySelector;
      document.querySelector = vi.fn().mockReturnValue(mockElement);

      const { result } = renderHook(() => useKeyboardInsertion({
        tasks: validTasks,
        onInsertTask: mockOnInsertTask,
        enabled: true
      }));

      expect(() => {
        act(() => {
          result.current.navigateToTask('next');
        });
      }).not.toThrow();

      // Restore original method
      document.querySelector = originalQuerySelector;
    });

    it('should handle navigation with empty task lists', () => {
      const { result } = renderHook(() => useKeyboardInsertion({
        tasks: [],
        onInsertTask: mockOnInsertTask,
        enabled: true
      }));

      expect(() => {
        act(() => {
          result.current.navigateToTask('next');
        });
      }).not.toThrow();

      expect(() => {
        act(() => {
          result.current.navigateToTask('previous');
        });
      }).not.toThrow();
    });
  });

  describe('Shortcut configuration errors', () => {
    it('should handle invalid shortcut configurations', () => {
      const invalidShortcuts = {
        insertAfter: null,
        insertBefore: undefined,
        insertSubtask: 123,
        insertBetweenParentChild: {}
      } as any;

      expect(() => {
        renderHook(() => useKeyboardInsertion({
          tasks: validTasks,
          shortcuts: invalidShortcuts,
          onInsertTask: mockOnInsertTask,
          enabled: true
        }));
      }).not.toThrow();
    });

    it('should handle shortcut parsing errors', () => {
      const malformedShortcuts = {
        insertAfter: 'Ctrl+Alt+Shift+Meta+Super+Hyper+Enter', // Too complex
        insertBefore: '+++', // Invalid format
        insertSubtask: '', // Empty string
        insertBetweenParentChild: 'InvalidKey'
      };

      expect(() => {
        renderHook(() => useKeyboardInsertion({
          tasks: validTasks,
          shortcuts: malformedShortcuts,
          onInsertTask: mockOnInsertTask,
          enabled: true
        }));
      }).not.toThrow();
    });
  });

  describe('Task data errors', () => {
    it('should handle malformed task data', () => {
      const malformedTasks = [
        null,
        undefined,
        { id: 'incomplete' },
        { id: 'task-1', title: null, subtasks: 'invalid' },
        { id: '', title: 'Empty ID Task' }
      ] as any;

      expect(() => {
        renderHook(() => useKeyboardInsertion({
          tasks: malformedTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        }));
      }).not.toThrow();
    });

    it('should handle task updates during keyboard operations', () => {
      const { result, rerender } = renderHook(
        ({ tasks }) => useKeyboardInsertion({
          tasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        }),
        { initialProps: { tasks: validTasks } }
      );

      // Focus a task
      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      // Update tasks to remove the focused task
      const updatedTasks = validTasks.filter(task => task.id !== 'task-1');
      
      expect(() => {
        rerender({ tasks: updatedTasks });
      }).not.toThrow();

      // Should handle gracefully when focused task is removed
      expect(() => {
        act(() => {
          result.current.navigateToTask('next');
        });
      }).not.toThrow();
    });
  });
});

describe('Validation Error Handling', () => {
  describe('Validator initialization errors', () => {
    it('should handle invalid configuration objects', () => {
      const invalidConfigs = [
        null,
        undefined,
        'string',
        123,
        [],
        { maxDepth: 'invalid' },
        { maxSiblings: -1 },
        { allowedPositions: 'not-array' },
        { parentRestrictions: null }
      ] as any;

      invalidConfigs.forEach(config => {
        expect(() => {
          createInsertionValidator(config);
        }).not.toThrow();
      });
    });

    it('should provide default values for missing configuration', () => {
      const validator = createInsertionValidator({});
      const constraints = validator.getInsertionConstraints('any-task');

      expect(typeof constraints.maxDepth).toBe('number');
      expect(typeof constraints.maxSiblings).toBe('number');
      expect(Array.isArray(constraints.allowedPositions)).toBe(true);
      expect(Array.isArray(constraints.parentRestrictions)).toBe(true);
    });
  });

  describe('Position validation errors', () => {
    it('should handle null and undefined positions', () => {
      const validator = new TaskInsertionValidator();

      expect(() => {
        validator.validatePosition(null as any, validTasks);
      }).not.toThrow();

      expect(() => {
        validator.validatePosition(undefined as any, validTasks);
      }).not.toThrow();

      const nullResult = validator.validatePosition(null as any, validTasks);
      expect(nullResult.isValid).toBe(false);
      expect(nullResult.errors.length).toBeGreaterThan(0);
    });

    it('should handle positions with missing required fields', () => {
      const incompletePositions = [
        {},
        { type: 'after' },
        { targetTaskId: 'task-1' },
        { type: 'after', targetTaskId: '' },
        { type: '', targetTaskId: 'task-1' }
      ] as any;

      const validator = new TaskInsertionValidator();

      incompletePositions.forEach(position => {
        expect(() => {
          validator.validatePosition(position, validTasks);
        }).not.toThrow();

        const result = validator.validatePosition(position, validTasks);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    it('should handle validation with corrupted task data', () => {
      const corruptedTasks = [
        { id: null, title: 'Null ID' },
        { id: 'task-1', subtasks: 'not-array' },
        { id: 'task-2', title: undefined, subtasks: [null, undefined] },
        null,
        undefined
      ] as any;

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      expect(() => {
        validateInsertionPosition(position, corruptedTasks);
      }).not.toThrow();
    });

    it('should handle recursive validation failures', () => {
      const validator = new TaskInsertionValidator();
      
      // Mock internal method to throw error
      const originalValidateBasicProperties = (validator as any).validateBasicProperties;
      (validator as any).validateBasicProperties = vi.fn().mockImplementation(() => {
        throw new Error('Internal validation failed');
      });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      expect(() => {
        validator.validatePosition(position, validTasks);
      }).not.toThrow();

      // Should return error result instead of throwing
      const result = validator.validatePosition(position, validTasks);
      expect(result.isValid).toBe(false);

      // Restore original method
      (validator as any).validateBasicProperties = originalValidateBasicProperties;
    });
  });

  describe('Alternative suggestion errors', () => {
    it('should handle errors during alternative generation', () => {
      const position: InsertionPosition = {
        type: 'invalid' as any,
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      expect(() => {
        validateInsertionPosition(position, validTasks);
      }).not.toThrow();

      const result = validateInsertionPosition(position, validTasks);
      expect(result.isValid).toBe(false);
      expect(Array.isArray(result.suggestedAlternatives)).toBe(true);
    });

    it('should handle circular references in alternative generation', () => {
      const circularTasks = [...validTasks];
      // Create a task that references itself as parent
      const circularTask = createTestTask('circular', 'Circular Task');
      circularTask.subtasks = [circularTask]; // This creates a circular reference
      circularTasks.push(circularTask);

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'circular',
        parentId: 'circular',
        level: 1
      };

      expect(() => {
        validateInsertionPosition(position, circularTasks);
      }).not.toThrow();
    });
  });

  describe('Performance under error conditions', () => {
    it('should maintain performance during validation errors', () => {
      const validator = new TaskInsertionValidator();
      const invalidPositions = Array.from({ length: 100 }, (_, i) => ({
        type: 'invalid' as any,
        targetTaskId: `nonexistent-${i}`,
        parentId: null,
        level: -1
      }));

      const startTime = performance.now();
      
      invalidPositions.forEach(position => {
        try {
          validator.validatePosition(position, validTasks);
        } catch (error) {
          // Ignore errors for performance test
        }
      });

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle memory pressure during error recovery', () => {
      const validator = new TaskInsertionValidator();
      const largeTasks = Array.from({ length: 1000 }, (_, i) =>
        createTestTask(`large-task-${i}`, `Large Task ${i}`)
      );

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'nonexistent-task',
        parentId: null,
        level: 0
      };

      expect(() => {
        validator.validatePosition(position, largeTasks);
      }).not.toThrow();

      // Should complete without memory issues
      const result = validator.validatePosition(position, largeTasks);
      expect(typeof result.isValid).toBe('boolean');
    });
  });

  describe('Concurrent error handling', () => {
    it('should handle concurrent validation failures', async () => {
      const validator = new TaskInsertionValidator();
      const invalidPositions = Array.from({ length: 50 }, (_, i) => ({
        type: 'invalid' as any,
        targetTaskId: `invalid-${i}`,
        parentId: null,
        level: -1
      }));

      const validationPromises = invalidPositions.map(position =>
        Promise.resolve().then(() => validator.validatePosition(position, validTasks))
      );

      expect(async () => {
        await Promise.all(validationPromises);
      }).not.toThrow();

      const results = await Promise.all(validationPromises);
      expect(results).toHaveLength(50);
      results.forEach(result => {
        expect(result.isValid).toBe(false);
        expect(Array.isArray(result.errors)).toBe(true);
      });
    });

    it('should handle validator state corruption during concurrent access', async () => {
      const validator = new TaskInsertionValidator();
      
      // Simulate concurrent access that might corrupt internal state
      const operations = Array.from({ length: 20 }, (_, i) => 
        Promise.resolve().then(() => {
          const position: InsertionPosition = {
            type: i % 2 === 0 ? 'after' : 'before',
            targetTaskId: `task-${i % 2 + 1}`,
            parentId: null,
            level: 0
          };
          return validator.validatePosition(position, validTasks);
        })
      );

      const results = await Promise.all(operations);
      expect(results).toHaveLength(20);
      
      // All results should be valid since we're using valid positions
      results.forEach(result => {
        expect(typeof result.isValid).toBe('boolean');
      });
    });
  });
});