import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useInsertionZones, clearAllZoneCache } from '@/hooks/useInsertionZones';
import { 
  calculateZoneBoundaries, 
  detectInsertionZone, 
  calculateInsertionPosition,
  optimizeZoneCalculations,
  validateZoneConfiguration
} from '@/lib/utils/insertionZoneUtils';
import { zoneCacheUtils, cachePerformance } from '@/lib/utils/insertionZoneCache';
import type { Task, InsertionZone } from '@/lib/types';

// Mock DOM methods
const mockGetBoundingClientRect = vi.fn();
const mockQuerySelector = vi.fn();

// Mock HTMLElement
const createMockElement = (bounds: DOMRect) => ({
  getBoundingClientRect: () => bounds,
  querySelector: mockQuerySelector,
  parentElement: null
} as unknown as HTMLElement);

// Sample task data
const sampleTask: Task = {
  id: 'task-1',
  title: 'Sample Task',
  description: 'A sample task for testing',
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks: [
    {
      id: 'subtask-1',
      title: 'Subtask 1',
      description: 'First subtask',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    }
  ]
};

const sampleTaskWithoutSubtasks: Task = {
  id: 'task-2',
  title: 'Simple Task',
  description: 'A task without subtasks',
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks: []
};

describe('useInsertionZones Hook', () => {
  beforeEach(() => {
    clearAllZoneCache();
    vi.clearAllMocks();
  });

  afterEach(() => {
    clearAllZoneCache();
  });

  it('should return empty zones when no task element is provided', () => {
    const { result } = renderHook(() => useInsertionZones({
      task: sampleTask,
      level: 0,
      parentId: null,
      taskElement: null
    }));

    expect(result.current.zones).toEqual([]);
    expect(result.current.isCalculating).toBe(false);
  });

  it('should calculate zones for task with subtasks', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const subtaskBounds = new DOMRect(20, 120, 280, 50);
    
    const mockTaskElement = createMockElement(taskBounds);
    const mockSubtaskElement = createMockElement(subtaskBounds);
    
    mockQuerySelector.mockReturnValue(mockSubtaskElement);

    const { result } = renderHook(() => useInsertionZones({
      task: sampleTask,
      level: 0,
      parentId: null,
      taskElement: mockTaskElement
    }));

    expect(result.current.zones).toHaveLength(3);
    
    // Check before zone
    const beforeZone = result.current.zones.find(z => z.type === 'before');
    expect(beforeZone).toBeDefined();
    expect(beforeZone?.targetTaskId).toBe('task-1');
    
    // Check after zone
    const afterZone = result.current.zones.find(z => z.type === 'after');
    expect(afterZone).toBeDefined();
    expect(afterZone?.targetTaskId).toBe('task-1');
    
    // Check between parent and child zone
    const betweenZone = result.current.zones.find(z => z.type === 'between_parent_child');
    expect(betweenZone).toBeDefined();
    expect(betweenZone?.targetTaskId).toBe('task-1');
    expect(betweenZone?.parentId).toBe('task-1');
  });

  it('should calculate zones for task without subtasks', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const mockTaskElement = createMockElement(taskBounds);

    const { result } = renderHook(() => useInsertionZones({
      task: sampleTaskWithoutSubtasks,
      level: 0,
      parentId: null,
      taskElement: mockTaskElement
    }));

    expect(result.current.zones).toHaveLength(2);
    
    // Should only have before and after zones
    const zoneTypes = result.current.zones.map(z => z.type);
    expect(zoneTypes).toContain('before');
    expect(zoneTypes).toContain('after');
    expect(zoneTypes).not.toContain('between_parent_child');
  });

  it('should find zone at specific point', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const mockTaskElement = createMockElement(taskBounds);

    const { result } = renderHook(() => useInsertionZones({
      task: sampleTaskWithoutSubtasks,
      level: 0,
      parentId: null,
      taskElement: mockTaskElement
    }));

    // Test finding zone at coordinates within before zone
    const foundZone = result.current.findZoneAtPoint(150, -5);
    expect(foundZone?.type).toBe('before');
  });

  it('should filter zones by type', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const subtaskBounds = new DOMRect(20, 120, 280, 50);
    
    const mockTaskElement = createMockElement(taskBounds);
    const mockSubtaskElement = createMockElement(subtaskBounds);
    
    mockQuerySelector.mockReturnValue(mockSubtaskElement);

    const { result } = renderHook(() => useInsertionZones({
      task: sampleTask,
      level: 0,
      parentId: null,
      taskElement: mockTaskElement
    }));

    const beforeZones = result.current.getZonesByType('before');
    expect(beforeZones).toHaveLength(1);
    expect(beforeZones[0].type).toBe('before');

    const betweenZones = result.current.getZonesByType('between_parent_child');
    expect(betweenZones).toHaveLength(1);
    expect(betweenZones[0].type).toBe('between_parent_child');
  });

  it('should use caching when enabled', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const mockTaskElement = createMockElement(taskBounds);

    // First render
    const { result, rerender } = renderHook(() => useInsertionZones({
      task: sampleTaskWithoutSubtasks,
      level: 0,
      parentId: null,
      taskElement: mockTaskElement,
      enableCaching: true
    }));

    const firstZones = result.current.zones;
    expect(firstZones).toHaveLength(2);

    // Second render should use cache
    rerender();
    const secondZones = result.current.zones;
    
    // Zones should be the same (from cache)
    expect(secondZones).toEqual(firstZones);
  });
});

describe('Zone Boundary Calculations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should calculate boundaries for all zone types', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const subtaskBounds = new DOMRect(20, 120, 280, 50);
    
    const mockTaskElement = createMockElement(taskBounds);
    const mockSubtaskElement = createMockElement(subtaskBounds);
    
    mockQuerySelector.mockReturnValue(mockSubtaskElement);

    const zones = calculateZoneBoundaries(
      mockTaskElement,
      sampleTask,
      0,
      null
    );

    expect(zones).toHaveLength(3);
    
    // Verify zone properties
    zones.forEach(zone => {
      expect(zone.id).toBeDefined();
      expect(zone.type).toBeDefined();
      expect(zone.bounds).toBeDefined();
      expect(zone.targetTaskId).toBe('task-1');
      expect(zone.metadata).toBeDefined();
    });
  });

  it('should adjust boundaries for touch mode', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const mockTaskElement = createMockElement(taskBounds);

    const normalZones = calculateZoneBoundaries(
      mockTaskElement,
      sampleTaskWithoutSubtasks,
      0,
      null,
      { touchMode: false }
    );

    const touchZones = calculateZoneBoundaries(
      mockTaskElement,
      sampleTaskWithoutSubtasks,
      0,
      null,
      { touchMode: true }
    );

    // Touch zones should be larger
    expect(touchZones[0].bounds.height).toBeGreaterThan(normalZones[0].bounds.height);
    expect(touchZones[0].metadata?.touchFriendly).toBe(true);
    expect(normalZones[0].metadata?.touchFriendly).toBe(false);
  });

  it('should handle custom zone options', () => {
    const taskBounds = new DOMRect(0, 0, 300, 100);
    const mockTaskElement = createMockElement(taskBounds);

    const zones = calculateZoneBoundaries(
      mockTaskElement,
      sampleTaskWithoutSubtasks,
      0,
      null,
      {
        zoneHeight: 30,
        zoneOffset: 15,
        hierarchyIndent: 25
      }
    );

    expect(zones).toHaveLength(2);
    expect(zones[0].bounds.height).toBe(30);
  });
});

describe('Zone Detection', () => {
  const sampleZones: InsertionZone[] = [
    {
      id: 'before-test',
      type: 'before',
      bounds: new DOMRect(0, -10, 300, 20),
      targetTaskId: 'test-task',
      parentId: null,
      level: 0,
      metadata: { isVisible: true, priority: 1, touchFriendly: false }
    },
    {
      id: 'after-test',
      type: 'after',
      bounds: new DOMRect(0, 100, 300, 20),
      targetTaskId: 'test-task',
      parentId: null,
      level: 0,
      metadata: { isVisible: true, priority: 2, touchFriendly: false }
    }
  ];

  it('should detect zone at coordinates', () => {
    const zone = detectInsertionZone(150, 0, sampleZones);
    expect(zone?.type).toBe('before');
    expect(zone?.id).toBe('before-test');
  });

  it('should return null for coordinates outside zones', () => {
    const zone = detectInsertionZone(150, 50, sampleZones);
    expect(zone).toBeNull();
  });

  it('should prioritize zones correctly', () => {
    // Create overlapping zones with different priorities
    const overlappingZones: InsertionZone[] = [
      {
        ...sampleZones[0],
        metadata: { isVisible: true, priority: 2, touchFriendly: false }
      },
      {
        ...sampleZones[1],
        bounds: new DOMRect(0, -5, 300, 20), // Overlapping with first zone
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      }
    ];

    const zone = detectInsertionZone(150, 0, overlappingZones);
    expect(zone?.metadata?.priority).toBe(1); // Should return higher priority zone
  });
});

describe('Insertion Position Calculation', () => {
  const sampleTasks: Task[] = [sampleTask, sampleTaskWithoutSubtasks];
  
  const sampleZone: InsertionZone = {
    id: 'before-test',
    type: 'before',
    bounds: new DOMRect(0, -10, 300, 20),
    targetTaskId: 'task-1',
    parentId: null,
    level: 0,
    metadata: { isVisible: true, priority: 1, touchFriendly: false }
  };

  it('should calculate insertion position with context', () => {
    const position = calculateInsertionPosition(sampleZone, 150, 0, sampleTasks);
    
    expect(position.type).toBe('before');
    expect(position.targetTaskId).toBe('task-1');
    expect(position.parentId).toBeNull();
    expect(position.level).toBe(0);
    expect(position.context).toBeDefined();
    expect(position.context?.siblingCount).toBe(2);
    expect(position.context?.hierarchyPath).toEqual(['1']);
  });

  it('should handle between parent child insertion', () => {
    const betweenZone: InsertionZone = {
      id: 'between-test',
      type: 'between_parent_child',
      bounds: new DOMRect(20, 110, 280, 20),
      targetTaskId: 'task-1',
      parentId: 'task-1',
      level: 1,
      metadata: { isVisible: true, priority: 3, touchFriendly: false }
    };

    const position = calculateInsertionPosition(betweenZone, 150, 120, sampleTasks);
    
    expect(position.type).toBe('between_parent_child');
    expect(position.parentId).toBe('task-1');
    expect(position.level).toBe(1);
    expect(position.context?.insertionIndex).toBe(0);
  });
});

describe('Zone Optimization', () => {
  it('should remove overlapping zones', () => {
    const overlappingZones: InsertionZone[] = [
      {
        id: 'zone-1',
        type: 'before',
        bounds: new DOMRect(0, 0, 100, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      },
      {
        id: 'zone-2',
        type: 'after',
        bounds: new DOMRect(10, 10, 100, 20), // Overlapping
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 2, touchFriendly: false }
      },
      {
        id: 'zone-3',
        type: 'before',
        bounds: new DOMRect(200, 0, 100, 20), // Non-overlapping
        targetTaskId: 'task-2',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      }
    ];

    const optimized = optimizeZoneCalculations(overlappingZones);
    expect(optimized.length).toBeLessThan(overlappingZones.length);
    expect(optimized).toHaveLength(2); // Should remove one overlapping zone
  });
});

describe('Zone Validation', () => {
  it('should validate correct zone configuration', () => {
    const validZone: InsertionZone = {
      id: 'valid-zone',
      type: 'before',
      bounds: new DOMRect(0, 0, 100, 20),
      targetTaskId: 'task-1',
      parentId: null,
      level: 0,
      metadata: { isVisible: true, priority: 1, touchFriendly: false }
    };

    expect(validateZoneConfiguration(validZone)).toBe(true);
  });

  it('should reject invalid zone configurations', () => {
    const invalidZones = [
      // Invalid bounds
      {
        id: 'invalid-bounds',
        type: 'before' as const,
        bounds: new DOMRect(0, 0, 0, 20), // Zero width
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      },
      // Missing ID
      {
        id: '',
        type: 'before' as const,
        bounds: new DOMRect(0, 0, 100, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      },
      // Invalid type
      {
        id: 'invalid-type',
        type: 'invalid' as any,
        bounds: new DOMRect(0, 0, 100, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      }
    ];

    invalidZones.forEach(zone => {
      expect(validateZoneConfiguration(zone)).toBe(false);
    });
  });
});

describe('Zone Cache', () => {
  beforeEach(() => {
    zoneCacheUtils.clear();
  });

  afterEach(() => {
    zoneCacheUtils.clear();
  });

  it('should cache and retrieve zones', () => {
    const bounds = new DOMRect(0, 0, 300, 100);
    const zones: InsertionZone[] = [
      {
        id: 'test-zone',
        type: 'before',
        bounds: new DOMRect(0, -10, 300, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      }
    ];

    const key = zoneCacheUtils.generateKey('task-1', 0, bounds, 0);
    
    // Cache zones
    zoneCacheUtils.set(key, zones, bounds);
    
    // Retrieve zones
    const cachedZones = zoneCacheUtils.get(key, bounds);
    expect(cachedZones).toEqual(zones);
  });

  it('should invalidate cache when bounds change', () => {
    const originalBounds = new DOMRect(0, 0, 300, 100);
    const changedBounds = new DOMRect(0, 10, 300, 100); // Y position changed
    
    const zones: InsertionZone[] = [
      {
        id: 'test-zone',
        type: 'before',
        bounds: new DOMRect(0, -10, 300, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      }
    ];

    const key = zoneCacheUtils.generateKey('task-1', 0, originalBounds, 0);
    
    // Cache with original bounds
    zoneCacheUtils.set(key, zones, originalBounds);
    
    // Try to retrieve with changed bounds
    const cachedZones = zoneCacheUtils.get(key, changedBounds);
    expect(cachedZones).toBeNull(); // Should be invalidated
  });

  it('should provide cache statistics', () => {
    const bounds = new DOMRect(0, 0, 300, 100);
    const zones: InsertionZone[] = [];
    const key = zoneCacheUtils.generateKey('task-1', 0, bounds, 0);
    
    zoneCacheUtils.set(key, zones, bounds);
    zoneCacheUtils.get(key, bounds); // Hit
    zoneCacheUtils.get('non-existent-key'); // Miss
    
    const stats = zoneCacheUtils.getStats();
    expect(stats.size).toBe(1);
    expect(stats.totalRequests).toBe(2);
    expect(stats.totalHits).toBe(1);
    expect(stats.hitRate).toBe(0.5);
  });
});

describe('Performance Monitoring', () => {
  it('should provide performance metrics', () => {
    const metrics = cachePerformance.getMetrics();
    expect(metrics).toHaveProperty('size');
    expect(metrics).toHaveProperty('hitRate');
    expect(metrics).toHaveProperty('totalRequests');
    expect(metrics).toHaveProperty('memoryUsage');
  });

  it('should reset metrics', () => {
    // Add some cache entries
    const bounds = new DOMRect(0, 0, 300, 100);
    const key = zoneCacheUtils.generateKey('task-1', 0, bounds, 0);
    zoneCacheUtils.set(key, [], bounds);
    zoneCacheUtils.get(key);

    // Reset metrics
    cachePerformance.resetMetrics();
    
    const metrics = cachePerformance.getMetrics();
    expect(metrics.size).toBe(0);
    expect(metrics.totalRequests).toBe(0);
  });
});