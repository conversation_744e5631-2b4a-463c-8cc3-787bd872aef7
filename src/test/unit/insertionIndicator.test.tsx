import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { InsertionIndicator } from '@/components/InsertionIndicator';
import type { InsertionPosition } from '@/lib/types';

// Mock the utils
vi.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}));

describe('InsertionIndicator', () => {
  const mockPosition: InsertionPosition = {
    type: 'after',
    targetTaskId: 'test-task-1',
    parentId: null,
    level: 0
  };

  const mockOnInsert = vi.fn();

  beforeEach(() => {
    mockOnInsert.mockClear();
  });

  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toBeInTheDocument();
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Insert new task after current task');
    });

    it('renders with custom className', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          className="custom-class"
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveClass('custom-class');
    });

    it('renders different insertion types correctly', () => {
      const positions: InsertionPosition[] = [
        { type: 'before', targetTaskId: 'task-1', parentId: null, level: 0 },
        { type: 'after', targetTaskId: 'task-2', parentId: null, level: 0 },
        { type: 'between_parent_child', targetTaskId: 'task-3', parentId: 'parent-1', level: 1 }
      ];

      positions.forEach((position) => {
        const { unmount } = render(
          <InsertionIndicator
            position={position}
            isVisible={true}
            onInsert={mockOnInsert}
          />
        );

        const indicator = screen.getByTestId(`insertion-indicator-${position.type}-${position.targetTaskId}`);
        expect(indicator).toBeInTheDocument();
        
        unmount();
      });
    });
  });

  describe('Visibility States', () => {
    it('shows indicator when isVisible is true', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).not.toHaveClass('opacity-0');
    });

    it('hides indicator when isVisible is false', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={false}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveClass('opacity-0');
      expect(indicator).toHaveClass('pointer-events-none');
    });

    it('applies hover styles when isHovered is true', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          isHovered={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveClass('opacity-100');
      expect(indicator).toHaveClass('scale-100');
    });
  });

  describe('Click Handling', () => {
    it('calls onInsert when button is clicked', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });

    it('does not call onInsert when disabled', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          disabled={true}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnInsert).not.toHaveBeenCalled();
    });

    it('prevents event propagation on click', () => {
      const parentClickHandler = vi.fn();
      
      render(
        <div onClick={parentClickHandler}>
          <InsertionIndicator
            position={mockPosition}
            isVisible={true}
            onInsert={mockOnInsert}
          />
        </div>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnInsert).toHaveBeenCalledTimes(1);
      expect(parentClickHandler).not.toHaveBeenCalled();
    });
  });

  describe('Touch Handling', () => {
    it('handles touch events correctly', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
        />
      );

      const button = screen.getByRole('button');
      
      // Simulate touch events
      fireEvent.touchStart(button);
      fireEvent.touchEnd(button);

      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });

    it('applies touch-friendly styles when touchFriendly is true', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('touch-manipulation');
    });
  });

  describe('Variants', () => {
    it('applies default variant styles', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          variant="default"
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveStyle({ height: '20px' });
    });

    it('applies compact variant styles', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          variant="compact"
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveStyle({ height: '16px' });
    });

    it('applies touch variant styles', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          variant="touch"
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveStyle({ height: '40px' });
    });
  });

  describe('Hierarchy Indentation', () => {
    it('applies correct indentation for between_parent_child type', () => {
      const betweenPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'test-task-1',
        parentId: 'parent-1',
        level: 1
      };

      render(
        <InsertionIndicator
          position={betweenPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-between_parent_child-test-task-1');
      expect(indicator).toHaveStyle({ paddingLeft: '20px' });
    });

    it('applies no indentation for before/after types', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
      expect(indicator).toHaveStyle({ paddingLeft: '0px' });
    });
  });

  describe('Disabled State', () => {
    it('applies disabled styles when disabled', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          disabled={true}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveClass('cursor-not-allowed');
      expect(button).toHaveClass('opacity-50');
    });

    it('does not respond to mouse events when disabled', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          disabled={true}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.mouseDown(button);
      fireEvent.mouseUp(button);
      fireEvent.click(button);

      expect(mockOnInsert).not.toHaveBeenCalled();
    });
  });

  describe('Animation States', () => {
    it('applies pressed state on mouse down', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.mouseDown(button);
      expect(button).toHaveClass('scale-95');
      
      fireEvent.mouseUp(button);
      expect(button).not.toHaveClass('scale-95');
    });

    it('removes pressed state on mouse leave', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.mouseDown(button);
      expect(button).toHaveClass('scale-95');
      
      fireEvent.mouseLeave(button);
      expect(button).not.toHaveClass('scale-95');
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label', 'Insert new task after current task');
      expect(button).toHaveAttribute('title', 'Insert task after test-task-1');
    });

    it('is focusable when not disabled', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      button.focus();
      expect(button).toHaveFocus();
    });

    it('is not focusable when disabled', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          disabled={true}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('supports keyboard activation', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      button.focus();
      
      fireEvent.keyDown(button, { key: 'Enter' });
      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });
  });

  describe('Visual Feedback', () => {
    it('shows ripple effect when pressed', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.mouseDown(button);

      // Check for ripple effect element
      await waitFor(() => {
        const ripple = document.querySelector('.animate-ping');
        expect(ripple).toBeInTheDocument();
      });

      fireEvent.mouseUp(button);
    });

    it('rotates plus icon on hover', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          isHovered={true}
          onInsert={mockOnInsert}
        />
      );

      const plusIcon = document.querySelector('svg');
      expect(plusIcon).toHaveClass('rotate-90');
    });

    it('scales plus icon when pressed', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.mouseDown(button);

      const plusIcon = document.querySelector('svg');
      expect(plusIcon).toHaveClass('scale-90');
    });
  });

  describe('Performance', () => {
    it('does not re-render unnecessarily', () => {
      const renderSpy = vi.fn();
      
      const TestComponent = (props: any) => {
        renderSpy();
        return <InsertionIndicator {...props} />;
      };

      const { rerender } = render(
        <TestComponent
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      expect(renderSpy).toHaveBeenCalledTimes(1);

      // Re-render with same props
      rerender(
        <TestComponent
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      // Should still only be called once due to React optimization
      expect(renderSpy).toHaveBeenCalledTimes(2);
    });
  });
});