/**
 * Tests for insertion animations and visual feedback
 * Implements requirements 4.4, 4.5 for polished animations and visual feedback
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { InsertionIndicator } from '@/components/InsertionIndicator';
import { InsertionFeedback } from '@/lib/utils/insertionFeedback';
import type { InsertionPosition } from '@/lib/types';

// Mock the toast manager
vi.mock('@/lib/utils/toastManager', () => ({
  ToastManager: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn()
  }
}));

// Mock the mobile hook
vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: () => false
}));

// Mock the performance monitor
vi.mock('@/lib/utils/insertionPerformanceMonitor', () => ({
  useInsertionPerformanceMonitor: () => ({
    recordRender: vi.fn(),
    recordHoverEvent: vi.fn()
  })
}));

// Mock the debounce utility
vi.mock('@/lib/utils/debounce', () => ({
  useDebouncedCallback: (fn: any) => {
    const mockFn = fn;
    mockFn.cancel = vi.fn();
    return mockFn;
  }
}));

describe('InsertionIndicator Animations', () => {
  const mockPosition: InsertionPosition = {
    type: 'after',
    targetTaskId: 'test-task-1',
    parentId: null,
    level: 0
  };

  const mockOnInsert = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock requestAnimationFrame
    global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
    global.cancelAnimationFrame = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Entrance Animations', () => {
    it('should apply correct entrance animation for "before" insertion type', () => {
      const beforePosition: InsertionPosition = {
        ...mockPosition,
        type: 'before'
      };

      render(
        <InsertionIndicator
          position={beforePosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId(`insertion-indicator-before-${mockPosition.targetTaskId}`);
      expect(indicator).toHaveClass('slide-in-top');
    });

    it('should apply correct entrance animation for "after" insertion type', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId(`insertion-indicator-after-${mockPosition.targetTaskId}`);
      expect(indicator).toHaveClass('slide-in-bottom');
    });

    it('should apply correct entrance animation for "between_parent_child" insertion type', () => {
      const betweenPosition: InsertionPosition = {
        ...mockPosition,
        type: 'between_parent_child'
      };

      render(
        <InsertionIndicator
          position={betweenPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId(`insertion-indicator-between_parent_child-${mockPosition.targetTaskId}`);
      expect(indicator).toHaveClass('slide-in-left');
    });

    it('should transition from entering to visible state', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId(`insertion-indicator-after-${mockPosition.targetTaskId}`);
      
      // Initially should have entering animation
      expect(indicator).toHaveClass('slide-in-bottom');
      
      // After animation completes, should be in visible state
      await waitFor(() => {
        expect(indicator).not.toHaveClass('opacity-0');
      }, { timeout: 1000 });
    });
  });

  describe('Hover Animations', () => {
    it('should show hover effects when hovered', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          isHovered={true}
          onInsert={mockOnInsert}
        />
      );

      const indicator = screen.getByTestId(`insertion-indicator-after-${mockPosition.targetTaskId}`);
      expect(indicator).toHaveClass('opacity-100', 'scale-100');
    });

    it('should apply pulse glow animation on hover', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          isHovered={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.mouseEnter(button);
      
      // Check if hover styles are applied (this would be tested via CSS classes)
      expect(button).toHaveClass('bg-primary/80');
    });
  });

  describe('Success Animations', () => {
    it('should trigger success animation when task is inserted', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      act(() => {
        fireEvent.click(button);
      });

      // Should show success animation
      await waitFor(() => {
        expect(button).toHaveClass('success-animation');
      });

      // Should show checkmark icon instead of plus
      await waitFor(() => {
        const checkmark = screen.getByRole('button').querySelector('svg polyline');
        expect(checkmark).toBeInTheDocument();
      });

      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });

    it('should reset animation after success completion', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      act(() => {
        fireEvent.click(button);
      });

      // Wait for success animation to complete
      await waitFor(() => {
        expect(button).not.toHaveClass('success-animation');
      }, { timeout: 1000 });

      // Should return to normal state
      const plusIcon = screen.getByRole('button').querySelector('svg[data-lucide="plus"]');
      expect(plusIcon).toBeInTheDocument();
    });
  });

  describe('Touch Animations', () => {
    it('should show pressed state on touch start', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.touchStart(button);
      
      expect(button).toHaveClass('scale-95');
    });

    it('should show ripple effect on touch', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.touchStart(button);
      
      // Should show ripple effect
      const ripple = button.parentElement?.querySelector('.animate-ping');
      expect(ripple).toBeInTheDocument();
    });
  });

  describe('Long Press Animations', () => {
    it('should show long press progress indicator', async () => {
      const mockOnLongPress = vi.fn();
      
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
          longPressDelay={100}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.touchStart(button);
      
      // Should show progress indicator
      await waitFor(() => {
        const progressIndicator = button.parentElement?.querySelector('[style*="longPressProgress"]');
        expect(progressIndicator).toBeInTheDocument();
      });
    });

    it('should trigger long press animation on completion', async () => {
      const mockOnLongPress = vi.fn();
      
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
          longPressDelay={50}
        />
      );

      const button = screen.getByRole('button');
      
      fireEvent.touchStart(button);
      
      // Wait for long press to complete
      await waitFor(() => {
        expect(button).toHaveClass('scale-110');
      }, { timeout: 100 });

      expect(mockOnLongPress).toHaveBeenCalledTimes(1);
    });
  });

  describe('Reduced Motion Support', () => {
    beforeEach(() => {
      // Mock matchMedia for reduced motion
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });
    });

    it('should respect reduced motion preferences', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      // When reduced motion is preferred, animations should be disabled
      // This would be tested via CSS media queries in actual implementation
      expect(window.matchMedia('(prefers-reduced-motion: reduce)').matches).toBe(true);
    });
  });
});

describe('InsertionFeedback', () => {
  const mockPosition: InsertionPosition = {
    type: 'after',
    targetTaskId: 'test-task-1',
    parentId: null,
    level: 0
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock Web Audio API
    global.AudioContext = vi.fn().mockImplementation(() => ({
      createBuffer: vi.fn().mockReturnValue({
        getChannelData: vi.fn().mockReturnValue(new Float32Array(1000)),
        length: 1000
      }),
      createBufferSource: vi.fn().mockReturnValue({
        buffer: null,
        connect: vi.fn(),
        start: vi.fn()
      }),
      createGain: vi.fn().mockReturnValue({
        gain: {
          setValueAtTime: vi.fn(),
          linearRampToValueAtTime: vi.fn()
        },
        connect: vi.fn()
      }),
      destination: {},
      currentTime: 0,
      sampleRate: 44100
    }));

    // Mock navigator.vibrate
    Object.defineProperty(navigator, 'vibrate', {
      writable: true,
      value: vi.fn()
    });
  });

  describe('Success Feedback', () => {
    it('should show success toast with correct message', async () => {
      const { ToastManager } = await import('@/lib/utils/toastManager');
      
      await InsertionFeedback.showSuccess({
        position: mockPosition,
        taskTitle: 'Test Task',
        showToast: true
      });

      expect(ToastManager.success).toHaveBeenCalledWith({
        title: 'Aufgabe erfolgreich eingefügt',
        description: 'Neue Aufgabe "Test Task" wurde nach der aktuellen Aufgabe eingefügt.',
        duration: 3000
      });
    });

    it('should trigger haptic feedback on supported devices', async () => {
      await InsertionFeedback.showSuccess({
        position: mockPosition,
        hapticFeedback: true
      });

      expect(navigator.vibrate).toHaveBeenCalledWith([50, 30, 50]);
    });

    it('should play success sound when enabled', async () => {
      const mockAudioContext = new AudioContext();
      
      await InsertionFeedback.showSuccess({
        position: mockPosition,
        playSound: true
      });

      // Audio context should be created
      expect(AudioContext).toHaveBeenCalled();
    });
  });

  describe('Error Feedback', () => {
    it('should show error toast with retry action', () => {
      const { ToastManager } = require('@/lib/utils/toastManager');
      const mockRetry = vi.fn();
      
      InsertionFeedback.showError({
        position: mockPosition,
        error: 'Test error',
        retryAction: mockRetry
      });

      expect(ToastManager.error).toHaveBeenCalledWith({
        title: 'Aufgabe konnte nicht eingefügt werden',
        description: 'Einfügen nach der aktuellen Aufgabe fehlgeschlagen: Test error',
        duration: 6000,
        action: {
          label: 'Erneut versuchen',
          onClick: mockRetry
        }
      });
    });

    it('should trigger error haptic feedback pattern', () => {
      InsertionFeedback.showError({
        position: mockPosition,
        error: 'Test error'
      });

      expect(navigator.vibrate).toHaveBeenCalledWith([100, 50, 100, 50, 100]);
    });
  });

  describe('Ripple Effect', () => {
    it('should create ripple effect element', () => {
      const mockElement = document.createElement('div');
      document.body.appendChild(mockElement);
      
      InsertionFeedback.createRippleEffect(mockElement);
      
      const ripple = mockElement.querySelector('div');
      expect(ripple).toBeInTheDocument();
      expect(ripple).toHaveStyle({
        position: 'absolute',
        borderRadius: '50%',
        pointerEvents: 'none'
      });
    });

    it('should clean up ripple effect after animation', async () => {
      const mockElement = document.createElement('div');
      document.body.appendChild(mockElement);
      
      InsertionFeedback.createRippleEffect(mockElement);
      
      // Should clean up after 600ms
      await new Promise(resolve => setTimeout(resolve, 650));
      
      const ripple = mockElement.querySelector('div');
      expect(ripple).not.toBeInTheDocument();
    });
  });

  describe('User Preferences', () => {
    beforeEach(() => {
      // Mock localStorage
      const localStorageMock = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn()
      };
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock
      });
    });

    it('should respect sound preference', () => {
      localStorage.getItem = vi.fn().mockReturnValue('false');
      
      const soundEnabled = InsertionFeedback.isSoundEnabled();
      expect(soundEnabled).toBe(false);
    });

    it('should respect haptic preference', () => {
      localStorage.getItem = vi.fn().mockReturnValue('false');
      
      const hapticEnabled = InsertionFeedback.isHapticEnabled();
      expect(hapticEnabled).toBe(false);
    });

    it('should save user preferences', () => {
      InsertionFeedback.setFeedbackPreferences({
        sound: false,
        haptic: true,
        toast: false
      });

      expect(localStorage.setItem).toHaveBeenCalledWith('insertion-sound-enabled', 'false');
      expect(localStorage.setItem).toHaveBeenCalledWith('insertion-haptic-enabled', 'true');
      expect(localStorage.setItem).toHaveBeenCalledWith('insertion-toast-enabled', 'false');
    });
  });
});

describe('Cross-browser Animation Compatibility', () => {
  it('should apply vendor prefixes for animations', () => {
    // This would be tested by checking the generated CSS
    // In a real test environment, we would verify that the CSS contains
    // -webkit-, -moz-, -o- prefixes for animations
    expect(true).toBe(true); // Placeholder for CSS prefix testing
  });

  it('should use hardware acceleration properties', () => {
    // This would verify that transform3d and will-change properties are applied
    expect(true).toBe(true); // Placeholder for hardware acceleration testing
  });

  it('should handle different timing functions across browsers', () => {
    // This would test that cubic-bezier timing functions work consistently
    expect(true).toBe(true); // Placeholder for timing function testing
  });
});