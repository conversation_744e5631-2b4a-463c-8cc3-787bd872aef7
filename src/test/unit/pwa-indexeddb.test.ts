/**
 * Unit tests for IndexedDB data persistence and migration
 * Tests CRUD operations, hierarchical relationships, and data integrity
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { indexedDBManager, type StoredProject, type StoredTask } from '@/lib/storage/indexeddb';
import type { Task, User } from '@/lib/types';

// Mock IndexedDB
class MockIDBDatabase {
  objectStoreNames = {
    contains: vi.fn(() => false)
  };
  transaction = vi.fn();
  close = vi.fn();
  onerror = vi.fn();
}

class MockIDBObjectStore {
  add = vi.fn();
  put = vi.fn();
  get = vi.fn();
  delete = vi.fn();
  clear = vi.fn();
  count = vi.fn();
  getAll = vi.fn();
  createIndex = vi.fn();
  index = vi.fn();
  openCursor = vi.fn();
}

class MockIDBTransaction {
  objectStore = vi.fn();
  onerror = vi.fn();
  onabort = vi.fn();
  oncomplete = vi.fn();
}

class MockIDBRequest {
  result: any = null;
  error: any = null;
  onsuccess = vi.fn();
  onerror = vi.fn();
}

class MockIDBOpenDBRequest extends MockIDBRequest {
  onupgradeneeded = vi.fn();
}

class MockIDBIndex {
  getAll = vi.fn();
  openCursor = vi.fn();
}

// Mock global IndexedDB
const mockIndexedDB = {
  open: vi.fn(),
  deleteDatabase: vi.fn()
};

describe('IndexedDB Data Persistence', () => {
  let mockDB: MockIDBDatabase;
  let mockStore: MockIDBObjectStore;
  let mockTransaction: MockIDBTransaction;
  let mockRequest: MockIDBRequest;
  let mockIndex: MockIDBIndex;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create fresh mock instances
    mockDB = new MockIDBDatabase();
    mockStore = new MockIDBObjectStore();
    mockTransaction = new MockIDBTransaction();
    mockRequest = new MockIDBRequest();
    mockIndex = new MockIDBIndex();

    // Setup mock relationships
    mockTransaction.objectStore.mockReturnValue(mockStore);
    mockStore.index.mockReturnValue(mockIndex);

    // Mock global IndexedDB
    Object.defineProperty(window, 'indexedDB', {
      value: mockIndexedDB,
      writable: true
    });

    // Mock crypto.randomUUID
    Object.defineProperty(crypto, 'randomUUID', {
      value: vi.fn(() => 'mock-uuid-' + Math.random()),
      writable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Database Initialization', () => {
    it('should initialize database with correct schema', async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);

      // Simulate successful database opening
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      await indexedDBManager.initDatabase();

      expect(mockIndexedDB.open).toHaveBeenCalledWith('KIProjectPlannerDB', 1);
    });

    it('should handle database initialization failure', async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);

      // Simulate database error
      setTimeout(() => {
        mockOpenRequest.error = new Error('Database initialization failed');
        mockOpenRequest.onerror?.();
      }, 0);

      await expect(indexedDBManager.initDatabase()).rejects.toThrow();
    });

    it('should create object stores during upgrade', async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);

      // Mock createObjectStore
      const mockCreateObjectStore = vi.fn().mockReturnValue(mockStore);
      mockDB.createObjectStore = mockCreateObjectStore;

      // Simulate upgrade needed
      setTimeout(() => {
        const upgradeEvent = {
          target: { result: mockDB }
        };
        mockOpenRequest.onupgradeneeded?.(upgradeEvent as any);
        
        // Then simulate success
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      await indexedDBManager.initDatabase();

      expect(mockCreateObjectStore).toHaveBeenCalledWith('projects', { keyPath: 'id' });
      expect(mockCreateObjectStore).toHaveBeenCalledWith('tasks', { keyPath: 'id' });
      expect(mockCreateObjectStore).toHaveBeenCalledWith('users', { keyPath: 'id' });
      expect(mockCreateObjectStore).toHaveBeenCalledWith('settings', { keyPath: 'id' });
    });

    it('should handle missing IndexedDB support', async () => {
      // @ts-ignore - Testing runtime behavior
      delete window.indexedDB;

      await expect(indexedDBManager.initDatabase()).rejects.toThrow(
        'IndexedDB is not supported in this environment'
      );
    });
  });

  describe('Project CRUD Operations', () => {
    beforeEach(async () => {
      // Mock successful database initialization
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      mockDB.transaction.mockReturnValue(mockTransaction);
      await indexedDBManager.initDatabase();
    });

    it('should create a new project successfully', async () => {
      const projectData = {
        title: 'Test Project',
        description: 'Test Description',
        isOfflineOnly: true
      };

      mockStore.add.mockReturnValue(mockRequest);
      
      // Simulate successful add
      setTimeout(() => {
        mockRequest.onsuccess?.();
      }, 0);

      const projectId = await indexedDBManager.createProject(projectData);

      expect(projectId).toMatch(/^mock-uuid-/);
      expect(mockStore.add).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Project',
          description: 'Test Description',
          isOfflineOnly: true,
          id: expect.any(String),
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date)
        })
      );
    });

    it('should handle project creation failure', async () => {
      const projectData = {
        title: 'Test Project',
        description: 'Test Description',
        isOfflineOnly: true
      };

      mockStore.add.mockReturnValue(mockRequest);
      
      // Simulate add failure
      setTimeout(() => {
        mockRequest.error = new Error('Add failed');
        mockRequest.onerror?.();
      }, 0);

      await expect(indexedDBManager.createProject(projectData)).rejects.toThrow();
    });

    it('should update an existing project', async () => {
      const projectId = 'test-project-id';
      const updates = { title: 'Updated Title' };

      const getRequest = new MockIDBRequest();
      const putRequest = new MockIDBRequest();
      
      mockStore.get.mockReturnValue(getRequest);
      mockStore.put.mockReturnValue(putRequest);

      // Simulate successful get and put
      setTimeout(() => {
        getRequest.result = {
          id: projectId,
          title: 'Original Title',
          createdAt: new Date('2023-01-01')
        };
        getRequest.onsuccess?.();
      }, 0);

      setTimeout(() => {
        putRequest.onsuccess?.();
      }, 10);

      await indexedDBManager.updateProject(projectId, updates);

      expect(mockStore.put).toHaveBeenCalledWith(
        expect.objectContaining({
          id: projectId,
          title: 'Updated Title',
          updatedAt: expect.any(Date)
        })
      );
    });

    it('should handle updating non-existent project', async () => {
      const projectId = 'non-existent-id';
      const updates = { title: 'Updated Title' };

      const getRequest = new MockIDBRequest();
      mockStore.get.mockReturnValue(getRequest);

      // Simulate project not found
      setTimeout(() => {
        getRequest.result = null;
        getRequest.onsuccess?.();
      }, 0);

      await expect(indexedDBManager.updateProject(projectId, updates)).rejects.toThrow(
        `Project with id ${projectId} not found`
      );
    });

    it('should delete a project and all its tasks', async () => {
      const projectId = 'test-project-id';

      const taskCursorRequest = new MockIDBRequest();
      const projectDeleteRequest = new MockIDBRequest();
      
      mockIndex.openCursor.mockReturnValue(taskCursorRequest);
      mockStore.delete.mockReturnValue(projectDeleteRequest);

      // Simulate no tasks found and successful project deletion
      setTimeout(() => {
        taskCursorRequest.result = null; // No tasks
        taskCursorRequest.onsuccess?.();
      }, 0);

      setTimeout(() => {
        projectDeleteRequest.onsuccess?.();
      }, 10);

      await indexedDBManager.deleteProject(projectId);

      expect(mockStore.delete).toHaveBeenCalledWith(projectId);
    });

    it('should get all projects', async () => {
      const mockProjects: StoredProject[] = [
        {
          id: 'project-1',
          title: 'Project 1',
          description: 'Description 1',
          isOfflineOnly: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      mockStore.getAll.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.result = mockProjects;
        mockRequest.onsuccess?.();
      }, 0);

      const projects = await indexedDBManager.getAllProjects();

      expect(projects).toEqual(mockProjects);
      expect(mockStore.getAll).toHaveBeenCalled();
    });

    it('should get a single project by ID', async () => {
      const projectId = 'test-project-id';
      const mockProject: StoredProject = {
        id: projectId,
        title: 'Test Project',
        description: 'Test Description',
        isOfflineOnly: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockStore.get.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.result = mockProject;
        mockRequest.onsuccess?.();
      }, 0);

      const project = await indexedDBManager.getProject(projectId);

      expect(project).toEqual(mockProject);
      expect(mockStore.get).toHaveBeenCalledWith(projectId);
    });
  });

  describe('Task CRUD Operations with Hierarchical Support', () => {
    beforeEach(async () => {
      // Mock successful database initialization
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      mockDB.transaction.mockReturnValue(mockTransaction);
      await indexedDBManager.initDatabase();
    });

    it('should create a new task with proper hierarchy', async () => {
      const taskData = {
        title: 'Test Task',
        description: 'Test Description',
        content: 'Test Content',
        status: 'pending' as const,
        assignees: [] as User[],
        subtasks: [] as Task[]
      };
      const projectId = 'test-project-id';
      const parentTaskId = 'parent-task-id';

      // Mock order index calculation
      const orderCursorRequest = new MockIDBRequest();
      mockIndex.openCursor.mockReturnValue(orderCursorRequest);
      
      setTimeout(() => {
        orderCursorRequest.result = null; // No existing tasks
        orderCursorRequest.onsuccess?.();
      }, 0);

      mockStore.add.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.onsuccess?.();
      }, 10);

      const taskId = await indexedDBManager.createTask(taskData, projectId, parentTaskId);

      expect(taskId).toMatch(/^mock-uuid-/);
      expect(mockStore.add).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Task',
          projectId,
          parentTaskId,
          orderIndex: 0,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date)
        })
      );
    });

    it('should calculate correct order index for new tasks', async () => {
      const taskData = {
        title: 'Test Task',
        description: 'Test Description',
        content: 'Test Content',
        status: 'pending' as const,
        assignees: [] as User[],
        subtasks: [] as Task[]
      };
      const projectId = 'test-project-id';

      // Mock existing task with orderIndex 5
      const orderCursorRequest = new MockIDBRequest();
      mockIndex.openCursor.mockReturnValue(orderCursorRequest);
      
      setTimeout(() => {
        orderCursorRequest.result = {
          value: { orderIndex: 5 }
        };
        orderCursorRequest.onsuccess?.();
      }, 0);

      mockStore.add.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.onsuccess?.();
      }, 10);

      await indexedDBManager.createTask(taskData, projectId);

      expect(mockStore.add).toHaveBeenCalledWith(
        expect.objectContaining({
          orderIndex: 6 // Should be 5 + 1
        })
      );
    });

    it('should update an existing task', async () => {
      const taskId = 'test-task-id';
      const updates = { title: 'Updated Task Title' };

      const getRequest = new MockIDBRequest();
      const putRequest = new MockIDBRequest();
      
      mockStore.get.mockReturnValue(getRequest);
      mockStore.put.mockReturnValue(putRequest);

      setTimeout(() => {
        getRequest.result = {
          id: taskId,
          title: 'Original Title',
          projectId: 'project-id',
          createdAt: new Date('2023-01-01')
        };
        getRequest.onsuccess?.();
      }, 0);

      setTimeout(() => {
        putRequest.onsuccess?.();
      }, 10);

      await indexedDBManager.updateTask(taskId, updates);

      expect(mockStore.put).toHaveBeenCalledWith(
        expect.objectContaining({
          id: taskId,
          title: 'Updated Task Title',
          updatedAt: expect.any(Date)
        })
      );
    });

    it('should delete a task and all its subtasks recursively', async () => {
      const taskId = 'test-task-id';

      // Mock subtask cursor (no subtasks)
      const subtaskCursorRequest = new MockIDBRequest();
      mockIndex.openCursor.mockReturnValue(subtaskCursorRequest);
      
      setTimeout(() => {
        subtaskCursorRequest.result = null; // No subtasks
        subtaskCursorRequest.onsuccess?.();
      }, 0);

      const deleteRequest = new MockIDBRequest();
      mockStore.delete.mockReturnValue(deleteRequest);
      
      setTimeout(() => {
        deleteRequest.onsuccess?.();
      }, 10);

      await indexedDBManager.deleteTask(taskId);

      expect(mockStore.delete).toHaveBeenCalledWith(taskId);
    });

    it('should build hierarchical task structure correctly', async () => {
      const projectId = 'test-project-id';
      const mockStoredTasks: StoredTask[] = [
        {
          id: 'task-1',
          title: 'Parent Task',
          description: '',
          content: '',
          status: 'pending',
          assignees: [],
          subtasks: [],
          projectId,
          parentTaskId: undefined,
          orderIndex: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'task-2',
          title: 'Child Task',
          description: '',
          content: '',
          status: 'pending',
          assignees: [],
          subtasks: [],
          projectId,
          parentTaskId: 'task-1',
          orderIndex: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      mockIndex.getAll.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.result = mockStoredTasks;
        mockRequest.onsuccess?.();
      }, 0);

      const tasks = await indexedDBManager.getTasksByProject(projectId);

      expect(tasks).toHaveLength(1); // Only root task
      expect(tasks[0].id).toBe('task-1');
      expect(tasks[0].subtasks).toHaveLength(1); // Child task
      expect(tasks[0].subtasks[0].id).toBe('task-2');
    });
  });

  describe('Settings Operations', () => {
    beforeEach(async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      mockDB.transaction.mockReturnValue(mockTransaction);
      await indexedDBManager.initDatabase();
    });

    it('should save settings successfully', async () => {
      const settings = {
        id: 'default',
        installPromptShown: true,
        offlineMode: 'auto' as const,
        cacheSize: 100,
        touchFeedback: true,
        swipeActions: true,
        compactMode: false,
        preferredAIProvider: 'gemini',
        aiProviders: []
      };

      mockStore.put.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.onsuccess?.();
      }, 0);

      await indexedDBManager.saveSettings(settings);

      expect(mockStore.put).toHaveBeenCalledWith(
        expect.objectContaining({
          ...settings,
          updatedAt: expect.any(Date)
        })
      );
    });

    it('should load settings successfully', async () => {
      const settingsId = 'default';
      const mockSettings = {
        id: settingsId,
        installPromptShown: true,
        offlineMode: 'auto' as const,
        cacheSize: 100,
        touchFeedback: true,
        swipeActions: true,
        compactMode: false,
        preferredAIProvider: 'gemini',
        aiProviders: [],
        updatedAt: new Date()
      };

      mockStore.get.mockReturnValue(mockRequest);
      
      setTimeout(() => {
        mockRequest.result = mockSettings;
        mockRequest.onsuccess?.();
      }, 0);

      const settings = await indexedDBManager.loadSettings(settingsId);

      expect(settings).toEqual(mockSettings);
      expect(mockStore.get).toHaveBeenCalledWith(settingsId);
    });
  });

  describe('Data Migration and Integrity', () => {
    beforeEach(async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      mockDB.transaction.mockReturnValue(mockTransaction);
      await indexedDBManager.initDatabase();
    });

    it('should clear all data successfully', async () => {
      const clearRequests = [
        new MockIDBRequest(),
        new MockIDBRequest(),
        new MockIDBRequest(),
        new MockIDBRequest()
      ];

      mockStore.clear
        .mockReturnValueOnce(clearRequests[0])
        .mockReturnValueOnce(clearRequests[1])
        .mockReturnValueOnce(clearRequests[2])
        .mockReturnValueOnce(clearRequests[3]);

      // Simulate successful clear operations
      clearRequests.forEach((request, index) => {
        setTimeout(() => {
          request.onsuccess?.();
        }, index * 5);
      });

      await indexedDBManager.clearAllData();

      expect(mockStore.clear).toHaveBeenCalledTimes(4);
    });

    it('should get database statistics', async () => {
      const countRequests = [
        new MockIDBRequest(),
        new MockIDBRequest(),
        new MockIDBRequest()
      ];

      mockStore.count
        .mockReturnValueOnce(countRequests[0])
        .mockReturnValueOnce(countRequests[1])
        .mockReturnValueOnce(countRequests[2]);

      // Simulate count results
      setTimeout(() => {
        countRequests[0].result = 5; // projects
        countRequests[0].onsuccess?.();
      }, 0);

      setTimeout(() => {
        countRequests[1].result = 25; // tasks
        countRequests[1].onsuccess?.();
      }, 5);

      setTimeout(() => {
        countRequests[2].result = 3; // users
        countRequests[2].onsuccess?.();
      }, 10);

      const stats = await indexedDBManager.getStats();

      expect(stats).toEqual({
        projects: 5,
        tasks: 25,
        users: 3
      });
    });

    it('should handle transaction errors gracefully', async () => {
      mockDB.transaction.mockImplementation(() => {
        const transaction = new MockIDBTransaction();
        setTimeout(() => {
          transaction.onerror?.();
        }, 0);
        return transaction;
      });

      await expect(indexedDBManager.createProject({
        title: 'Test',
        description: 'Test',
        isOfflineOnly: true
      })).rejects.toThrow();
    });
  });

  describe('Database Connection Management', () => {
    it('should close database connection properly', () => {
      // Mock that database is initialized
      (indexedDBManager as any).db = mockDB;

      indexedDBManager.close();

      expect(mockDB.close).toHaveBeenCalled();
    });

    it('should handle multiple initialization attempts', async () => {
      const mockOpenRequest = new MockIDBOpenDBRequest();
      mockIndexedDB.open.mockReturnValue(mockOpenRequest);
      
      setTimeout(() => {
        mockOpenRequest.result = mockDB;
        mockOpenRequest.onsuccess?.();
      }, 0);

      // Call initDatabase multiple times
      const promises = [
        indexedDBManager.initDatabase(),
        indexedDBManager.initDatabase(),
        indexedDBManager.initDatabase()
      ];

      await Promise.all(promises);

      // Should only open database once
      expect(mockIndexedDB.open).toHaveBeenCalledTimes(1);
    });
  });
});