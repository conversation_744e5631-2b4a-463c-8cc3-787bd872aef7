/**
 * Unit tests for storage functionality
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { indexedDBManager } from '@/lib/storage/indexeddb';
import { localStorageManager } from '@/lib/storage/localStorageManager';
import type { Project, Task } from '@/lib/types';

// Mock IndexedDB for testing
const mockIDBFactory = {
  open: vi.fn(),
  deleteDatabase: vi.fn(),
  cmp: vi.fn()
};

const mockIDBDatabase = {
  createObjectStore: vi.fn(),
  transaction: vi.fn(),
  close: vi.fn(),
  onerror: null,
  onabort: null,
  onversionchange: null
};

const mockIDBObjectStore = {
  add: vi.fn(),
  put: vi.fn(),
  get: vi.fn(),
  delete: vi.fn(),
  clear: vi.fn(),
  count: vi.fn(),
  getAll: vi.fn(),
  createIndex: vi.fn(),
  index: vi.fn()
};

const mockIDBTransaction = {
  objectStore: vi.fn(() => mockIDBObjectStore),
  onerror: null,
  onabort: null,
  oncomplete: null
};

// Setup IndexedDB mock
Object.defineProperty(window, 'indexedDB', {
  value: mockIDBFactory,
  writable: true
});

describe('Storage System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup successful IndexedDB operations
    mockIDBFactory.open.mockReturnValue({
      result: mockIDBDatabase,
      onsuccess: null,
      onerror: null,
      onupgradeneeded: null
    });

    mockIDBDatabase.transaction.mockReturnValue(mockIDBTransaction);
    
    // Mock successful operations
    mockIDBObjectStore.add.mockReturnValue({ onsuccess: null, onerror: null });
    mockIDBObjectStore.put.mockReturnValue({ onsuccess: null, onerror: null });
    mockIDBObjectStore.get.mockReturnValue({ onsuccess: null, onerror: null, result: null });
    mockIDBObjectStore.getAll.mockReturnValue({ onsuccess: null, onerror: null, result: [] });
    mockIDBObjectStore.count.mockReturnValue({ onsuccess: null, onerror: null, result: 0 });
  });

  afterEach(() => {
    localStorageManager.cleanup();
  });

  describe('IndexedDB Manager', () => {
    it('should initialize database successfully', async () => {
      // Simulate successful database opening
      const openRequest = {
        onsuccess: null as any,
        onerror: null as any,
        onupgradeneeded: null as any,
        result: mockIDBDatabase
      };

      mockIDBFactory.open.mockReturnValue(openRequest);

      const initPromise = indexedDBManager.initDatabase();
      
      // Simulate successful opening
      setTimeout(() => {
        if (openRequest.onsuccess) {
          openRequest.onsuccess();
        }
      }, 0);

      await expect(initPromise).resolves.toBeUndefined();
    });

    it('should handle database initialization errors', async () => {
      const openRequest = {
        onsuccess: null as any,
        onerror: null as any,
        onupgradeneeded: null as any,
        error: new Error('Database error')
      };

      mockIDBFactory.open.mockReturnValue(openRequest);

      const initPromise = indexedDBManager.initDatabase();
      
      // Simulate error
      setTimeout(() => {
        if (openRequest.onerror) {
          openRequest.onerror();
        }
      }, 0);

      await expect(initPromise).rejects.toThrow();
    });
  });

  describe('Local Storage Manager', () => {
    it('should validate project data correctly', () => {
      const validProject: Project = {
        title: 'Test Project',
        description: 'A test project',
        tasks: []
      };

      // Access private method through any cast for testing
      const validation = (localStorageManager as any).validateProject(validProject);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid project data', () => {
      const invalidProject: Project = {
        title: '', // Empty title should be invalid
        description: 'A test project',
        tasks: []
      };

      const validation = (localStorageManager as any).validateProject(invalidProject);
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0].field).toBe('title');
    });

    it('should validate task data correctly', () => {
      const validTask: Task = {
        id: 'test-task-1',
        title: 'Test Task',
        description: 'A test task',
        content: 'Task content',
        status: 'To Do',
        assignees: [],
        subtasks: []
      };

      const validation = (localStorageManager as any).validateTask(validTask);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid task data', () => {
      const invalidTask: Task = {
        id: '',
        title: '',
        description: 'A test task',
        content: 'Task content',
        status: 'Invalid Status' as any,
        assignees: [],
        subtasks: []
      };

      const validation = (localStorageManager as any).validateTask(invalidTask);
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('should calculate task depth correctly', () => {
      const deepTask: Task = {
        id: 'task-1',
        title: 'Level 1',
        description: '',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: [{
          id: 'task-2',
          title: 'Level 2',
          description: '',
          content: '',
          status: 'To Do',
          assignees: [],
          subtasks: [{
            id: 'task-3',
            title: 'Level 3',
            description: '',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          }]
        }]
      };

      const depth = (localStorageManager as any).calculateTaskDepth(deepTask);
      expect(depth).toBe(2); // 0-indexed depth
    });

    it('should handle auto-save debouncing', (done) => {
      const testTask: Task = {
        id: 'auto-save-task',
        title: 'Auto Save Test',
        description: 'Testing auto-save functionality',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      };

      // Set up event listener to verify auto-save started
      localStorageManager.addEventListener('auto_save_started', (event) => {
        expect(event.type).toBe('auto_save_started');
        expect(event.data.taskId).toBe(testTask.id);
        done();
      });

      // Trigger auto-save
      localStorageManager.autoSaveTask(testTask);
    });

    it('should handle storage events correctly', (done) => {
      let eventCount = 0;
      
      const eventListener = (event: any) => {
        eventCount++;
        expect(event.type).toBe('auto_save_started');
        expect(event.timestamp).toBeInstanceOf(Date);
        
        if (eventCount === 1) {
          // Remove listener and verify it's not called again
          localStorageManager.removeEventListener('auto_save_started', eventListener);
          
          // Emit another event
          (localStorageManager as any).emitEvent('auto_save_started', { test: true });
          
          // Should not increment eventCount
          setTimeout(() => {
            expect(eventCount).toBe(1);
            done();
          }, 10);
        }
      };

      localStorageManager.addEventListener('auto_save_started', eventListener);
      
      // Emit test event
      (localStorageManager as any).emitEvent('auto_save_started', { test: true });
    });
  });

  describe('Integration', () => {
    it('should handle storage statistics', async () => {
      // Mock the stats response
      mockIDBObjectStore.count.mockReturnValue({
        onsuccess: null,
        onerror: null,
        result: 5
      });

      // Simulate successful count operations
      const countRequest = mockIDBObjectStore.count();
      setTimeout(() => {
        if (countRequest.onsuccess) {
          countRequest.onsuccess();
        }
      }, 0);

      // Note: This test would need proper IndexedDB initialization
      // For now, we just verify the method exists and can be called
      expect(typeof localStorageManager.getStats).toBe('function');
    });

    it('should cleanup resources properly', () => {
      // Add some auto-save timers
      const testTask: Task = {
        id: 'cleanup-task',
        title: 'Cleanup Test',
        description: '',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      };

      localStorageManager.autoSaveTask(testTask);
      
      // Verify cleanup clears timers
      localStorageManager.cleanup();
      
      // Should not throw any errors
      expect(true).toBe(true);
    });
  });
});