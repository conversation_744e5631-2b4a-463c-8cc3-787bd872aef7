/**
 * Tests for insertion performance optimizations
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { debounce, throttle, debounceMouseEvent } from '@/lib/utils/debounce';
import { insertionPerformanceMonitor } from '@/lib/utils/insertionPerformanceMonitor';
import { zoneCacheUtils } from '@/lib/utils/insertionZoneCache';
import type { InsertionZone } from '@/lib/types';

// Mock performance API
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 10 // 10MB
    }
  }
});

describe('Insertion Performance Optimizations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    insertionPerformanceMonitor.clear();
    zoneCacheUtils.clear();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Debouncing Utilities', () => {
    it('should debounce function calls correctly', async () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      // Call multiple times rapidly
      debouncedFn();
      debouncedFn();
      debouncedFn();

      // Should not have been called yet
      expect(mockFn).not.toHaveBeenCalled();

      // Wait for debounce delay
      await new Promise(resolve => setTimeout(resolve, 150));

      // Should have been called only once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should throttle function calls correctly', async () => {
      const mockFn = vi.fn();
      const throttledFn = throttle(mockFn, 100);

      // Call multiple times rapidly
      throttledFn();
      throttledFn();
      throttledFn();

      // Should have been called immediately (leading edge)
      expect(mockFn).toHaveBeenCalledTimes(1);

      // Wait for throttle period
      await new Promise(resolve => setTimeout(resolve, 150));

      // Call again
      throttledFn();

      // Should have been called again
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should debounce mouse events with optimized performance', () => {
      const mockCallback = vi.fn();
      const debouncedMouseHandler = debounceMouseEvent(mockCallback, 16);

      // Create mock mouse events
      const mockEvent = {
        clientX: 100,
        clientY: 200,
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as any;

      // Call multiple times rapidly
      debouncedMouseHandler(mockEvent);
      debouncedMouseHandler(mockEvent);
      debouncedMouseHandler(mockEvent);

      // Should not have been called yet
      expect(mockCallback).not.toHaveBeenCalled();
    });

    it('should cancel debounced functions correctly', () => {
      const mockFn = vi.fn();
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      debouncedFn.cancel();

      // Wait longer than debounce delay
      setTimeout(() => {
        expect(mockFn).not.toHaveBeenCalled();
      }, 150);
    });

    it('should flush debounced functions correctly', () => {
      const mockFn = vi.fn(() => 'result');
      const debouncedFn = debounce(mockFn, 100);

      debouncedFn();
      const result = debouncedFn.flush();

      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(result).toBe('result');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track zone calculation performance', () => {
      const operationId = insertionPerformanceMonitor.startZoneCalculation('task-1', 0);
      
      // Simulate some work
      const calculationTime = insertionPerformanceMonitor.endZoneCalculation(operationId, 5, 0, false);
      
      expect(calculationTime).toBeGreaterThanOrEqual(0);
      
      const metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.summary.totalCalculations).toBe(1);
    });

    it('should record hover events', () => {
      insertionPerformanceMonitor.recordHoverEvent();
      insertionPerformanceMonitor.recordHoverEvent();
      insertionPerformanceMonitor.recordHoverEvent();

      const metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.summary).toBeDefined();
    });

    it('should detect slow calculations and generate alerts', () => {
      // Mock slow calculation
      vi.mocked(performance.now)
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(50); // 50ms calculation

      const operationId = insertionPerformanceMonitor.startZoneCalculation('task-1', 0);
      insertionPerformanceMonitor.endZoneCalculation(operationId, 10, 0, false);

      const metrics = insertionPerformanceMonitor.getMetrics();
      const slowCalculationAlerts = metrics.alerts.filter(alert => alert.type === 'slow_calculation');
      
      expect(slowCalculationAlerts.length).toBeGreaterThan(0);
    });

    it('should track component-specific metrics', () => {
      const componentName = 'TestComponent';
      
      insertionPerformanceMonitor.recordRender(componentName, 10);
      insertionPerformanceMonitor.recordRender(componentName, 15);
      insertionPerformanceMonitor.recordRender(componentName, 12);

      const componentMetrics = insertionPerformanceMonitor.getComponentMetrics(componentName);
      
      expect(componentMetrics).toBeDefined();
      expect(componentMetrics?.renderCount).toBe(3);
      expect(componentMetrics?.averageRenderTime).toBeCloseTo(12.33, 1);
    });

    it('should provide performance recommendations', () => {
      // Generate some performance issues
      const operationId = insertionPerformanceMonitor.startZoneCalculation('task-1', 0);
      
      // Mock slow calculation
      vi.mocked(performance.now)
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(30); // 30ms calculation
      
      insertionPerformanceMonitor.endZoneCalculation(operationId, 5, 0, false);

      const recommendations = insertionPerformanceMonitor.getRecommendations();
      expect(recommendations.length).toBeGreaterThan(0);
      expect(recommendations.some(rec => rec.includes('slow'))).toBe(true);
    });

    it('should clear metrics correctly', () => {
      insertionPerformanceMonitor.recordHoverEvent();
      insertionPerformanceMonitor.recordRender('TestComponent', 10);
      
      let metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.components.length).toBeGreaterThan(0);
      
      insertionPerformanceMonitor.clear();
      
      metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.components.length).toBe(0);
      expect(metrics.alerts.length).toBe(0);
    });
  });

  describe('Zone Cache Optimization', () => {
    const mockZones: InsertionZone[] = [
      {
        id: 'zone-1',
        type: 'before',
        bounds: new DOMRect(0, 0, 100, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      },
      {
        id: 'zone-2',
        type: 'after',
        bounds: new DOMRect(0, 25, 100, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      }
    ];

    const mockBounds = new DOMRect(0, 0, 100, 50);

    it('should cache and retrieve zones correctly', () => {
      const key = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 0);
      
      // Set cache
      zoneCacheUtils.set(key, mockZones, mockBounds);
      
      // Get from cache
      const cachedZones = zoneCacheUtils.get(key, mockBounds);
      
      expect(cachedZones).toEqual(mockZones);
    });

    it('should invalidate cache when bounds change', () => {
      const key = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 0);
      
      // Set cache
      zoneCacheUtils.set(key, mockZones, mockBounds);
      
      // Try to get with different bounds
      const differentBounds = new DOMRect(10, 10, 100, 50);
      const cachedZones = zoneCacheUtils.get(key, differentBounds);
      
      expect(cachedZones).toBeNull();
    });

    it('should generate consistent cache keys', () => {
      const key1 = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 2);
      const key2 = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 2);
      
      expect(key1).toBe(key2);
      
      const key3 = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 3);
      expect(key1).not.toBe(key3);
    });

    it('should provide cache statistics', () => {
      const key1 = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 0);
      const key2 = zoneCacheUtils.generateKey('task-2', 0, mockBounds, 0);
      
      zoneCacheUtils.set(key1, mockZones, mockBounds);
      zoneCacheUtils.set(key2, mockZones, mockBounds);
      
      const stats = zoneCacheUtils.getStats();
      
      expect(stats.size).toBe(2);
      expect(stats.totalRequests).toBeGreaterThanOrEqual(0);
    });

    it('should optimize cache when requested', () => {
      // Fill cache with some entries
      for (let i = 0; i < 10; i++) {
        const key = zoneCacheUtils.generateKey(`task-${i}`, 0, mockBounds, 0);
        zoneCacheUtils.set(key, mockZones, mockBounds);
      }
      
      const statsBefore = zoneCacheUtils.getStats();
      
      zoneCacheUtils.optimize();
      
      const statsAfter = zoneCacheUtils.getStats();
      
      // Cache size should remain reasonable after optimization
      expect(statsAfter.size).toBeLessThanOrEqual(statsBefore.size);
    });

    it('should clear cache correctly', () => {
      const key = zoneCacheUtils.generateKey('task-1', 0, mockBounds, 0);
      zoneCacheUtils.set(key, mockZones, mockBounds);
      
      expect(zoneCacheUtils.has(key)).toBe(true);
      
      zoneCacheUtils.clear();
      
      expect(zoneCacheUtils.has(key)).toBe(false);
    });
  });

  describe('Performance Thresholds', () => {
    it('should detect excessive hover events', () => {
      // Simulate many hover events in quick succession
      for (let i = 0; i < 70; i++) {
        insertionPerformanceMonitor.recordHoverEvent();
      }

      const metrics = insertionPerformanceMonitor.getMetrics();
      const excessiveRenderAlerts = metrics.alerts.filter(alert => alert.type === 'excessive_renders');
      
      expect(excessiveRenderAlerts.length).toBeGreaterThan(0);
    });

    it('should detect memory usage issues', () => {
      // Mock high memory usage
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 100 * 1024 * 1024 // 100MB
        }
      });

      insertionPerformanceMonitor.recordMemoryUsage();

      const metrics = insertionPerformanceMonitor.getMetrics();
      const memoryAlerts = metrics.alerts.filter(alert => alert.type === 'memory_leak');
      
      expect(memoryAlerts.length).toBeGreaterThan(0);
    });

    it('should detect low cache hit rates', () => {
      insertionPerformanceMonitor.recordCachePerformance(0.3, 3, 7); // 30% hit rate

      const metrics = insertionPerformanceMonitor.getMetrics();
      const cacheAlerts = metrics.alerts.filter(alert => alert.type === 'cache_miss_rate');
      
      expect(cacheAlerts.length).toBeGreaterThan(0);
    });
  });

  describe('Integration Performance', () => {
    it('should handle rapid zone calculations efficiently', () => {
      const startTime = performance.now();
      
      // Simulate rapid zone calculations
      for (let i = 0; i < 100; i++) {
        const operationId = insertionPerformanceMonitor.startZoneCalculation(`task-${i}`, 0);
        insertionPerformanceMonitor.endZoneCalculation(operationId, 1, 0, i % 2 === 0); // 50% cache hit rate
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      // Should complete within reasonable time
      expect(totalTime).toBeLessThan(100); // 100ms for 100 calculations
      
      const metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.summary.totalCalculations).toBe(100);
      expect(metrics.summary.cacheEfficiency).toBeCloseTo(0.5, 1);
    });

    it('should maintain performance under stress', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      // Stress test with many operations
      for (let i = 0; i < iterations; i++) {
        insertionPerformanceMonitor.recordHoverEvent();
        
        if (i % 10 === 0) {
          const operationId = insertionPerformanceMonitor.startZoneCalculation(`task-${i}`, 0);
          insertionPerformanceMonitor.endZoneCalculation(operationId, 1, 0, false);
        }
        
        if (i % 5 === 0) {
          insertionPerformanceMonitor.recordRender('StressTestComponent', Math.random() * 20);
        }
      }
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      // Should handle stress test efficiently
      expect(totalTime).toBeLessThan(500); // 500ms for 1000 operations
      
      const metrics = insertionPerformanceMonitor.getMetrics();
      expect(metrics.summary.totalCalculations).toBe(100);
      expect(metrics.components.length).toBeGreaterThan(0);
    });
  });
});