import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Task } from '@/lib/types';
import { exportToJson } from '@/lib/export/json';
import { exportToBackup, validateBackupData } from '@/lib/export/backup';
import { importProject, validateImportFile } from '@/lib/export/import';
import type { PWAMetadata } from '@/lib/export';

// Mock IndexedDB manager
vi.mock('@/lib/storage/indexeddb', () => ({
  indexedDBManager: {
    getAllProjects: vi.fn().mockResolvedValue([]),
    loadSettings: vi.fn().mockResolvedValue(null),
    getStats: vi.fn().mockResolvedValue({ projects: 0, tasks: 0, users: 0 }),
    createProject: vi.fn().mockResolvedValue('test-project-id'),
    createTask: vi.fn().mockResolvedValue('test-task-id'),
    getTasksByProject: vi.fn().mockResolvedValue([])
  }
}));

// Mock DOM methods
Object.defineProperty(window, 'URL', {
  value: {
    createObjectURL: vi.fn(() => 'mock-url'),
    revokeObjectURL: vi.fn()
  }
});

Object.defineProperty(document, 'createElement', {
  value: vi.fn(() => ({
    setAttribute: vi.fn(),
    click: vi.fn(),
    style: {}
  }))
});

Object.defineProperty(document, 'body', {
  value: {
    appendChild: vi.fn(),
    removeChild: vi.fn()
  }
});

describe('Enhanced Export and Import Functionality', () => {
  const mockProject = {
    mainProject: 'Test Project',
    mainProjectDescription: 'A test project for export/import',
    tasks: [
      {
        id: '1',
        title: 'Task 1',
        description: 'First task',
        content: 'Task 1 content',
        status: 'To Do' as const,
        assignees: [],
        subtasks: [
          {
            id: '1.1',
            title: 'Subtask 1.1',
            description: 'First subtask',
            content: '',
            status: 'Done' as const,
            assignees: [],
            subtasks: []
          }
        ]
      },
      {
        id: '2',
        title: 'Task 2',
        description: 'Second task',
        content: 'Task 2 content with AI elaboration',
        status: 'In Progress' as const,
        assignees: [],
        subtasks: []
      }
    ] as Task[]
  };

  const mockPWAMetadata: PWAMetadata = {
    isOfflineOnly: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
    lastSyncAt: new Date('2024-01-10'),
    installDate: new Date('2024-01-01'),
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Test Browser)',
      platform: 'Test Platform',
      language: 'en-US'
    },
    appVersion: '1.0.0'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('JSON Export', () => {
    it('should export project to JSON with PWA metadata', () => {
      expect(() => {
        exportToJson(mockProject, mockPWAMetadata, true);
      }).not.toThrow();
    });

    it('should export project to JSON without timestamps', () => {
      expect(() => {
        exportToJson(mockProject, mockPWAMetadata, false);
      }).not.toThrow();
    });

    it('should handle project without PWA metadata', () => {
      expect(() => {
        exportToJson(mockProject, undefined, true);
      }).not.toThrow();
    });
  });

  describe('Backup Export', () => {
    it('should create backup with complete app state', async () => {
      await expect(
        exportToBackup(mockProject, mockPWAMetadata)
      ).resolves.not.toThrow();
    });

    it('should validate backup data structure', () => {
      const validBackup = {
        metadata: {
          backupDate: new Date(),
          backupVersion: '1.0',
          appVersion: '1.0.0',
          deviceInfo: {
            userAgent: 'test',
            platform: 'test',
            language: 'en'
          }
        },
        projects: [],
        tasks: [],
        appState: {
          lastActiveDate: new Date()
        }
      };

      expect(validateBackupData(validBackup)).toBe(true);
    });

    it('should reject invalid backup data', () => {
      const invalidBackup = {
        metadata: {
          backupDate: new Date()
          // missing required fields
        }
      };

      expect(validateBackupData(invalidBackup)).toBe(false);
    });
  });

  describe('Import Validation', () => {
    it('should validate supported file types', () => {
      const jsonFile = new File(['{}'], 'test.json', { type: 'application/json' });
      const result = validateImportFile(jsonFile);
      expect(result.valid).toBe(true);
    });

    it('should reject unsupported file types', () => {
      const invalidFile = new File(['test'], 'test.exe', { type: 'application/exe' });
      const result = validateImportFile(invalidFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Unsupported file type');
    });

    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(60 * 1024 * 1024)], 'large.json', { 
        type: 'application/json' 
      });
      const result = validateImportFile(largeFile);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('File size too large');
    });
  });

  describe('Import Functionality', () => {
    it('should import JSON data successfully', async () => {
      const jsonData = {
        project: {
          title: 'Imported Project',
          description: 'Test import'
        },
        tasks: [
          {
            id: 'imported-1',
            title: 'Imported Task',
            description: 'Test task',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          }
        ]
      };

      const file = new File([JSON.stringify(jsonData)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(true);
      expect(result.imported.projects).toBe(1);
      expect(result.imported.tasks).toBe(1);
    });

    it('should handle import errors gracefully', async () => {
      const invalidFile = new File(['invalid json'], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(invalidFile, {
        format: 'json',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should import backup data successfully', async () => {
      const backupData = {
        metadata: {
          backupDate: new Date(),
          backupVersion: '1.0',
          appVersion: '1.0.0',
          deviceInfo: {
            userAgent: 'test',
            platform: 'test',
            language: 'en'
          }
        },
        projects: [
          {
            id: 'project-1',
            title: 'Backup Project',
            createdAt: new Date(),
            updatedAt: new Date(),
            isOfflineOnly: true
          }
        ],
        tasks: [
          {
            id: 'task-1',
            projectId: 'project-1',
            title: 'Backup Task',
            description: '',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            orderIndex: 0
          }
        ],
        appState: {
          lastActiveDate: new Date(),
          totalExports: 1
        }
      };

      const file = new File([JSON.stringify(backupData)], 'backup.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'backup',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(true);
      expect(result.imported.projects).toBe(1);
      expect(result.imported.tasks).toBe(1);
    });

    it('should import markdown data with basic parsing', async () => {
      const markdownContent = `# Test Project

> A test project for markdown import

- **1. First Task** ✅
  - *Task description*

- **2. Second Task** 🔄
  - *Another task*`;

      const file = new File([markdownContent], 'test.md', { 
        type: 'text/markdown' 
      });

      const result = await importProject(file, {
        format: 'markdown',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(true);
      expect(result.imported.projects).toBe(1);
      expect(result.warnings).toContain('Markdown import is basic - some formatting may be lost');
    });

    it('should import CSV data with basic parsing', async () => {
      const csvContent = `title,description,status
"Task 1","First task","To Do"
"Task 2","Second task","Done"`;

      const file = new File([csvContent], 'test.csv', { 
        type: 'text/csv' 
      });

      const result = await importProject(file, {
        format: 'csv',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(true);
      expect(result.imported.projects).toBe(1);
      expect(result.imported.tasks).toBe(2);
      expect(result.warnings).toContain('CSV import is basic - hierarchical structure is not preserved');
    });
  });

  describe('Data Validation', () => {
    it('should validate JSON structure', async () => {
      const invalidJsonData = {
        // Missing required project.title
        tasks: []
      };

      const file = new File([JSON.stringify(invalidJsonData)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'append',
        validateData: true
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });

    it('should handle validation warnings', async () => {
      const jsonDataWithWarnings = {
        project: {
          title: 'Test Project'
        },
        tasks: [
          {
            id: 'task-1',
            title: 'Valid Task',
            description: '',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          },
          {
            id: 'task-2',
            title: 'Task with Invalid Status',
            description: '',
            content: '',
            status: 'Invalid Status', // This should generate a warning
            assignees: [],
            subtasks: []
          }
        ]
      };

      const file = new File([JSON.stringify(jsonDataWithWarnings)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'append',
        validateData: true
      });

      expect(result.success).toBe(true);
      expect(result.warnings.length).toBeGreaterThan(0);
    });
  });

  describe('Merge Strategies', () => {
    it('should handle append merge strategy', async () => {
      const jsonData = {
        project: { title: 'Test Project' },
        tasks: []
      };

      const file = new File([JSON.stringify(jsonData)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'append'
      });

      expect(result.success).toBe(true);
    });

    it('should handle merge strategy', async () => {
      const jsonData = {
        project: { title: 'Test Project' },
        tasks: []
      };

      const file = new File([JSON.stringify(jsonData)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'merge'
      });

      expect(result.success).toBe(true);
    });

    it('should handle replace strategy', async () => {
      const jsonData = {
        project: { title: 'Test Project' },
        tasks: []
      };

      const file = new File([JSON.stringify(jsonData)], 'test.json', { 
        type: 'application/json' 
      });

      const result = await importProject(file, {
        format: 'json',
        mergeStrategy: 'replace'
      });

      expect(result.success).toBe(true);
    });
  });
});