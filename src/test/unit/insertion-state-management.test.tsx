import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { TaskList } from '@/components/TaskList';
import type { Task, InsertionState, InsertionPosition } from '@/lib/types';

describe('Insertion State Management', () => {
  const mockTasks: Task[] = [
    {
      id: 'task-1',
      title: 'Task 1',
      description: 'Description 1',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    },
    {
      id: 'task-2',
      title: 'Task 2',
      description: 'Description 2',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    }
  ];

  const mockInsertionState: InsertionState = {
    activeInsertionPoint: null,
    hoveredZone: null,
    keyboardMode: false,
    insertionHistory: [],
    lastCalculatedZones: new Map(),
    zoneCalculationCache: new Map()
  };

  const mockProps = {
    tasks: mockTasks,
    onUpdateTask: vi.fn(),
    onOpenSolveModal: vi.fn(),
    onAddTask: vi.fn(),
    onAddTaskAfter: vi.fn(),
    onDeleteTask: vi.fn(),
    onBreakdownTask: vi.fn(),
    loading: {},
    setLoading: vi.fn(),
    onInsertTask: vi.fn(),
    showInsertionIndicators: true,
    insertionMode: 'hover' as const,
    insertionState: mockInsertionState,
    onInsertionStateChange: vi.fn(),
    insertionLoading: {}
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render TaskList with insertion state props', () => {
    render(<TaskList {...mockProps} />);
    
    expect(screen.getByText('Task 1')).toBeInTheDocument();
    expect(screen.getByText('Task 2')).toBeInTheDocument();
  });

  it('should handle insertion state changes', () => {
    const mockOnInsertionStateChange = vi.fn();
    
    render(
      <TaskList 
        {...mockProps} 
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // The component should be able to receive insertion state changes
    expect(mockOnInsertionStateChange).not.toHaveBeenCalled();
  });

  it('should track insertion history', () => {
    const insertionHistory: InsertionPosition[] = [
      {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      }
    ];

    const stateWithHistory: InsertionState = {
      ...mockInsertionState,
      insertionHistory
    };

    render(
      <TaskList 
        {...mockProps} 
        insertionState={stateWithHistory}
      />
    );

    // Component should render with insertion history
    expect(screen.getByText('Task 1')).toBeInTheDocument();
  });

  it('should handle loading states for insertions', () => {
    const insertionLoading = {
      'after-task-1': true
    };

    render(
      <TaskList 
        {...mockProps} 
        insertionLoading={insertionLoading}
      />
    );

    // Component should render with loading states
    expect(screen.getByText('Task 1')).toBeInTheDocument();
  });
});