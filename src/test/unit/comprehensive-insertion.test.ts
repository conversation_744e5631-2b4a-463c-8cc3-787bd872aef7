/**
 * Comprehensive unit tests for insertion functionality
 * Covers insertion zone calculation, keyboard shortcuts, validation, and error handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useInsertionZones } from '@/hooks/useInsertionZones';
import { useKeyboardInsertion } from '@/hooks/useKeyboardInsertion';
import { 
  validateInsertionPosition,
  canInsertAtPosition,
  createInsertionValidator,
  getInsertionAlternatives
} from '@/lib/utils/insertionValidation';
import { 
  calculateZoneBoundaries,
  detectInsertionZone,
  calculateInsertionPosition
} from '@/lib/utils/insertionZoneUtils';
import type { Task, InsertionPosition, InsertionZone } from '@/lib/types';

// Mock DOM methods
const mockGetBoundingClientRect = vi.fn();
const mockQuerySelector = vi.fn();
const mockQuerySelectorAll = vi.fn();
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();

// Setup DOM mocks
beforeEach(() => {
  Object.defineProperty(document, 'querySelector', {
    writable: true,
    value: mockQuerySelector
  });
  Object.defineProperty(document, 'querySelectorAll', {
    writable: true,
    value: mockQuerySelectorAll
  });
  Object.defineProperty(document, 'addEventListener', {
    writable: true,
    value: mockAddEventListener
  });
  Object.defineProperty(document, 'removeEventListener', {
    writable: true,
    value: mockRemoveEventListener
  });
  
  vi.clearAllMocks();
});

// Test data
const createMockElement = (bounds: DOMRect, id?: string) => ({
  getBoundingClientRect: () => bounds,
  querySelector: mockQuerySelector,
  getAttribute: vi.fn().mockReturnValue(id || 'test-id'),
  focus: vi.fn(),
  hasAttribute: vi.fn().mockReturnValue(false),
  parentElement: null
} as unknown as HTMLElement);

const createTestTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
  id,
  title,
  description: `Description for ${title}`,
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks
});

const testTasks: Task[] = [
  createTestTask('task-1', 'Task 1', [
    createTestTask('task-1-1', 'Subtask 1.1'),
    createTestTask('task-1-2', 'Subtask 1.2', [
      createTestTask('task-1-2-1', 'Deep Subtask')
    ])
  ]),
  createTestTask('task-2', 'Task 2'),
  createTestTask('task-3', 'Task 3', [
    createTestTask('task-3-1', 'Subtask 3.1')
  ])
];

describe('Comprehensive Insertion Zone Calculation Tests', () => {
  describe('Zone boundary calculations', () => {
    it('should calculate correct zones for tasks with various hierarchy levels', () => {
      const taskBounds = new DOMRect(0, 0, 300, 50);
      const subtaskBounds = new DOMRect(20, 60, 280, 40);
      
      const mockTaskElement = createMockElement(taskBounds);
      const mockSubtaskElement = createMockElement(subtaskBounds);
      
      mockQuerySelector.mockReturnValue(mockSubtaskElement);

      const zones = calculateZoneBoundaries(
        mockTaskElement,
        testTasks[0], // Task with subtasks
        0,
        null
      );

      // Should have before, after, and between_parent_child zones
      expect(zones.length).toBeGreaterThanOrEqual(2);
      
      // Verify zone types
      const zoneTypes = zones.map(z => z.type);
      expect(zoneTypes).toContain('before');
      expect(zoneTypes).toContain('after');
      
      // Verify zone properties
      zones.forEach(zone => {
        expect(zone.id).toBeTruthy();
        expect(zone.bounds).toBeInstanceOf(DOMRect);
        expect(zone.targetTaskId).toBe('task-1');
        expect(zone.metadata).toBeDefined();
        expect(typeof zone.metadata?.isVisible).toBe('boolean');
        expect(typeof zone.metadata?.priority).toBe('number');
      });
    });

    it('should handle edge cases in zone calculation', () => {
      // Test with zero-sized element
      const zeroBounds = new DOMRect(0, 0, 0, 0);
      const mockElement = createMockElement(zeroBounds);
      
      const zones = calculateZoneBoundaries(
        mockElement,
        testTasks[1], // Task without subtasks
        0,
        null
      );

      // Should still create zones even with zero bounds
      expect(zones.length).toBeGreaterThanOrEqual(1);
      zones.forEach(zone => {
        expect(zone.bounds).toBeInstanceOf(DOMRect);
      });
    });

    it('should adjust zone sizes for touch mode', () => {
      const taskBounds = new DOMRect(0, 0, 300, 50);
      const mockElement = createMockElement(taskBounds);

      const normalZones = calculateZoneBoundaries(
        mockElement,
        testTasks[1],
        0,
        null,
        { touchMode: false, zoneHeight: 20 }
      );

      const touchZones = calculateZoneBoundaries(
        mockElement,
        testTasks[1],
        0,
        null,
        { touchMode: true, zoneHeight: 40 }
      );

      // Touch zones should be larger
      expect(touchZones[0].bounds.height).toBeGreaterThan(normalZones[0].bounds.height);
      expect(touchZones[0].metadata?.touchFriendly).toBe(true);
      expect(normalZones[0].metadata?.touchFriendly).toBe(false);
    });

    it('should handle deeply nested task hierarchies', () => {
      const deepTask = createTestTask('deep-root', 'Deep Root', [
        createTestTask('level-1', 'Level 1', [
          createTestTask('level-2', 'Level 2', [
            createTestTask('level-3', 'Level 3', [
              createTestTask('level-4', 'Level 4')
            ])
          ])
        ])
      ]);

      const taskBounds = new DOMRect(0, 0, 300, 50);
      const mockElement = createMockElement(taskBounds);

      const zones = calculateZoneBoundaries(
        mockElement,
        deepTask,
        4, // Deep level
        'level-3'
      );

      expect(zones.length).toBeGreaterThan(0);
      zones.forEach(zone => {
        expect(zone.level).toBe(4);
        expect(zone.parentId).toBe('level-3');
      });
    });
  });

  describe('Zone detection and positioning', () => {
    const sampleZones: InsertionZone[] = [
      {
        id: 'before-task-1',
        type: 'before',
        bounds: new DOMRect(0, -10, 300, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      },
      {
        id: 'after-task-1',
        type: 'after',
        bounds: new DOMRect(0, 50, 300, 20),
        targetTaskId: 'task-1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 2, touchFriendly: false }
      },
      {
        id: 'between-task-1-subtask',
        type: 'between_parent_child',
        bounds: new DOMRect(20, 25, 280, 20),
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1,
        metadata: { isVisible: true, priority: 3, touchFriendly: false }
      }
    ];

    it('should detect zones at precise coordinates', () => {
      // Test center of before zone
      const beforeZone = detectInsertionZone(150, 0, sampleZones);
      expect(beforeZone?.type).toBe('before');
      expect(beforeZone?.id).toBe('before-task-1');

      // Test center of after zone
      const afterZone = detectInsertionZone(150, 60, sampleZones);
      expect(afterZone?.type).toBe('after');
      expect(afterZone?.id).toBe('after-task-1');

      // Test center of between zone
      const betweenZone = detectInsertionZone(150, 35, sampleZones);
      expect(betweenZone?.type).toBe('between_parent_child');
      expect(betweenZone?.id).toBe('between-task-1-subtask');
    });

    it('should handle edge coordinates correctly', () => {
      // Test exact boundaries
      const leftEdge = detectInsertionZone(0, 0, sampleZones);
      expect(leftEdge?.type).toBe('before');

      const rightEdge = detectInsertionZone(300, 0, sampleZones);
      expect(rightEdge?.type).toBe('before');

      // Test outside boundaries
      const outside = detectInsertionZone(-10, 0, sampleZones);
      expect(outside).toBeNull();

      const farOutside = detectInsertionZone(400, 0, sampleZones);
      expect(farOutside).toBeNull();
    });

    it('should prioritize overlapping zones correctly', () => {
      const overlappingZones: InsertionZone[] = [
        {
          ...sampleZones[0],
          metadata: { isVisible: true, priority: 3, touchFriendly: false }
        },
        {
          ...sampleZones[1],
          bounds: new DOMRect(0, -5, 300, 30), // Overlaps with first zone
          metadata: { isVisible: true, priority: 1, touchFriendly: false }
        }
      ];

      const zone = detectInsertionZone(150, 0, overlappingZones);
      expect(zone?.metadata?.priority).toBe(1); // Should return higher priority (lower number)
    });

    it('should calculate insertion positions with proper context', () => {
      const zone = sampleZones[0]; // before zone
      const position = calculateInsertionPosition(zone, 150, 0, testTasks);

      expect(position.type).toBe('before');
      expect(position.targetTaskId).toBe('task-1');
      expect(position.parentId).toBeNull();
      expect(position.level).toBe(0);
      expect(position.context).toBeDefined();
      expect(position.context?.siblingCount).toBe(testTasks.length);
      expect(position.context?.hierarchyPath).toEqual(['1']);
    });
  });
});

describe('Comprehensive Keyboard Insertion Tests', () => {
  const mockOnInsertTask = vi.fn();

  beforeEach(() => {
    mockOnInsertTask.mockClear();
    mockQuerySelector.mockReturnValue(null);
    mockQuerySelectorAll.mockReturnValue([]);
  });

  describe('Focus state management', () => {
    it('should track focused task correctly', () => {
      const mockElement = createMockElement(new DOMRect(0, 0, 100, 50), 'task-1');
      mockQuerySelector.mockReturnValue(mockElement);

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      expect(result.current.focusedTask).toBeDefined();
      expect(result.current.focusedTask?.taskId).toBe('task-1');
      expect(result.current.focusedTask?.task).toEqual(testTasks[0]);
    });

    it('should calculate available insertion points for focused task', () => {
      const mockElement = createMockElement(new DOMRect(0, 0, 100, 50), 'task-1');
      mockQuerySelector.mockReturnValue(mockElement);

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      expect(result.current.availableInsertionPoints.length).toBeGreaterThan(0);
      
      // Should include basic insertion types
      const types = result.current.availableInsertionPoints.map(p => p.type);
      expect(types).toContain('after');
      expect(types).toContain('before');
    });

    it('should handle focus changes between different task levels', () => {
      const mockElements = [
        createMockElement(new DOMRect(0, 0, 100, 50), 'task-1'),
        createMockElement(new DOMRect(20, 60, 80, 40), 'task-1-1')
      ];

      mockQuerySelector.mockImplementation((selector) => {
        if (selector.includes('task-1-1')) return mockElements[1];
        if (selector.includes('task-1')) return mockElements[0];
        return null;
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Focus parent task
      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      const parentInsertionPoints = result.current.availableInsertionPoints;

      // Focus child task
      act(() => {
        result.current.updateFocusedTask('task-1-1');
      });

      const childInsertionPoints = result.current.availableInsertionPoints;

      // Should have different insertion points for different levels
      expect(parentInsertionPoints).not.toEqual(childInsertionPoints);
    });
  });

  describe('Keyboard shortcut handling', () => {
    it('should handle custom shortcut configurations', () => {
      const customShortcuts = {
        insertAfter: 'Space',
        insertBefore: 'Shift+Space',
        insertSubtask: 'Ctrl+Space',
        insertBetweenParentChild: 'Alt+Space'
      };

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          shortcuts: customShortcuts,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(result.current.activeShortcuts).toEqual({
        ...customShortcuts,
        insertBetweenParentChild: 'Alt+Space'
      });
    });

    it('should validate shortcut key combinations', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // All shortcuts should be valid key combinations
      Object.values(result.current.activeShortcuts).forEach(shortcut => {
        expect(typeof shortcut).toBe('string');
        expect(shortcut.length).toBeGreaterThan(0);
        // Should not contain invalid characters
        expect(shortcut).not.toMatch(/[^a-zA-Z0-9+\-_]/);
      });
    });

    it('should handle conflicting shortcuts gracefully', () => {
      const conflictingShortcuts = {
        insertAfter: 'Enter',
        insertBefore: 'Enter', // Same as insertAfter
        insertSubtask: 'Tab+Enter',
        insertBetweenParentChild: 'Ctrl+Enter'
      };

      // Should not throw error even with conflicting shortcuts
      expect(() => {
        renderHook(() =>
          useKeyboardInsertion({
            tasks: testTasks,
            shortcuts: conflictingShortcuts,
            onInsertTask: mockOnInsertTask,
            enabled: true
          })
        );
      }).not.toThrow();
    });
  });

  describe('Task navigation', () => {
    it('should navigate between tasks in correct order', () => {
      const mockElements = testTasks.map((task, index) => 
        createMockElement(new DOMRect(0, index * 60, 100, 50), task.id)
      );

      mockQuerySelectorAll.mockReturnValue(mockElements);
      mockQuerySelector.mockImplementation((selector) => {
        const taskId = selector.match(/task-[\w-]+/)?.[0];
        return mockElements.find(el => el.getAttribute('data-task-id') === taskId) || null;
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Set initial focus
      act(() => {
        result.current.updateFocusedTask('task-1');
      });

      // Navigate to next task
      act(() => {
        result.current.navigateToTask('next');
      });

      // Should focus the next element
      expect(mockElements[1].focus).toHaveBeenCalled();
    });

    it('should handle navigation at boundaries', () => {
      const mockElements = testTasks.map((task, index) => 
        createMockElement(new DOMRect(0, index * 60, 100, 50), task.id)
      );

      mockQuerySelectorAll.mockReturnValue(mockElements);
      mockQuerySelector.mockImplementation((selector) => {
        const taskId = selector.match(/task-[\w-]+/)?.[0];
        return mockElements.find(el => el.getAttribute('data-task-id') === taskId) || null;
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Focus last task
      act(() => {
        result.current.updateFocusedTask('task-3');
      });

      // Try to navigate beyond last task
      act(() => {
        result.current.navigateToTask('next');
      });

      // Should handle gracefully (might wrap to first or stay at last)
      expect(() => result.current.navigateToTask('next')).not.toThrow();
    });
  });

  describe('Keyboard mode state', () => {
    it('should toggle keyboard mode correctly', () => {
      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      expect(result.current.keyboardMode).toBe(false);

      act(() => {
        result.current.setKeyboardMode(true);
      });

      expect(result.current.keyboardMode).toBe(true);

      act(() => {
        result.current.setKeyboardMode(false);
      });

      expect(result.current.keyboardMode).toBe(false);
    });

    it('should maintain keyboard mode state across re-renders', () => {
      const { result, rerender } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      act(() => {
        result.current.setKeyboardMode(true);
      });

      rerender();

      expect(result.current.keyboardMode).toBe(true);
    });
  });
});

describe('Comprehensive Insertion Validation Tests', () => {
  describe('Position validation with various constraints', () => {
    it('should validate positions against multiple constraint types', () => {
      const validator = createInsertionValidator({
        maxDepth: 3,
        maxSiblings: 5,
        allowedPositions: ['before', 'after', 'between_parent_child'],
        parentRestrictions: ['restricted-parent']
      });

      const validPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const result = validator.validatePosition(validPosition, testTasks);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should provide detailed error messages for constraint violations', () => {
      const validator = createInsertionValidator({
        maxDepth: 1,
        maxSiblings: 1,
        allowedPositions: ['before'],
        parentRestrictions: ['task-1']
      });

      const violations = [
        {
          position: { type: 'after' as const, targetTaskId: 'task-1', parentId: null, level: 0 },
          expectedError: 'not allowed'
        },
        {
          position: { type: 'before' as const, targetTaskId: 'task-1', parentId: 'task-1', level: 2 },
          expectedError: 'exceeds maximum allowed depth'
        },
        {
          position: { type: 'before' as const, targetTaskId: 'task-1', parentId: 'task-1', level: 1 },
          expectedError: 'restricted parent task'
        }
      ];

      violations.forEach(({ position, expectedError }) => {
        const result = validator.validatePosition(position, testTasks);
        expect(result.isValid).toBe(false);
        expect(result.errors.some(error => error.includes(expectedError))).toBe(true);
      });
    });

    it('should suggest valid alternatives for invalid positions', () => {
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const alternatives = getInsertionAlternatives(position, testTasks, {
        allowedPositions: ['before', 'after']
      });

      expect(alternatives.length).toBeGreaterThan(0);
      alternatives.forEach(alt => {
        expect(['before', 'after']).toContain(alt.type);
        const validation = validateInsertionPosition(alt, testTasks, {
          allowedPositions: ['before', 'after']
        });
        expect(validation.isValid).toBe(true);
      });
    });

    it('should handle circular reference detection', () => {
      const circularPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1', // Same as target - potential circular reference
        level: 1
      };

      const result = validateInsertionPosition(circularPosition, testTasks);
      
      // Should detect and handle circular references
      if (!result.isValid) {
        expect(result.errors.some(error => 
          error.includes('circular') || error.includes('parent-child relationship')
        )).toBe(true);
      }
    });
  });

  describe('Performance validation', () => {
    it('should validate large task hierarchies efficiently', () => {
      const largeTasks: Task[] = Array.from({ length: 100 }, (_, i) =>
        createTestTask(`large-task-${i}`, `Large Task ${i}`, 
          Array.from({ length: 10 }, (_, j) =>
            createTestTask(`large-task-${i}-${j}`, `Subtask ${i}.${j}`)
          )
        )
      );

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'large-task-50',
        parentId: null,
        level: 0
      };

      const startTime = performance.now();
      const result = validateInsertionPosition(position, largeTasks);
      const endTime = performance.now();

      expect(result.isValid).toBe(true);
      expect(endTime - startTime).toBeLessThan(50); // Should be fast
    });

    it('should handle validation of multiple positions concurrently', async () => {
      const positions: InsertionPosition[] = Array.from({ length: 20 }, (_, i) => ({
        type: 'after' as const,
        targetTaskId: i < testTasks.length ? testTasks[i % testTasks.length].id : 'task-1',
        parentId: null,
        level: 0
      }));

      const startTime = performance.now();
      const results = await Promise.all(
        positions.map(position => 
          Promise.resolve(validateInsertionPosition(position, testTasks))
        )
      );
      const endTime = performance.now();

      expect(results).toHaveLength(20);
      results.forEach(result => {
        expect(typeof result.isValid).toBe('boolean');
      });
      expect(endTime - startTime).toBeLessThan(100);
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle malformed task data gracefully', () => {
      const malformedTasks = [
        { id: 'task1', title: 'Task 1' } as Task, // Missing required fields
        { id: '', title: 'Empty ID', subtasks: [] } as Task, // Empty ID
        null as any, // Null task
        undefined as any // Undefined task
      ].filter(Boolean);

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      expect(() => {
        validateInsertionPosition(position, malformedTasks);
      }).not.toThrow();
    });

    it('should handle empty and null inputs', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      // Empty task array
      const emptyResult = validateInsertionPosition(position, []);
      expect(emptyResult.isValid).toBe(false);

      // Null/undefined inputs
      expect(() => {
        validateInsertionPosition(null as any, testTasks);
      }).not.toThrow();

      expect(() => {
        validateInsertionPosition(position, null as any);
      }).not.toThrow();
    });

    it('should provide fallback behavior for validation failures', () => {
      const invalidPosition: InsertionPosition = {
        type: '' as any, // Invalid type
        targetTaskId: '',
        parentId: null,
        level: -1
      };

      const result = validateInsertionPosition(invalidPosition, testTasks);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.suggestedAlternatives).toBeDefined();
    });

    it('should handle validation with missing dependencies', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: 'nonexistent-parent',
        level: 1
      };

      const result = validateInsertionPosition(position, testTasks);
      
      // Should handle missing parent gracefully
      expect(typeof result.isValid).toBe('boolean');
      expect(Array.isArray(result.errors)).toBe(true);
    });
  });
});

describe('Integration Error Handling Tests', () => {
  describe('Zone calculation error recovery', () => {
    it('should handle DOM element access failures', () => {
      const faultyElement = {
        getBoundingClientRect: vi.fn().mockImplementation(() => {
          throw new Error('DOM access failed');
        }),
        querySelector: vi.fn()
      } as unknown as HTMLElement;

      expect(() => {
        calculateZoneBoundaries(faultyElement, testTasks[0], 0, null);
      }).not.toThrow();
    });

    it('should provide fallback zones when calculation fails', () => {
      const nullElement = null as any;
      
      const zones = calculateZoneBoundaries(nullElement, testTasks[0], 0, null);
      
      // Should return empty array or minimal zones rather than throwing
      expect(Array.isArray(zones)).toBe(true);
    });
  });

  describe('Keyboard insertion error recovery', () => {
    it('should handle missing DOM elements gracefully', () => {
      mockQuerySelector.mockReturnValue(null);
      mockQuerySelectorAll.mockReturnValue([]);

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: mockOnInsertTask,
          enabled: true
        })
      );

      // Should not throw when DOM elements are missing
      expect(() => {
        act(() => {
          result.current.updateFocusedTask('nonexistent-task');
        });
      }).not.toThrow();

      expect(() => {
        act(() => {
          result.current.navigateToTask('next');
        });
      }).not.toThrow();
    });

    it('should handle callback errors gracefully', () => {
      const faultyCallback = vi.fn().mockImplementation(() => {
        throw new Error('Callback failed');
      });

      const { result } = renderHook(() =>
        useKeyboardInsertion({
          tasks: testTasks,
          onInsertTask: faultyCallback,
          enabled: true
        })
      );

      // Should not crash the hook when callback fails
      expect(result.current).toBeDefined();
      expect(typeof result.current.updateFocusedTask).toBe('function');
    });
  });

  describe('Validation error recovery', () => {
    it('should handle validator initialization failures', () => {
      const invalidConfig = {
        maxDepth: -1, // Invalid
        maxSiblings: 'invalid' as any, // Wrong type
        allowedPositions: null as any // Null
      };

      expect(() => {
        createInsertionValidator(invalidConfig);
      }).not.toThrow();
    });

    it('should provide meaningful error messages for all failure modes', () => {
      const testCases = [
        { targetTaskId: '', expectedError: 'empty' },
        { targetTaskId: 'nonexistent', expectedError: 'not found' },
        { type: 'invalid' as any, expectedError: 'invalid' },
        { level: -1, expectedError: 'negative' }
      ];

      testCases.forEach(({ expectedError, ...positionOverrides }) => {
        const position: InsertionPosition = {
          type: 'after',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0,
          ...positionOverrides
        };

        const result = validateInsertionPosition(position, testTasks);
        
        if (!result.isValid) {
          expect(result.errors.some(error => 
            error.toLowerCase().includes(expectedError.toLowerCase())
          )).toBe(true);
        }
      });
    });
  });
});