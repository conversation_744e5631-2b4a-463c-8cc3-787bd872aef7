/**
 * Unit tests for insertion position validation system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import type { Task, InsertionPosition } from '@/lib/types';
import {
  TaskInsertionValidator,
  createInsertionValidator,
  validateInsertionPosition,
  canInsertAtPosition,
  getInsertionAlternatives,
  validateMultiplePositions,
  getGlobalInsertionConstraints
} from '@/lib/utils/insertionValidation';

// Test data setup
const createTestTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
  id,
  title,
  description: `Description for ${title}`,
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks
});

const createTestTasks = (): Task[] => [
  createTestTask('task1', 'Task 1', [
    createTestTask('task1-1', 'Task 1.1'),
    createTestTask('task1-2', 'Task 1.2', [
      createTestTask('task1-2-1', 'Task 1.2.1')
    ])
  ]),
  createTestTask('task2', 'Task 2'),
  createTestTask('task3', 'Task 3', [
    createTestTask('task3-1', 'Task 3.1')
  ])
];

describe('TaskInsertionValidator', () => {
  let validator: TaskInsertionValidator;
  let testTasks: Task[];

  beforeEach(() => {
    validator = new TaskInsertionValidator();
    testTasks = createTestTasks();
  });

  describe('validatePosition', () => {
    it('should validate a valid insertion position', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject insertion with missing target task', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'nonexistent',
        parentId: null,
        level: 0
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Target task with ID 'nonexistent' not found");
    });

    it('should reject insertion with invalid type', () => {
      const validator = new TaskInsertionValidator({
        allowedPositions: ['before', 'after'] // Exclude between_parent_child
      });

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain("Insertion type 'between_parent_child' is not allowed");
      expect(result.suggestedAlternatives).toBeDefined();
      expect(result.suggestedAlternatives!.length).toBeGreaterThan(0);
    });

    it('should reject insertion exceeding maximum depth', () => {
      const validator = new TaskInsertionValidator({ maxDepth: 2 });

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1-2-1', // Already at depth 3
        parentId: 'task1-2-1',
        level: 3
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('exceeds maximum allowed depth'))).toBe(true);
      expect(result.suggestedAlternatives).toBeDefined();
    });

    it('should reject insertion exceeding maximum siblings', () => {
      const validator = new TaskInsertionValidator({ maxSiblings: 1 });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('maximum sibling count'))).toBe(true);
    });

    it('should reject insertion under restricted parent', () => {
      const validator = new TaskInsertionValidator({
        parentRestrictions: ['task1']
      });

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: 'task1',
        level: 1
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.includes('restricted parent task'))).toBe(true);
      expect(result.suggestedAlternatives).toBeDefined();
    });

    it('should provide warnings for deep nesting', () => {
      const validator = new TaskInsertionValidator({ maxDepth: 10 });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1-2-1',
        parentId: 'task1-2',
        level: 8 // Close to max depth (80% of 10 = 8)
      };

      const result = validator.validatePosition(position, testTasks);

      expect(result.isValid).toBe(true);
      expect(result.warnings.some(warning => warning.includes('may impact performance'))).toBe(true);
    });

    it('should validate basic position properties', () => {
      const invalidPositions: InsertionPosition[] = [
        {
          type: 'after',
          targetTaskId: '', // Empty target ID
          parentId: null,
          level: 0
        },
        {
          type: '' as any, // Empty type
          targetTaskId: 'task1',
          parentId: null,
          level: 0
        },
        {
          type: 'after',
          targetTaskId: 'task1',
          parentId: null,
          level: -1 // Negative level
        }
      ];

      invalidPositions.forEach(position => {
        const result = validator.validatePosition(position, testTasks);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });

  describe('canInsertAtPosition', () => {
    it('should return true for valid position', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      expect(validator.canInsertAtPosition(position)).toBe(true);
    });

    it('should return false for invalid position', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: '', // Invalid
        parentId: null,
        level: 0
      };

      expect(validator.canInsertAtPosition(position)).toBe(false);
    });
  });

  describe('getInsertionConstraints', () => {
    it('should return correct constraints', () => {
      const constraints = validator.getInsertionConstraints('task1');

      expect(constraints.maxDepth).toBe(10); // Default
      expect(constraints.allowedPositions).toContain('before');
      expect(constraints.allowedPositions).toContain('after');
      expect(constraints.allowedPositions).toContain('between_parent_child');
      expect(constraints.maxSiblings).toBe(50); // Default
    });
  });
});

describe('Utility Functions', () => {
  let testTasks: Task[];

  beforeEach(() => {
    testTasks = createTestTasks();
  });

  describe('createInsertionValidator', () => {
    it('should create validator with default config', () => {
      const validator = createInsertionValidator();
      expect(validator).toBeInstanceOf(TaskInsertionValidator);
    });

    it('should create validator with custom config', () => {
      const validator = createInsertionValidator({
        maxDepth: 5,
        maxSiblings: 10
      });
      
      const constraints = validator.getInsertionConstraints('test');
      expect(constraints.maxDepth).toBe(5);
      expect(constraints.maxSiblings).toBe(10);
    });
  });

  describe('validateInsertionPosition', () => {
    it('should validate position using utility function', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const result = validateInsertionPosition(position, testTasks);
      expect(result.isValid).toBe(true);
    });
  });

  describe('canInsertAtPosition', () => {
    it('should check insertion possibility using utility function', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      expect(canInsertAtPosition(position, testTasks)).toBe(true);
    });
  });

  describe('getInsertionAlternatives', () => {
    it('should return alternatives for invalid position', () => {
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const alternatives = getInsertionAlternatives(position, testTasks, {
        allowedPositions: ['before', 'after'] // Exclude between_parent_child
      });

      expect(alternatives.length).toBeGreaterThan(0);
      expect(alternatives.every(alt => alt.type !== 'between_parent_child')).toBe(true);
    });

    it('should return empty array for valid position', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const alternatives = getInsertionAlternatives(position, testTasks);
      expect(alternatives).toHaveLength(0);
    });
  });

  describe('validateMultiplePositions', () => {
    it('should validate multiple positions', () => {
      const positions: InsertionPosition[] = [
        {
          type: 'after',
          targetTaskId: 'task1',
          parentId: null,
          level: 0
        },
        {
          type: 'before',
          targetTaskId: 'task2',
          parentId: null,
          level: 0
        },
        {
          type: 'after',
          targetTaskId: 'nonexistent', // Invalid
          parentId: null,
          level: 0
        }
      ];

      const results = validateMultiplePositions(positions, testTasks);

      expect(results).toHaveLength(3);
      expect(results[0].result.isValid).toBe(true);
      expect(results[1].result.isValid).toBe(true);
      expect(results[2].result.isValid).toBe(false);
    });
  });

  describe('getGlobalInsertionConstraints', () => {
    it('should return global constraints and recommendations', () => {
      const constraints = getGlobalInsertionConstraints(testTasks);

      expect(constraints.currentDepth).toBeGreaterThan(0);
      expect(constraints.maxDepth).toBe(10); // Default
      expect(constraints.totalTasks).toBeGreaterThan(0);
      expect(typeof constraints.canInsertMore).toBe('boolean');
      expect(Array.isArray(constraints.recommendations)).toBe(true);
    });

    it('should provide recommendations for deep hierarchies', () => {
      const constraints = getGlobalInsertionConstraints(testTasks, { maxDepth: 3 });

      expect(constraints.recommendations.some(rec => 
        rec.includes('flattening deep task hierarchies')
      )).toBe(true);
    });
  });
});

describe('Edge Cases and Error Handling', () => {
  let testTasks: Task[];

  beforeEach(() => {
    testTasks = createTestTasks();
  });

  it('should handle empty task array', () => {
    const position: InsertionPosition = {
      type: 'after',
      targetTaskId: 'task1',
      parentId: null,
      level: 0
    };

    const result = validateInsertionPosition(position, []);
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Target task with ID 'task1' not found");
  });

  it('should handle circular reference detection', () => {
    const position: InsertionPosition = {
      type: 'between_parent_child',
      targetTaskId: 'task1',
      parentId: 'task1', // Same as target - circular
      level: 1
    };

    const result = validateInsertionPosition(position, testTasks);
    expect(result.isValid).toBe(false);
    expect(result.errors.some(error => error.includes('circular parent-child relationship'))).toBe(true);
  });

  it('should handle level consistency warnings', () => {
    const position: InsertionPosition = {
      type: 'after',
      targetTaskId: 'task1-1',
      parentId: 'task1',
      level: 5 // Wrong level for this parent
    };

    const result = validateInsertionPosition(position, testTasks);
    expect(result.warnings.some(warning => warning.includes('Level mismatch'))).toBe(true);
  });

  it('should handle validation errors gracefully', () => {
    // Create a position that might cause internal errors
    const position: InsertionPosition = {
      type: 'between_parent_child',
      targetTaskId: 'task1',
      parentId: null,
      level: 0
    };

    // Should not throw, even if internal calculations fail
    expect(() => {
      validateInsertionPosition(position, testTasks);
    }).not.toThrow();
  });

  it('should provide fallback alternatives when specific validation fails', () => {
    const validator = new TaskInsertionValidator({
      maxDepth: 1, // Very restrictive
      maxSiblings: 1 // Very restrictive
    });

    const position: InsertionPosition = {
      type: 'between_parent_child',
      targetTaskId: 'task1',
      parentId: 'task1',
      level: 2 // Exceeds max depth
    };

    const result = validator.validatePosition(position, testTasks);
    expect(result.isValid).toBe(false);
    expect(result.suggestedAlternatives).toBeDefined();
    expect(result.suggestedAlternatives!.length).toBeGreaterThan(0);
    
    // Should suggest alternatives at allowed levels
    const alternatives = result.suggestedAlternatives!;
    expect(alternatives.some(alt => alt.level <= 1)).toBe(true);
  });
});

describe('Configuration Options', () => {
  it('should respect custom maxDepth configuration', () => {
    const validator = new TaskInsertionValidator({ maxDepth: 2 });
    
    const position: InsertionPosition = {
      type: 'after',
      targetTaskId: 'task1-2-1', // At depth 3
      parentId: 'task1-2',
      level: 2
    };

    const result = validator.validatePosition(position, createTestTasks());
    expect(result.isValid).toBe(true); // Should be valid at level 2 with maxDepth 2
  });

  it('should respect custom allowedPositions configuration', () => {
    const validator = new TaskInsertionValidator({
      allowedPositions: ['after'] // Only allow 'after'
    });

    const beforePosition: InsertionPosition = {
      type: 'before',
      targetTaskId: 'task1',
      parentId: null,
      level: 0
    };

    const afterPosition: InsertionPosition = {
      type: 'after',
      targetTaskId: 'task1',
      parentId: null,
      level: 0
    };

    expect(validator.canInsertAtPosition(beforePosition)).toBe(false);
    expect(validator.canInsertAtPosition(afterPosition)).toBe(true);
  });

  it('should respect strictMode configuration', () => {
    const strictValidator = new TaskInsertionValidator({ strictMode: true });
    const lenientValidator = new TaskInsertionValidator({ strictMode: false });

    // Both should behave the same for basic validation
    // (strictMode could be used for additional checks in future)
    const position: InsertionPosition = {
      type: 'after',
      targetTaskId: 'task1',
      parentId: null,
      level: 0
    };

    const strictResult = strictValidator.validatePosition(position, createTestTasks());
    const lenientResult = lenientValidator.validatePosition(position, createTestTasks());

    expect(strictResult.isValid).toBe(lenientResult.isValid);
  });
});