import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { InsertionIndicator } from '@/components/InsertionIndicator';
import type { InsertionPosition } from '@/lib/types';

// Mock the mobile hook
vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: vi.fn()
}));

import { useIsMobile } from '@/hooks/use-mobile';

describe('Mobile Touch Insertion Controls', () => {
  const mockPosition: InsertionPosition = {
    type: 'after',
    targetTaskId: 'test-task',
    parentId: null,
    level: 0
  };

  const mockOnInsert = vi.fn();
  const mockOnLongPress = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Desktop behavior', () => {
    beforeEach(() => {
      useIsMobile.mockReturnValue(false);
    });

    it('should render with default variant on desktop', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('insertion-button');
      expect(button).not.toHaveClass('min-w-[44px]');
    });

    it('should handle mouse clicks on desktop', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });
  });

  describe('Mobile behavior', () => {
    beforeEach(() => {
      useIsMobile.mockReturnValue(true);
    });

    it('should render with touch-friendly sizing on mobile', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('insertion-button');
      expect(button).toHaveClass('min-w-[44px]');
      expect(button).toHaveClass('min-h-[44px]');
    });

    it('should handle touch events', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      // Simulate touch start and end
      fireEvent.touchStart(button);
      fireEvent.touchEnd(button);
      
      expect(mockOnInsert).toHaveBeenCalledTimes(1);
    });

    it('should handle long press gestures', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
          longPressDelay={100} // Short delay for testing
        />
      );

      const button = screen.getByRole('button');
      
      // Start touch
      fireEvent.touchStart(button);
      
      // Wait for long press delay
      await waitFor(() => {
        expect(mockOnLongPress).toHaveBeenCalledTimes(1);
      }, { timeout: 200 });
      
      // End touch
      fireEvent.touchEnd(button);
      
      // Should not trigger regular insert after long press
      expect(mockOnInsert).not.toHaveBeenCalled();
    });

    it('should cancel long press on touch cancel', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
          longPressDelay={100}
        />
      );

      const button = screen.getByRole('button');
      
      // Start touch
      fireEvent.touchStart(button);
      
      // Cancel touch before long press completes
      fireEvent.touchCancel(button);
      
      // Wait to ensure long press doesn't trigger
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(mockOnLongPress).not.toHaveBeenCalled();
      expect(mockOnInsert).not.toHaveBeenCalled();
    });

    it('should show touch variant when touchFriendly is true', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          touchFriendly={true}
          variant="touch"
        />
      );

      const container = screen.getByTestId(`insertion-indicator-${mockPosition.type}-${mockPosition.targetTaskId}`);
      expect(container).toBeInTheDocument();
      
      // Should have larger dimensions for touch
      const button = screen.getByRole('button');
      expect(button).toHaveClass('touch-manipulation');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for touch devices', () => {
      useIsMobile.mockReturnValue(true);
      
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-label');
      expect(button.getAttribute('aria-label')).toContain('Long press for options');
    });

    it('should prevent text selection on touch devices', () => {
      useIsMobile.mockReturnValue(true);
      
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('select-none');
    });
  });

  describe('Visual feedback', () => {
    it('should show long press progress indicator', async () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
          onLongPress={mockOnLongPress}
          longPressDelay={200}
        />
      );

      const button = screen.getByRole('button');
      
      // Start touch to trigger long press progress
      fireEvent.touchStart(button);
      
      // Should show progress indicator
      await waitFor(() => {
        const progressIndicator = document.querySelector('[style*="longPressProgress"]');
        expect(progressIndicator).toBeInTheDocument();
      });
      
      fireEvent.touchCancel(button);
    });

    it('should show ripple effect on touch', () => {
      render(
        <InsertionIndicator
          position={mockPosition}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      // Start touch to show ripple
      fireEvent.touchStart(button);
      
      // Should show ripple effect
      const ripple = document.querySelector('.animate-ping');
      expect(ripple).toBeInTheDocument();
      
      fireEvent.touchEnd(button);
    });
  });
});