import { describe, it, expect, vi } from 'vitest';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock implementation of the enhanced handleAddTaskAfter logic for testing
const insertTaskBefore = (tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
  if (!parentId) {
    // Insert at root level
    const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
    if (targetIndex !== -1) {
      const newTasks = [...tasks];
      newTasks.splice(targetIndex, 0, newTask);
      return newTasks;
    }
    // If not found at root, search recursively
    return tasks.map(task => ({
      ...task,
      subtasks: insertTaskBefore(task.subtasks || [], targetTaskId, newTask, parentId)
    }));
  } else {
    // Insert within specific parent's subtasks
    return tasks.map(task => {
      if (task.id === parentId) {
        const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
        if (targetIndex !== -1) {
          const newSubtasks = [...(task.subtasks || [])];
          newSubtasks.splice(targetIndex, 0, newTask);
          return { ...task, subtasks: newSubtasks };
        }
      }
      if (task.subtasks) {
        return { ...task, subtasks: insertTaskBefore(task.subtasks, targetTaskId, newTask, parentId) };
      }
      return task;
    });
  }
};

const insertTaskAfter = (tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
  if (!parentId) {
    // Insert at root level
    const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
    if (targetIndex !== -1) {
      const newTasks = [...tasks];
      newTasks.splice(targetIndex + 1, 0, newTask);
      return newTasks;
    }
    // If not found at root, search recursively
    return tasks.map(task => ({
      ...task,
      subtasks: insertTaskAfter(task.subtasks || [], targetTaskId, newTask, parentId)
    }));
  } else {
    // Insert within specific parent's subtasks
    return tasks.map(task => {
      if (task.id === parentId) {
        const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
        if (targetIndex !== -1) {
          const newSubtasks = [...(task.subtasks || [])];
          newSubtasks.splice(targetIndex + 1, 0, newTask);
          return { ...task, subtasks: newSubtasks };
        }
      }
      if (task.subtasks) {
        return { ...task, subtasks: insertTaskAfter(task.subtasks, targetTaskId, newTask, parentId) };
      }
      return task;
    });
  }
};

const insertTaskBetweenParentChild = (tasks: Task[], parentTaskId: string, newTask: Task): Task[] => {
  return tasks.map(task => {
    if (task.id === parentTaskId) {
      // Insert the new task at the beginning of the parent's subtasks
      // This places it "between" the parent and its first child
      return {
        ...task,
        subtasks: [newTask, ...(task.subtasks || [])]
      };
    }
    if (task.subtasks) {
      return {
        ...task,
        subtasks: insertTaskBetweenParentChild(task.subtasks, parentTaskId, newTask)
      };
    }
    return task;
  });
};

// Mock handleAddTaskAfter function that mimics the enhanced version
const handleAddTaskAfter = (
  tasks: Task[],
  afterIdOrPosition: string | InsertionPosition,
  newTask: Task,
  parentId: string | null = null
): Task[] => {
  // Handle InsertionPosition parameter
  if (typeof afterIdOrPosition === 'object') {
    const position = afterIdOrPosition;
    
    switch (position.type) {
      case 'before':
        return insertTaskBefore(tasks, position.targetTaskId, newTask, position.parentId);
      
      case 'after':
        return insertTaskAfter(tasks, position.targetTaskId, newTask, position.parentId);
      
      case 'between_parent_child':
        return insertTaskBetweenParentChild(tasks, position.targetTaskId, newTask);
      
      default:
        console.warn('Unknown insertion type:', position.type);
        return tasks;
    }
  }

  // Legacy string-based handling for backward compatibility
  const afterId = afterIdOrPosition as string;
  
  // Special case: Add at the beginning of the list
  if (afterId === '__FIRST__') {
    if (!parentId) {
      return [newTask, ...tasks];
    } else {
      return tasks.map(task => {
        if (task.id === parentId) {
          return {...task, subtasks: [newTask, ...(task.subtasks || [])]};
        }
        if (task.subtasks) {
          const addAtBeginning = (taskList: Task[]): Task[] => {
            return taskList.map(t => {
              if (t.id === parentId) {
                return {...t, subtasks: [newTask, ...(t.subtasks || [])]};
              }
              if (t.subtasks) {
                return {...t, subtasks: addAtBeginning(t.subtasks)};
              }
              return t;
            });
          };
          return {...task, subtasks: addAtBeginning(task.subtasks)};
        }
        return task;
      });
    }
  }

  // Regular case: Add after specific task
  const addTask = (taskList: Task[]): Task[] => {
      const index = taskList.findIndex(t => t.id === afterId);
      if (index !== -1) {
          const newTasks = [...taskList];
          newTasks.splice(index + 1, 0, newTask);
          return newTasks;
      }
      return taskList.map(t => ({...t, subtasks: addTask(t.subtasks || [])}));
  };

  if (!parentId) {
      return addTask(tasks);
  } else {
      return tasks.map(task => {
          if (task.id === parentId) {
              return {...task, subtasks: addTask(task.subtasks || [])};
          }
          if (task.subtasks) {
              return {...task, subtasks: addTask(task.subtasks)};
          }
          return task;
      });
  }
};

describe('Enhanced handleAddTaskAfter Function', () => {
  const createMockTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
    id,
    title,
    description: `Description for ${title}`,
    content: '',
    status: 'To Do',
    assignees: [],
    subtasks
  });

  const newTask = createMockTask('new-task', 'New Task');

  describe('InsertionPosition parameter support', () => {
    it('should insert task before target task using InsertionPosition', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const task2 = createMockTask('task2', 'Task 2');
      const tasks = [task1, task2];

      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'task2',
        parentId: null,
        level: 0
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('task1');
      expect(result[1].id).toBe('new-task');
      expect(result[2].id).toBe('task2');
    });

    it('should insert task after target task using InsertionPosition', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const task2 = createMockTask('task2', 'Task 2');
      const tasks = [task1, task2];

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('task1');
      expect(result[1].id).toBe('new-task');
      expect(result[2].id).toBe('task2');
    });

    it('should insert task between parent and child using InsertionPosition', () => {
      const subtask1 = createMockTask('subtask1', 'Subtask 1');
      const subtask2 = createMockTask('subtask2', 'Subtask 2');
      const parentTask = createMockTask('parent', 'Parent Task', [subtask1, subtask2]);
      const tasks = [parentTask];

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'parent',
        parentId: null,
        level: 1
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('parent');
      expect(result[0].subtasks).toHaveLength(3);
      expect(result[0].subtasks[0].id).toBe('new-task');
      expect(result[0].subtasks[1].id).toBe('subtask1');
      expect(result[0].subtasks[2].id).toBe('subtask2');
    });
  });

  describe('Backward compatibility with string parameters', () => {
    it('should maintain backward compatibility with string afterId parameter', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const task2 = createMockTask('task2', 'Task 2');
      const tasks = [task1, task2];

      const result = handleAddTaskAfter(tasks, 'task1', newTask);
      
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('task1');
      expect(result[1].id).toBe('new-task');
      expect(result[2].id).toBe('task2');
    });

    it('should handle __FIRST__ special case', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const task2 = createMockTask('task2', 'Task 2');
      const tasks = [task1, task2];

      const result = handleAddTaskAfter(tasks, '__FIRST__', newTask);
      
      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('new-task');
      expect(result[1].id).toBe('task1');
      expect(result[2].id).toBe('task2');
    });
  });

  describe('Nested task insertion', () => {
    it('should insert task before subtask within parent', () => {
      const subtask1 = createMockTask('subtask1', 'Subtask 1');
      const subtask2 = createMockTask('subtask2', 'Subtask 2');
      const parentTask = createMockTask('parent', 'Parent Task', [subtask1, subtask2]);
      const tasks = [parentTask];

      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'subtask2',
        parentId: 'parent',
        level: 1
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result[0].subtasks).toHaveLength(3);
      expect(result[0].subtasks[0].id).toBe('subtask1');
      expect(result[0].subtasks[1].id).toBe('new-task');
      expect(result[0].subtasks[2].id).toBe('subtask2');
    });

    it('should insert task after subtask within parent', () => {
      const subtask1 = createMockTask('subtask1', 'Subtask 1');
      const subtask2 = createMockTask('subtask2', 'Subtask 2');
      const parentTask = createMockTask('parent', 'Parent Task', [subtask1, subtask2]);
      const tasks = [parentTask];

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'subtask1',
        parentId: 'parent',
        level: 1
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result[0].subtasks).toHaveLength(3);
      expect(result[0].subtasks[0].id).toBe('subtask1');
      expect(result[0].subtasks[1].id).toBe('new-task');
      expect(result[0].subtasks[2].id).toBe('subtask2');
    });
  });

  describe('Parent-child relationship maintenance', () => {
    it('should maintain parent-child relationships when inserting between parent and child', () => {
      const deepSubtask = createMockTask('deep-subtask', 'Deep Subtask');
      const subtask1 = createMockTask('subtask1', 'Subtask 1', [deepSubtask]);
      const parentTask = createMockTask('parent', 'Parent Task', [subtask1]);
      const tasks = [parentTask];

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'parent',
        parentId: null,
        level: 1
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      // Verify the structure is maintained
      expect(result[0].subtasks).toHaveLength(2);
      expect(result[0].subtasks[0].id).toBe('new-task');
      expect(result[0].subtasks[1].id).toBe('subtask1');
      expect(result[0].subtasks[1].subtasks).toHaveLength(1);
      expect(result[0].subtasks[1].subtasks[0].id).toBe('deep-subtask');
    });

    it('should properly reorder tasks when inserting at specific positions', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const task2 = createMockTask('task2', 'Task 2');
      const task3 = createMockTask('task3', 'Task 3');
      const tasks = [task1, task2, task3];

      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'task2',
        parentId: null,
        level: 0
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result).toHaveLength(4);
      expect(result.map(t => t.id)).toEqual(['task1', 'new-task', 'task2', 'task3']);
    });
  });

  describe('Error handling', () => {
    it('should handle unknown insertion type gracefully', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const tasks = [task1];

      const position = {
        type: 'unknown_type',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      } as any;

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const result = handleAddTaskAfter(tasks, position, newTask);
      
      expect(result).toEqual(tasks); // Should return original tasks unchanged
      expect(consoleSpy).toHaveBeenCalledWith('Unknown insertion type:', 'unknown_type');
      
      consoleSpy.mockRestore();
    });

    it('should handle non-existent target task gracefully', () => {
      const task1 = createMockTask('task1', 'Task 1');
      const tasks = [task1];

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'non-existent',
        parentId: null,
        level: 0
      };

      const result = handleAddTaskAfter(tasks, position, newTask);
      
      // Should return original tasks unchanged when target not found
      expect(result).toEqual(tasks);
    });
  });
});