import { describe, it, expect } from 'vitest';
import {
  calculateContentMetrics,
  countCharacters,
  countWords,
  estimateTokenCount,
  stripHtmlTags,
  formatContentMetrics
} from '@/lib/utils/contentMetrics';

describe('Content Metrics Utilities', () => {
  describe('stripHtmlTags', () => {
    it('should remove HTML tags from content', () => {
      const htmlContent = '<p>Hello <strong>world</strong>!</p>';
      const result = stripHtmlTags(htmlContent);
      expect(result).toBe('Hello world!');
    });

    it('should handle HTML entities', () => {
      const htmlContent = 'Hello &amp; goodbye &lt;test&gt;';
      const result = stripHtmlTags(htmlContent);
      expect(result).toBe('Hello & goodbye <test>');
    });

    it('should handle empty content', () => {
      expect(stripHtmlTags('')).toBe('');
      expect(stripHtmlTags(null as any)).toBe('');
      expect(stripHtmlTags(undefined as any)).toBe('');
    });
  });

  describe('countCharacters', () => {
    it('should count characters correctly', () => {
      expect(countCharacters('Hello world')).toBe(11);
      expect(countCharacters('<p>Hello world</p>')).toBe(11);
      expect(countCharacters('')).toBe(0);
    });
  });

  describe('countWords', () => {
    it('should count words correctly', () => {
      expect(countWords('Hello world')).toBe(2);
      expect(countWords('Hello   world   test')).toBe(3);
      expect(countWords('<p>Hello <strong>world</strong></p>')).toBe(2);
      expect(countWords('')).toBe(0);
      expect(countWords('   ')).toBe(0);
    });
  });

  describe('estimateTokenCount', () => {
    it('should estimate tokens based on character count', () => {
      // 12 characters / 4 = 3 tokens
      expect(estimateTokenCount('Hello world!')).toBe(3);
      // 20 characters / 4 = 5 tokens
      expect(estimateTokenCount('This is a test text.')).toBe(5);
      expect(estimateTokenCount('')).toBe(0);
    });
  });

  describe('calculateContentMetrics', () => {
    it('should calculate all metrics correctly', () => {
      const content = '<p>Hello <strong>world</strong>! This is a test.</p>';
      const metrics = calculateContentMetrics(content);
      
      expect(metrics.characterCount).toBe(28); // "Hello world! This is a test."
      expect(metrics.wordCount).toBe(6);
      expect(metrics.estimatedTokenCount).toBe(7); // 28/4 = 7
    });

    it('should handle empty content', () => {
      const metrics = calculateContentMetrics('');
      expect(metrics.characterCount).toBe(0);
      expect(metrics.wordCount).toBe(0);
      expect(metrics.estimatedTokenCount).toBe(0);
    });
  });

  describe('formatContentMetrics', () => {
    it('should format metrics in German', () => {
      const metrics = {
        characterCount: 1247,
        wordCount: 186,
        estimatedTokenCount: 312
      };
      
      const formatted = formatContentMetrics(metrics);
      expect(formatted).toBe('1.247 Zeichen | 186 Wörter | ~312 Token');
    });
  });
});