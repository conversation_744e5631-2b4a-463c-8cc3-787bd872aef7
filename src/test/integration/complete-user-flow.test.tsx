import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import App from '@/app/page'

// Mock the AI client
vi.mock('@/lib/ai/gemini', () => ({
  GeminiClient: vi.fn().mockImplementation(() => ({
    generateTasks: vi.fn().mockResolvedValue([
      {
        id: '1',
        title: 'Test Task 1',
        description: 'Description for task 1',
        content: '<p>AI generated content for task 1</p>',
        status: 'todo',
        assignees: [],
        subtasks: []
      },
      {
        id: '2',
        title: 'Test Task 2',
        description: 'Description for task 2',
        content: '<p>AI generated content for task 2</p>',
        status: 'todo',
        assignees: [],
        subtasks: []
      }
    ]),
    generateSubtasks: vi.fn().mockResolvedValue([
      {
        id: '1-1',
        title: 'Subtask 1.1',
        description: 'Subtask description',
        content: '<p>Subtask AI content</p>',
        status: 'todo',
        assignees: [],
        subtasks: []
      }
    ]),
    generateContent: vi.fn().mockResolvedValue({
      content: '<p>Generated AI content</p>',
      success: true
    }),
    generateQuestions: vi.fn().mockResolvedValue([
      'What is the main goal?',
      'What resources do you have?'
    ])
  }))
}))

describe('Complete User Flow Integration Test', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  it('should complete full project creation to export flow', async () => {
    const user = userEvent.setup()
    render(<App />)

    // Step 1: Enter project goal
    const projectInput = screen.getByPlaceholderText(/eine weltreise planen/i)
    await user.type(projectInput, 'Test Project Goal')

    const descriptionInput = screen.getByPlaceholderText(/optionale beschreibung/i)
    await user.type(descriptionInput, 'Test project description')

    // Step 2: Start project
    const startButtons = screen.getAllByRole('button', { name: /projekt starten/i })
    await user.click(startButtons[0])

    // Wait for AI to generate tasks
    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument()
      expect(screen.getByText('Test Task 2')).toBeInTheDocument()
    }, { timeout: 5000 })

    // Step 3: Break down a task
    const breakdownButtons = screen.getAllByRole('button', { name: /aufschlüsseln/i })
    await user.click(breakdownButtons[0])

    // Wait for subtasks to be generated
    await waitFor(() => {
      expect(screen.getByText('Subtask 1.1')).toBeInTheDocument()
    }, { timeout: 5000 })

    // Step 4: Edit a task - click on the task title to edit
    const taskTitle = screen.getByText('Test Task 1')
    await user.click(taskTitle)

    const titleInput = screen.getByDisplayValue('Test Task 1')
    await user.clear(titleInput)
    await user.type(titleInput, 'Updated Task Title')

    // Save the edit
    await user.keyboard('{Enter}')

    await waitFor(() => {
      expect(screen.getByText('Updated Task Title')).toBeInTheDocument()
    })

    // Step 5: Use solve modal
    const solveButtons = screen.getAllByRole('button', { name: /lösen/i })
    await user.click(solveButtons[0])

    // Modal should open
    await waitFor(() => {
      expect(screen.getByText(/autopilot/i)).toBeInTheDocument()
      expect(screen.getByText(/co-pilot/i)).toBeInTheDocument()
    })

    // Choose Autopilot
    const autopilotButton = screen.getByText(/autopilot/i)
    await user.click(autopilotButton)

    // Add additional context and generate
    const contextInput = screen.getByPlaceholderText(/zusätzlicher kontext/i)
    await user.type(contextInput, 'Additional context for AI')

    const generateButton = screen.getByRole('button', { name: /generieren/i })
    await user.click(generateButton)

    // Wait for content generation
    await waitFor(() => {
      expect(screen.getByText(/schließen/i)).toBeInTheDocument()
    }, { timeout: 5000 })

    // Close modal
    const closeButton = screen.getByRole('button', { name: /schließen/i })
    await user.click(closeButton)

    // Step 6: Test export functionality - PDF export should be visible when tasks exist
    const pdfExportButton = screen.getByRole('button', { name: /pdf/i })
    await user.click(pdfExportButton)

    // Verify export was attempted
    await waitFor(() => {
      // The export should have been called (mocked)
      expect(global.pdfMake.createPdf).toHaveBeenCalled()
    })
  })

  it('should handle AI API errors gracefully', async () => {
    const user = userEvent.setup()
    
    // Mock API failure
    const mockGeminiClient = await import('@/lib/ai/gemini')
    vi.mocked(mockGeminiClient.GeminiClient).mockImplementation(() => ({
      generateTasks: vi.fn().mockRejectedValue(new Error('API Error')),
      generateSubtasks: vi.fn().mockRejectedValue(new Error('API Error')),
      generateContent: vi.fn().mockRejectedValue(new Error('API Error')),
      generateQuestions: vi.fn().mockRejectedValue(new Error('API Error'))
    }))

    render(<App />)

    // Try to start project
    const projectInput = screen.getByPlaceholderText(/eine weltreise planen/i)
    await user.type(projectInput, 'Test Project')

    const startButtons = screen.getAllByRole('button', { name: /projekt starten/i })
    await user.click(startButtons[0])

    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/fehler/i)).toBeInTheDocument()
    }, { timeout: 5000 })
  })

  it('should persist theme preferences', async () => {
    const user = userEvent.setup()
    render(<App />)

    // Toggle theme
    const themeToggles = screen.getAllByLabelText(/toggle dark mode/i)
    await user.click(themeToggles[0])

    // Check localStorage was called
    expect(localStorage.setItem).toHaveBeenCalledWith('theme', expect.any(String))
  })
})