import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import App from '@/app/page'

// Mock console.error to avoid noise in tests
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

describe('Error Handling and Recovery Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockConsoleError.mockClear()
    localStorage.clear()
  })

  it('should handle API key missing error', async () => {
    // Remove API key
    delete process.env.NEXT_PUBLIC_GEMINI_API_KEY

    const user = userEvent.setup()
    render(<App />)

    const projectInput = screen.getByPlaceholderText(/eine weltreise planen/i)
    await user.type(projectInput, 'Test Project')

    const startButtons = screen.getAllByRole('button', { name: /projekt starten/i })
    await user.click(startButtons[0])

    await waitFor(() => {
      expect(screen.getByText(/fehler/i)).toBeInTheDocument()
    }, { timeout: 5000 })

    // Restore API key
    process.env.NEXT_PUBLIC_GEMINI_API_KEY = 'test-api-key'
  })

  it('should handle network connectivity issues', async () => {
    // Mock network error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))

    const user = userEvent.setup()
    render(<App />)

    const projectInput = screen.getByPlaceholderText(/eine weltreise planen/i)
    await user.type(projectInput, 'Test Project')

    const startButtons = screen.getAllByRole('button', { name: /projekt starten/i })
    await user.click(startButtons[0])

    await waitFor(() => {
      expect(screen.getByText(/fehler/i)).toBeInTheDocument()
    }, { timeout: 5000 })
  })

  it('should handle localStorage errors', async () => {
    // Mock localStorage error
    const mockSetItem = vi.fn().mockImplementation(() => {
      throw new Error('Storage quota exceeded')
    })
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: mockSetItem,
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
    })

    const user = userEvent.setup()
    render(<App />)

    // Try to change theme (which uses localStorage)
    const themeToggles = screen.getAllByLabelText(/toggle dark mode/i)
    await user.click(themeToggles[0])

    // Should not crash, but may show a warning
    expect(mockSetItem).toHaveBeenCalled()
  })

  it('should recover from errors with retry mechanism', async () => {
    let callCount = 0
    global.fetch = vi.fn().mockImplementation(() => {
      callCount++
      if (callCount === 1) {
        return Promise.reject(new Error('Temporary error'))
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({
          candidates: [{
            content: {
              parts: [{
                text: JSON.stringify([{
                  title: 'Recovered Task',
                  description: 'Task after retry',
                  content: '<p>Recovered content</p>'
                }])
              }]
            }
          }]
        })
      })
    })

    const user = userEvent.setup()
    render(<App />)

    const projectInput = screen.getByPlaceholderText(/eine weltreise planen/i)
    await user.type(projectInput, 'Test Project')

    const startButtons = screen.getAllByRole('button', { name: /projekt starten/i })
    await user.click(startButtons[0])

    // Should eventually succeed after retry
    await waitFor(() => {
      expect(screen.getByText('Recovered Task')).toBeInTheDocument()
    }, { timeout: 10000 })

    expect(callCount).toBeGreaterThan(1)
  })
})