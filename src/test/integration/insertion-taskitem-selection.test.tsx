/**
 * Integration tests for insertion with TaskItem selection functionality
 * Tests that tasks can be created from AI content selection with proper insertion positioning
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TaskItem } from '@/components/TaskItem';
import type { Task, InsertionPosition, InsertionState } from '@/lib/types';

// Mock dependencies
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: () => false
}));

vi.mock('@/lib/ai', () => ({
  geminiClient: {
    generateSubtasks: vi.fn(),
    generateTaskContent: vi.fn(),
    elaborateContent: vi.fn()
  }
}));

vi.mock('@/hooks/useAutoGrowTextarea', () => ({
  useAutoGrowTextarea: () => ({ current: null })
}));

vi.mock('@/hooks/useInsertionZones', () => ({
  useInsertionZones: () => ({
    zones: [],
    isCalculating: false,
    findZoneAtPoint: vi.fn(),
    getZonesByType: vi.fn(() => []),
    forceRecalculate: vi.fn()
  })
}));

describe('TaskItem Selection Integration with Insertion', () => {
  let mockTask: Task;
  let mockInsertionState: InsertionState;
  let mockOnUpdateTask: ReturnType<typeof vi.fn>;
  let mockOnOpenSolveModal: ReturnType<typeof vi.fn>;
  let mockOnAddTask: ReturnType<typeof vi.fn>;
  let mockOnAddTaskAfter: ReturnType<typeof vi.fn>;
  let mockOnDeleteTask: ReturnType<typeof vi.fn>;
  let mockOnInsertTask: ReturnType<typeof vi.fn>;
  let mockOnInsertionStateChange: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockTask = {
      id: 'test-task-1',
      title: 'Test Task',
      description: 'Test task description',
      content: '<p>This is some AI-generated content that can be selected.</p><p>More content here for testing selection.</p>',
      status: 'To Do',
      assignees: [],
      subtasks: []
    };

    mockInsertionState = {
      activeInsertionPoint: null,
      hoveredZone: null,
      keyboardMode: false,
      insertionHistory: [],
      lastCalculatedZones: new Map(),
      zoneCalculationCache: new Map()
    };

    mockOnUpdateTask = vi.fn();
    mockOnOpenSolveModal = vi.fn();
    mockOnAddTask = vi.fn();
    mockOnAddTaskAfter = vi.fn();
    mockOnDeleteTask = vi.fn();
    mockOnInsertTask = vi.fn();
    mockOnInsertionStateChange = vi.fn();

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should support creating tasks from AI content selection with insertion positioning', async () => {
    const user = userEvent.setup();

    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify the task content is rendered
    expect(screen.getByText('This is some AI-generated content that can be selected.')).toBeInTheDocument();

    // Test that the component supports insertion functionality
    expect(mockOnInsertTask).toBeDefined();
    expect(mockOnInsertionStateChange).toBeDefined();
  });

  it('should handle insertion position updates when creating tasks from selection', () => {
    const mockPosition: InsertionPosition = {
      type: 'after',
      targetTaskId: mockTask.id,
      parentId: null,
      level: 0
    };

    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="always"
        insertionState={{
          ...mockInsertionState,
          activeInsertionPoint: mockPosition
        }}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify that insertion state is properly handled
    expect(screen.getByTestId(mockTask.id)).toBeInTheDocument();
  });

  it('should support enhanced onAddTaskAfter with insertion positions', () => {
    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify that the enhanced onAddTaskAfter function is available
    expect(mockOnAddTaskAfter).toBeDefined();
    expect(typeof mockOnAddTaskAfter).toBe('function');
  });

  it('should handle insertion zone interactions', () => {
    const mockZones = [
      {
        id: 'after-test-task-1',
        type: 'after' as const,
        bounds: new DOMRect(0, 100, 300, 20),
        targetTaskId: mockTask.id,
        parentId: null,
        level: 0
      }
    ];

    // Mock the useInsertionZones hook to return test zones
    vi.mocked(require('@/hooks/useInsertionZones').useInsertionZones).mockReturnValue({
      zones: mockZones,
      isCalculating: false,
      findZoneAtPoint: vi.fn((x, y) => {
        // Simulate finding a zone at the given coordinates
        if (x >= 0 && x <= 300 && y >= 100 && y <= 120) {
          return mockZones[0];
        }
        return null;
      }),
      getZonesByType: vi.fn((type) => mockZones.filter(z => z.type === type)),
      forceRecalculate: vi.fn()
    });

    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify the component renders with insertion zones
    expect(screen.getByTestId(mockTask.id)).toBeInTheDocument();
  });

  it('should maintain insertion state consistency during task updates', () => {
    const updatedTask = {
      ...mockTask,
      title: 'Updated Task Title',
      content: '<p>Updated AI content with new selection possibilities.</p>'
    };

    const { rerender } = render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Update the task
    rerender(
      <TaskItem
        task={updatedTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify the updated content is rendered
    expect(screen.getByText('Updated AI content with new selection possibilities.')).toBeInTheDocument();
  });

  it('should handle keyboard insertion mode properly', () => {
    const keyboardInsertionState = {
      ...mockInsertionState,
      keyboardMode: true
    };

    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="keyboard"
        insertionState={keyboardInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify the component handles keyboard mode
    expect(screen.getByTestId(mockTask.id)).toBeInTheDocument();
  });

  it('should support different insertion types for selection-based tasks', () => {
    const insertionTypes: Array<'before' | 'after' | 'between_parent_child'> = ['before', 'after', 'between_parent_child'];

    insertionTypes.forEach(type => {
      const position: InsertionPosition = {
        type,
        targetTaskId: mockTask.id,
        parentId: type === 'between_parent_child' ? mockTask.id : null,
        level: type === 'between_parent_child' ? 1 : 0
      };

      render(
        <TaskItem
          task={mockTask}
          level={0}
          parentId={null}
          onUpdateTask={mockOnUpdateTask}
          onOpenSolveModal={mockOnOpenSolveModal}
          onAddTask={mockOnAddTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          onDeleteTask={mockOnDeleteTask}
          numbering="1"
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
          insertionMode="always"
          insertionState={{
            ...mockInsertionState,
            activeInsertionPoint: position
          }}
          onInsertionStateChange={mockOnInsertionStateChange}
        />
      );

      // Verify the component renders with the specific insertion type
      expect(screen.getByTestId(mockTask.id)).toBeInTheDocument();
    });
  });

  it('should handle loading states during insertion operations', () => {
    const insertionLoading = {
      [`after-${mockTask.id}`]: true
    };

    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
        insertionLoading={insertionLoading}
      />
    );

    // Verify the component handles loading states
    expect(screen.getByTestId(mockTask.id)).toBeInTheDocument();
  });

  it('should integrate with solve modal for selection-based task creation', () => {
    render(
      <TaskItem
        task={mockTask}
        level={0}
        parentId={null}
        onUpdateTask={mockOnUpdateTask}
        onOpenSolveModal={mockOnOpenSolveModal}
        onAddTask={mockOnAddTask}
        onAddTaskAfter={mockOnAddTaskAfter}
        onDeleteTask={mockOnDeleteTask}
        numbering="1"
        onInsertTask={mockOnInsertTask}
        showInsertionIndicators={true}
        insertionMode="hover"
        insertionState={mockInsertionState}
        onInsertionStateChange={mockOnInsertionStateChange}
      />
    );

    // Verify that the solve modal integration is available
    expect(mockOnOpenSolveModal).toBeDefined();
    expect(typeof mockOnOpenSolveModal).toBe('function');
  });
});