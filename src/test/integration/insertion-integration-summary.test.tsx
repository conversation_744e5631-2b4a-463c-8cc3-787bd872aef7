/**
 * Summary integration test for manual task insertion with existing features
 * Tests the core integration requirements without complex UI testing
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Task, InsertionPosition, ContextStrategies } from '@/lib/types';
import { InsertionIntegration, TaskOperationIntegration, ExportIntegration } from '@/lib/utils/insertionIntegration';

// Mock AI dependencies
vi.mock('@/lib/ai', () => ({
  geminiClient: {
    generateSubtasks: vi.fn(),
    generateTaskContent: vi.fn(),
    elaborateContent: vi.fn()
  },
  contextBuilder: {
    buildContextForAI: vi.fn(() => 'Mock AI context'),
    buildBreakdownContext: vi.fn(() => 'Mock breakdown context'),
    buildCopilotContext: vi.fn(() => 'Mock copilot context'),
    findTask: vi.fn(),
    findTaskPath: vi.fn(() => [])
  }
}));

describe('Insertion Integration Summary', () => {
  let mockTasks: Task[];
  let mockInsertionHistory: InsertionPosition[];

  beforeEach(() => {
    mockTasks = [
      {
        id: 'task-1',
        title: 'Main Task',
        description: 'Main task description',
        content: 'Main task AI content',
        status: 'To Do',
        assignees: [],
        subtasks: [
          {
            id: 'task-1-1',
            title: 'Subtask 1',
            description: 'Subtask description',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          }
        ]
      },
      {
        id: 'task-2',
        title: 'Second Task',
        description: 'Second task description',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ];

    mockInsertionHistory = [
      {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      }
    ];

    vi.clearAllMocks();
  });

  describe('Requirement 5.1: AI Task Generation Integration', () => {
    it('should integrate insertion with AI task generation', async () => {
      const { geminiClient } = await import('@/lib/ai');
      (geminiClient.generateTaskContent as any).mockResolvedValue({
        content: '<p>AI generated task content</p>',
        error: null
      });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true,
          contextStrategies: { strategy1: true, strategy2: true, strategy3: false },
          mainProject: 'Test Project'
        }
      );

      expect(result.success).toBe(true);
      expect(result.newTaskId).toBeDefined();
      expect(geminiClient.generateTaskContent).toHaveBeenCalled();
    });

    it('should create tasks from AI content selection', async () => {
      const { geminiClient } = await import('@/lib/ai');
      (geminiClient.elaborateContent as any).mockResolvedValue({
        content: '<p>Elaborated content from selection</p>',
        error: null
      });

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1
      };

      const result = await InsertionIntegration.createTaskFromSelection(
        'Selected AI content text',
        'task-1',
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true
        }
      );

      expect(result.success).toBe(true);
      expect(result.newTaskId).toBeDefined();
      expect(geminiClient.elaborateContent).toHaveBeenCalled();
    });
  });

  describe('Requirement 5.2: Task Editing and Deletion Compatibility', () => {
    it('should handle task editing with insertion compatibility', () => {
      const editedTask: Task = {
        ...mockTasks[0],
        title: 'Updated Main Task',
        description: 'Updated description'
      };

      const { updatedTasks, updatedHistory } = InsertionIntegration.handleTaskEditingWithInsertion(
        editedTask,
        mockTasks,
        mockInsertionHistory
      );

      expect(updatedTasks).toBeDefined();
      expect(updatedTasks[0].title).toBe('Updated Main Task');
      expect(updatedHistory).toBeDefined();
      expect(updatedHistory.length).toBe(mockInsertionHistory.length);
    });

    it('should handle task deletion with insertion state cleanup', () => {
      const { updatedTasks, updatedHistory } = TaskOperationIntegration.handleTaskDeletionWithInsertion(
        'task-1',
        mockTasks,
        mockInsertionHistory
      );

      expect(updatedTasks).toBeDefined();
      expect(updatedTasks.length).toBe(1); // One task should remain
      expect(updatedTasks[0].id).toBe('task-2');
      expect(updatedHistory).toBeDefined();
      expect(updatedHistory.length).toBe(0); // History should be cleaned up
    });

    it('should validate task relationships after insertion', () => {
      const insertedTaskId = 'new-task-id';
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1
      };

      // Add the inserted task to the mock data
      const tasksWithInserted = [...mockTasks];
      tasksWithInserted[0].subtasks.push({
        id: insertedTaskId,
        title: 'Inserted Task',
        description: 'Inserted via manual insertion',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      });

      const validation = InsertionIntegration.validateTaskRelationships(
        tasksWithInserted,
        insertedTaskId,
        position
      );

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });
  });

  describe('Requirement 5.3: Export Function Integration', () => {
    it('should enhance export data with insertion metadata', () => {
      const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
        mockTasks,
        mockInsertionHistory,
        true // Include metadata
      );

      expect(enhancedTasks).toBeDefined();
      expect(enhancedTasks.length).toBe(mockTasks.length);
      expect(enhancedTasks[0]).toHaveProperty('exportMetadata');
      expect(enhancedTasks[0].exportMetadata).toHaveProperty('wasManuallyInserted');
      expect(enhancedTasks[0].exportMetadata).toHaveProperty('insertionCount');
    });

    it('should validate task structure for export', () => {
      const validation = ExportIntegration.validateTaskStructureForExport(mockTasks);

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should maintain proper task relationships in export', () => {
      const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
        mockTasks,
        mockInsertionHistory,
        false // Don't include metadata
      );

      expect(enhancedTasks).toEqual(mockTasks); // Should be unchanged without metadata
      expect(enhancedTasks[0].subtasks).toHaveLength(1);
      expect(enhancedTasks[0].subtasks[0].id).toBe('task-1-1');
    });
  });

  describe('Enhanced Task Breakdown Integration', () => {
    it('should perform enhanced breakdown with insertion context', async () => {
      const { geminiClient } = await import('@/lib/ai');
      const mockSubtasks = [
        { title: 'Breakdown Subtask 1', description: 'First breakdown subtask' },
        { title: 'Breakdown Subtask 2', description: 'Second breakdown subtask' }
      ];

      (geminiClient.generateSubtasks as any).mockResolvedValue({
        tasks: mockSubtasks,
        error: null
      });

      const result = await TaskOperationIntegration.enhancedBreakdownTask(
        'task-1',
        mockTasks,
        mockInsertionHistory,
        {
          useContextFromParent: true,
          contextStrategies: { strategy1: true, strategy2: true, strategy3: false },
          mainProject: 'Test Project',
          mainProjectDescription: 'Test Description'
        }
      );

      expect(result.success).toBe(true);
      expect(result.newSubtasks).toHaveLength(2);
      expect(result.newSubtasks[0].title).toBe('Breakdown Subtask 1');
      expect(result.newSubtasks[1].title).toBe('Breakdown Subtask 2');
      expect(geminiClient.generateSubtasks).toHaveBeenCalledWith(
        'Main Task',
        'Main task description',
        'Main task AI content'
      );
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid insertion positions gracefully', async () => {
      const invalidPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'non-existent-task',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        invalidPosition,
        mockTasks,
        {
          validateBeforeInsertion: true
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid insertion position');
    });

    it('should handle AI service failures during insertion', async () => {
      const { geminiClient } = await import('@/lib/ai');
      (geminiClient.generateTaskContent as any).mockRejectedValue(
        new Error('AI service connection failed')
      );

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true
        }
      );

      expect(result.success).toBe(true); // Should still create manual task
      expect(result.newTaskId).toBeDefined();
    });

    it('should handle missing task context gracefully', () => {
      const emptyTasks: Task[] = [];
      const validation = InsertionIntegration.validateTaskRelationships(
        emptyTasks,
        'non-existent-task',
        {
          type: 'after',
          targetTaskId: 'non-existent-task',
          parentId: null,
          level: 0
        }
      );

      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large task trees efficiently', () => {
      // Create a large task tree
      const largeTasks: Task[] = Array.from({ length: 100 }, (_, i) => ({
        id: `task-${i}`,
        title: `Task ${i}`,
        description: `Description ${i}`,
        content: '',
        status: 'To Do' as const,
        assignees: [],
        subtasks: Array.from({ length: 10 }, (_, j) => ({
          id: `task-${i}-${j}`,
          title: `Subtask ${i}-${j}`,
          description: `Subtask description ${i}-${j}`,
          content: '',
          status: 'To Do' as const,
          assignees: [],
          subtasks: []
        }))
      }));

      const startTime = performance.now();
      
      const validation = ExportIntegration.validateTaskStructureForExport(largeTasks);
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;

      expect(validation.isValid).toBe(true);
      expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should maintain insertion history integrity', () => {
      const largeHistory: InsertionPosition[] = Array.from({ length: 50 }, (_, i) => ({
        type: 'after' as const,
        targetTaskId: `task-${i}`,
        parentId: null,
        level: 0
      }));

      const cleanedHistory = InsertionIntegration.cleanupInsertionStateOnDeletion(
        'task-25',
        largeHistory
      );

      expect(cleanedHistory.length).toBe(49); // One item should be removed
      expect(cleanedHistory.some(pos => pos.targetTaskId === 'task-25')).toBe(false);
    });
  });

  describe('Integration Summary', () => {
    it('should demonstrate complete integration workflow', async () => {
      // This test demonstrates the complete integration workflow
      const { geminiClient } = await import('@/lib/ai');
      
      // 1. AI Task Generation Integration
      (geminiClient.generateTaskContent as any).mockResolvedValue({
        content: '<p>AI generated content</p>',
        error: null
      });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const aiTaskResult = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        { generateContent: true, enableAIGeneration: true }
      );

      expect(aiTaskResult.success).toBe(true);

      // 2. Task Editing Compatibility
      const editedTask: Task = {
        ...mockTasks[0],
        title: 'Updated Task'
      };

      const { updatedTasks } = InsertionIntegration.handleTaskEditingWithInsertion(
        editedTask,
        mockTasks,
        mockInsertionHistory
      );

      expect(updatedTasks[0].title).toBe('Updated Task');

      // 3. Export Integration
      const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
        updatedTasks,
        mockInsertionHistory,
        true
      );

      expect(enhancedTasks[0]).toHaveProperty('exportMetadata');

      // 4. Validation
      const validation = ExportIntegration.validateTaskStructureForExport(enhancedTasks);
      expect(validation.isValid).toBe(true);

      // Integration is complete and working
      expect(true).toBe(true);
    });
  });
});