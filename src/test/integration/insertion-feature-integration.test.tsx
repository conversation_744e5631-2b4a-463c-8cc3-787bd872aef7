/**
 * Integration tests for manual task insertion with existing features
 * Tests Requirements 5.1, 5.2, 5.3, 5.5
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import type { Task, InsertionPosition, ContextStrategies } from '@/lib/types';
import { InsertionIntegration, TaskOperationIntegration, ExportIntegration } from '@/lib/utils/insertionIntegration';
import { geminiClient } from '@/lib/ai';

// Mock the AI client
vi.mock('@/lib/ai', () => ({
  geminiClient: {
    generateSubtasks: vi.fn(),
    generateTaskContent: vi.fn(),
    elaborateContent: vi.fn()
  },
  contextBuilder: {
    buildContextForAI: vi.fn(() => 'Mock context for AI'),
    buildBreakdownContext: vi.fn(() => 'Mock breakdown context'),
    buildCopilotContext: vi.fn(() => 'Mock copilot context'),
    findTask: vi.fn(),
    findTaskPath: vi.fn(() => [])
  }
}));

// Mock toast notifications
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

describe('Insertion Feature Integration', () => {
  let mockTasks: Task[];
  let mockInsertionHistory: InsertionPosition[];
  let mockContextStrategies: ContextStrategies;

  beforeEach(() => {
    mockTasks = [
      {
        id: 'task-1',
        title: 'Main Task',
        description: 'Main task description',
        content: 'Main task content',
        status: 'To Do',
        assignees: [],
        subtasks: [
          {
            id: 'task-1-1',
            title: 'Subtask 1',
            description: 'Subtask description',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          }
        ]
      },
      {
        id: 'task-2',
        title: 'Second Task',
        description: 'Second task description',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ];

    mockInsertionHistory = [
      {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      }
    ];

    mockContextStrategies = {
      strategy1: true,
      strategy2: true,
      strategy3: false
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('AI Task Generation Integration', () => {
    it('should create task with AI content at insertion position', async () => {
      // Requirement 5.1: Insertion works seamlessly with AI task generation
      const mockAIContent = '<p>AI generated content for the task</p>';
      (geminiClient.generateTaskContent as any).mockResolvedValue({
        content: mockAIContent,
        error: null
      });

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        {
          generateContent: true,
          useContextFromParent: true,
          contextStrategies: mockContextStrategies,
          mainProject: 'Test Project',
          mainProjectDescription: 'Test Description',
          enableAIGeneration: true
        }
      );

      expect(result.success).toBe(true);
      expect(result.newTaskId).toBeDefined();
      expect(result.position).toEqual(position);
      expect(geminiClient.generateTaskContent).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String),
        'Mock context for AI',
        expect.stringContaining('nach "Main Task"')
      );
    });

    it('should handle AI generation failure gracefully', async () => {
      (geminiClient.generateTaskContent as any).mockResolvedValue({
        content: '',
        error: 'AI service unavailable'
      });

      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'task-2',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true
        }
      );

      expect(result.success).toBe(true); // Should still succeed with manual task
      expect(result.newTaskId).toBeDefined();
    });

    it('should create task from AI content selection', async () => {
      // Requirement 5.5: Add insertion support for tasks created from AI content selection
      const selectionText = 'Selected text from AI content';
      const mockElaboratedContent = '<p>Elaborated content based on selection</p>';
      
      (geminiClient.elaborateContent as any).mockResolvedValue({
        content: mockElaboratedContent,
        error: null
      });

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1
      };

      const result = await InsertionIntegration.createTaskFromSelection(
        selectionText,
        'task-1',
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true,
          contextStrategies: mockContextStrategies,
          mainProject: 'Test Project'
        }
      );

      expect(result.success).toBe(true);
      expect(result.newTaskId).toBeDefined();
      expect(geminiClient.elaborateContent).toHaveBeenCalledWith(
        selectionText,
        'Mock context for AI',
        selectionText
      );
    });
  });

  describe('Task Editing and Deletion Integration', () => {
    it('should handle task editing with insertion compatibility', () => {
      // Requirement 5.2: Test insertion compatibility with task editing
      const editedTask: Task = {
        ...mockTasks[0],
        title: 'Updated Main Task',
        description: 'Updated description'
      };

      const { updatedTasks, updatedHistory } = InsertionIntegration.handleTaskEditingWithInsertion(
        editedTask,
        mockTasks,
        mockInsertionHistory
      );

      expect(updatedTasks).toBeDefined();
      expect(updatedTasks[0].title).toBe('Updated Main Task');
      expect(updatedHistory).toBeDefined();
      expect(updatedHistory.length).toBe(mockInsertionHistory.length);
    });

    it('should handle task deletion with insertion state cleanup', () => {
      // Requirement 5.2: Test insertion compatibility with task deletion
      const { updatedTasks, updatedHistory } = TaskOperationIntegration.handleTaskDeletionWithInsertion(
        'task-1',
        mockTasks,
        mockInsertionHistory
      );

      expect(updatedTasks).toBeDefined();
      expect(updatedTasks.length).toBe(1); // One task should remain
      expect(updatedTasks[0].id).toBe('task-2');
      expect(updatedHistory).toBeDefined();
      expect(updatedHistory.length).toBe(0); // History should be cleaned up
    });

    it('should validate task relationships after insertion', () => {
      const insertedTaskId = 'new-task-id';
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1
      };

      // Add the inserted task to the mock data
      const tasksWithInserted = [...mockTasks];
      tasksWithInserted[0].subtasks.push({
        id: insertedTaskId,
        title: 'Inserted Task',
        description: 'Inserted via manual insertion',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      });

      const validation = InsertionIntegration.validateTaskRelationships(
        tasksWithInserted,
        insertedTaskId,
        position
      );

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });
  });

  describe('Enhanced Task Breakdown Integration', () => {
    it('should perform enhanced breakdown with insertion context', async () => {
      const mockSubtasks = [
        { title: 'Breakdown Subtask 1', description: 'First breakdown subtask' },
        { title: 'Breakdown Subtask 2', description: 'Second breakdown subtask' }
      ];

      (geminiClient.generateSubtasks as any).mockResolvedValue({
        tasks: mockSubtasks,
        error: null
      });

      const result = await TaskOperationIntegration.enhancedBreakdownTask(
        'task-1',
        mockTasks,
        mockInsertionHistory,
        {
          useContextFromParent: true,
          contextStrategies: mockContextStrategies,
          mainProject: 'Test Project',
          mainProjectDescription: 'Test Description'
        }
      );

      expect(result.success).toBe(true);
      expect(result.newSubtasks).toHaveLength(2);
      expect(result.newSubtasks[0].title).toBe('Breakdown Subtask 1');
      expect(result.newSubtasks[1].title).toBe('Breakdown Subtask 2');
      expect(geminiClient.generateSubtasks).toHaveBeenCalledWith(
        'Main Task',
        'Main task description',
        'Main task content'
      );
    });

    it('should handle breakdown failure with fallback', async () => {
      (geminiClient.generateSubtasks as any).mockResolvedValue({
        tasks: [],
        error: 'AI breakdown failed'
      });

      const result = await TaskOperationIntegration.enhancedBreakdownTask(
        'task-1',
        mockTasks,
        mockInsertionHistory
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('AI breakdown failed');
      expect(result.newSubtasks).toHaveLength(0);
    });
  });

  describe('Export Integration', () => {
    it('should enhance export data with insertion metadata', () => {
      // Requirement 5.3: Verify insertion maintains proper task relationships for export functions
      const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
        mockTasks,
        mockInsertionHistory,
        true // Include metadata
      );

      expect(enhancedTasks).toBeDefined();
      expect(enhancedTasks.length).toBe(mockTasks.length);
      expect(enhancedTasks[0]).toHaveProperty('exportMetadata');
      expect(enhancedTasks[0].exportMetadata).toHaveProperty('wasManuallyInserted');
      expect(enhancedTasks[0].exportMetadata).toHaveProperty('insertionCount');
    });

    it('should validate task structure for export', () => {
      const validation = ExportIntegration.validateTaskStructureForExport(mockTasks);

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should detect invalid task structure', () => {
      const invalidTasks: Task[] = [
        {
          id: '',
          title: '',
          description: 'Task with missing ID and title',
          content: '',
          status: 'To Do',
          assignees: [],
          subtasks: []
        }
      ];

      const validation = ExportIntegration.validateTaskStructureForExport(invalidTasks);

      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
      expect(validation.issues.some(issue => issue.includes('missing ID'))).toBe(true);
      expect(validation.issues.some(issue => issue.includes('missing title'))).toBe(true);
    });

    it('should maintain task hierarchy in export data', () => {
      const enhancedTasks = ExportIntegration.enhanceExportWithInsertionData(
        mockTasks,
        mockInsertionHistory,
        false // Don't include metadata
      );

      expect(enhancedTasks).toEqual(mockTasks); // Should be unchanged without metadata
      expect(enhancedTasks[0].subtasks).toHaveLength(1);
      expect(enhancedTasks[0].subtasks[0].id).toBe('task-1-1');
    });
  });

  describe('Integration Error Handling', () => {
    it('should handle invalid insertion positions gracefully', async () => {
      const invalidPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'non-existent-task',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        invalidPosition,
        mockTasks,
        {
          validateBeforeInsertion: true
        }
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid insertion position');
    });

    it('should handle AI service failures during insertion', async () => {
      (geminiClient.generateTaskContent as any).mockRejectedValue(
        new Error('AI service connection failed')
      );

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      const result = await InsertionIntegration.createTaskWithAI(
        position,
        mockTasks,
        {
          generateContent: true,
          enableAIGeneration: true
        }
      );

      expect(result.success).toBe(true); // Should still create manual task
      expect(result.newTaskId).toBeDefined();
    });

    it('should handle missing task context gracefully', () => {
      const emptyTasks: Task[] = [];
      const validation = InsertionIntegration.validateTaskRelationships(
        emptyTasks,
        'non-existent-task',
        {
          type: 'after',
          targetTaskId: 'non-existent-task',
          parentId: null,
          level: 0
        }
      );

      expect(validation.isValid).toBe(false);
      expect(validation.issues.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Compatibility', () => {
    it('should handle large task trees efficiently', async () => {
      // Create a large task tree
      const largeTasks: Task[] = Array.from({ length: 100 }, (_, i) => ({
        id: `task-${i}`,
        title: `Task ${i}`,
        description: `Description ${i}`,
        content: '',
        status: 'To Do' as const,
        assignees: [],
        subtasks: Array.from({ length: 10 }, (_, j) => ({
          id: `task-${i}-${j}`,
          title: `Subtask ${i}-${j}`,
          description: `Subtask description ${i}-${j}`,
          content: '',
          status: 'To Do' as const,
          assignees: [],
          subtasks: []
        }))
      }));

      const startTime = performance.now();
      
      const validation = ExportIntegration.validateTaskStructureForExport(largeTasks);
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;

      expect(validation.isValid).toBe(true);
      expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should maintain insertion history integrity', () => {
      const largeHistory: InsertionPosition[] = Array.from({ length: 50 }, (_, i) => ({
        type: 'after' as const,
        targetTaskId: `task-${i}`,
        parentId: null,
        level: 0
      }));

      const cleanedHistory = InsertionIntegration.cleanupInsertionStateOnDeletion(
        'task-25',
        largeHistory
      );

      expect(cleanedHistory.length).toBe(49); // One item should be removed
      expect(cleanedHistory.some(pos => pos.targetTaskId === 'task-25')).toBe(false);
    });
  });
});