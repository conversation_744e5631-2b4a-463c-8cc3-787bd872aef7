/**
 * Complete PWA Integration Test
 * 
 * Tests the complete integration of all PWA components with the existing application,
 * ensuring seamless transitions between online and offline modes and verifying
 * complete user workflows from project creation to export.
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Import components and hooks
import { usePWAIntegration } from '@/hooks/usePWAIntegration';
import { pwaIntegrationManager } from '@/lib/pwa/integrationManager';
import { serviceWorkerManager } from '@/lib/pwa/serviceWorker';
import { indexedDBManager } from '@/lib/storage/indexeddb';
import { localStorageManager } from '@/lib/storage/localStorageManager';
import { offlineAwareAIClient } from '@/lib/ai/offlineAwareClient';

// Import test utilities
import { createMockProject, createMockTask } from '@/test/utils/mobile-simulation';

// Mock external dependencies
vi.mock('@/lib/pwa/serviceWorker');
vi.mock('@/lib/storage/indexeddb');
vi.mock('@/lib/storage/localStorageManager');
vi.mock('@/lib/ai/offlineAwareClient');

describe('PWA Complete Integration', () => {
  let mockNavigator: any;
  let originalNavigator: any;

  beforeAll(() => {
    // Setup global mocks
    originalNavigator = global.navigator;
    mockNavigator = {
      onLine: true,
      serviceWorker: {
        register: vi.fn(),
        addEventListener: vi.fn(),
        ready: Promise.resolve({
          active: { postMessage: vi.fn() }
        })
      }
    };
    global.navigator = mockNavigator;

    // Mock IndexedDB
    global.indexedDB = {
      open: vi.fn(() => ({
        result: {
          createObjectStore: vi.fn(),
          transaction: vi.fn(() => ({
            objectStore: vi.fn(() => ({
              add: vi.fn(),
              get: vi.fn(),
              put: vi.fn(),
              delete: vi.fn(),
              getAll: vi.fn(() => ({ onsuccess: null, result: [] }))
            }))
          }))
        },
        onsuccess: null,
        onerror: null
      })),
      deleteDatabase: vi.fn()
    } as any;
    
    // Mock performance API
    global.performance = {
      now: vi.fn(() => Date.now()),
      getEntriesByType: vi.fn(() => []),
      mark: vi.fn(),
      measure: vi.fn()
    } as any;

    // Mock PerformanceObserver
    global.PerformanceObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      disconnect: vi.fn()
    }));
  });

  afterAll(() => {
    global.navigator = originalNavigator;
  });

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default mock implementations
    vi.mocked(serviceWorkerManager.register).mockResolvedValue({
      active: { postMessage: vi.fn() },
      addEventListener: vi.fn()
    } as any);
    
    vi.mocked(indexedDBManager.initDatabase).mockResolvedValue();
    vi.mocked(indexedDBManager.getAllProjects).mockResolvedValue([]);
    vi.mocked(indexedDBManager.getTasksByProject).mockResolvedValue([]);
    
    vi.mocked(localStorageManager.hasLegacyData).mockResolvedValue(false);
    
    vi.mocked(offlineAwareAIClient.getQueueLength).mockReturnValue(0);
    vi.mocked(offlineAwareAIClient.isOnline).mockReturnValue(true);
  });

  afterEach(() => {
    // Cleanup
    pwaIntegrationManager.cleanup();
  });

  describe('PWA Integration Manager', () => {
    it('should initialize all PWA components successfully', async () => {
      await expect(pwaIntegrationManager.initialize()).resolves.not.toThrow();
      
      expect(serviceWorkerManager.register).toHaveBeenCalled();
      expect(indexedDBManager.initDatabase).toHaveBeenCalled();
      
      const state = pwaIntegrationManager.getState();
      expect(state.serviceWorkerReady).toBe(true);
    });

    it('should handle network status changes correctly', async () => {
      await pwaIntegrationManager.initialize();
      
      const initialState = pwaIntegrationManager.getState();
      expect(initialState.isOnline).toBe(true);

      // Simulate going offline
      mockNavigator.onLine = false;
      const offlineEvent = new Event('offline');
      window.dispatchEvent(offlineEvent);

      await waitFor(() => {
        const state = pwaIntegrationManager.getState();
        expect(state.isOnline).toBe(false);
      });

      // Simulate going online
      mockNavigator.onLine = true;
      const onlineEvent = new Event('online');
      window.dispatchEvent(onlineEvent);

      await waitFor(() => {
        const state = pwaIntegrationManager.getState();
        expect(state.isOnline).toBe(true);
      });
    });

    it('should migrate legacy data on initialization', async () => {
      const mockLegacyProject = createMockProject();
      
      vi.mocked(localStorageManager.hasLegacyData).mockResolvedValue(true);
      vi.mocked(localStorageManager.exportData).mockResolvedValue({
        projects: [mockLegacyProject]
      });

      await pwaIntegrationManager.initialize();

      expect(indexedDBManager.createProject).toHaveBeenCalledWith(mockLegacyProject);
      expect(localStorageManager.clearLegacyData).toHaveBeenCalled();
    });

    it('should handle AI request queue processing when coming online', async () => {
      vi.mocked(offlineAwareAIClient.getQueueLength).mockReturnValue(3);
      vi.mocked(offlineAwareAIClient.processQueue).mockResolvedValue();

      await pwaIntegrationManager.initialize();

      // Simulate going online with queued requests
      mockNavigator.onLine = true;
      const onlineEvent = new Event('online');
      window.dispatchEvent(onlineEvent);

      await waitFor(() => {
        expect(offlineAwareAIClient.processQueue).toHaveBeenCalled();
      });
    });
  });

  describe('PWA Integration Hook', () => {
    it('should provide complete PWA functionality through hook interface', async () => {
      const { result } = renderHook(() => usePWAIntegration());

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      expect(result.current.state).toBeDefined();
      expect(result.current.initialize).toBeDefined();
      expect(result.current.saveProject).toBeDefined();
      expect(result.current.loadProjects).toBeDefined();
      expect(result.current.exportData).toBeDefined();
      expect(result.current.getOfflineCapabilities).toBeDefined();
    });

    it('should handle project creation and persistence', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      const mockProject = createMockProject();

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      await act(async () => {
        await result.current.saveProject(mockProject);
      });

      expect(indexedDBManager.createProject).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockProject,
          updatedAt: expect.any(Date),
          pendingSync: false
        })
      );
    });

    it('should handle task management with PWA metadata', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      const mockTask = createMockTask();
      const projectId = 'test-project-id';

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      await act(async () => {
        await result.current.saveTask(mockTask, projectId);
      });

      expect(indexedDBManager.createTask).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockTask,
          updatedAt: expect.any(Date),
          pendingSync: false
        }),
        projectId
      );
    });

    it('should provide offline capabilities information', async () => {
      const { result } = renderHook(() => usePWAIntegration());

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      const capabilities = result.current.getOfflineCapabilities();

      expect(capabilities).toEqual({
        canCreateProjects: true,
        canEditTasks: true,
        canDeleteTasks: true,
        canExportData: true,
        canUseAI: true,
        limitations: []
      });
    });

    it('should handle offline workflows correctly', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      const mockProject = createMockProject();

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      const workflow = {
        type: 'create_project' as const,
        data: mockProject,
        timestamp: new Date()
      };

      await act(async () => {
        await result.current.handleOfflineWorkflow(workflow);
      });

      expect(indexedDBManager.createProject).toHaveBeenCalled();
    });
  });

  describe('Complete User Workflows', () => {
    it('should support complete project creation to export workflow', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Step 1: Create project
      const mockProject = createMockProject();
      await act(async () => {
        await result.current.saveProject(mockProject);
      });

      // Step 2: Add tasks
      const mockTask1 = createMockTask();
      const mockTask2 = createMockTask();
      
      await act(async () => {
        await result.current.saveTask(mockTask1, mockProject.id);
        await result.current.saveTask(mockTask2, mockProject.id);
      });

      // Step 3: Load data
      vi.mocked(indexedDBManager.getAllProjects).mockResolvedValue([mockProject]);
      vi.mocked(indexedDBManager.getTasksByProject).mockResolvedValue([mockTask1, mockTask2]);

      let projects: any[] = [];
      await act(async () => {
        projects = await result.current.loadProjects();
      });

      expect(projects).toHaveLength(1);
      expect(projects[0].id).toBe(mockProject.id);

      // Step 4: Export data
      let exportedData: any;
      await act(async () => {
        exportedData = await result.current.exportData();
      });

      expect(exportedData).toEqual({
        version: '2.0.0',
        exportedAt: expect.any(String),
        pwaMetadata: expect.objectContaining({
          isOnline: true,
          serviceWorkerReady: true
        }),
        projects: expect.arrayContaining([
          expect.objectContaining({
            id: mockProject.id,
            tasks: expect.arrayContaining([
              expect.objectContaining({ id: mockTask1.id }),
              expect.objectContaining({ id: mockTask2.id })
            ])
          })
        ])
      });
    });

    it('should handle offline-to-online transition workflow', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Start offline
      mockNavigator.onLine = false;
      const offlineEvent = new Event('offline');
      window.dispatchEvent(offlineEvent);

      await waitFor(() => {
        expect(result.current.state.isOnline).toBe(false);
      });

      // Create project while offline
      const mockProject = createMockProject();
      await act(async () => {
        await result.current.saveProject(mockProject);
      });

      // Verify project is marked as pending sync
      expect(indexedDBManager.createProject).toHaveBeenCalledWith(
        expect.objectContaining({
          pendingSync: true
        })
      );

      // Go back online
      mockNavigator.onLine = true;
      vi.mocked(offlineAwareAIClient.getQueueLength).mockReturnValue(2);
      
      const onlineEvent = new Event('online');
      window.dispatchEvent(onlineEvent);

      await waitFor(() => {
        expect(result.current.state.isOnline).toBe(true);
        expect(offlineAwareAIClient.processQueue).toHaveBeenCalled();
      });
    });

    it('should run complete workflow test successfully', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Mock successful workflow test
      vi.mocked(indexedDBManager.getAllProjects).mockResolvedValue([]);
      vi.mocked(indexedDBManager.getTasksByProject).mockResolvedValue([]);

      let testResult: boolean = false;
      await act(async () => {
        testResult = await result.current.testWorkflow();
      });

      expect(testResult).toBe(true);
      expect(indexedDBManager.createProject).toHaveBeenCalled();
      expect(indexedDBManager.createTask).toHaveBeenCalled();
      expect(indexedDBManager.deleteProject).toHaveBeenCalled();
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle IndexedDB failures gracefully', async () => {
      vi.mocked(indexedDBManager.initDatabase).mockRejectedValue(new Error('IndexedDB not available'));
      
      await expect(pwaIntegrationManager.initialize()).rejects.toThrow('IndexedDB not available');
    });

    it('should fallback to localStorage when IndexedDB fails', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Mock IndexedDB failure
      vi.mocked(indexedDBManager.getAllProjects).mockRejectedValue(new Error('IndexedDB error'));
      
      const mockProject = createMockProject();
      vi.mocked(localStorageManager.loadProject).mockResolvedValue(mockProject);

      let projects: any[] = [];
      await act(async () => {
        projects = await result.current.loadProjects();
      });

      expect(projects).toHaveLength(1);
      expect(projects[0].id).toBe(mockProject.id);
      expect(localStorageManager.loadProject).toHaveBeenCalled();
    });

    it('should handle service worker registration failures', async () => {
      vi.mocked(serviceWorkerManager.register).mockRejectedValue(new Error('Service worker not supported'));
      
      // Should not throw - app should work without service worker
      await expect(pwaIntegrationManager.initialize()).resolves.not.toThrow();
      
      const state = pwaIntegrationManager.getState();
      expect(state.serviceWorkerReady).toBe(false);
      expect(state.performance.offlineCapability).toBe(false);
    });

    it('should handle network errors during sync', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Mock network error
      mockNavigator.onLine = false;

      await expect(result.current.forceSyncData()).rejects.toThrow('Cannot sync data while offline');
      expect(result.current.error).toBe('Cannot sync data while offline');
    });
  });

  describe('Performance and Optimization', () => {
    it('should track performance metrics', async () => {
      await pwaIntegrationManager.initialize();
      
      const metrics = pwaIntegrationManager.getPerformanceMetrics();
      
      expect(metrics).toEqual({
        loadTime: expect.any(Number),
        cacheHitRate: expect.any(Number),
        offlineCapability: expect.any(Boolean)
      });
    });

    it('should handle large datasets efficiently', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      // Create large dataset
      const largeProjectList = Array.from({ length: 100 }, () => createMockProject());
      const largeTaskList = Array.from({ length: 1000 }, () => createMockTask());

      vi.mocked(indexedDBManager.getAllProjects).mockResolvedValue(largeProjectList);
      vi.mocked(indexedDBManager.getTasksByProject).mockResolvedValue(largeTaskList);

      const startTime = performance.now();
      
      let projects: any[] = [];
      await act(async () => {
        projects = await result.current.loadProjects();
      });

      const endTime = performance.now();
      const loadTime = endTime - startTime;

      expect(projects).toHaveLength(100);
      expect(loadTime).toBeLessThan(1000); // Should load within 1 second
    });
  });

  describe('Data Integrity and Consistency', () => {
    it('should maintain data consistency across storage systems', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      const mockProject = createMockProject();
      
      await act(async () => {
        await result.current.saveProject(mockProject);
      });

      // Verify data is saved to both IndexedDB and localStorage
      expect(indexedDBManager.createProject).toHaveBeenCalled();
      expect(localStorageManager.saveProject).toHaveBeenCalled();
    });

    it('should handle concurrent data modifications', async () => {
      const { result } = renderHook(() => usePWAIntegration());
      
      await waitFor(() => {
        expect(result.current.isInitialized).toBe(true);
      });

      const mockProject1 = createMockProject();
      const mockProject2 = createMockProject();

      // Simulate concurrent saves
      await act(async () => {
        await Promise.all([
          result.current.saveProject(mockProject1),
          result.current.saveProject(mockProject2)
        ]);
      });

      expect(indexedDBManager.createProject).toHaveBeenCalledTimes(2);
    });
  });
});