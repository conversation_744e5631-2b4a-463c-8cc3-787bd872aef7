import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { InsertionIndicator } from '@/components/InsertionIndicator';
import { useInsertionZones } from '@/hooks/useInsertionZones';
import type { Task, InsertionPosition } from '@/lib/types';
import { beforeEach } from 'node:test';

// Mock the insertion zones hook
vi.mock('@/hooks/useInsertionZones');
const mockUseInsertionZones = vi.mocked(useInsertionZones);

// Mock the utils
vi.mock('@/lib/utils', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' ')
}));

describe('InsertionIndicator Integration', () => {
  const sampleTask: Task = {
    id: 'test-task-1',
    title: 'Test Task',
    description: 'Test Description',
    content: '',
    status: 'To Do',
    assignees: [],
    subtasks: [
      {
        id: 'subtask-1',
        title: 'Subtask 1',
        description: '',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ]
  };

  const mockZones = [
    {
      id: 'before-test-task-1',
      type: 'before' as const,
      bounds: new DOMRect(0, 0, 100, 20),
      targetTaskId: 'test-task-1',
      parentId: null,
      level: 0,
      metadata: {
        isVisible: true,
        priority: 1,
        touchFriendly: false
      }
    },
    {
      id: 'after-test-task-1',
      type: 'after' as const,
      bounds: new DOMRect(0, 50, 100, 20),
      targetTaskId: 'test-task-1',
      parentId: null,
      level: 0,
      metadata: {
        isVisible: true,
        priority: 2,
        touchFriendly: false
      }
    },
    {
      id: 'between-test-task-1-subtask-1',
      type: 'between_parent_child' as const,
      bounds: new DOMRect(20, 25, 80, 20),
      targetTaskId: 'test-task-1',
      parentId: 'test-task-1',
      level: 1,
      metadata: {
        isVisible: true,
        priority: 3,
        touchFriendly: false
      }
    }
  ];

  beforeEach(() => {
    mockUseInsertionZones.mockReturnValue({
      zones: mockZones,
      isCalculating: false,
      findZoneAtPoint: vi.fn((x, y) => {
        return mockZones.find(zone => 
          x >= zone.bounds.left && x <= zone.bounds.right &&
          y >= zone.bounds.top && y <= zone.bounds.bottom
        ) || null;
      }),
      getZonesByType: vi.fn((type) => mockZones.filter(zone => zone.type === type)),
      clearCache: vi.fn(),
      forceRecalculate: vi.fn(),
      updateZones: vi.fn()
    });
  });

  describe('Integration with Insertion Zones', () => {
    it('renders indicators for all calculated zones', () => {
      const mockOnInsert = vi.fn();

      render(
        <div>
          {mockZones.map((zone) => {
            const position: InsertionPosition = {
              type: zone.type,
              targetTaskId: zone.targetTaskId,
              parentId: zone.parentId,
              level: zone.level
            };

            return (
              <InsertionIndicator
                key={zone.id}
                position={position}
                isVisible={true}
                onInsert={() => mockOnInsert(position)}
              />
            );
          })}
        </div>
      );

      // Check that all indicators are rendered
      expect(screen.getByTestId('insertion-indicator-before-test-task-1')).toBeInTheDocument();
      expect(screen.getByTestId('insertion-indicator-after-test-task-1')).toBeInTheDocument();
      expect(screen.getByTestId('insertion-indicator-between_parent_child-test-task-1')).toBeInTheDocument();
    });

    it('handles insertion for different zone types', () => {
      const mockOnInsert = vi.fn();
      const insertionResults: InsertionPosition[] = [];

      render(
        <div>
          {mockZones.map((zone) => {
            const position: InsertionPosition = {
              type: zone.type,
              targetTaskId: zone.targetTaskId,
              parentId: zone.parentId,
              level: zone.level
            };

            return (
              <InsertionIndicator
                key={zone.id}
                position={position}
                isVisible={true}
                onInsert={() => {
                  insertionResults.push(position);
                  mockOnInsert(position);
                }}
              />
            );
          })}
        </div>
      );

      // Click each indicator
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => fireEvent.click(button));

      expect(mockOnInsert).toHaveBeenCalledTimes(3);
      expect(insertionResults).toHaveLength(3);

      // Verify correct insertion positions
      expect(insertionResults).toContainEqual({
        type: 'before',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      });

      expect(insertionResults).toContainEqual({
        type: 'after',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      });

      expect(insertionResults).toContainEqual({
        type: 'between_parent_child',
        targetTaskId: 'test-task-1',
        parentId: 'test-task-1',
        level: 1
      });
    });

    it('applies correct styling based on zone metadata', () => {
      const touchFriendlyZone = {
        ...mockZones[0],
        metadata: {
          ...mockZones[0].metadata,
          touchFriendly: true
        }
      };

      const position: InsertionPosition = {
        type: touchFriendlyZone.type,
        targetTaskId: touchFriendlyZone.targetTaskId,
        parentId: touchFriendlyZone.parentId,
        level: touchFriendlyZone.level
      };

      render(
        <InsertionIndicator
          position={position}
          isVisible={true}
          onInsert={vi.fn()}
          touchFriendly={touchFriendlyZone.metadata.touchFriendly}
        />
      );

      const button = screen.getByRole('button');
      expect(button).toHaveClass('touch-manipulation');
    });

    it('handles hover states correctly', () => {
      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      };

      const { rerender } = render(
        <InsertionIndicator
          position={position}
          isVisible={true}
          isHovered={false}
          onInsert={vi.fn()}
        />
      );

      const indicator = screen.getByTestId('insertion-indicator-before-test-task-1');
      expect(indicator).toHaveClass('opacity-70');

      // Simulate hover
      rerender(
        <InsertionIndicator
          position={position}
          isVisible={true}
          isHovered={true}
          onInsert={vi.fn()}
        />
      );

      expect(indicator).toHaveClass('opacity-100');
    });
  });

  describe('Responsive Behavior', () => {
    it('adapts to different variants', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      };

      const variants = ['default', 'compact', 'touch'] as const;
      const expectedHeights = ['20px', '16px', '40px'];

      variants.forEach((variant, index) => {
        const { unmount } = render(
          <InsertionIndicator
            position={position}
            isVisible={true}
            onInsert={vi.fn()}
            variant={variant}
          />
        );

        const indicator = screen.getByTestId('insertion-indicator-after-test-task-1');
        expect(indicator).toHaveStyle({ height: expectedHeights[index] });

        unmount();
      });
    });

    it('handles hierarchy indentation correctly', () => {
      const positions: InsertionPosition[] = [
        { type: 'before', targetTaskId: 'task-1', parentId: null, level: 0 },
        { type: 'after', targetTaskId: 'task-2', parentId: null, level: 0 },
        { type: 'between_parent_child', targetTaskId: 'task-3', parentId: 'parent-1', level: 1 }
      ];

      const expectedIndentations = ['0px', '0px', '20px'];

      positions.forEach((position, index) => {
        const { unmount } = render(
          <InsertionIndicator
            position={position}
            isVisible={true}
            onInsert={vi.fn()}
          />
        );

        const indicator = screen.getByTestId(`insertion-indicator-${position.type}-${position.targetTaskId}`);
        expect(indicator).toHaveStyle({ paddingLeft: expectedIndentations[index] });

        unmount();
      });
    });
  });

  describe('Performance Integration', () => {
    it('handles rapid state changes efficiently', () => {
      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      };

      const { rerender } = render(
        <InsertionIndicator
          position={position}
          isVisible={false}
          isHovered={false}
          onInsert={vi.fn()}
        />
      );

      // Simulate rapid visibility and hover changes
      for (let i = 0; i < 10; i++) {
        rerender(
          <InsertionIndicator
            position={position}
            isVisible={i % 2 === 0}
            isHovered={i % 3 === 0}
            onInsert={vi.fn()}
          />
        );
      }

      // Component should still be functional
      const indicator = screen.getByTestId('insertion-indicator-before-test-task-1');
      expect(indicator).toBeInTheDocument();
    });

    it('maintains performance with many indicators', () => {
      const positions: InsertionPosition[] = Array.from({ length: 50 }, (_, i) => ({
        type: 'after' as const,
        targetTaskId: `task-${i}`,
        parentId: null,
        level: 0
      }));

      const startTime = performance.now();

      render(
        <div>
          {positions.map((position, index) => (
            <InsertionIndicator
              key={`${position.type}-${position.targetTaskId}`}
              position={position}
              isVisible={true}
              onInsert={vi.fn()}
            />
          ))}
        </div>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Rendering 50 indicators should be reasonably fast (< 200ms)
      expect(renderTime).toBeLessThan(200);

      // All indicators should be rendered
      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(50);
    });
  });

  describe('Error Handling', () => {
    it('handles invalid position gracefully', () => {
      const invalidPosition = {
        type: 'invalid' as any,
        targetTaskId: '',
        parentId: null,
        level: -1
      };

      expect(() => {
        render(
          <InsertionIndicator
            position={invalidPosition}
            isVisible={true}
            onInsert={vi.fn()}
          />
        );
      }).not.toThrow();
    });

    it('handles missing onInsert callback gracefully', () => {
      const position: InsertionPosition = {
        type: 'before',
        targetTaskId: 'test-task-1',
        parentId: null,
        level: 0
      };

      // Use a mock function that's undefined to test graceful handling
      const mockOnInsert = undefined as any;

      render(
        <InsertionIndicator
          position={position}
          isVisible={true}
          onInsert={mockOnInsert}
        />
      );

      const button = screen.getByRole('button');
      
      // Should not throw when clicked, even with undefined callback
      expect(() => {
        fireEvent.click(button);
      }).not.toThrow();
      
      // Should not throw on keyboard events either
      expect(() => {
        fireEvent.keyDown(button, { key: 'Enter' });
      }).not.toThrow();
    });
  });
});