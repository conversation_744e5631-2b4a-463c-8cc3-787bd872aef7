/**
 * Integration tests for complete offline user workflows
 * Tests end-to-end PWA functionality including offline task management
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react-dom/test-utils';

// Mock components and hooks
import { usePWA } from '@/hooks/usePWA';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { indexedDBManager } from '@/lib/storage/indexeddb';
import { localStorageManager } from '@/lib/storage/localStorageManager';

// Test components
import TaskList from '@/components/TaskList';
import { PWAInstallPrompt } from '@/components/PWAInstallPrompt';
import { PWAUpdateNotification } from '@/components/PWAUpdateNotification';
import { OfflineBanner } from '@/components/OfflineBanner';

// Mock the hooks
vi.mock('@/hooks/usePWA');
vi.mock('@/hooks/useNetworkStatus');
vi.mock('@/lib/storage/indexeddb');
vi.mock('@/lib/storage/localStorageManager');

const mockUsePWA = vi.mocked(usePWA);
const mockUseNetworkStatus = vi.mocked(useNetworkStatus);
const mockIndexedDB = vi.mocked(indexedDBManager);
const mockLocalStorage = vi.mocked(localStorageManager);

// Mock project data
const mockProject = {
  id: 'test-project-1',
  title: 'Offline Test Project',
  description: 'Testing offline functionality',
  tasks: [
    {
      id: 'task-1',
      title: 'First Task',
      description: 'First task description',
      content: 'Task content',
      status: 'pending' as const,
      assignees: [],
      subtasks: []
    },
    {
      id: 'task-2',
      title: 'Second Task',
      description: 'Second task description',
      content: 'Task content',
      status: 'completed' as const,
      assignees: [],
      subtasks: [
        {
          id: 'subtask-1',
          title: 'Subtask 1',
          description: 'Subtask description',
          content: 'Subtask content',
          status: 'pending' as const,
          assignees: [],
          subtasks: []
        }
      ]
    }
  ]
};

describe('PWA Offline Workflows Integration', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    mockUsePWA.mockReturnValue({
      isInstalled: false,
      isOnline: true,
      updateAvailable: false,
      updateInfo: {},
      isUpdating: false,
      updateApp: vi.fn(),
      checkForUpdates: vi.fn(),
      getUpdateSize: vi.fn(() => '< 1 MB'),
      registration: null
    });

    mockUseNetworkStatus.mockReturnValue({
      isOnline: true,
      isSlowConnection: false,
      connectionType: 'wifi',
      effectiveType: '4g'
    });

    // Mock IndexedDB operations
    mockIndexedDB.initDatabase.mockResolvedValue();
    mockIndexedDB.getAllProjects.mockResolvedValue([]);
    mockIndexedDB.getTasksByProject.mockResolvedValue([]);
    mockIndexedDB.createProject.mockResolvedValue('new-project-id');
    mockIndexedDB.createTask.mockResolvedValue('new-task-id');
    mockIndexedDB.updateTask.mockResolvedValue();
    mockIndexedDB.deleteTask.mockResolvedValue();

    // Mock localStorage operations
    mockLocalStorage.saveProject.mockResolvedValue();
    mockLocalStorage.loadProject.mockResolvedValue(mockProject);
    mockLocalStorage.saveTasks.mockResolvedValue();
    mockLocalStorage.loadTasks.mockResolvedValue(mockProject.tasks);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('PWA Installation Workflow', () => {
    it('should show installation prompt when app is installable', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: false,
        isOnline: true,
        updateAvailable: false,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: null
      });

      // Mock beforeinstallprompt event
      const mockPromptEvent = {
        preventDefault: vi.fn(),
        prompt: vi.fn().mockResolvedValue({ outcome: 'accepted' })
      };

      render(<PWAInstallPrompt />);

      // Simulate beforeinstallprompt event
      act(() => {
        window.dispatchEvent(new CustomEvent('beforeinstallprompt', {
          detail: mockPromptEvent
        }));
      });

      await waitFor(() => {
        expect(screen.getByText(/App installieren/i)).toBeInTheDocument();
      });

      // Click install button
      const installButton = screen.getByRole('button', { name: /installieren/i });
      await user.click(installButton);

      expect(mockPromptEvent.prompt).toHaveBeenCalled();
    });

    it('should hide installation prompt after successful installation', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: false,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: null
      });

      render(<PWAInstallPrompt />);

      // Should not show install prompt when already installed
      expect(screen.queryByText(/App installieren/i)).not.toBeInTheDocument();
    });
  });

  describe('Offline Task Management Workflow', () => {
    it('should display offline banner when network is unavailable', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      render(<OfflineBanner />);

      expect(screen.getByText(/Offline-Modus/i)).toBeInTheDocument();
      expect(screen.getByText(/Ihre Änderungen werden lokal gespeichert/i)).toBeInTheDocument();
    });

    it('should allow creating tasks while offline', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Find and click add task button
      const addButton = screen.getByRole('button', { name: /aufgabe hinzufügen/i });
      await user.click(addButton);

      // Enter task title
      const titleInput = screen.getByPlaceholderText(/aufgabentitel eingeben/i);
      await user.type(titleInput, 'New Offline Task');

      // Submit the task
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockOnTasksChange).toHaveBeenCalled();
      });

      // Verify task was saved locally
      expect(mockLocalStorage.saveTasks).toHaveBeenCalled();
    });

    it('should allow editing tasks while offline', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Find first task and click to edit
      const firstTask = screen.getByText('First Task');
      await user.click(firstTask);

      // Edit the task title
      const editInput = screen.getByDisplayValue('First Task');
      await user.clear(editInput);
      await user.type(editInput, 'Updated Offline Task');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockOnTasksChange).toHaveBeenCalled();
      });

      // Verify task was updated locally
      expect(mockLocalStorage.saveTasks).toHaveBeenCalled();
    });

    it('should allow deleting tasks while offline', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Find delete button for first task
      const deleteButtons = screen.getAllByRole('button', { name: /löschen/i });
      await user.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockOnTasksChange).toHaveBeenCalled();
      });

      // Verify task was deleted locally
      expect(mockLocalStorage.saveTasks).toHaveBeenCalled();
    });

    it('should handle hierarchical task operations offline', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Expand task with subtasks
      const expandButton = screen.getByRole('button', { name: /erweitern/i });
      await user.click(expandButton);

      // Verify subtask is visible
      expect(screen.getByText('Subtask 1')).toBeInTheDocument();

      // Add new subtask
      const addSubtaskButton = screen.getByRole('button', { name: /unteraufgabe hinzufügen/i });
      await user.click(addSubtaskButton);

      const subtaskInput = screen.getByPlaceholderText(/unteraufgabe eingeben/i);
      await user.type(subtaskInput, 'New Offline Subtask');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockOnTasksChange).toHaveBeenCalled();
      });

      // Verify hierarchical structure was maintained
      expect(mockLocalStorage.saveTasks).toHaveBeenCalled();
    });
  });

  describe('Data Persistence and Recovery Workflow', () => {
    it('should persist data across app restarts', async () => {
      // Simulate app restart by re-rendering with fresh data load
      mockLocalStorage.loadProject.mockResolvedValue(mockProject);
      mockLocalStorage.loadTasks.mockResolvedValue(mockProject.tasks);

      const { rerender } = render(
        <TaskList
          tasks={[]}
          onTasksChange={vi.fn()}
          projectTitle=""
        />
      );

      // Simulate data loading after restart
      await act(async () => {
        rerender(
          <TaskList
            tasks={mockProject.tasks}
            onTasksChange={vi.fn()}
            projectTitle={mockProject.title}
          />
        );
      });

      // Verify data was loaded from storage
      expect(mockLocalStorage.loadProject).toHaveBeenCalled();
      expect(mockLocalStorage.loadTasks).toHaveBeenCalled();

      // Verify tasks are displayed
      expect(screen.getByText('First Task')).toBeInTheDocument();
      expect(screen.getByText('Second Task')).toBeInTheDocument();
    });

    it('should handle storage quota exceeded gracefully', async () => {
      mockLocalStorage.saveTasks.mockRejectedValue(
        new Error('QuotaExceededError: Storage quota exceeded')
      );

      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Try to add a task that would exceed quota
      const addButton = screen.getByRole('button', { name: /aufgabe hinzufügen/i });
      await user.click(addButton);

      const titleInput = screen.getByPlaceholderText(/aufgabentitel eingeben/i);
      await user.type(titleInput, 'Task that exceeds quota');
      await user.keyboard('{Enter}');

      // Should show error message about storage quota
      await waitFor(() => {
        expect(screen.getByText(/speicherplatz voll/i)).toBeInTheDocument();
      });
    });

    it('should migrate data from localStorage to IndexedDB', async () => {
      // Mock existing localStorage data
      const legacyData = {
        project: mockProject,
        tasks: mockProject.tasks
      };

      mockLocalStorage.loadProject.mockResolvedValue(legacyData.project);
      mockLocalStorage.loadTasks.mockResolvedValue(legacyData.tasks);

      // Mock successful migration
      mockIndexedDB.createProject.mockResolvedValue('migrated-project-id');
      mockIndexedDB.createTask.mockResolvedValue('migrated-task-id');

      // Simulate migration trigger
      await act(async () => {
        // This would typically be triggered during app initialization
        const project = await mockLocalStorage.loadProject();
        if (project) {
          await mockIndexedDB.createProject({
            title: project.title,
            description: project.description,
            isOfflineOnly: true
          });
        }
      });

      expect(mockIndexedDB.createProject).toHaveBeenCalledWith({
        title: mockProject.title,
        description: mockProject.description,
        isOfflineOnly: true
      });
    });
  });

  describe('PWA Update Workflow', () => {
    it('should show update notification when update is available', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {
          version: '2.0.0',
          releaseNotes: ['Neue Features', 'Bugfixes', 'Performance-Verbesserungen'],
          size: 1024 * 1024 // 1MB
        },
        isUpdating: false,
        updateApp: vi.fn().mockResolvedValue(undefined),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '1 MB'),
        registration: null
      });

      render(<PWAUpdateNotification />);

      expect(screen.getByText(/Update verfügbar/i)).toBeInTheDocument();
      expect(screen.getByText(/Version 2.0.0/i)).toBeInTheDocument();
      expect(screen.getByText(/Neue Features/i)).toBeInTheDocument();
    });

    it('should handle update installation process', async () => {
      const mockUpdateApp = vi.fn().mockResolvedValue(undefined);
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {
          version: '2.0.0',
          releaseNotes: ['Neue Features'],
        },
        isUpdating: false,
        updateApp: mockUpdateApp,
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '1 MB'),
        registration: null
      });

      render(<PWAUpdateNotification />);

      const updateButton = screen.getByRole('button', { name: /jetzt aktualisieren/i });
      await user.click(updateButton);

      expect(mockUpdateApp).toHaveBeenCalled();
    });

    it('should show update progress during installation', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: true,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '1 MB'),
        registration: null
      });

      render(<PWAUpdateNotification />);

      expect(screen.getByText(/Update wird installiert/i)).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('Network State Transitions', () => {
    it('should handle online to offline transition gracefully', async () => {
      const { rerender } = render(<OfflineBanner />);

      // Start online
      mockUseNetworkStatus.mockReturnValue({
        isOnline: true,
        isSlowConnection: false,
        connectionType: 'wifi',
        effectiveType: '4g'
      });

      rerender(<OfflineBanner />);
      expect(screen.queryByText(/Offline-Modus/i)).not.toBeInTheDocument();

      // Go offline
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      rerender(<OfflineBanner />);
      expect(screen.getByText(/Offline-Modus/i)).toBeInTheDocument();
    });

    it('should handle offline to online transition and sync data', async () => {
      // Start offline with pending changes
      mockUseNetworkStatus.mockReturnValue({
        isOnline: false,
        isSlowConnection: false,
        connectionType: 'none',
        effectiveType: 'slow-2g'
      });

      const { rerender } = render(<OfflineBanner />);
      expect(screen.getByText(/Offline-Modus/i)).toBeInTheDocument();

      // Go back online
      mockUseNetworkStatus.mockReturnValue({
        isOnline: true,
        isSlowConnection: false,
        connectionType: 'wifi',
        effectiveType: '4g'
      });

      rerender(<OfflineBanner />);

      // Should show sync notification
      await waitFor(() => {
        expect(screen.getByText(/Synchronisierung/i)).toBeInTheDocument();
      });
    });

    it('should handle slow connection scenarios', async () => {
      mockUseNetworkStatus.mockReturnValue({
        isOnline: true,
        isSlowConnection: true,
        connectionType: 'cellular',
        effectiveType: 'slow-2g'
      });

      render(<OfflineBanner />);

      expect(screen.getByText(/Langsame Verbindung/i)).toBeInTheDocument();
      expect(screen.getByText(/Einige Features sind möglicherweise eingeschränkt/i)).toBeInTheDocument();
    });
  });

  describe('Error Recovery Workflows', () => {
    it('should recover from IndexedDB errors by falling back to localStorage', async () => {
      // Mock IndexedDB failure
      mockIndexedDB.initDatabase.mockRejectedValue(new Error('IndexedDB not available'));
      
      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockProject.tasks}
          onTasksChange={mockOnTasksChange}
          projectTitle={mockProject.title}
        />
      );

      // Try to add a task
      const addButton = screen.getByRole('button', { name: /aufgabe hinzufügen/i });
      await user.click(addButton);

      const titleInput = screen.getByPlaceholderText(/aufgabentitel eingeben/i);
      await user.type(titleInput, 'Fallback Task');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(mockOnTasksChange).toHaveBeenCalled();
      });

      // Should fall back to localStorage
      expect(mockLocalStorage.saveTasks).toHaveBeenCalled();
    });

    it('should handle service worker registration failures gracefully', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: false,
        isOnline: true,
        updateAvailable: false,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn().mockRejectedValue(new Error('Service Worker failed')),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: null
      });

      render(<PWAInstallPrompt />);

      // App should still function without service worker
      expect(screen.queryByText(/Fehler beim Laden/i)).not.toBeInTheDocument();
    });

    it('should handle corrupted data gracefully', async () => {
      // Mock corrupted data
      mockLocalStorage.loadProject.mockRejectedValue(new Error('Corrupted data'));
      
      const { rerender } = render(
        <TaskList
          tasks={[]}
          onTasksChange={vi.fn()}
          projectTitle=""
        />
      );

      // Should show error message and allow fresh start
      await waitFor(() => {
        expect(screen.getByText(/Daten konnten nicht geladen werden/i)).toBeInTheDocument();
      });

      // Should allow creating new project
      const newProjectButton = screen.getByRole('button', { name: /neues projekt/i });
      expect(newProjectButton).toBeInTheDocument();
    });
  });
});