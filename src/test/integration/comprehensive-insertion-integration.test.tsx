/**
 * Comprehensive integration tests for insertion functionality with existing task operations
 * Tests insertion compatibility with AI generation, task editing, deletion, and export
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TaskList } from '@/components/TaskList';
import { TaskItem } from '@/components/TaskItem';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock AI module
vi.mock('@/lib/ai', () => ({
  generateTaskBreakdown: vi.fn(),
  elaborateTask: vi.fn(),
  solveTask: vi.fn()
}));

// Mock export utilities
vi.mock('@/lib/export', () => ({
  exportToPDF: vi.fn(),
  exportToCSV: vi.fn(),
  exportToMarkdown: vi.fn()
}));

// Mock insertion hooks
vi.mock('@/hooks/useInsertionZones', () => ({
  useInsertionZones: () => ({
    zones: [],
    isCalculating: false,
    findZoneAtPoint: vi.fn(),
    getZonesByType: vi.fn(),
    clearCache: vi.fn(),
    forceRecalculate: vi.fn(),
    updateZones: vi.fn()
  })
}));

vi.mock('@/hooks/useKeyboardInsertion', () => ({
  useKeyboardInsertion: () => ({
    focusedTask: null,
    keyboardMode: false,
    availableInsertionPoints: [],
    currentInsertionIndex: 0,
    updateFocusedTask: vi.fn(),
    navigateToTask: vi.fn(),
    setKeyboardMode: vi.fn(),
    activeShortcuts: {
      insertAfter: 'Enter',
      insertBefore: 'Shift+Enter',
      insertSubtask: 'Tab+Enter',
      insertBetweenParentChild: 'Ctrl+Enter'
    }
  })
}));

// Test data
const createTestTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
  id,
  title,
  description: `Description for ${title}`,
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks
});

const initialTasks: Task[] = [
  createTestTask('task-1', 'Task 1', [
    createTestTask('task-1-1', 'Subtask 1.1'),
    createTestTask('task-1-2', 'Subtask 1.2')
  ]),
  createTestTask('task-2', 'Task 2'),
  createTestTask('task-3', 'Task 3')
];

describe('Comprehensive Insertion Integration Tests', () => {
  let mockTasks: Task[];
  let mockOnTasksChange: ReturnType<typeof vi.fn>;
  let mockOnInsertTask: ReturnType<typeof vi.fn>;
  let mockOnUpdateTask: ReturnType<typeof vi.fn>;
  let mockOnDeleteTask: ReturnType<typeof vi.fn>;
  let mockOnAddTaskAfter: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockTasks = JSON.parse(JSON.stringify(initialTasks)); // Deep clone
    mockOnTasksChange = vi.fn();
    mockOnInsertTask = vi.fn();
    mockOnUpdateTask = vi.fn();
    mockOnDeleteTask = vi.fn();
    mockOnAddTaskAfter = vi.fn();
  });

  describe('Integration with Task CRUD Operations', () => {
    it('should maintain insertion functionality after task creation', async () => {
      const user = userEvent.setup();
      
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onUpdateTask={mockOnUpdateTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          showInsertionIndicators={true}
        />
      );

      // Verify initial tasks are rendered
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-2')).toBeInTheDocument();

      // Simulate adding a new task
      const newTask = createTestTask('task-4', 'New Task 4');
      const updatedTasks = [...mockTasks, newTask];

      // Re-render with new task
      render(
        <TaskList
          tasks={updatedTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onUpdateTask={mockOnUpdateTask}
          onAddTaskAfter={mockOnAddTaskAfter}
          showInsertionIndicators={true}
        />
      );

      // Verify new task is rendered and insertion still works
      expect(screen.getByTestId('task-4')).toBeInTheDocument();
      
      // Test insertion after task creation
      const insertionPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-4',
        parentId: null,
        level: 0
      };

      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);
    });

    it('should handle insertion during task editing', async () => {
      const user = userEvent.setup();
      
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onUpdateTask={mockOnUpdateTask}
          showInsertionIndicators={true}
        />
      );

      // Start editing a task
      const taskTitle = screen.getByDisplayValue('Task 1');
      await user.click(taskTitle);
      await user.clear(taskTitle);
      await user.type(taskTitle, 'Edited Task 1');

      // Test insertion while editing
      const insertionPosition: InsertionPosition = {
        type: 'before',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);

      // Verify editing state is maintained
      expect(taskTitle).toHaveValue('Edited Task 1');
    });

    it('should update insertion points after task deletion', () => {
      const { rerender } = render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onDeleteTask={mockOnDeleteTask}
          showInsertionIndicators={true}
        />
      );

      // Verify initial tasks
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-2')).toBeInTheDocument();
      expect(screen.getByTestId('task-3')).toBeInTheDocument();

      // Simulate task deletion
      const tasksAfterDeletion = mockTasks.filter(task => task.id !== 'task-2');

      rerender(
        <TaskList
          tasks={tasksAfterDeletion}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onDeleteTask={mockOnDeleteTask}
          showInsertionIndicators={true}
        />
      );

      // Verify task is removed
      expect(screen.queryByTestId('task-2')).not.toBeInTheDocument();
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-3')).toBeInTheDocument();

      // Test insertion after deletion
      const insertionPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task-1',
        parentId: 'task-1',
        level: 1
      };

      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);
    });

    it('should handle insertion with hierarchical task operations', () => {
      const tasksWithDeepHierarchy = [
        createTestTask('root', 'Root Task', [
          createTestTask('level-1', 'Level 1', [
            createTestTask('level-2', 'Level 2', [
              createTestTask('level-3', 'Level 3')
            ])
          ])
        ])
      ];

      render(
        <TaskList
          tasks={tasksWithDeepHierarchy}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test insertion at different hierarchy levels
      const insertionPositions: InsertionPosition[] = [
        {
          type: 'after',
          targetTaskId: 'root',
          parentId: null,
          level: 0
        },
        {
          type: 'between_parent_child',
          targetTaskId: 'level-1',
          parentId: 'level-1',
          level: 2
        },
        {
          type: 'after',
          targetTaskId: 'level-3',
          parentId: 'level-2',
          level: 3
        }
      ];

      insertionPositions.forEach(position => {
        mockOnInsertTask(position);
      });

      expect(mockOnInsertTask).toHaveBeenCalledTimes(3);
      insertionPositions.forEach(position => {
        expect(mockOnInsertTask).toHaveBeenCalledWith(position);
      });
    });
  });

  describe('Integration with AI Task Generation', () => {
    it('should support insertion between AI-generated tasks', async () => {
      const { generateTaskBreakdown } = await import('@/lib/ai');
      const mockGenerateTaskBreakdown = vi.mocked(generateTaskBreakdown);

      // Mock AI response
      const aiGeneratedTasks = [
        createTestTask('ai-task-1', 'AI Generated Task 1'),
        createTestTask('ai-task-2', 'AI Generated Task 2'),
        createTestTask('ai-task-3', 'AI Generated Task 3')
      ];

      mockGenerateTaskBreakdown.mockResolvedValue({
        tasks: aiGeneratedTasks,
        context: 'AI generated context'
      });

      const tasksWithAI = [...mockTasks, ...aiGeneratedTasks];

      render(
        <TaskList
          tasks={tasksWithAI}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test insertion between AI-generated tasks
      const insertionPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'ai-task-1',
        parentId: null,
        level: 0
      };

      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);
    });

    it('should maintain insertion functionality during AI task elaboration', async () => {
      const { elaborateTask } = await import('@/lib/ai');
      const mockElaborateTask = vi.mocked(elaborateTask);

      mockElaborateTask.mockResolvedValue({
        content: 'Elaborated task content with detailed steps and explanations.'
      });

      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test insertion while AI elaboration is in progress
      const insertionPosition: InsertionPosition = {
        type: 'before',
        targetTaskId: 'task-1',
        parentId: null,
        level: 0
      };

      // Simulate AI elaboration call
      mockElaborateTask('Task 1', 'Context');

      // Test insertion during AI operation
      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);
    });

    it('should handle insertion with AI-generated subtasks', async () => {
      const { generateTaskBreakdown } = await import('@/lib/ai');
      const mockGenerateTaskBreakdown = vi.mocked(generateTaskBreakdown);

      const aiSubtasks = [
        createTestTask('ai-subtask-1', 'AI Subtask 1'),
        createTestTask('ai-subtask-2', 'AI Subtask 2')
      ];

      mockGenerateTaskBreakdown.mockResolvedValue({
        tasks: aiSubtasks,
        context: 'AI subtask context'
      });

      const taskWithAISubtasks = {
        ...mockTasks[0],
        subtasks: [...mockTasks[0].subtasks, ...aiSubtasks]
      };

      const updatedTasks = [taskWithAISubtasks, ...mockTasks.slice(1)];

      render(
        <TaskList
          tasks={updatedTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test insertion between AI-generated subtasks
      const insertionPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'ai-subtask-1',
        parentId: 'task-1',
        level: 1
      };

      mockOnInsertTask(insertionPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(insertionPosition);
    });
  });

  describe('Integration with Export Functionality', () => {
    it('should include manually inserted tasks in export operations', async () => {
      const { exportToPDF, exportToCSV, exportToMarkdown } = await import('@/lib/export');
      const mockExportToPDF = vi.mocked(exportToPDF);
      const mockExportToCSV = vi.mocked(exportToCSV);
      const mockExportToMarkdown = vi.mocked(exportToMarkdown);

      // Mock export functions
      mockExportToPDF.mockResolvedValue(undefined);
      mockExportToCSV.mockReturnValue('csv,data');
      mockExportToMarkdown.mockReturnValue('# Markdown content');

      // Simulate tasks with manually inserted items
      const tasksWithInsertions = [
        mockTasks[0],
        createTestTask('inserted-1', 'Manually Inserted Task 1'),
        mockTasks[1],
        createTestTask('inserted-2', 'Manually Inserted Task 2'),
        mockTasks[2]
      ];

      render(
        <TaskList
          tasks={tasksWithInsertions}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test that export functions would receive all tasks including inserted ones
      const exportData = {
        projectTitle: 'Test Project',
        projectDescription: 'Test Description',
        tasks: tasksWithInsertions
      };

      // Simulate export operations
      await mockExportToPDF(exportData);
      mockExportToCSV(tasksWithInsertions);
      mockExportToMarkdown(exportData);

      // Verify export functions were called with complete task list
      expect(mockExportToPDF).toHaveBeenCalledWith(exportData);
      expect(mockExportToCSV).toHaveBeenCalledWith(tasksWithInsertions);
      expect(mockExportToMarkdown).toHaveBeenCalledWith(exportData);
    });

    it('should maintain task order in exports after insertions', () => {
      const { exportToMarkdown } = require('@/lib/export');
      const mockExportToMarkdown = vi.mocked(exportToMarkdown);

      mockExportToMarkdown.mockReturnValue('# Markdown with ordered tasks');

      // Simulate specific insertion order
      const orderedTasks = [
        createTestTask('first', 'First Task'),
        createTestTask('inserted-before-second', 'Inserted Before Second'),
        createTestTask('second', 'Second Task'),
        createTestTask('inserted-after-second', 'Inserted After Second'),
        createTestTask('third', 'Third Task')
      ];

      render(
        <TaskList
          tasks={orderedTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Verify tasks are rendered in correct order
      const taskElements = screen.getAllByTestId(/^(first|second|third|inserted-)/);
      const taskIds = taskElements.map(el => el.getAttribute('data-testid'));
      
      expect(taskIds).toEqual([
        'first',
        'inserted-before-second',
        'second',
        'inserted-after-second',
        'third'
      ]);
    });
  });

  describe('Performance Integration Tests', () => {
    it('should maintain performance with frequent insertions', () => {
      const manyTasks = Array.from({ length: 100 }, (_, i) =>
        createTestTask(`perf-task-${i}`, `Performance Task ${i}`)
      );

      const startTime = performance.now();

      render(
        <TaskList
          tasks={manyTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render efficiently even with many tasks
      expect(renderTime).toBeLessThan(1000); // 1 second threshold

      // Test multiple insertions
      const insertionPositions = Array.from({ length: 10 }, (_, i) => ({
        type: 'after' as const,
        targetTaskId: `perf-task-${i * 10}`,
        parentId: null,
        level: 0
      }));

      const insertionStartTime = performance.now();
      insertionPositions.forEach(position => {
        mockOnInsertTask(position);
      });
      const insertionEndTime = performance.now();

      expect(insertionEndTime - insertionStartTime).toBeLessThan(100);
    });

    it('should handle rapid insertion state changes efficiently', () => {
      const { rerender } = render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={false}
        />
      );

      // Rapidly toggle insertion indicators
      const startTime = performance.now();
      
      for (let i = 0; i < 20; i++) {
        rerender(
          <TaskList
            tasks={mockTasks}
            onTasksChange={mockOnTasksChange}
            onInsertTask={mockOnInsertTask}
            showInsertionIndicators={i % 2 === 0}
          />
        );
      }

      const endTime = performance.now();
      expect(endTime - startTime).toBeLessThan(500);
    });
  });

  describe('Error Recovery Integration Tests', () => {
    it('should recover gracefully from insertion callback errors', () => {
      const faultyInsertCallback = vi.fn().mockImplementation(() => {
        throw new Error('Insertion callback failed');
      });

      // Should not crash the component
      expect(() => {
        render(
          <TaskList
            tasks={mockTasks}
            onTasksChange={mockOnTasksChange}
            onInsertTask={faultyInsertCallback}
            showInsertionIndicators={true}
          />
        );
      }).not.toThrow();

      // Component should still be functional
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
    });

    it('should handle task data corruption gracefully', () => {
      const corruptedTasks = [
        mockTasks[0],
        { id: 'corrupted', title: null, subtasks: undefined } as any,
        mockTasks[1],
        null as any,
        mockTasks[2]
      ].filter(Boolean);

      expect(() => {
        render(
          <TaskList
            tasks={corruptedTasks}
            onTasksChange={mockOnTasksChange}
            onInsertTask={mockOnInsertTask}
            showInsertionIndicators={true}
          />
        );
      }).not.toThrow();

      // Valid tasks should still render
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-3')).toBeInTheDocument();
    });

    it('should maintain functionality during concurrent operations', async () => {
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          onUpdateTask={mockOnUpdateTask}
          onDeleteTask={mockOnDeleteTask}
          showInsertionIndicators={true}
        />
      );

      // Simulate concurrent operations
      const operations = [
        () => mockOnInsertTask({
          type: 'after',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0
        }),
        () => mockOnUpdateTask('task-2', { title: 'Updated Task 2' }),
        () => mockOnDeleteTask('task-3'),
        () => mockOnInsertTask({
          type: 'before',
          targetTaskId: 'task-2',
          parentId: null,
          level: 0
        })
      ];

      // Execute operations concurrently
      await Promise.all(operations.map(op => Promise.resolve(op())));

      // All operations should have been called
      expect(mockOnInsertTask).toHaveBeenCalledTimes(2);
      expect(mockOnUpdateTask).toHaveBeenCalledTimes(1);
      expect(mockOnDeleteTask).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility Integration Tests', () => {
    it('should maintain accessibility during insertion operations', () => {
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Check for accessibility attributes
      const taskElements = screen.getAllByTestId(/task-/);
      taskElements.forEach(element => {
        expect(element).toHaveAttribute('data-task-id');
      });

      // Test keyboard navigation support
      const firstTask = screen.getByTestId('task-1');
      expect(firstTask).toBeInTheDocument();
      
      // Should be able to focus elements
      fireEvent.focus(firstTask);
      expect(document.activeElement).toBe(firstTask);
    });

    it('should provide proper ARIA labels for insertion controls', () => {
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Look for insertion-related buttons or controls
      const buttons = screen.getAllByRole('button');
      const insertionButtons = buttons.filter(button => 
        button.getAttribute('aria-label')?.includes('Insert') ||
        button.textContent?.includes('+')
      );

      insertionButtons.forEach(button => {
        expect(button).toHaveAttribute('aria-label');
      });
    });
  });

  describe('State Management Integration Tests', () => {
    it('should maintain consistent state across insertion operations', () => {
      let currentTasks = [...mockTasks];
      const stateTracker = vi.fn((newTasks) => {
        currentTasks = newTasks;
      });

      const { rerender } = render(
        <TaskList
          tasks={currentTasks}
          onTasksChange={stateTracker}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Simulate state changes through insertions
      const insertionSequence = [
        { type: 'after' as const, targetTaskId: 'task-1', parentId: null, level: 0 },
        { type: 'before' as const, targetTaskId: 'task-2', parentId: null, level: 0 },
        { type: 'between_parent_child' as const, targetTaskId: 'task-1', parentId: 'task-1', level: 1 }
      ];

      insertionSequence.forEach((position, index) => {
        mockOnInsertTask(position);
        
        // Simulate state update
        const newTask = createTestTask(`inserted-${index}`, `Inserted Task ${index}`);
        currentTasks = [...currentTasks, newTask];
        
        rerender(
          <TaskList
            tasks={currentTasks}
            onTasksChange={stateTracker}
            onInsertTask={mockOnInsertTask}
            showInsertionIndicators={true}
          />
        );
      });

      // Verify final state
      expect(currentTasks.length).toBe(mockTasks.length + 3);
      expect(mockOnInsertTask).toHaveBeenCalledTimes(3);
    });

    it('should handle undo/redo operations with insertions', () => {
      const stateHistory: Task[][] = [mockTasks];
      let currentStateIndex = 0;

      const undoRedo = {
        undo: () => {
          if (currentStateIndex > 0) {
            currentStateIndex--;
            return stateHistory[currentStateIndex];
          }
          return stateHistory[currentStateIndex];
        },
        redo: () => {
          if (currentStateIndex < stateHistory.length - 1) {
            currentStateIndex++;
            return stateHistory[currentStateIndex];
          }
          return stateHistory[currentStateIndex];
        },
        addState: (newState: Task[]) => {
          stateHistory.splice(currentStateIndex + 1);
          stateHistory.push(newState);
          currentStateIndex = stateHistory.length - 1;
        }
      };

      const { rerender } = render(
        <TaskList
          tasks={stateHistory[currentStateIndex]}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Simulate insertion and state tracking
      const newTask = createTestTask('inserted-undo-test', 'Undo Test Task');
      const newState = [...mockTasks, newTask];
      undoRedo.addState(newState);

      rerender(
        <TaskList
          tasks={stateHistory[currentStateIndex]}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Test undo
      const undoState = undoRedo.undo();
      rerender(
        <TaskList
          tasks={undoState}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Should be back to original state
      expect(undoState).toEqual(mockTasks);

      // Test redo
      const redoState = undoRedo.redo();
      rerender(
        <TaskList
          tasks={redoState}
          onTasksChange={mockOnTasksChange}
          onInsertTask={mockOnInsertTask}
          showInsertionIndicators={true}
        />
      );

      // Should have the inserted task again
      expect(redoState.length).toBe(mockTasks.length + 1);
    });
  });
});