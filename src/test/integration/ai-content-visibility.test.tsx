import { describe, it, expect, beforeEach } from 'vitest'
import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'

// Minimaler Mock für benötigte Komponenten/Typen
const MockAIContentWithLoading = ({ content }: { content: string }) => (
  <div data-testid="ai-content" dangerouslySetInnerHTML={{ __html: content }} />
)

// Vereinfachte Variante des relevanten Ausklapp-Containers
function CollapsibleAI({ content }: { content: string }) {
  const [isVisible, setIsVisible] = React.useState(true)
  const aiRef = React.useRef<HTMLDivElement>(null)
  const [maxHeight, setMaxHeight] = React.useState(0)

  React.useEffect(() => {
    const update = () => {
      const node = aiRef.current
      if (!node) return
      setMaxHeight(node.scrollHeight + 32)
    }
    update()
    window.addEventListener('resize', update)
    return () => window.removeEventListener('resize', update)
  }, [content])

  return (
    <div>
      <button aria-label="toggle" onClick={() => setIsVisible(v => !v)}>toggle</button>
      <div
        data-testid="collapsible"
        style={{ maxHeight: isVisible ? maxHeight : 0, overflow: 'hidden', transition: 'max-height 0.2s ease' }}
      >
        <div ref={aiRef}>
          <MockAIContentWithLoading content={content} />
        </div>
      </div>
    </div>
  )
}

describe('AI-Content Collapsible Visibility', () => {
  beforeEach(() => {
    // jsdom hat kein echtes Layout, aber scrollHeight ist 0. Wir testen den Flow und Sichtbarkeitswechsel ohne echtes Maß.
  })

  it('blendet Inhalt ein/aus ohne abgeschnittene Überschrift', async () => {
    const content = '<h3>Relevante technische Details</h3><p>Text</p>'
    render(<CollapsibleAI content={content} />)

    const collapsible = screen.getByTestId('collapsible')
    // initial sichtbar (maxHeight > 0 in echter Umgebung); in jsdom 0, aber keine Exception
    expect(collapsible).toBeInTheDocument()

    // Toggle aus
    fireEvent.click(screen.getByLabelText('toggle'))
    // Toggle ein
    fireEvent.click(screen.getByLabelText('toggle'))

    // Überschrift ist im DOM vorhanden
    expect(screen.getByText('Relevante technische Details')).toBeInTheDocument()
  })
})


