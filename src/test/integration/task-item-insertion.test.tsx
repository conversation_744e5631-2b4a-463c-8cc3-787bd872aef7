import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TaskItem } from '@/components/TaskItem';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock the insertion-related hooks and components
vi.mock('@/hooks/useInsertionZones', () => ({
  useInsertionZones: () => ({
    zones: [
      {
        id: 'before-task1',
        type: 'before',
        bounds: new DOMRect(0, 0, 100, 20),
        targetTaskId: 'task1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 1, touchFriendly: false }
      },
      {
        id: 'after-task1',
        type: 'after',
        bounds: new DOMRect(0, 50, 100, 20),
        targetTaskId: 'task1',
        parentId: null,
        level: 0,
        metadata: { isVisible: true, priority: 2, touchFriendly: false }
      }
    ],
    isCalculating: false,
    findZoneAtPoint: vi.fn((x: number, y: number) => {
      if (x >= 0 && x <= 100 && y >= 0 && y <= 20) {
        return {
          id: 'before-task1',
          type: 'before',
          bounds: new DOMRect(0, 0, 100, 20),
          targetTaskId: 'task1',
          parentId: null,
          level: 0,
          metadata: { isVisible: true, priority: 1, touchFriendly: false }
        };
      }
      return null;
    }),
    getZonesByType: vi.fn(),
    forceRecalculate: vi.fn()
  })
}));

vi.mock('@/components/InsertionIndicator', () => ({
  InsertionIndicator: ({ position, isVisible, onInsert }: any) => (
    <div 
      data-testid={`insertion-indicator-${position.type}-${position.targetTaskId}`}
      style={{ display: isVisible ? 'block' : 'none' }}
      onClick={onInsert}
    >
      Insert {position.type}
    </div>
  )
}));

describe('TaskItem Insertion Integration', () => {
  const mockTask: Task = {
    id: 'task1',
    title: 'Test Task',
    description: 'Test Description',
    content: 'Test Content',
    status: 'To Do',
    assignees: [],
    subtasks: []
  };

  const mockProps = {
    task: mockTask,
    level: 0,
    parentId: null,
    onUpdateTask: vi.fn(),
    onOpenSolveModal: vi.fn(),
    onAddTask: vi.fn(),
    onAddTaskAfter: vi.fn(),
    onDeleteTask: vi.fn(),
    numbering: '1',
    loading: {},
    setLoading: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Insertion Indicator Rendering', () => {
    it('should render insertion indicators when showInsertionIndicators is true', async () => {
      const onInsertTask = vi.fn();
      
      render(
        <TaskItem 
          {...mockProps} 
          onInsertTask={onInsertTask}
          showInsertionIndicators={true}
          insertionMode="always"
        />
      );

      // Simulate mouse enter to show indicators
      const taskElement = screen.getByTestId('task1');
      fireEvent.mouseEnter(taskElement);

      await waitFor(() => {
        expect(screen.queryByTestId('insertion-indicator-before-task1')).toBeInTheDocument();
        expect(screen.queryByTestId('insertion-indicator-after-task1')).toBeInTheDocument();
      });
    });

    it('should not render insertion indicators when showInsertionIndicators is false', () => {
      render(
        <TaskItem 
          {...mockProps} 
          showInsertionIndicators={false}
          insertionMode="hover"
        />
      );

      expect(screen.queryByTestId('insertion-indicator-before-task1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('insertion-indicator-after-task1')).not.toBeInTheDocument();
    });
  });

  describe('Insertion Mode Behavior', () => {
    it('should show indicators on hover when insertionMode is "hover"', async () => {
      render(
        <TaskItem 
          {...mockProps} 
          showInsertionIndicators={true}
          insertionMode="hover"
        />
      );

      const taskElement = screen.getByTestId('task1');
      
      // Initially no indicators should be visible
      expect(screen.queryByTestId('insertion-indicator-before-task1')).not.toBeInTheDocument();

      // Hover should show indicators
      fireEvent.mouseEnter(taskElement);
      
      await waitFor(() => {
        expect(screen.queryByTestId('insertion-indicator-before-task1')).toBeInTheDocument();
      });

      // Mouse leave should hide indicators
      fireEvent.mouseLeave(taskElement);
      
      await waitFor(() => {
        expect(screen.queryByTestId('insertion-indicator-before-task1')).not.toBeInTheDocument();
      });
    });

    it('should always show indicators when insertionMode is "always"', async () => {
      render(
        <TaskItem 
          {...mockProps} 
          showInsertionIndicators={true}
          insertionMode="always"
        />
      );

      const taskElement = screen.getByTestId('task1');
      fireEvent.mouseEnter(taskElement);

      await waitFor(() => {
        expect(screen.queryByTestId('insertion-indicator-before-task1')).toBeInTheDocument();
      });
    });
  });

  describe('Insertion Callbacks', () => {
    it('should call onInsertTask when insertion indicator is clicked', async () => {
      const onInsertTask = vi.fn();
      
      render(
        <TaskItem 
          {...mockProps} 
          onInsertTask={onInsertTask}
          showInsertionIndicators={true}
          insertionMode="always"
        />
      );

      const taskElement = screen.getByTestId('task1');
      fireEvent.mouseEnter(taskElement);

      await waitFor(() => {
        const indicator = screen.getByTestId('insertion-indicator-before-task1');
        fireEvent.click(indicator);
      });

      expect(onInsertTask).toHaveBeenCalledWith({
        type: 'before',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      });
    });

    it('should fallback to existing methods when onInsertTask is not provided', async () => {
      render(
        <TaskItem 
          {...mockProps} 
          showInsertionIndicators={true}
          insertionMode="always"
        />
      );

      const taskElement = screen.getByTestId('task1');
      fireEvent.mouseEnter(taskElement);

      await waitFor(() => {
        const indicator = screen.getByTestId('insertion-indicator-after-task1');
        fireEvent.click(indicator);
      });

      // Should call the existing onAddTaskAfter method
      expect(mockProps.onAddTaskAfter).toHaveBeenCalledWith('task1', null);
    });
  });

  describe('Available Insertion Types', () => {
    it('should determine available insertion types based on task structure', () => {
      const taskWithSubtasks: Task = {
        ...mockTask,
        subtasks: [
          {
            id: 'subtask1',
            title: 'Subtask 1',
            description: '',
            content: '',
            status: 'To Do',
            assignees: [],
            subtasks: []
          }
        ]
      };

      render(
        <TaskItem 
          {...mockProps} 
          task={taskWithSubtasks}
          showInsertionIndicators={true}
          insertionMode="always"
        />
      );

      // Should support all insertion types for tasks with subtasks
      const taskElement = screen.getByTestId('task1');
      fireEvent.mouseEnter(taskElement);

      // The component should render indicators for before, after, and between_parent_child
      // This is tested indirectly through the mocked zones
    });
  });

  describe('Mouse Event Handling', () => {
    it('should track mouse position for zone detection', async () => {
      render(
        <TaskItem 
          {...mockProps} 
          showInsertionIndicators={true}
          insertionMode="hover"
        />
      );

      const taskElement = screen.getByTestId('task1');
      
      fireEvent.mouseEnter(taskElement);
      fireEvent.mouseMove(taskElement, { clientX: 50, clientY: 10 });

      // Mouse position should be tracked for zone detection
      // This is tested indirectly through the zone finding logic
    });
  });
});