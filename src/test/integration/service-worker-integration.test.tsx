/**
 * Service Worker Integration Tests
 * Tests the service worker registration and lifecycle management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { serviceWorkerManager } from '@/lib/pwa/serviceWorker';

// Mock service worker API
const mockServiceWorker = {
  register: vi.fn(),
  getRegistration: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
};

const mockRegistration = {
  installing: null,
  waiting: null,
  active: null,
  addEventListener: vi.fn(),
  update: vi.fn(),
  unregister: vi.fn(),
};

// Mock navigator
Object.defineProperty(global, 'navigator', {
  value: {
    serviceWorker: mockServiceWorker,
  },
  writable: true,
});

// Mock window
Object.defineProperty(global, 'window', {
  value: {
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  },
  writable: true,
});

describe('Service Worker Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockServiceWorker.register.mockResolvedValue(mockRegistration);
    mockServiceWorker.getRegistration.mockResolvedValue(mockRegistration);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Service Worker Support Detection', () => {
    it('should detect service worker support', () => {
      expect(serviceWorkerManager.isSupported()).toBe(true);
    });

    it('should handle missing service worker support', () => {
      const originalNavigator = global.navigator;
      // @ts-ignore
      global.navigator = {};
      
      expect(serviceWorkerManager.isSupported()).toBe(false);
      
      global.navigator = originalNavigator;
    });
  });

  describe('Service Worker Registration', () => {
    it('should register service worker successfully', async () => {
      const registration = await serviceWorkerManager.register();
      
      expect(mockServiceWorker.register).toHaveBeenCalledWith('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      });
      expect(registration).toBe(mockRegistration);
    });

    it('should handle registration failure', async () => {
      const error = new Error('Registration failed');
      mockServiceWorker.register.mockRejectedValue(error);
      
      const registration = await serviceWorkerManager.register();
      
      expect(registration).toBeNull();
    });

    it('should check for updates after registration', async () => {
      await serviceWorkerManager.register();
      
      expect(mockRegistration.update).toHaveBeenCalled();
    });
  });

  describe('Service Worker Lifecycle', () => {
    it('should handle update detection', async () => {
      const mockNewWorker = {
        state: 'installing',
        addEventListener: vi.fn(),
      };
      
      mockRegistration.installing = mockNewWorker;
      
      await serviceWorkerManager.register();
      
      // Simulate updatefound event
      const updateFoundCallback = mockRegistration.addEventListener.mock.calls
        .find(call => call[0] === 'updatefound')?.[1];
      
      if (updateFoundCallback) {
        updateFoundCallback();
        
        // Simulate state change to installed
        mockNewWorker.state = 'installed';
        const stateChangeCallback = mockNewWorker.addEventListener.mock.calls
          .find(call => call[0] === 'statechange')?.[1];
        
        if (stateChangeCallback) {
          stateChangeCallback();
        }
      }
      
      expect(mockNewWorker.addEventListener).toHaveBeenCalledWith('statechange', expect.any(Function));
    });

    it('should handle waiting service worker', async () => {
      mockRegistration.waiting = { postMessage: vi.fn() };
      
      await serviceWorkerManager.register();
      
      expect(global.window.dispatchEvent).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'sw-update-available'
        })
      );
    });
  });

  describe('Service Worker Updates', () => {
    it('should check for updates', async () => {
      await serviceWorkerManager.register();
      
      const result = await serviceWorkerManager.checkForUpdates();
      
      expect(mockRegistration.update).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle update check failure', async () => {
      mockRegistration.update.mockRejectedValue(new Error('Update failed'));
      
      await serviceWorkerManager.register();
      
      const result = await serviceWorkerManager.checkForUpdates();
      
      expect(result).toBe(false);
    });

    it('should force update and reload', async () => {
      const mockWaitingWorker = { postMessage: vi.fn() };
      mockRegistration.waiting = mockWaitingWorker;
      
      await serviceWorkerManager.register();
      await serviceWorkerManager.forceUpdate();
      
      expect(mockWaitingWorker.postMessage).toHaveBeenCalledWith({ type: 'SKIP_WAITING' });
    });
  });

  describe('Service Worker Unregistration', () => {
    it('should unregister service worker', async () => {
      mockRegistration.unregister.mockResolvedValue(true);
      
      const result = await serviceWorkerManager.unregister();
      
      expect(mockServiceWorker.getRegistration).toHaveBeenCalled();
      expect(mockRegistration.unregister).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle unregistration failure', async () => {
      mockRegistration.unregister.mockRejectedValue(new Error('Unregister failed'));
      
      const result = await serviceWorkerManager.unregister();
      
      expect(result).toBe(false);
    });
  });
});