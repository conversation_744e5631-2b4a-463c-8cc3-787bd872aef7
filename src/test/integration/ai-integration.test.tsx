import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GeminiClient } from '@/lib/ai/gemini'
import { ContextBuilder } from '@/lib/ai/context'

// Mock fetch for API calls
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('AI Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockFetch.mockClear()
  })

  describe('GeminiClient', () => {
    it('should generate tasks successfully', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify([
                {
                  title: 'Task 1',
                  description: 'Description 1',
                  content: '<p>Content 1</p>'
                },
                {
                  title: 'Task 2',
                  description: 'Description 2',
                  content: '<p>Content 2</p>'
                }
              ])
            }]
          }
        }]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const client = new GeminiClient()
      const tasks = await client.generateTasks('Test Project', 'Test Description')

      expect(tasks).toHaveLength(2)
      expect(tasks[0].title).toBe('Task 1')
      expect(tasks[1].title).toBe('Task 2')
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('generativelanguage.googleapis.com'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
    })

    it('should handle API errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const client = new GeminiClient()
      
      await expect(client.generateTasks('Test Project')).rejects.toThrow()
    })

    it('should generate subtasks correctly', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify([
                {
                  title: 'Subtask 1',
                  description: 'Subtask Description 1',
                  content: '<p>Subtask Content 1</p>'
                }
              ])
            }]
          }
        }]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const client = new GeminiClient()
      const subtasks = await client.generateSubtasks('Parent Task', 'Parent Description')

      expect(subtasks).toHaveLength(1)
      expect(subtasks[0].title).toBe('Subtask 1')
    })

    it('should generate questions for co-pilot mode', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: JSON.stringify([
                'What is your main objective?',
                'What resources do you have available?',
                'What is your timeline?'
              ])
            }]
          }
        }]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const client = new GeminiClient()
      const questions = await client.generateQuestions('Test context')

      expect(questions).toHaveLength(3)
      expect(questions[0]).toBe('What is your main objective?')
    })

    it('should elaborate content correctly', async () => {
      const mockResponse = {
        candidates: [{
          content: {
            parts: [{
              text: 'Elaborated content with more details and explanations.'
            }]
          }
        }]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })

      const client = new GeminiClient()
      const result = await client.elaborateContent('Original content', 'Context')

      expect(result).toBe('Elaborated content with more details and explanations.')
    })
  })

  describe('ContextBuilder', () => {
    const mockTasks = [
      {
        id: '1',
        title: 'Main Task',
        description: 'Main description',
        content: '<p>Main content</p>',
        status: 'todo' as const,
        assignees: [],
        subtasks: [
          {
            id: '1-1',
            title: 'Subtask',
            description: 'Sub description',
            content: '<p>Sub content</p>',
            status: 'todo' as const,
            assignees: [],
            subtasks: []
          }
        ]
      }
    ]

    it('should build context with strategy 1 (task path)', () => {
      const builder = new ContextBuilder()
      const context = builder.buildContextForAI('1-1', {
        strategies: { strategy1: true, strategy2: false, strategy3: false },
        mainProject: 'Test Project',
        mainProjectDescription: 'Test Description',
        tasks: mockTasks
      })

      expect(context).toContain('Test Project')
      expect(context).toContain('Main Task')
      expect(context).toContain('Subtask')
    })

    it('should build context with strategy 2 (intelligent summary)', () => {
      const builder = new ContextBuilder()
      const context = builder.buildContextForAI('1-1', {
        strategies: { strategy1: false, strategy2: true, strategy3: false },
        mainProject: 'Test Project',
        mainProjectDescription: 'Test Description',
        tasks: mockTasks
      })

      expect(context).toContain('Test Project')
      expect(context).toContain('Zusammenfassung')
    })

    it('should build context with strategy 3 (complete tree)', () => {
      const builder = new ContextBuilder()
      const context = builder.buildContextForAI('1-1', {
        strategies: { strategy1: false, strategy2: false, strategy3: true },
        mainProject: 'Test Project',
        mainProjectDescription: 'Test Description',
        tasks: mockTasks
      })

      expect(context).toContain('Test Project')
      expect(context).toContain('Vollständiger Projektbaum')
      expect(context).toContain('Main Task')
      expect(context).toContain('Subtask')
    })

    it('should combine multiple strategies', () => {
      const builder = new ContextBuilder()
      const context = builder.buildContextForAI('1-1', {
        strategies: { strategy1: true, strategy2: true, strategy3: false },
        mainProject: 'Test Project',
        mainProjectDescription: 'Test Description',
        tasks: mockTasks
      })

      expect(context).toContain('Test Project')
      expect(context).toContain('Main Task')
      expect(context).toContain('Zusammenfassung')
    })
  })
})