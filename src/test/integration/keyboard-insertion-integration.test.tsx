import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { TaskList } from '@/components/TaskList';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock the useInsertionZones hook
vi.mock('@/hooks/useInsertionZones', () => ({
  useInsertionZones: () => ({
    zones: [],
    isCalculating: false,
    findZoneAtPoint: vi.fn(),
    getZonesByType: vi.fn(),
    clearCache: vi.fn(),
    forceRecalculate: vi.fn(),
    updateZones: vi.fn()
  })
}));

// Mock the useKeyboardInsertion hook
vi.mock('@/hooks/useKeyboardInsertion', () => ({
  useKeyboardInsertion: () => ({
    focusedTask: null,
    keyboardMode: false,
    availableInsertionPoints: [],
    currentInsertionIndex: 0,
    updateFocusedTask: vi.fn(),
    navigateToTask: vi.fn(),
    setKeyboardMode: vi.fn(),
    activeShortcuts: {
      insertAfter: 'Enter',
      insertBefore: 'Shift+Enter',
      insertSubtask: 'Tab+Enter',
      insertBetweenParentChild: 'Ctrl+Enter'
    }
  })
}));

describe('Keyboard Insertion Integration', () => {
  const mockTasks: Task[] = [
    {
      id: 'task-1',
      title: 'Task 1',
      description: 'Description 1',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: [
        {
          id: 'task-1-1',
          title: 'Subtask 1.1',
          description: 'Subtask description',
          content: '',
          status: 'To Do',
          assignees: [],
          subtasks: []
        }
      ]
    },
    {
      id: 'task-2',
      title: 'Task 2',
      description: 'Description 2',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    }
  ];

  const mockProps = {
    tasks: mockTasks,
    onUpdateTask: vi.fn(),
    onOpenSolveModal: vi.fn(),
    onAddTask: vi.fn(),
    onAddTaskAfter: vi.fn(),
    onDeleteTask: vi.fn(),
    onBreakdownTask: vi.fn(),
    loading: {},
    setLoading: vi.fn(),
    onInsertTask: vi.fn(),
    showInsertionIndicators: true,
    insertionMode: 'hover' as const
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TaskList with keyboard insertion', () => {
    it('should render tasks with focusable elements', () => {
      render(<TaskList {...mockProps} />);
      
      // Check that tasks are rendered with data-task-id attributes
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
      expect(screen.getByTestId('task-2')).toBeInTheDocument();
      expect(screen.getByTestId('task-1-1')).toBeInTheDocument();
    });

    it('should make task cards focusable', () => {
      render(<TaskList {...mockProps} />);
      
      const taskCards = screen.getAllByRole('generic').filter(el => 
        el.hasAttribute('tabindex') && el.getAttribute('tabindex') === '0'
      );
      
      // Should have focusable task cards
      expect(taskCards.length).toBeGreaterThan(0);
    });

    it('should call onInsertTask when insertion is triggered', async () => {
      const user = userEvent.setup();
      render(<TaskList {...mockProps} />);
      
      // Find an insertion indicator (if visible)
      const insertionButtons = screen.queryAllByRole('button');
      const insertionButton = insertionButtons.find(button => 
        button.getAttribute('aria-label')?.includes('Insert') ||
        button.textContent?.includes('+')
      );
      
      if (insertionButton) {
        await user.click(insertionButton);
        expect(mockProps.onInsertTask).toHaveBeenCalled();
      }
    });

    it('should handle keyboard focus on task elements', async () => {
      const user = userEvent.setup();
      render(<TaskList {...mockProps} />);
      
      const taskElement = screen.getByTestId('task-1');
      
      // Focus the task element
      await user.click(taskElement);
      
      // Verify the element can receive focus
      expect(taskElement).toHaveAttribute('tabindex', '0');
    });

    it('should support keyboard navigation between tasks', async () => {
      const user = userEvent.setup();
      render(<TaskList {...mockProps} />);
      
      const firstTask = screen.getByTestId('task-1');
      const secondTask = screen.getByTestId('task-2');
      
      // Focus first task
      await user.click(firstTask);
      
      // Tab to next focusable element
      await user.tab();
      
      // Verify navigation is possible
      expect(document.activeElement).toBeDefined();
    });
  });

  describe('Insertion position handling', () => {
    it('should handle different insertion position types', () => {
      const insertionPositions: InsertionPosition[] = [
        {
          type: 'before',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0
        },
        {
          type: 'after',
          targetTaskId: 'task-1',
          parentId: null,
          level: 0
        },
        {
          type: 'between_parent_child',
          targetTaskId: 'task-1',
          parentId: 'task-1',
          level: 1
        }
      ];

      render(<TaskList {...mockProps} />);

      // Simulate insertion calls for each position type
      insertionPositions.forEach(position => {
        mockProps.onInsertTask(position);
      });

      expect(mockProps.onInsertTask).toHaveBeenCalledTimes(3);
      expect(mockProps.onInsertTask).toHaveBeenCalledWith(insertionPositions[0]);
      expect(mockProps.onInsertTask).toHaveBeenCalledWith(insertionPositions[1]);
      expect(mockProps.onInsertTask).toHaveBeenCalledWith(insertionPositions[2]);
    });

    it('should fallback to onAddTaskAfter when onInsertTask is not provided', () => {
      const propsWithoutInsertTask = {
        ...mockProps,
        onInsertTask: undefined
      };

      render(<TaskList {...propsWithoutInsertTask} />);

      // The component should still work without onInsertTask
      expect(screen.getByTestId('task-1')).toBeInTheDocument();
    });
  });

  describe('Insertion indicators', () => {
    it('should show insertion indicators when showInsertionIndicators is true', () => {
      render(<TaskList {...mockProps} showInsertionIndicators={true} />);
      
      // Look for insertion indicators (they might be hidden by default)
      const insertionElements = screen.queryAllByRole('button').filter(button =>
        button.getAttribute('aria-label')?.includes('Insert') ||
        button.textContent?.includes('+')
      );
      
      // Insertion indicators should be present in the DOM (even if hidden)
      expect(insertionElements.length).toBeGreaterThanOrEqual(0);
    });

    it('should hide insertion indicators when showInsertionIndicators is false', () => {
      render(<TaskList {...mockProps} showInsertionIndicators={false} />);
      
      // Insertion indicators should not be visible
      const insertionElements = screen.queryAllByRole('button').filter(button =>
        button.getAttribute('aria-label')?.includes('Insert')
      );
      
      expect(insertionElements.length).toBe(0);
    });

    it('should support different insertion modes', () => {
      const modes: Array<'hover' | 'always' | 'keyboard'> = ['hover', 'always', 'keyboard'];
      
      modes.forEach(mode => {
        const { unmount } = render(<TaskList {...mockProps} insertionMode={mode} />);
        
        // Component should render without errors for each mode
        expect(screen.getByTestId('task-1')).toBeInTheDocument();
        
        unmount();
      });
    });
  });

  describe('Accessibility', () => {
    it('should provide proper ARIA attributes for focusable elements', () => {
      render(<TaskList {...mockProps} />);
      
      const taskElements = screen.getAllByTestId(/task-/);
      
      taskElements.forEach(element => {
        // Each task should be focusable
        expect(element).toHaveAttribute('tabindex');
        
        // Should have proper data attributes for identification
        expect(element).toHaveAttribute('data-task-id');
      });
    });

    it('should support keyboard navigation with proper focus management', async () => {
      const user = userEvent.setup();
      render(<TaskList {...mockProps} />);
      
      const firstTask = screen.getByTestId('task-1');
      
      // Should be able to focus task elements
      await user.click(firstTask);
      
      // Should be able to navigate with keyboard
      await user.keyboard('{Tab}');
      
      // Focus should move to next focusable element
      expect(document.activeElement).toBeDefined();
    });

    it('should provide visual focus indicators', () => {
      render(<TaskList {...mockProps} />);
      
      const taskElements = screen.getAllByTestId(/task-/);
      
      taskElements.forEach(element => {
        // Should have focus styles in className
        expect(element.className).toContain('focus:');
      });
    });
  });

  describe('Performance', () => {
    it('should handle large task lists efficiently', () => {
      const largeTasks: Task[] = Array.from({ length: 50 }, (_, i) => ({
        id: `task-${i}`,
        title: `Task ${i}`,
        description: `Description ${i}`,
        content: '',
        status: 'To Do' as const,
        assignees: [],
        subtasks: []
      }));

      const largeTaskProps = {
        ...mockProps,
        tasks: largeTasks
      };

      const startTime = performance.now();
      render(<TaskList {...largeTaskProps} />);
      const endTime = performance.now();

      // Should render within reasonable time (less than 100ms)
      expect(endTime - startTime).toBeLessThan(100);
      
      // Should still render all tasks
      expect(screen.getByTestId('task-0')).toBeInTheDocument();
      expect(screen.getByTestId('task-49')).toBeInTheDocument();
    });

    it('should not cause excessive re-renders on hover', async () => {
      const user = userEvent.setup();
      render(<TaskList {...mockProps} />);
      
      const taskElement = screen.getByTestId('task-1');
      
      // Simulate multiple hover events
      for (let i = 0; i < 10; i++) {
        await user.hover(taskElement);
        await user.unhover(taskElement);
      }
      
      // Component should still be responsive
      expect(taskElement).toBeInTheDocument();
    });
  });
});