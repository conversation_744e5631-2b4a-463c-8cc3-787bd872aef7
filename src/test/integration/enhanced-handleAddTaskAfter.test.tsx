import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { useState } from 'react';
import type { Task, InsertionPosition } from '@/lib/types';

// Mock component to test the enhanced handleAddTaskAfter functionality
const TestComponent = () => {
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: 'task1',
      title: 'Task 1',
      description: 'Description 1',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: [
        {
          id: 'subtask1',
          title: 'Subtask 1',
          description: 'Subtask Description 1',
          content: '',
          status: 'To Do',
          assignees: [],
          subtasks: []
        }
      ]
    },
    {
      id: 'task2',
      title: 'Task 2',
      description: 'Description 2',
      content: '',
      status: 'To Do',
      assignees: [],
      subtasks: []
    }
  ]);

  // Helper functions for different insertion types (copied from page.tsx)
  const insertTaskBefore = (tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
    if (!parentId) {
      const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
      if (targetIndex !== -1) {
        const newTasks = [...tasks];
        newTasks.splice(targetIndex, 0, newTask);
        return newTasks;
      }
      return tasks.map(task => ({
        ...task,
        subtasks: insertTaskBefore(task.subtasks || [], targetTaskId, newTask, parentId)
      }));
    } else {
      return tasks.map(task => {
        if (task.id === parentId) {
          const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
          if (targetIndex !== -1) {
            const newSubtasks = [...(task.subtasks || [])];
            newSubtasks.splice(targetIndex, 0, newTask);
            return { ...task, subtasks: newSubtasks };
          }
        }
        if (task.subtasks) {
          return { ...task, subtasks: insertTaskBefore(task.subtasks, targetTaskId, newTask, parentId) };
        }
        return task;
      });
    }
  };

  const insertTaskAfter = (tasks: Task[], targetTaskId: string, newTask: Task, parentId: string | null): Task[] => {
    if (!parentId) {
      const targetIndex = tasks.findIndex(t => t.id === targetTaskId);
      if (targetIndex !== -1) {
        const newTasks = [...tasks];
        newTasks.splice(targetIndex + 1, 0, newTask);
        return newTasks;
      }
      return tasks.map(task => ({
        ...task,
        subtasks: insertTaskAfter(task.subtasks || [], targetTaskId, newTask, parentId)
      }));
    } else {
      return tasks.map(task => {
        if (task.id === parentId) {
          const targetIndex = (task.subtasks || []).findIndex(t => t.id === targetTaskId);
          if (targetIndex !== -1) {
            const newSubtasks = [...(task.subtasks || [])];
            newSubtasks.splice(targetIndex + 1, 0, newTask);
            return { ...task, subtasks: newSubtasks };
          }
        }
        if (task.subtasks) {
          return { ...task, subtasks: insertTaskAfter(task.subtasks, targetTaskId, newTask, parentId) };
        }
        return task;
      });
    }
  };

  const insertTaskBetweenParentChild = (tasks: Task[], parentTaskId: string, newTask: Task): Task[] => {
    return tasks.map(task => {
      if (task.id === parentTaskId) {
        return {
          ...task,
          subtasks: [newTask, ...(task.subtasks || [])]
        };
      }
      if (task.subtasks) {
        return {
          ...task,
          subtasks: insertTaskBetweenParentChild(task.subtasks, parentTaskId, newTask)
        };
      }
      return task;
    });
  };

  // Enhanced handleAddTaskAfter function
  const handleAddTaskAfter = (
    afterIdOrPosition: string | InsertionPosition,
    parentId: string | null = null,
    title = 'New Task',
    description = 'New Description'
  ) => {
    const newTask: Task = {
      id: `new-${Date.now()}`,
      title,
      description,
      subtasks: [],
      content: '',
      status: 'To Do',
      assignees: []
    };

    setTasks(prevTasks => {
      // Handle InsertionPosition parameter
      if (typeof afterIdOrPosition === 'object') {
        const position = afterIdOrPosition;
        
        switch (position.type) {
          case 'before':
            return insertTaskBefore(prevTasks, position.targetTaskId, newTask, position.parentId);
          
          case 'after':
            return insertTaskAfter(prevTasks, position.targetTaskId, newTask, position.parentId);
          
          case 'between_parent_child':
            return insertTaskBetweenParentChild(prevTasks, position.targetTaskId, newTask);
          
          default:
            console.warn('Unknown insertion type:', position.type);
            return prevTasks;
        }
      }

      // Legacy string-based handling for backward compatibility
      const afterId = afterIdOrPosition as string;
      
      if (afterId === '__FIRST__') {
        if (!parentId) {
          return [newTask, ...prevTasks];
        }
      }

      // Regular case: Add after specific task
      const addTask = (taskList: Task[]): Task[] => {
          const index = taskList.findIndex(t => t.id === afterId);
          if (index !== -1) {
              const newTasks = [...taskList];
              newTasks.splice(index + 1, 0, newTask);
              return newTasks;
          }
          return taskList.map(t => ({...t, subtasks: addTask(t.subtasks || [])}));
      };

      return addTask(prevTasks);
    });
  };

  return (
    <div>
      <div data-testid="task-count">{tasks.length}</div>
      <div data-testid="subtask-count">{tasks[0]?.subtasks?.length || 0}</div>
      
      <button
        data-testid="insert-before-task2"
        onClick={() => handleAddTaskAfter({
          type: 'before',
          targetTaskId: 'task2',
          parentId: null,
          level: 0
        })}
      >
        Insert Before Task 2
      </button>
      
      <button
        data-testid="insert-after-task1"
        onClick={() => handleAddTaskAfter({
          type: 'after',
          targetTaskId: 'task1',
          parentId: null,
          level: 0
        })}
      >
        Insert After Task 1
      </button>
      
      <button
        data-testid="insert-between-parent-child"
        onClick={() => handleAddTaskAfter({
          type: 'between_parent_child',
          targetTaskId: 'task1',
          parentId: null,
          level: 1
        })}
      >
        Insert Between Parent and Child
      </button>
      
      <button
        data-testid="legacy-insert-after"
        onClick={() => handleAddTaskAfter('task1')}
      >
        Legacy Insert After Task 1
      </button>
      
      <button
        data-testid="legacy-insert-first"
        onClick={() => handleAddTaskAfter('__FIRST__')}
      >
        Legacy Insert First
      </button>
      
      <div data-testid="task-titles">
        {tasks.map(task => (
          <div key={task.id}>
            {task.title}
            {task.subtasks.map(subtask => (
              <div key={subtask.id} style={{ marginLeft: '20px' }}>
                {subtask.title}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

describe('Enhanced handleAddTaskAfter Integration', () => {
  it('should insert task before target task using InsertionPosition', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('2');
    
    fireEvent.click(screen.getByTestId('insert-before-task2'));
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('3');
    
    const taskTitles = screen.getByTestId('task-titles');
    expect(taskTitles).toHaveTextContent('Task 1');
    expect(taskTitles).toHaveTextContent('New Task');
    expect(taskTitles).toHaveTextContent('Task 2');
  });

  it('should insert task after target task using InsertionPosition', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('2');
    
    fireEvent.click(screen.getByTestId('insert-after-task1'));
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('3');
    
    const taskTitles = screen.getByTestId('task-titles');
    expect(taskTitles).toHaveTextContent('Task 1');
    expect(taskTitles).toHaveTextContent('New Task');
    expect(taskTitles).toHaveTextContent('Task 2');
  });

  it('should insert task between parent and child using InsertionPosition', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('subtask-count')).toHaveTextContent('1');
    
    fireEvent.click(screen.getByTestId('insert-between-parent-child'));
    
    expect(screen.getByTestId('subtask-count')).toHaveTextContent('2');
    
    const taskTitles = screen.getByTestId('task-titles');
    expect(taskTitles).toHaveTextContent('Task 1');
    expect(taskTitles).toHaveTextContent('New Task');
    expect(taskTitles).toHaveTextContent('Subtask 1');
  });

  it('should maintain backward compatibility with string parameters', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('2');
    
    fireEvent.click(screen.getByTestId('legacy-insert-after'));
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('3');
    
    const taskTitles = screen.getByTestId('task-titles');
    expect(taskTitles).toHaveTextContent('Task 1');
    expect(taskTitles).toHaveTextContent('New Task');
    expect(taskTitles).toHaveTextContent('Task 2');
  });

  it('should handle __FIRST__ special case', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('2');
    
    fireEvent.click(screen.getByTestId('legacy-insert-first'));
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('3');
    
    const taskTitles = screen.getByTestId('task-titles');
    const textContent = taskTitles.textContent;
    
    // New task should be first
    expect(textContent?.indexOf('New Task')).toBeLessThan(textContent?.indexOf('Task 1') || Infinity);
  });

  it('should properly reorder tasks when inserting at specific positions', () => {
    render(<TestComponent />);
    
    // Insert before task2
    fireEvent.click(screen.getByTestId('insert-before-task2'));
    
    // Insert after task1
    fireEvent.click(screen.getByTestId('insert-after-task1'));
    
    expect(screen.getByTestId('task-count')).toHaveTextContent('4');
    
    const taskTitles = screen.getByTestId('task-titles');
    const textContent = taskTitles.textContent || '';
    
    // Check order: Task 1, New Task (after task1), New Task (before task2), Task 2
    const task1Index = textContent.indexOf('Task 1');
    const task2Index = textContent.indexOf('Task 2');
    const newTaskIndices = [];
    let searchIndex = 0;
    
    while (true) {
      const index = textContent.indexOf('New Task', searchIndex);
      if (index === -1) break;
      newTaskIndices.push(index);
      searchIndex = index + 1;
    }
    
    expect(newTaskIndices).toHaveLength(2);
    expect(task1Index).toBeLessThan(newTaskIndices[0]);
    expect(newTaskIndices[0]).toBeLessThan(newTaskIndices[1]);
    expect(newTaskIndices[1]).toBeLessThan(task2Index);
  });
});