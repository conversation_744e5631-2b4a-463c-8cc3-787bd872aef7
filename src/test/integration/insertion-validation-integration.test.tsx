/**
 * Integration tests for insertion validation system with existing components
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import type { Task, InsertionPosition } from '@/lib/types';
import { TaskList } from '@/components/TaskList';
import { 
  validateInsertionPosition, 
  canInsertAtPosition,
  createInsertionValidator 
} from '@/lib/utils/insertionValidation';

// Mock the AI module
vi.mock('@/lib/ai', () => ({
  generateTaskBreakdown: vi.fn(),
  elaborateTask: vi.fn(),
  solveTask: vi.fn()
}));

// Test data
const createTestTask = (id: string, title: string, subtasks: Task[] = []): Task => ({
  id,
  title,
  description: `Description for ${title}`,
  content: '',
  status: 'To Do',
  assignees: [],
  subtasks
});

const createTestTasks = (): Task[] => [
  createTestTask('task1', 'Task 1', [
    createTestTask('task1-1', 'Task 1.1'),
    createTestTask('task1-2', 'Task 1.2')
  ]),
  createTestTask('task2', 'Task 2'),
  createTestTask('task3', 'Task 3')
];

describe('Insertion Validation Integration', () => {
  let testTasks: Task[];
  let mockOnTasksChange: ReturnType<typeof vi.fn>;
  let mockOnInsertTask: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    testTasks = createTestTasks();
    mockOnTasksChange = vi.fn();
    mockOnInsertTask = vi.fn();
  });

  describe('Integration with TaskList Component', () => {
    it('should validate insertion positions before allowing insertion', async () => {
      const validator = createInsertionValidator({ maxDepth: 2 });
      
      // Mock the insertion handler to include validation
      const validatedInsertHandler = vi.fn((position: InsertionPosition) => {
        const validation = validator.validatePosition(position, testTasks);
        if (validation.isValid) {
          mockOnInsertTask(position);
        } else {
          console.warn('Invalid insertion:', validation.errors);
        }
      });

      render(
        <TaskList
          tasks={testTasks}
          onTasksChange={mockOnTasksChange}
          onInsertTask={validatedInsertHandler}
          showInsertionIndicators={true}
        />
      );

      // Simulate a valid insertion
      const validPosition: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      validatedInsertHandler(validPosition);
      expect(mockOnInsertTask).toHaveBeenCalledWith(validPosition);

      // Simulate an invalid insertion (exceeds max depth)
      const invalidPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1-2', // Would create depth 3, exceeding maxDepth 2
        parentId: 'task1-2',
        level: 3
      };

      validatedInsertHandler(invalidPosition);
      expect(mockOnInsertTask).not.toHaveBeenCalledWith(invalidPosition);
    });

    it('should provide alternative suggestions for invalid insertions', () => {
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const validation = validateInsertionPosition(position, testTasks, {
        allowedPositions: ['before', 'after'] // Exclude between_parent_child
      });

      expect(validation.isValid).toBe(false);
      expect(validation.suggestedAlternatives).toBeDefined();
      expect(validation.suggestedAlternatives!.length).toBeGreaterThan(0);
      
      // Alternatives should be valid
      validation.suggestedAlternatives!.forEach(alternative => {
        const altValidation = validateInsertionPosition(alternative, testTasks, {
          allowedPositions: ['before', 'after']
        });
        expect(altValidation.isValid).toBe(true);
      });
    });
  });

  describe('Validation with Real Task Operations', () => {
    it('should validate before actual task insertion', () => {
      const positions: InsertionPosition[] = [
        {
          type: 'after',
          targetTaskId: 'task1',
          parentId: null,
          level: 0
        },
        {
          type: 'before',
          targetTaskId: 'task2',
          parentId: null,
          level: 0
        },
        {
          type: 'between_parent_child',
          targetTaskId: 'task1',
          parentId: 'task1',
          level: 1
        }
      ];

      positions.forEach(position => {
        const isValid = canInsertAtPosition(position, testTasks);
        expect(typeof isValid).toBe('boolean');
        
        if (isValid) {
          // If validation passes, the position should be structurally sound
          expect(position.targetTaskId).toBeTruthy();
          expect(position.type).toMatch(/^(before|after|between_parent_child)$/);
          expect(position.level).toBeGreaterThanOrEqual(0);
        }
      });
    });

    it('should handle edge cases in task hierarchy', () => {
      // Test with deeply nested tasks
      const deepTasks: Task[] = [
        createTestTask('root', 'Root', [
          createTestTask('level1', 'Level 1', [
            createTestTask('level2', 'Level 2', [
              createTestTask('level3', 'Level 3', [
                createTestTask('level4', 'Level 4')
              ])
            ])
          ])
        ])
      ];

      const deepPosition: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'level4',
        parentId: 'level4',
        level: 5
      };

      const validation = validateInsertionPosition(deepPosition, deepTasks, {
        maxDepth: 4
      });

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => error.includes('exceeds maximum allowed depth'))).toBe(true);
      expect(validation.suggestedAlternatives).toBeDefined();
    });

    it('should validate sibling count constraints', () => {
      // Create a task with many siblings
      const taskWithManySiblings = createTestTask('parent', 'Parent', 
        Array.from({ length: 10 }, (_, i) => 
          createTestTask(`child-${i}`, `Child ${i}`)
        )
      );

      const tasksWithManySiblings = [taskWithManySiblings];

      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'parent',
        parentId: 'parent',
        level: 1
      };

      const validation = validateInsertionPosition(position, tasksWithManySiblings, {
        maxSiblings: 5 // Restrict to 5 siblings
      });

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => error.includes('maximum sibling count'))).toBe(true);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle malformed task data gracefully', () => {
      const malformedTasks = [
        {
          id: 'task1',
          title: 'Task 1',
          // Missing required fields
        } as Task
      ];

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      // Should not throw even with malformed data
      expect(() => {
        validateInsertionPosition(position, malformedTasks);
      }).not.toThrow();
    });

    it('should provide meaningful error messages', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'nonexistent',
        parentId: null,
        level: 0
      };

      const validation = validateInsertionPosition(position, testTasks);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain("Target task with ID 'nonexistent' not found");
    });

    it('should handle concurrent validation requests', async () => {
      const positions: InsertionPosition[] = Array.from({ length: 10 }, (_, i) => ({
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      }));

      // Validate multiple positions concurrently
      const validationPromises = positions.map(position =>
        Promise.resolve(validateInsertionPosition(position, testTasks))
      );

      const results = await Promise.all(validationPromises);

      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.isValid).toBe(true);
      });
    });
  });

  describe('Performance Considerations', () => {
    it('should validate large task trees efficiently', () => {
      // Create a large task tree
      const largeTasks: Task[] = Array.from({ length: 100 }, (_, i) =>
        createTestTask(`task-${i}`, `Task ${i}`, 
          Array.from({ length: 5 }, (_, j) =>
            createTestTask(`task-${i}-${j}`, `Task ${i}.${j}`)
          )
        )
      );

      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task-50',
        parentId: null,
        level: 0
      };

      const startTime = performance.now();
      const validation = validateInsertionPosition(position, largeTasks);
      const endTime = performance.now();

      expect(validation.isValid).toBe(true);
      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
    });

    it('should cache validation results for repeated queries', () => {
      const validator = createInsertionValidator();
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      // First validation
      const startTime1 = performance.now();
      const result1 = validator.validatePosition(position, testTasks);
      const endTime1 = performance.now();

      // Second validation (should be faster if cached)
      const startTime2 = performance.now();
      const result2 = validator.validatePosition(position, testTasks);
      const endTime2 = performance.now();

      expect(result1.isValid).toBe(result2.isValid);
      // Note: Actual caching implementation would make this faster
      // This test documents the expected behavior
    });
  });

  describe('Accessibility and User Experience', () => {
    it('should provide user-friendly error messages', () => {
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: 'task1',
        level: 1
      };

      const validation = validateInsertionPosition(position, testTasks, {
        parentRestrictions: ['task1']
      });

      expect(validation.isValid).toBe(false);
      expect(validation.errors.some(error => 
        error.includes('restricted parent task')
      )).toBe(true);
    });

    it('should provide helpful warnings for suboptimal insertions', () => {
      const position: InsertionPosition = {
        type: 'after',
        targetTaskId: 'task1-2',
        parentId: 'task1',
        level: 8 // Deep nesting
      };

      const validation = validateInsertionPosition(position, testTasks, {
        maxDepth: 10
      });

      expect(validation.isValid).toBe(true);
      expect(validation.warnings.some(warning => 
        warning.includes('may impact performance')
      )).toBe(true);
    });

    it('should suggest practical alternatives', () => {
      const position: InsertionPosition = {
        type: 'between_parent_child',
        targetTaskId: 'task1',
        parentId: null,
        level: 0
      };

      const validation = validateInsertionPosition(position, testTasks, {
        allowedPositions: ['before', 'after']
      });

      expect(validation.isValid).toBe(false);
      expect(validation.suggestedAlternatives).toBeDefined();
      
      const alternatives = validation.suggestedAlternatives!;
      expect(alternatives.length).toBeGreaterThan(0);
      expect(alternatives.every(alt => 
        ['before', 'after'].includes(alt.type)
      )).toBe(true);
    });
  });
});