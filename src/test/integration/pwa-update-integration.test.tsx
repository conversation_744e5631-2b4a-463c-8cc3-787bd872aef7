import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PWAUpdateNotification } from '@/components/PWAUpdateNotification';
import { usePWA } from '@/hooks/usePWA';

// Mock the usePWA hook
vi.mock('@/hooks/usePWA');
const mockUsePWA = vi.mocked(usePWA);

// Mock service worker
const mockServiceWorker = {
  register: vi.fn(),
  getRegistration: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  controller: null,
};

const mockRegistration = {
  waiting: null,
  installing: null,
  active: null,
  update: vi.fn(),
  addEventListener: vi.fn(),
  postMessage: vi.fn(),
  navigationPreload: {} as any,
  onupdatefound: null,
  pushManager: {} as any,
  scope: '',
  unregister: vi.fn(),
  showNotification: vi.fn(),
  getNotifications: vi.fn(),
} as ServiceWorkerRegistration;

// Setup global mocks
Object.defineProperty(window, 'navigator', {
  value: {
    serviceWorker: mockServiceWorker,
    onLine: true,
  },
  writable: true,
});

describe('PWA Update Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock timers to control delays
    vi.useFakeTimers();
    
    // Reset session storage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('PWAUpdateNotification Component', () => {
    it('should not render when no update is available', () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: false,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: null,
      });

      render(<PWAUpdateNotification />);
      
      expect(screen.queryByText('Update verfügbar')).not.toBeInTheDocument();
    });

    it('should render update notification when update is available', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {
          version: 'v2.0.0',
          releaseNotes: ['New features', 'Bug fixes'],
        },
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Update verfügbar')).toBeInTheDocument();
      });
      
      expect(screen.getByText('Eine neue Version von KI Projekt-Planer ist verfügbar')).toBeInTheDocument();
      expect(screen.getByText('Jetzt aktualisieren')).toBeInTheDocument();
      expect(screen.getByText('Später')).toBeInTheDocument();
    });

    it('should show updating state when update is in progress', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: true,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Update wird installiert...')).toBeInTheDocument();
      });
      
      expect(screen.getByText('Bitte schließen Sie die App nicht.')).toBeInTheDocument();
    });

    it('should call updateApp when update button is clicked', async () => {
      const mockUpdateApp = vi.fn().mockResolvedValue(undefined);
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: mockUpdateApp,
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Jetzt aktualisieren')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('Jetzt aktualisieren'));
      
      expect(mockUpdateApp).toHaveBeenCalled();
    });

    it('should handle update errors gracefully', async () => {
      const mockUpdateApp = vi.fn().mockRejectedValue(new Error('Update failed'));
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: mockUpdateApp,
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Jetzt aktualisieren')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('Jetzt aktualisieren'));
      
      await waitFor(() => {
        expect(screen.getByText('Update fehlgeschlagen')).toBeInTheDocument();
      });
      
      expect(screen.getByText('Update fehlgeschlagen. Bitte versuchen Sie es später erneut.')).toBeInTheDocument();
    });

    it('should dismiss notification when later button is clicked', async () => {
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Später')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('Später'));
      
      await waitFor(() => {
        expect(screen.queryByText('Update verfügbar')).not.toBeInTheDocument();
      });
    });

    it('should not show notification if dismissed in session', () => {
      (window.sessionStorage.getItem as any).mockReturnValue('true');
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: vi.fn(),
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      expect(screen.queryByText('Update verfügbar')).not.toBeInTheDocument();
    });
  });

  describe('Update Flow Integration', () => {
    it('should complete full update flow successfully', async () => {
      const mockUpdateApp = vi.fn().mockResolvedValue(undefined);
      const onUpdateApplied = vi.fn();
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: true,
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: mockUpdateApp,
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification onUpdateApplied={onUpdateApplied} />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      // Wait for notification to appear
      await waitFor(() => {
        expect(screen.getByText('Jetzt aktualisieren')).toBeInTheDocument();
      });
      
      // Click update button
      fireEvent.click(screen.getByText('Jetzt aktualisieren'));
      
      // Verify update was called
      expect(mockUpdateApp).toHaveBeenCalled();
      
      // Wait for success state
      await waitFor(() => {
        expect(screen.getByText('Update erfolgreich!')).toBeInTheDocument();
      });
      
      // Verify callback is called after delay
      await waitFor(() => {
        expect(onUpdateApplied).toHaveBeenCalled();
      }, { timeout: 2000 });
    });

    it('should handle network errors during update', async () => {
      const mockUpdateApp = vi.fn().mockRejectedValue(new Error('Network error'));
      
      mockUsePWA.mockReturnValue({
        isInstalled: true,
        isOnline: false, // Simulate offline
        updateAvailable: true,
        updateInfo: {},
        isUpdating: false,
        updateApp: mockUpdateApp,
        checkForUpdates: vi.fn(),
        getUpdateSize: vi.fn(() => '< 1 MB'),
        registration: mockRegistration,
      });

      render(<PWAUpdateNotification />);
      
      // Advance timers to trigger the notification display
      vi.advanceTimersByTime(2000);
      
      await waitFor(() => {
        expect(screen.getByText('Jetzt aktualisieren')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('Jetzt aktualisieren'));
      
      await waitFor(() => {
        expect(screen.getByText('Update fehlgeschlagen')).toBeInTheDocument();
      });
      
      // Should show retry button
      expect(screen.getByText('Erneut versuchen')).toBeInTheDocument();
    });
  });

  describe('Service Worker Integration', () => {
    it('should register service worker and detect updates', async () => {
      const testRegistration = {
        ...mockRegistration,
        waiting: { postMessage: vi.fn() },
        addEventListener: vi.fn(),
        update: vi.fn().mockResolvedValue(undefined),
      };

      mockServiceWorker.register.mockResolvedValue(testRegistration);
      mockServiceWorker.getRegistration.mockResolvedValue(testRegistration);

      // Verify the mocks are set up correctly
      expect(mockServiceWorker.register).toBeDefined();
    });

    it('should handle service worker registration failure', async () => {
      mockServiceWorker.register.mockRejectedValue(new Error('Registration failed'));
      
      // Test error handling in usePWA hook
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      try {
        // This would normally test the actual hook behavior
        // For now, we verify the mock is set up correctly
        expect(mockServiceWorker.register).toBeDefined();
      } catch (error) {
        // Expected to handle gracefully
      }
      
      consoleSpy.mockRestore();
    });
  });
});