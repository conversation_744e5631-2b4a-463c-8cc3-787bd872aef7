/**
 * Cross-platform tests for different mobile browsers and devices
 * Tests PWA functionality across various mobile environments
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Test utilities for mobile simulation
import { simulateMobileDevice, simulateTouchEvents } from '../utils/mobile-simulation';

// Components to test
import TaskList from '@/components/TaskList';
import { MobileTaskItem } from '@/components/MobileTaskItem';
import { MobileBottomNavigation } from '@/components/MobileBottomNavigation';
import { PWAInstallPrompt } from '@/components/PWAInstallPrompt';

// Mock data
const mockTasks = [
  {
    id: 'task-1',
    title: 'Mobile Test Task',
    description: 'Testing mobile functionality',
    content: 'Task content',
    status: 'pending' as const,
    assignees: [],
    subtasks: []
  }
];

// Browser/Device configurations for testing
const mobileConfigurations = [
  {
    name: 'iPhone 12 Safari',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 390, height: 844 },
    touchSupport: true,
    standalone: false
  },
  {
    name: 'iPhone 12 PWA',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 390, height: 844 },
    touchSupport: true,
    standalone: true
  },
  {
    name: 'Android Chrome',
    userAgent: 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
    viewport: { width: 360, height: 800 },
    touchSupport: true,
    standalone: false
  },
  {
    name: 'Android PWA',
    userAgent: 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
    viewport: { width: 360, height: 800 },
    touchSupport: true,
    standalone: true
  },
  {
    name: 'iPad Safari',
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    viewport: { width: 768, height: 1024 },
    touchSupport: true,
    standalone: false
  },
  {
    name: 'Samsung Internet',
    userAgent: 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36',
    viewport: { width: 360, height: 800 },
    touchSupport: true,
    standalone: false
  }
];

describe('Cross-Platform Mobile Browser Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Reset viewport and user agent
    Object.defineProperty(window, 'innerWidth', { value: 1024, writable: true });
    Object.defineProperty(window, 'innerHeight', { value: 768, writable: true });
    Object.defineProperty(navigator, 'userAgent', { 
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      writable: true 
    });
  });

  describe.each(mobileConfigurations)('$name', (config) => {
    beforeEach(() => {
      simulateMobileDevice(config);
    });

    it('should render task list correctly on mobile viewport', async () => {
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Mobile Test Project"
        />
      );

      // Check if mobile-optimized layout is applied
      const taskList = screen.getByRole('list');
      expect(taskList).toBeInTheDocument();

      // Verify touch-friendly button sizes
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const styles = window.getComputedStyle(button);
        const minHeight = parseInt(styles.minHeight);
        expect(minHeight).toBeGreaterThanOrEqual(44); // iOS minimum touch target
      });
    });

    it('should handle touch interactions correctly', async () => {
      const mockOnTasksChange = vi.fn();
      
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={mockOnTasksChange}
          projectTitle="Mobile Test Project"
        />
      );

      const taskElement = screen.getByText('Mobile Test Task');
      
      // Simulate touch events
      if (config.touchSupport) {
        await simulateTouchEvents.tap(taskElement);
        
        // Should enter edit mode on touch
        await waitFor(() => {
          expect(screen.getByDisplayValue('Mobile Test Task')).toBeInTheDocument();
        });
      }
    });

    it('should show PWA install prompt appropriately', async () => {
      // Mock beforeinstallprompt event availability
      const mockPromptEvent = {
        preventDefault: vi.fn(),
        prompt: vi.fn().mockResolvedValue({ outcome: 'accepted' })
      };

      render(<PWAInstallPrompt />);

      if (!config.standalone) {
        // Simulate beforeinstallprompt event
        fireEvent(window, new CustomEvent('beforeinstallprompt', {
          detail: mockPromptEvent
        }));

        await waitFor(() => {
          expect(screen.getByText(/App installieren/i)).toBeInTheDocument();
        });
      } else {
        // Should not show install prompt in standalone mode
        expect(screen.queryByText(/App installieren/i)).not.toBeInTheDocument();
      }
    });

    it('should handle viewport changes correctly', async () => {
      const { rerender } = render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Mobile Test Project"
        />
      );

      // Simulate orientation change
      Object.defineProperty(window, 'innerWidth', { 
        value: config.viewport.height, 
        writable: true 
      });
      Object.defineProperty(window, 'innerHeight', { 
        value: config.viewport.width, 
        writable: true 
      });

      fireEvent(window, new Event('resize'));

      rerender(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Mobile Test Project"
        />
      );

      // Layout should adapt to new orientation
      const taskList = screen.getByRole('list');
      expect(taskList).toBeInTheDocument();
    });

    it('should support mobile-specific gestures', async () => {
      const mockOnTasksChange = vi.fn();
      
      render(
        <MobileTaskItem
          task={mockTasks[0]}
          onTaskChange={mockOnTasksChange}
          onDelete={vi.fn()}
          onAddSubtask={vi.fn()}
        />
      );

      const taskElement = screen.getByText('Mobile Test Task');

      if (config.touchSupport) {
        // Test swipe gesture
        await simulateTouchEvents.swipeLeft(taskElement);
        
        // Should show action buttons after swipe
        await waitFor(() => {
          expect(screen.getByRole('button', { name: /löschen/i })).toBeInTheDocument();
        });

        // Test long press
        await simulateTouchEvents.longPress(taskElement);
        
        // Should show context menu or selection
        await waitFor(() => {
          expect(taskElement).toHaveClass('selected');
        });
      }
    });

    it('should handle mobile keyboard interactions', async () => {
      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Mobile Test Project"
        />
      );

      // Click to edit task
      const taskElement = screen.getByText('Mobile Test Task');
      await user.click(taskElement);

      const input = screen.getByDisplayValue('Mobile Test Task');
      
      // Test mobile keyboard behavior
      await user.clear(input);
      await user.type(input, 'Updated Mobile Task');
      
      // Simulate mobile keyboard "Done" button (Enter key)
      await user.keyboard('{Enter}');

      expect(input).not.toHaveFocus();
    });

    it('should handle safe area insets correctly', async () => {
      // Mock safe area insets for devices with notches
      const mockSafeAreaInsets = {
        top: config.name.includes('iPhone') ? 44 : 0,
        bottom: config.name.includes('iPhone') ? 34 : 0,
        left: 0,
        right: 0
      };

      // Mock CSS environment variables
      Object.defineProperty(document.documentElement.style, 'getPropertyValue', {
        value: vi.fn((prop: string) => {
          switch (prop) {
            case '--safe-area-inset-top':
              return `${mockSafeAreaInsets.top}px`;
            case '--safe-area-inset-bottom':
              return `${mockSafeAreaInsets.bottom}px`;
            default:
              return '';
          }
        })
      });

      render(<MobileBottomNavigation />);

      const navigation = screen.getByRole('navigation');
      const styles = window.getComputedStyle(navigation);
      
      if (mockSafeAreaInsets.bottom > 0) {
        expect(styles.paddingBottom).toBe(`${mockSafeAreaInsets.bottom}px`);
      }
    });
  });

  describe('Browser-Specific Feature Detection', () => {
    it('should detect iOS Safari specific features', async () => {
      simulateMobileDevice(mobileConfigurations[0]); // iPhone Safari

      // Mock iOS-specific APIs
      Object.defineProperty(navigator, 'standalone', { value: false, writable: true });
      
      render(<PWAInstallPrompt />);

      // Should show iOS-specific install instructions
      fireEvent(window, new CustomEvent('beforeinstallprompt'));
      
      await waitFor(() => {
        expect(screen.getByText(/Safari-Menü/i)).toBeInTheDocument();
      });
    });

    it('should detect Android Chrome specific features', async () => {
      simulateMobileDevice(mobileConfigurations[2]); // Android Chrome

      const mockPromptEvent = {
        preventDefault: vi.fn(),
        prompt: vi.fn().mockResolvedValue({ outcome: 'accepted' })
      };

      render(<PWAInstallPrompt />);

      // Should show Android-specific install prompt
      fireEvent(window, new CustomEvent('beforeinstallprompt', {
        detail: mockPromptEvent
      }));

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /installieren/i })).toBeInTheDocument();
      });
    });

    it('should handle Samsung Internet browser quirks', async () => {
      simulateMobileDevice(mobileConfigurations[5]); // Samsung Internet

      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Samsung Test"
        />
      );

      // Samsung Internet has specific touch behavior
      const taskElement = screen.getByText('Mobile Test Task');
      
      // Test Samsung-specific touch handling
      fireEvent.touchStart(taskElement, {
        touches: [{ clientX: 100, clientY: 100 }]
      });
      
      fireEvent.touchEnd(taskElement, {
        changedTouches: [{ clientX: 100, clientY: 100 }]
      });

      // Should handle touch events correctly
      expect(taskElement).toBeInTheDocument();
    });
  });

  describe('Performance on Mobile Devices', () => {
    it('should maintain 60fps during scrolling on mobile', async () => {
      // Create large task list for scroll testing
      const largeTasks = Array.from({ length: 100 }, (_, i) => ({
        id: `task-${i}`,
        title: `Task ${i}`,
        description: `Description ${i}`,
        content: 'Content',
        status: 'pending' as const,
        assignees: [],
        subtasks: []
      }));

      render(
        <TaskList
          tasks={largeTasks}
          onTasksChange={vi.fn()}
          projectTitle="Performance Test"
        />
      );

      const taskList = screen.getByRole('list');
      
      // Mock performance measurement
      const performanceEntries: PerformanceEntry[] = [];
      const mockPerformanceObserver = vi.fn((callback) => {
        // Simulate performance entries
        callback({
          getEntries: () => performanceEntries
        });
      });

      Object.defineProperty(window, 'PerformanceObserver', {
        value: mockPerformanceObserver
      });

      // Simulate scroll events
      for (let i = 0; i < 10; i++) {
        fireEvent.scroll(taskList, { target: { scrollY: i * 100 } });
        
        // Add mock performance entry
        performanceEntries.push({
          name: 'scroll',
          duration: 16.67, // 60fps = 16.67ms per frame
          startTime: performance.now()
        } as PerformanceEntry);
      }

      // Verify smooth scrolling performance
      const avgFrameTime = performanceEntries.reduce((sum, entry) => sum + entry.duration, 0) / performanceEntries.length;
      expect(avgFrameTime).toBeLessThan(16.67); // Should maintain 60fps
    });

    it('should handle memory constraints on mobile devices', async () => {
      // Mock memory API
      Object.defineProperty(navigator, 'deviceMemory', { value: 2 }); // 2GB device
      
      // Create memory-intensive task list
      const memoryIntensiveTasks = Array.from({ length: 1000 }, (_, i) => ({
        id: `task-${i}`,
        title: `Memory Test Task ${i}`,
        description: 'A'.repeat(1000), // Large description
        content: 'B'.repeat(1000), // Large content
        status: 'pending' as const,
        assignees: [],
        subtasks: []
      }));

      render(
        <TaskList
          tasks={memoryIntensiveTasks}
          onTasksChange={vi.fn()}
          projectTitle="Memory Test"
        />
      );

      // Should implement virtualization for large lists
      const visibleTasks = screen.getAllByRole('listitem');
      expect(visibleTasks.length).toBeLessThan(50); // Should virtualize
    });
  });

  describe('Network Conditions Testing', () => {
    it('should handle slow 3G connections', async () => {
      // Mock slow connection
      Object.defineProperty(navigator, 'connection', {
        value: {
          effectiveType: 'slow-2g',
          downlink: 0.05,
          rtt: 2000
        }
      });

      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Slow Connection Test"
        />
      );

      // Should show loading states for slow connections
      expect(screen.getByText(/Langsame Verbindung/i)).toBeInTheDocument();
    });

    it('should handle offline scenarios gracefully', async () => {
      // Mock offline state
      Object.defineProperty(navigator, 'onLine', { value: false });

      render(
        <TaskList
          tasks={mockTasks}
          onTasksChange={vi.fn()}
          projectTitle="Offline Test"
        />
      );

      // Should show offline indicator
      expect(screen.getByText(/Offline/i)).toBeInTheDocument();
      
      // Should still allow task interactions
      const taskElement = screen.getByText('Mobile Test Task');
      expect(taskElement).toBeInTheDocument();
    });
  });
});