/**
 * Utilities for simulating mobile device environments in tests
 */

interface MobileConfig {
  name: string;
  userAgent: string;
  viewport: { width: number; height: number };
  touchSupport: boolean;
  standalone: boolean;
}

/**
 * Simulate a mobile device environment
 */
export function simulateMobileDevice(config: MobileConfig): void {
  // Set user agent
  Object.defineProperty(navigator, 'userAgent', {
    value: config.userAgent,
    writable: true
  });

  // Set viewport dimensions
  Object.defineProperty(window, 'innerWidth', {
    value: config.viewport.width,
    writable: true
  });
  
  Object.defineProperty(window, 'innerHeight', {
    value: config.viewport.height,
    writable: true
  });

  // Set screen dimensions
  Object.defineProperty(screen, 'width', {
    value: config.viewport.width,
    writable: true
  });
  
  Object.defineProperty(screen, 'height', {
    value: config.viewport.height,
    writable: true
  });

  // Mock touch support
  if (config.touchSupport) {
    Object.defineProperty(window, 'ontouchstart', {
      value: () => {},
      writable: true
    });

    // Mock touch events
    if (!window.TouchEvent) {
      (window as any).TouchEvent = class TouchEvent extends Event {
        touches: Touch[];
        changedTouches: Touch[];
        targetTouches: Touch[];

        constructor(type: string, eventInit?: TouchEventInit) {
          super(type, eventInit);
          this.touches = eventInit?.touches || [];
          this.changedTouches = eventInit?.changedTouches || [];
          this.targetTouches = eventInit?.targetTouches || [];
        }
      };
    }

    // Mock Touch interface
    if (!window.Touch) {
      (window as any).Touch = class Touch {
        identifier: number;
        target: EventTarget;
        clientX: number;
        clientY: number;
        pageX: number;
        pageY: number;
        screenX: number;
        screenY: number;
        radiusX: number;
        radiusY: number;
        rotationAngle: number;
        force: number;

        constructor(touchInit: TouchInit) {
          this.identifier = touchInit.identifier;
          this.target = touchInit.target;
          this.clientX = touchInit.clientX;
          this.clientY = touchInit.clientY;
          this.pageX = touchInit.pageX || touchInit.clientX;
          this.pageY = touchInit.pageY || touchInit.clientY;
          this.screenX = touchInit.screenX || touchInit.clientX;
          this.screenY = touchInit.screenY || touchInit.clientY;
          this.radiusX = touchInit.radiusX || 1;
          this.radiusY = touchInit.radiusY || 1;
          this.rotationAngle = touchInit.rotationAngle || 0;
          this.force = touchInit.force || 1;
        }
      };
    }
  }

  // Mock standalone mode for PWA
  if (config.standalone) {
    Object.defineProperty(navigator, 'standalone', {
      value: true,
      writable: true
    });

    // Mock display mode
    Object.defineProperty(window, 'matchMedia', {
      value: (query: string) => ({
        matches: query.includes('display-mode: standalone'),
        media: query,
        onchange: null,
        addListener: () => {},
        removeListener: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => true
      }),
      writable: true
    });
  }

  // Mock device pixel ratio
  const devicePixelRatio = config.name.includes('iPhone') ? 3 : 2;
  Object.defineProperty(window, 'devicePixelRatio', {
    value: devicePixelRatio,
    writable: true
  });

  // Mock orientation
  Object.defineProperty(screen, 'orientation', {
    value: {
      angle: 0,
      type: 'portrait-primary'
    },
    writable: true
  });

  // Trigger resize event to update layout
  window.dispatchEvent(new Event('resize'));
}

/**
 * Touch event simulation utilities
 */
export const simulateTouchEvents = {
  /**
   * Simulate a tap gesture
   */
  async tap(element: Element, options: { x?: number; y?: number } = {}): Promise<void> {
    const rect = element.getBoundingClientRect();
    const x = options.x ?? rect.left + rect.width / 2;
    const y = options.y ?? rect.top + rect.height / 2;

    const touch = new Touch({
      identifier: 1,
      target: element,
      clientX: x,
      clientY: y
    });

    const touchStart = new TouchEvent('touchstart', {
      touches: [touch],
      changedTouches: [touch],
      targetTouches: [touch]
    });

    const touchEnd = new TouchEvent('touchend', {
      touches: [],
      changedTouches: [touch],
      targetTouches: []
    });

    element.dispatchEvent(touchStart);
    
    // Simulate brief delay
    await new Promise(resolve => setTimeout(resolve, 50));
    
    element.dispatchEvent(touchEnd);

    // Also dispatch click for compatibility
    element.dispatchEvent(new MouseEvent('click', {
      clientX: x,
      clientY: y,
      bubbles: true
    }));
  },

  /**
   * Simulate a long press gesture
   */
  async longPress(element: Element, duration: number = 500): Promise<void> {
    const rect = element.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;

    const touch = new Touch({
      identifier: 1,
      target: element,
      clientX: x,
      clientY: y
    });

    const touchStart = new TouchEvent('touchstart', {
      touches: [touch],
      changedTouches: [touch],
      targetTouches: [touch]
    });

    element.dispatchEvent(touchStart);
    
    // Wait for long press duration
    await new Promise(resolve => setTimeout(resolve, duration));
    
    const touchEnd = new TouchEvent('touchend', {
      touches: [],
      changedTouches: [touch],
      targetTouches: []
    });

    element.dispatchEvent(touchEnd);
  },

  /**
   * Simulate a swipe gesture
   */
  async swipe(
    element: Element, 
    direction: 'left' | 'right' | 'up' | 'down',
    distance: number = 100
  ): Promise<void> {
    const rect = element.getBoundingClientRect();
    const startX = rect.left + rect.width / 2;
    const startY = rect.top + rect.height / 2;

    let endX = startX;
    let endY = startY;

    switch (direction) {
      case 'left':
        endX = startX - distance;
        break;
      case 'right':
        endX = startX + distance;
        break;
      case 'up':
        endY = startY - distance;
        break;
      case 'down':
        endY = startY + distance;
        break;
    }

    const startTouch = new Touch({
      identifier: 1,
      target: element,
      clientX: startX,
      clientY: startY
    });

    const endTouch = new Touch({
      identifier: 1,
      target: element,
      clientX: endX,
      clientY: endY
    });

    // Start touch
    element.dispatchEvent(new TouchEvent('touchstart', {
      touches: [startTouch],
      changedTouches: [startTouch],
      targetTouches: [startTouch]
    }));

    // Simulate movement
    const steps = 10;
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps;
      const currentX = startX + (endX - startX) * progress;
      const currentY = startY + (endY - startY) * progress;

      const moveTouch = new Touch({
        identifier: 1,
        target: element,
        clientX: currentX,
        clientY: currentY
      });

      element.dispatchEvent(new TouchEvent('touchmove', {
        touches: [moveTouch],
        changedTouches: [moveTouch],
        targetTouches: [moveTouch]
      }));

      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // End touch
    element.dispatchEvent(new TouchEvent('touchend', {
      touches: [],
      changedTouches: [endTouch],
      targetTouches: []
    }));
  },

  /**
   * Simulate swipe left gesture
   */
  async swipeLeft(element: Element, distance?: number): Promise<void> {
    return this.swipe(element, 'left', distance);
  },

  /**
   * Simulate swipe right gesture
   */
  async swipeRight(element: Element, distance?: number): Promise<void> {
    return this.swipe(element, 'right', distance);
  },

  /**
   * Simulate pinch gesture for zoom
   */
  async pinch(
    element: Element,
    scale: number = 0.5,
    centerX?: number,
    centerY?: number
  ): Promise<void> {
    const rect = element.getBoundingClientRect();
    const cx = centerX ?? rect.left + rect.width / 2;
    const cy = centerY ?? rect.top + rect.height / 2;

    const distance = 100;
    const startDistance = distance;
    const endDistance = distance * scale;

    // Two touches for pinch
    const touch1Start = new Touch({
      identifier: 1,
      target: element,
      clientX: cx - startDistance / 2,
      clientY: cy
    });

    const touch2Start = new Touch({
      identifier: 2,
      target: element,
      clientX: cx + startDistance / 2,
      clientY: cy
    });

    // Start pinch
    element.dispatchEvent(new TouchEvent('touchstart', {
      touches: [touch1Start, touch2Start],
      changedTouches: [touch1Start, touch2Start],
      targetTouches: [touch1Start, touch2Start]
    }));

    // Simulate pinch movement
    const steps = 10;
    for (let i = 1; i <= steps; i++) {
      const progress = i / steps;
      const currentDistance = startDistance + (endDistance - startDistance) * progress;

      const touch1Move = new Touch({
        identifier: 1,
        target: element,
        clientX: cx - currentDistance / 2,
        clientY: cy
      });

      const touch2Move = new Touch({
        identifier: 2,
        target: element,
        clientX: cx + currentDistance / 2,
        clientY: cy
      });

      element.dispatchEvent(new TouchEvent('touchmove', {
        touches: [touch1Move, touch2Move],
        changedTouches: [touch1Move, touch2Move],
        targetTouches: [touch1Move, touch2Move]
      }));

      await new Promise(resolve => setTimeout(resolve, 10));
    }

    // End pinch
    const touch1End = new Touch({
      identifier: 1,
      target: element,
      clientX: cx - endDistance / 2,
      clientY: cy
    });

    const touch2End = new Touch({
      identifier: 2,
      target: element,
      clientX: cx + endDistance / 2,
      clientY: cy
    });

    element.dispatchEvent(new TouchEvent('touchend', {
      touches: [],
      changedTouches: [touch1End, touch2End],
      targetTouches: []
    }));
  }
};

/**
 * Mock device-specific APIs
 */
export const mockDeviceAPIs = {
  /**
   * Mock device memory API
   */
  mockDeviceMemory(memoryGB: number): void {
    Object.defineProperty(navigator, 'deviceMemory', {
      value: memoryGB,
      writable: true
    });
  },

  /**
   * Mock network information API
   */
  mockNetworkInformation(options: {
    effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';
    downlink?: number;
    rtt?: number;
    saveData?: boolean;
  }): void {
    Object.defineProperty(navigator, 'connection', {
      value: {
        effectiveType: options.effectiveType || '4g',
        downlink: options.downlink || 10,
        rtt: options.rtt || 100,
        saveData: options.saveData || false
      },
      writable: true
    });
  },

  /**
   * Mock battery API
   */
  mockBattery(options: {
    level?: number;
    charging?: boolean;
    chargingTime?: number;
    dischargingTime?: number;
  }): void {
    const battery = {
      level: options.level || 0.8,
      charging: options.charging || false,
      chargingTime: options.chargingTime || Infinity,
      dischargingTime: options.dischargingTime || 3600,
      addEventListener: () => {},
      removeEventListener: () => {}
    };

    Object.defineProperty(navigator, 'getBattery', {
      value: () => Promise.resolve(battery),
      writable: true
    });
  },

  /**
   * Mock vibration API
   */
  mockVibration(): void {
    Object.defineProperty(navigator, 'vibrate', {
      value: (pattern: number | number[]) => {
        console.log('Vibration pattern:', pattern);
        return true;
      },
      writable: true
    });
  },

  /**
   * Mock geolocation API
   */
  mockGeolocation(options: {
    latitude?: number;
    longitude?: number;
    accuracy?: number;
  }): void {
    const position = {
      coords: {
        latitude: options.latitude || 37.7749,
        longitude: options.longitude || -122.4194,
        accuracy: options.accuracy || 10,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    };

    Object.defineProperty(navigator, 'geolocation', {
      value: {
        getCurrentPosition: (success: PositionCallback) => {
          setTimeout(() => success(position), 100);
        },
        watchPosition: (success: PositionCallback) => {
          setTimeout(() => success(position), 100);
          return 1;
        },
        clearWatch: () => {}
      },
      writable: true
    });
  }
};

/**
 * Performance testing utilities
 */
export const performanceUtils = {
  /**
   * Measure rendering performance
   */
  measureRenderTime(callback: () => void): number {
    const start = performance.now();
    callback();
    return performance.now() - start;
  },

  /**
   * Simulate slow device performance
   */
  simulateSlowDevice(): void {
    // Mock slower performance.now() to simulate slower device
    const originalNow = performance.now;
    let timeOffset = 0;
    
    Object.defineProperty(performance, 'now', {
      value: () => {
        timeOffset += Math.random() * 5; // Add random delay
        return originalNow.call(performance) + timeOffset;
      },
      writable: true
    });
  },

  /**
   * Mock performance observer
   */
  mockPerformanceObserver(entries: PerformanceEntry[]): void {
    Object.defineProperty(window, 'PerformanceObserver', {
      value: class MockPerformanceObserver {
        callback: (list: { getEntries: () => PerformanceEntry[] }) => void;

        constructor(callback: (list: { getEntries: () => PerformanceEntry[] }) => void) {
          this.callback = callback;
        }

        observe() {
          setTimeout(() => {
            this.callback({ getEntries: () => entries });
          }, 100);
        }

        disconnect() {}
      },
      writable: true
    });
  }
};