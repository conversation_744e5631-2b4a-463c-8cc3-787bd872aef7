import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import Home from '@/app/page'
import { geminiClient } from '@/lib/ai'

// Mock the AI client
vi.mock('@/lib/ai', () => ({
  geminiClient: {
    generateTasks: vi.fn(),
    generateSubtasks: vi.fn(),
    generateTaskContent: vi.fn(),
    generateQuestions: vi.fn(),
    elaborateContent: vi.fn()
  },
  contextBuilder: {
    buildContextForAI: vi.fn(() => 'mock context'),
    buildCopilotContext: vi.fn(() => 'mock copilot context')
  }
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'mock-uuid-' + Math.random().toString(36).substr(2, 9)
  }
})

describe('Compatibility Tests - Monolithic vs Modular', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('Core Functionality Compatibility', () => {
    it('should render the main interface identical to monolithic version', () => {
      render(<Home />)
      
      // Check for main title
      expect(screen.getByText('KI Projekt-Planer')).toBeInTheDocument()
      
      // Check for main input area
      expect(screen.getByText('Ihr großes Ziel')).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)).toBeInTheDocument()
      
      // Check for project start button
      expect(screen.getByText('Projekt starten')).toBeInTheDocument()
      
      // Check for theme toggle
      expect(screen.getByLabelText('Toggle Dark Mode')).toBeInTheDocument()
      
      // Check for help button
      expect(screen.getByLabelText('Hilfe anzeigen')).toBeInTheDocument()
    })

    it('should handle project creation like monolithic version', async () => {
      const mockTasks = [
        { title: 'Task 1', description: 'Description 1' },
        { title: 'Task 2', description: 'Description 2' }
      ]
      
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: mockTasks,
        error: null
      })

      render(<Home />)
      
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      const startButton = screen.getByText('Projekt starten')
      
      fireEvent.change(input, { target: { value: 'Test project' } })
      fireEvent.click(startButton)
      
      await waitFor(() => {
        expect(geminiClient.generateTasks).toHaveBeenCalledWith('Test project', '')
      })
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument()
        expect(screen.getByText('Task 2')).toBeInTheDocument()
      })
    })

    it('should handle task breakdown like monolithic version', async () => {
      const mockTasks = [
        { title: 'Main Task', description: 'Main Description' }
      ]
      
      const mockSubtasks = [
        { title: 'Subtask 1', description: 'Sub Description 1' },
        { title: 'Subtask 2', description: 'Sub Description 2' }
      ]
      
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: mockTasks,
        error: null
      })
      
      vi.mocked(geminiClient.generateSubtasks).mockResolvedValue({
        tasks: mockSubtasks,
        error: null
      })

      render(<Home />)
      
      // Create initial project
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      fireEvent.change(input, { target: { value: 'Test project' } })
      fireEvent.click(screen.getByText('Projekt starten'))
      
      await waitFor(() => {
        expect(screen.getByText('Main Task')).toBeInTheDocument()
      })
      
      // Click breakdown button
      const breakdownButton = screen.getByTitle('In Teilschritte zerlegen')
      fireEvent.click(breakdownButton)
      
      await waitFor(() => {
        expect(geminiClient.generateSubtasks).toHaveBeenCalled()
      })
      
      await waitFor(() => {
        expect(screen.getByText('Subtask 1')).toBeInTheDocument()
        expect(screen.getByText('Subtask 2')).toBeInTheDocument()
      })
    })
  })

  describe('UI/UX Compatibility', () => {
    it('should have identical theme switching behavior', () => {
      render(<Home />)
      
      const themeButton = screen.getByLabelText('Toggle Dark Mode')
      
      // Initial state should be dark theme (as per monolithic default)
      expect(document.documentElement.classList.contains('dark')).toBe(false)
      
      fireEvent.click(themeButton)
      
      // Should toggle to light theme
      expect(document.documentElement.classList.contains('dark')).toBe(true)
    })

    it('should show examples with same behavior as monolithic', () => {
      render(<Home />)
      
      // Examples should be visible by default
      expect(screen.getByText('Beispiele verbergen')).toBeInTheDocument()
      
      // Check for some example projects
      expect(screen.getByText('Ich möchte eine App entwickeln')).toBeInTheDocument()
      expect(screen.getByText('Ich möchte Trading lernen')).toBeInTheDocument()
      
      // Hide examples
      fireEvent.click(screen.getByText('Beispiele verbergen'))
      expect(screen.getByText('Beispiele anzeigen')).toBeInTheDocument()
    })

    it('should handle help modal like monolithic version', () => {
      render(<Home />)
      
      const helpButton = screen.getByLabelText('Hilfe anzeigen')
      fireEvent.click(helpButton)
      
      // Check for help modal content
      expect(screen.getByText('Anleitung')).toBeInTheDocument()
      expect(screen.getByText('1. Projekt starten')).toBeInTheDocument()
      expect(screen.getByText('KI-Kontext Strategien')).toBeInTheDocument()
    })
  })

  describe('Error Handling Compatibility', () => {
    it('should handle AI errors like monolithic version', async () => {
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: [],
        error: 'API Error: Rate limit exceeded'
      })

      render(<Home />)
      
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      fireEvent.change(input, { target: { value: 'Test project' } })
      fireEvent.click(screen.getByText('Projekt starten'))
      
      await waitFor(() => {
        expect(geminiClient.generateTasks).toHaveBeenCalled()
      })
      
      // Should show error handling similar to monolithic version
      // The exact error display might vary, but should be user-friendly
    })

    it('should handle partial AI failures like monolithic version', async () => {
      const mockTasks = [
        { title: 'Task 1', description: 'Description 1' }
      ]
      
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: mockTasks,
        error: 'Partial failure: Some tasks could not be generated'
      })

      render(<Home />)
      
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      fireEvent.change(input, { target: { value: 'Test project' } })
      fireEvent.click(screen.getByText('Projekt starten'))
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument()
      })
      
      // Should show both the task and handle the partial error
    })
  })

  describe('Context Strategy Compatibility', () => {
    it('should have identical context strategy options', () => {
      render(<Home />)
      
      // Open help modal to see context strategies
      fireEvent.click(screen.getByLabelText('Hilfe anzeigen'))
      
      // Check for context strategy options
      expect(screen.getByText('Aufgabenpfad einbeziehen')).toBeInTheDocument()
      expect(screen.getByText('Intelligente Projektzusammenfassung (empfohlen)')).toBeInTheDocument()
      expect(screen.getByText('Vollständigen Projektbaum einbeziehen')).toBeInTheDocument()
      
      // Strategy 2 should be checked by default
      const strategy2Checkbox = screen.getByLabelText('Intelligente Projektzusammenfassung (empfohlen)')
      expect(strategy2Checkbox).toBeChecked()
    })
  })

  describe('Export Functionality Compatibility', () => {
    it('should show export options when tasks exist', async () => {
      const mockTasks = [
        { title: 'Task 1', description: 'Description 1' }
      ]
      
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: mockTasks,
        error: null
      })

      render(<Home />)
      
      // Create project first
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      fireEvent.change(input, { target: { value: 'Test project' } })
      fireEvent.click(screen.getByText('Projekt starten'))
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument()
      })
      
      // Check for export section
      expect(screen.getByText('Projekt exportieren')).toBeInTheDocument()
      expect(screen.getByText('PDF')).toBeInTheDocument()
      expect(screen.getByText('Markdown')).toBeInTheDocument()
      expect(screen.getByText('CSV')).toBeInTheDocument()
    })
  })

  describe('Performance Compatibility', () => {
    it('should handle large task trees efficiently like monolithic version', async () => {
      // Generate a moderately large task tree
      const largeTasks = Array.from({ length: 20 }, (_, i) => ({
        title: `Task ${i + 1}`,
        description: `Description ${i + 1}`
      }))
      
      vi.mocked(geminiClient.generateTasks).mockResolvedValue({
        tasks: largeTasks,
        error: null
      })

      const startTime = performance.now()
      render(<Home />)
      
      const input = screen.getByPlaceholderText(/z\.B\. eine Weltreise planen/)
      fireEvent.change(input, { target: { value: 'Large project' } })
      fireEvent.click(screen.getByText('Projekt starten'))
      
      await waitFor(() => {
        expect(screen.getByText('Task 1')).toBeInTheDocument()
        expect(screen.getByText('Task 20')).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render reasonably quickly
      expect(renderTime).toBeLessThan(5000) // 5 seconds max for large trees
    })
  })

  describe('Motivation System Compatibility', () => {
    it('should show motivation messages like monolithic version', () => {
      render(<Home />)
      
      // Should show initial motivation message
      const motivationElement = screen.getByText(/Gemeinsam verwandeln wir Ideen in Imperien/)
      expect(motivationElement).toBeInTheDocument()
    })
  })

  describe('Accessibility Compatibility', () => {
    it('should maintain same accessibility features as monolithic version', () => {
      render(<Home />)
      
      // Check for proper ARIA labels
      expect(screen.getByLabelText('Toggle Dark Mode')).toBeInTheDocument()
      expect(screen.getByLabelText('Hilfe anzeigen')).toBeInTheDocument()
      
      // Check for proper heading structure
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('KI Projekt-Planer')
      expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('Ihr großes Ziel')
    })
  })
})