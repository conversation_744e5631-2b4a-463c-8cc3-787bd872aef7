import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { TaskList } from '@/components/TaskList'
import { usePerformanceMonitor } from '@/lib/utils/performanceMonitor'

// Mock the performance monitor
vi.mock('@/lib/utils/performanceMonitor', () => ({
  usePerformanceMonitor: vi.fn(() => ({
    startRender: vi.fn(),
    endRender: vi.fn(),
    countTasks: vi.fn(() => 100),
    getAverageRenderTime: vi.fn(() => 50),
    getMaxMemoryUsage: vi.fn(() => 25),
    detectMemoryLeaks: vi.fn(() => false)
  }))
}))

describe('Performance Tests', () => {
  const mockProps = {
    onUpdateTask: vi.fn(),
    onOpenSolveModal: vi.fn(),
    onAddTask: vi.fn(),
    onAddTaskAfter: vi.fn(),
    onDeleteTask: vi.fn(),
    loading: {},
    setLoading: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should handle large task trees efficiently', () => {
    // Generate a large task tree (3 levels deep, 5 tasks per level = 155 total tasks)
    const generateLargeTaskTree = (depth: number, breadth: number, currentDepth = 0): any[] => {
      if (currentDepth >= depth) return []
      
      const tasks = []
      for (let i = 0; i < breadth; i++) {
        tasks.push({
          id: `task-${currentDepth}-${i}`,
          title: `Task ${currentDepth}-${i}`,
          description: `Description ${currentDepth}-${i}`,
          content: `<p>Content ${currentDepth}-${i}</p>`,
          status: 'To Do',
          assignees: [],
          subtasks: generateLargeTaskTree(depth, breadth, currentDepth + 1)
        })
      }
      return tasks
    }

    const largeTasks = generateLargeTaskTree(3, 5)
    
    const startTime = performance.now()
    render(<TaskList tasks={largeTasks} {...mockProps} />)
    const endTime = performance.now()
    
    const renderTime = endTime - startTime
    
    // Should render within reasonable time (less than 2000ms for large trees with virtualization)
    expect(renderTime).toBeLessThan(2000)
    
    // Should render all tasks
    expect(screen.getByText('Task 0-0')).toBeInTheDocument()
  })

  it('should not cause memory leaks with frequent re-renders', () => {
    const tasks = [
      {
        id: '1',
        title: 'Test Task',
        description: 'Test Description',
        content: '<p>Test Content</p>',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ]

    // Render multiple times to simulate frequent updates
    for (let i = 0; i < 10; i++) {
      const { unmount } = render(<TaskList tasks={tasks} {...mockProps} />)
      unmount()
    }

    const mockMonitor = usePerformanceMonitor()
    expect(mockMonitor.detectMemoryLeaks()).toBe(false)
  })

  it('should optimize re-renders with memoization', () => {
    const tasks = [
      {
        id: '1',
        title: 'Test Task',
        description: 'Test Description',
        content: '<p>Test Content</p>',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ]

    const { rerender } = render(<TaskList tasks={tasks} {...mockProps} />)
    
    // Re-render with same props - should be optimized
    rerender(<TaskList tasks={tasks} {...mockProps} />)
    
    // Performance monitor should show good performance
    const mockMonitor = usePerformanceMonitor()
    expect(mockMonitor.getAverageRenderTime()).toBeLessThan(100)
  })

  it('should handle deep nesting without performance degradation', () => {
    // Create deeply nested task structure
    const createDeepTask = (depth: number): any => {
      if (depth === 0) {
        return {
          id: `deep-task-${depth}`,
          title: `Deep Task ${depth}`,
          description: `Deep Description ${depth}`,
          content: `<p>Deep Content ${depth}</p>`,
          status: 'To Do',
          assignees: [],
          subtasks: []
        }
      }
      
      return {
        id: `deep-task-${depth}`,
        title: `Deep Task ${depth}`,
        description: `Deep Description ${depth}`,
        content: `<p>Deep Content ${depth}</p>`,
        status: 'To Do',
        assignees: [],
        subtasks: [createDeepTask(depth - 1)]
      }
    }

    const deepTasks = [createDeepTask(20)] // 20 levels deep
    
    const startTime = performance.now()
    render(<TaskList tasks={deepTasks} {...mockProps} />)
    const endTime = performance.now()
    
    const renderTime = endTime - startTime
    
    // Should handle deep nesting efficiently (less than 1000ms for deep nesting)
    expect(renderTime).toBeLessThan(1000)
  })

  it('should maintain smooth UI interactions during AI operations', async () => {
    const tasks = [
      {
        id: '1',
        title: 'Test Task',
        description: 'Test Description',
        content: '<p>Test Content</p>',
        status: 'To Do',
        assignees: [],
        subtasks: []
      }
    ]

    const loadingStates = { '1': 'breakdown' as const }
    
    const startTime = performance.now()
    render(<TaskList tasks={tasks} loading={loadingStates} {...mockProps} />)
    const endTime = performance.now()
    
    const renderTime = endTime - startTime
    
    // Should render quickly even with loading states
    expect(renderTime).toBeLessThan(100)
  })
})