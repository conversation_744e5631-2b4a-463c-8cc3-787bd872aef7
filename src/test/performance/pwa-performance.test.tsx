/**
 * Performance benchmarks and regression testing for PWA functionality
 * Tests loading times, memory usage, and rendering performance
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

// Performance testing utilities
import { performanceUtils, mockDeviceAPIs } from '../utils/mobile-simulation';

// Components to test
import TaskList from '@/components/TaskList';
import { VirtualizedTaskList } from '@/components/VirtualizedTaskList';
import { PerformanceDashboard } from '@/components/PerformanceDashboard';

// Hooks and utilities
import { usePerformanceMonitoring } from '@/hooks/usePerformanceMonitoring';
import { indexedDBManager } from '@/lib/storage/indexeddb';
import { serviceWorkerManager } from '@/lib/pwa/serviceWorker';

// Mock performance APIs
vi.mock('@/hooks/usePerformanceMonitoring');
vi.mock('@/lib/storage/indexeddb');
vi.mock('@/lib/pwa/serviceWorker');

const mockUsePerformanceMonitoring = vi.mocked(usePerformanceMonitoring);
const mockIndexedDB = vi.mocked(indexedDBManager);
const mockServiceWorker = vi.mocked(serviceWorkerManager);

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  INITIAL_LOAD: 3000,      // App should load within 3 seconds
  TASK_RENDER: 100,        // Task rendering should be under 100ms
  SCROLL_FRAME: 16.67,     // 60fps = 16.67ms per frame
  MEMORY_LIMIT: 50 * 1024 * 1024, // 50MB memory limit
  BUNDLE_SIZE: 2 * 1024 * 1024,   // 2MB bundle size limit
  CACHE_ACCESS: 50,        // Cache access under 50ms
  DB_OPERATION: 200        // Database operations under 200ms
};

// Generate test data
function generateLargeTaskList(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: `task-${i}`,
    title: `Performance Test Task ${i}`,
    description: `Description for task ${i}`,
    content: `Content for task ${i}`,
    status: i % 3 === 0 ? 'completed' : 'pending' as const,
    assignees: [],
    subtasks: i % 5 === 0 ? [
      {
        id: `subtask-${i}-1`,
        title: `Subtask ${i}-1`,
        description: '',
        content: '',
        status: 'pending' as const,
        assignees: [],
        subtasks: []
      }
    ] : []
  }));
}

describe('PWA Performance Benchmarks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock performance monitoring
    mockUsePerformanceMonitoring.mockReturnValue({
      metrics: {
        renderTime: 0,
        memoryUsage: 0,
        cacheHitRate: 0,
        networkRequests: 0,
        errorRate: 0
      },
      startMeasurement: vi.fn(),
      endMeasurement: vi.fn(),
      recordMetric: vi.fn(),
      getMetrics: vi.fn()
    });

    // Mock IndexedDB operations
    mockIndexedDB.initDatabase.mockResolvedValue();
    mockIndexedDB.getAllProjects.mockResolvedValue([]);
    mockIndexedDB.getTasksByProject.mockResolvedValue([]);

    // Mock service worker
    mockServiceWorker.isSupported.mockReturnValue(true);
    mockServiceWorker.register.mockResolvedValue(null);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial Load Performance', () => {
    it('should load app within performance threshold', async () => {
      const startTime = performance.now();
      
      render(
        <TaskList
          tasks={[]}
          onTasksChange={vi.fn()}
          projectTitle="Performance Test"
        />
      );

      await waitFor(() => {
        expect(screen.getByRole('list')).toBeInTheDocument();
      });

      const loadTime = performance.now() - startTime;
      expect(loadTime).toBeLessThan(PERFORMANCE_THRESHOLDS.INITIAL_LOAD);
    });

    it('should initialize database within threshold', async () => {
      const startTime = performance.now();
      
      await mockIndexedDB.initDatabase();
      
      const initTime = performance.now() - startTime;
      expect(initTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION);
    });

    it('should register service worker efficiently', async () => {
      const startTime = performance.now();
      
      await mockServiceWorker.register();
      
      const registrationTime = performance.now() - startTime;
      expect(registrationTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_ACCESS);
    });
  });

  describe('Rendering Performance', () => {
    it('should render small task lists quickly', async () => {
      const tasks = generateLargeTaskList(10);
      
      const renderTime = performanceUtils.measureRenderTime(() => {
        render(
          <TaskList
            tasks={tasks}
            onTasksChange={vi.fn()}
            projectTitle="Small List Test"
          />
        );
      });

      expect(renderTime).toBeLessThan(PERFORMANCE_THRESHOLDS.TASK_RENDER);
    });

    it('should handle large task lists with virtualization', async () => {
      const tasks = generateLargeTaskList(1000);
      
      const renderTime = performanceUtils.measureRenderTime(() => {
        render(
          <VirtualizedTaskList
            tasks={tasks}
            onTasksChange={vi.fn()}
            projectTitle="Large List Test"
          />
        );
      });

      // Virtualized list should render quickly regardless of size
      expect(renderTime).toBeLessThan(PERFORMANCE_THRESHOLDS.TASK_RENDER * 2);
    });

    it('should maintain 60fps during scrolling', async () => {
      const tasks = generateLargeTaskList(100);
      
      render(
        <VirtualizedTaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Scroll Performance Test"
        />
      );

      const taskList = screen.getByRole('list');
      
      // Mock performance entries for scroll events
      const scrollEntries: PerformanceEntry[] = [];
      
      performanceUtils.mockPerformanceObserver(scrollEntries);

      // Simulate scroll events
      for (let i = 0; i < 20; i++) {
        const frameStart = performance.now();
        
        act(() => {
          taskList.scrollTop = i * 50;
          taskList.dispatchEvent(new Event('scroll'));
        });

        const frameTime = performance.now() - frameStart;
        scrollEntries.push({
          name: 'scroll-frame',
          duration: frameTime,
          startTime: frameStart
        } as PerformanceEntry);
      }

      // Calculate average frame time
      const avgFrameTime = scrollEntries.reduce((sum, entry) => sum + entry.duration, 0) / scrollEntries.length;
      expect(avgFrameTime).toBeLessThan(PERFORMANCE_THRESHOLDS.SCROLL_FRAME);
    });

    it('should handle rapid task updates efficiently', async () => {
      const tasks = generateLargeTaskList(50);
      let currentTasks = [...tasks];
      
      const { rerender } = render(
        <TaskList
          tasks={currentTasks}
          onTasksChange={vi.fn()}
          projectTitle="Update Performance Test"
        />
      );

      // Measure time for rapid updates
      const updateTimes: number[] = [];
      
      for (let i = 0; i < 10; i++) {
        const startTime = performance.now();
        
        // Modify a task
        currentTasks = currentTasks.map((task, index) => 
          index === i ? { ...task, title: `Updated Task ${i}` } : task
        );

        rerender(
          <TaskList
            tasks={currentTasks}
            onTasksChange={vi.fn()}
            projectTitle="Update Performance Test"
          />
        );

        const updateTime = performance.now() - startTime;
        updateTimes.push(updateTime);
      }

      const avgUpdateTime = updateTimes.reduce((sum, time) => sum + time, 0) / updateTimes.length;
      expect(avgUpdateTime).toBeLessThan(PERFORMANCE_THRESHOLDS.TASK_RENDER);
    });
  });

  describe('Memory Usage Performance', () => {
    it('should stay within memory limits for large datasets', async () => {
      // Mock memory measurement
      const mockMemoryInfo = {
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0
      };

      Object.defineProperty(performance, 'memory', {
        value: mockMemoryInfo,
        writable: true
      });

      const tasks = generateLargeTaskList(1000);
      
      // Measure initial memory
      const initialMemory = mockMemoryInfo.usedJSHeapSize = 10 * 1024 * 1024; // 10MB

      render(
        <VirtualizedTaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Memory Test"
        />
      );

      // Simulate memory usage after rendering
      const finalMemory = mockMemoryInfo.usedJSHeapSize = 35 * 1024 * 1024; // 35MB
      const memoryIncrease = finalMemory - initialMemory;

      expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.MEMORY_LIMIT);
    });

    it('should clean up memory when components unmount', async () => {
      const tasks = generateLargeTaskList(100);
      
      const { unmount } = render(
        <TaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Cleanup Test"
        />
      );

      // Mock memory before unmount
      const mockMemoryInfo = {
        usedJSHeapSize: 30 * 1024 * 1024 // 30MB
      };

      Object.defineProperty(performance, 'memory', {
        value: mockMemoryInfo,
        writable: true
      });

      const memoryBeforeUnmount = mockMemoryInfo.usedJSHeapSize;

      unmount();

      // Simulate garbage collection
      mockMemoryInfo.usedJSHeapSize = 20 * 1024 * 1024; // 20MB
      const memoryAfterUnmount = mockMemoryInfo.usedJSHeapSize;

      expect(memoryAfterUnmount).toBeLessThan(memoryBeforeUnmount);
    });

    it('should handle memory pressure gracefully', async () => {
      // Simulate low memory device
      mockDeviceAPIs.mockDeviceMemory(1); // 1GB device

      const tasks = generateLargeTaskList(500);
      
      render(
        <VirtualizedTaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Low Memory Test"
        />
      );

      // Should implement memory-conscious rendering
      const visibleItems = screen.getAllByRole('listitem');
      expect(visibleItems.length).toBeLessThan(50); // Should limit visible items
    });
  });

  describe('Database Performance', () => {
    it('should perform CRUD operations within thresholds', async () => {
      const projectData = {
        title: 'Performance Test Project',
        description: 'Testing database performance',
        isOfflineOnly: true
      };

      // Test create operation
      const createStart = performance.now();
      mockIndexedDB.createProject.mockResolvedValue('test-project-id');
      await mockIndexedDB.createProject(projectData);
      const createTime = performance.now() - createStart;

      expect(createTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION);

      // Test read operation
      const readStart = performance.now();
      mockIndexedDB.getAllProjects.mockResolvedValue([]);
      await mockIndexedDB.getAllProjects();
      const readTime = performance.now() - readStart;

      expect(readTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION);

      // Test update operation
      const updateStart = performance.now();
      mockIndexedDB.updateProject.mockResolvedValue();
      await mockIndexedDB.updateProject('test-project-id', { title: 'Updated Title' });
      const updateTime = performance.now() - updateStart;

      expect(updateTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION);

      // Test delete operation
      const deleteStart = performance.now();
      mockIndexedDB.deleteProject.mockResolvedValue();
      await mockIndexedDB.deleteProject('test-project-id');
      const deleteTime = performance.now() - deleteStart;

      expect(deleteTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION);
    });

    it('should handle batch operations efficiently', async () => {
      const batchSize = 100;
      const tasks = generateLargeTaskList(batchSize);

      const batchStart = performance.now();

      // Simulate batch insert
      for (const task of tasks) {
        mockIndexedDB.createTask.mockResolvedValue(`task-${task.id}`);
        await mockIndexedDB.createTask(task, 'project-id');
      }

      const batchTime = performance.now() - batchStart;
      const avgOperationTime = batchTime / batchSize;

      expect(avgOperationTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION / 2);
    });

    it('should optimize queries for large datasets', async () => {
      const largeTaskList = generateLargeTaskList(1000);
      
      const queryStart = performance.now();
      mockIndexedDB.getTasksByProject.mockResolvedValue(largeTaskList);
      await mockIndexedDB.getTasksByProject('large-project-id');
      const queryTime = performance.now() - queryStart;

      expect(queryTime).toBeLessThan(PERFORMANCE_THRESHOLDS.DB_OPERATION * 2);
    });
  });

  describe('Network and Caching Performance', () => {
    it('should cache resources efficiently', async () => {
      // Mock cache API
      const mockCache = {
        match: vi.fn(),
        add: vi.fn(),
        addAll: vi.fn(),
        put: vi.fn(),
        delete: vi.fn(),
        keys: vi.fn()
      };

      Object.defineProperty(window, 'caches', {
        value: {
          open: vi.fn().mockResolvedValue(mockCache),
          match: vi.fn(),
          has: vi.fn(),
          delete: vi.fn(),
          keys: vi.fn()
        },
        writable: true
      });

      const cacheStart = performance.now();
      
      // Simulate caching static assets
      const cache = await caches.open('pwa-cache-v1');
      await cache.addAll(['/static/js/main.js', '/static/css/main.css']);
      
      const cacheTime = performance.now() - cacheStart;
      expect(cacheTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_ACCESS);
    });

    it('should serve cached content quickly', async () => {
      const mockResponse = new Response('cached content');
      const mockCache = {
        match: vi.fn().mockResolvedValue(mockResponse)
      };

      Object.defineProperty(window, 'caches', {
        value: {
          match: vi.fn().mockResolvedValue(mockResponse)
        },
        writable: true
      });

      const cacheStart = performance.now();
      const cachedResponse = await caches.match('/api/tasks');
      const cacheTime = performance.now() - cacheStart;

      expect(cachedResponse).toBe(mockResponse);
      expect(cacheTime).toBeLessThan(PERFORMANCE_THRESHOLDS.CACHE_ACCESS);
    });

    it('should handle slow network conditions', async () => {
      // Mock slow network
      mockDeviceAPIs.mockNetworkInformation({
        effectiveType: 'slow-2g',
        downlink: 0.05,
        rtt: 2000
      });

      const tasks = generateLargeTaskList(10);
      
      render(
        <TaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Slow Network Test"
        />
      );

      // Should show loading states and optimize for slow connections
      expect(screen.getByText(/Langsame Verbindung/i)).toBeInTheDocument();
    });
  });

  describe('Performance Monitoring and Regression Detection', () => {
    it('should track performance metrics accurately', async () => {
      const mockMetrics = {
        renderTime: 50,
        memoryUsage: 25 * 1024 * 1024,
        cacheHitRate: 0.85,
        networkRequests: 5,
        errorRate: 0.01
      };

      mockUsePerformanceMonitoring.mockReturnValue({
        metrics: mockMetrics,
        startMeasurement: vi.fn(),
        endMeasurement: vi.fn(),
        recordMetric: vi.fn(),
        getMetrics: vi.fn().mockReturnValue(mockMetrics)
      });

      render(<PerformanceDashboard />);

      expect(screen.getByText(/Render Time: 50ms/i)).toBeInTheDocument();
      expect(screen.getByText(/Memory Usage: 25MB/i)).toBeInTheDocument();
      expect(screen.getByText(/Cache Hit Rate: 85%/i)).toBeInTheDocument();
    });

    it('should detect performance regressions', async () => {
      const baselineMetrics = {
        renderTime: 50,
        memoryUsage: 20 * 1024 * 1024,
        cacheHitRate: 0.9
      };

      const currentMetrics = {
        renderTime: 150, // Regression: 3x slower
        memoryUsage: 60 * 1024 * 1024, // Regression: 3x more memory
        cacheHitRate: 0.6 // Regression: Lower cache hit rate
      };

      // Check for regressions
      expect(currentMetrics.renderTime).toBeGreaterThan(baselineMetrics.renderTime * 2);
      expect(currentMetrics.memoryUsage).toBeGreaterThan(baselineMetrics.memoryUsage * 2);
      expect(currentMetrics.cacheHitRate).toBeLessThan(baselineMetrics.cacheHitRate * 0.8);
    });

    it('should benchmark against performance budgets', async () => {
      const performanceBudget = {
        maxRenderTime: PERFORMANCE_THRESHOLDS.TASK_RENDER,
        maxMemoryUsage: PERFORMANCE_THRESHOLDS.MEMORY_LIMIT,
        minCacheHitRate: 0.8,
        maxBundleSize: PERFORMANCE_THRESHOLDS.BUNDLE_SIZE
      };

      const currentMetrics = {
        renderTime: 80,
        memoryUsage: 30 * 1024 * 1024,
        cacheHitRate: 0.85,
        bundleSize: 1.5 * 1024 * 1024
      };

      // All metrics should be within budget
      expect(currentMetrics.renderTime).toBeLessThan(performanceBudget.maxRenderTime);
      expect(currentMetrics.memoryUsage).toBeLessThan(performanceBudget.maxMemoryUsage);
      expect(currentMetrics.cacheHitRate).toBeGreaterThan(performanceBudget.minCacheHitRate);
      expect(currentMetrics.bundleSize).toBeLessThan(performanceBudget.maxBundleSize);
    });
  });

  describe('Device-Specific Performance', () => {
    it('should adapt to low-end devices', async () => {
      // Simulate low-end device
      mockDeviceAPIs.mockDeviceMemory(1); // 1GB RAM
      performanceUtils.simulateSlowDevice();

      const tasks = generateLargeTaskList(200);
      
      render(
        <VirtualizedTaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Low-End Device Test"
        />
      );

      // Should implement performance optimizations
      const visibleItems = screen.getAllByRole('listitem');
      expect(visibleItems.length).toBeLessThan(20); // Reduced visible items
    });

    it('should handle battery constraints', async () => {
      // Mock low battery
      mockDeviceAPIs.mockBattery({
        level: 0.15, // 15% battery
        charging: false
      });

      const tasks = generateLargeTaskList(100);
      
      render(
        <TaskList
          tasks={tasks}
          onTasksChange={vi.fn()}
          projectTitle="Battery Test"
        />
      );

      // Should show battery-saving mode
      expect(screen.getByText(/Energiesparmodus/i)).toBeInTheDocument();
    });
  });
});