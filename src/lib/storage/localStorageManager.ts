/**
 * Local Storage Manager with automatic persistence
 * Provides auto-save functionality, conflict resolution, and data validation
 */

import type { Project, Task } from '@/lib/types';
import { indexedDBManager, type StoredProject, type StoredTask, type StoredSettings } from './indexeddb';
import { dataMigrationManager } from './migration';

// Auto-save configuration
const AUTO_SAVE_DELAY = 1000; // 1 second delay for debouncing
const CONFLICT_RESOLUTION_TIMEOUT = 30000; // 30 seconds to resolve conflicts

// Event types for storage operations
export type StorageEventType = 'project_created' | 'project_updated' | 'project_deleted' | 
                               'task_created' | 'task_updated' | 'task_deleted' |
                               'auto_save_started' | 'auto_save_completed' | 'auto_save_failed' |
                               'conflict_detected' | 'conflict_resolved';

export interface StorageEvent {
  type: StorageEventType;
  timestamp: Date;
  data?: any;
  error?: string;
}

export interface ConflictData {
  id: string;
  type: 'project' | 'task';
  localVersion: any;
  remoteVersion?: any;
  timestamp: Date;
  resolved: boolean;
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Storage manager class
class LocalStorageManager {
  private autoSaveTimers: Map<string, NodeJS.Timeout> = new Map();
  private eventListeners: Map<StorageEventType, ((event: StorageEvent) => void)[]> = new Map();
  private pendingConflicts: Map<string, ConflictData> = new Map();
  private isInitialized = false;
  private currentProjectId: string | null = null;

  /**
   * Initialize the storage manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize IndexedDB
      await indexedDBManager.initDatabase();

      // Check if migration is needed
      const migrationNeeded = await dataMigrationManager.isMigrationNeeded();
      if (migrationNeeded) {
        console.log('Performing data migration...');
        const migrationResult = await dataMigrationManager.migrateFromLocalStorage();
        
        if (!migrationResult.success) {
          console.error('Migration failed:', migrationResult.errors);
          throw new Error('Data migration failed');
        }
        
        console.log('Migration completed successfully');
      }

      this.isInitialized = true;
      this.emitEvent('auto_save_started', { message: 'Storage manager initialized' });
      
    } catch (error) {
      console.error('Failed to initialize storage manager:', error);
      throw error;
    }
  }

  /**
   * Set the current project ID for auto-save operations
   */
  setCurrentProject(projectId: string): void {
    this.currentProjectId = projectId;
  }

  // Project operations with auto-save

  /**
   * Save project with automatic persistence and validation
   */
  async saveProject(project: Project): Promise<void> {
    await this.ensureInitialized();

    try {
      // Validate project data
      const validation = this.validateProject(project);
      if (!validation.isValid) {
        throw new Error(`Project validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Check if project exists
      const existingProjects = await indexedDBManager.getAllProjects();
      const existingProject = existingProjects.find(p => p.title === project.title);

      if (existingProject) {
        // Update existing project
        const updates: Partial<StoredProject> = {
          title: project.title,
          description: project.description || '',
          updatedAt: new Date()
        };

        await indexedDBManager.updateProject(existingProject.id, updates);
        this.setCurrentProject(existingProject.id);
        
        // Save tasks separately
        await this.saveTasks(project.tasks, existingProject.id);
        
        this.emitEvent('project_updated', { projectId: existingProject.id, project });
      } else {
        // Create new project
        const newProject: Omit<StoredProject, 'id' | 'createdAt' | 'updatedAt'> = {
          title: project.title,
          description: project.description || '',
          isOfflineOnly: true,
          isFavorite: false
        };

        const projectId = await indexedDBManager.createProject(newProject);
        this.setCurrentProject(projectId);
        
        // Save tasks
        await this.saveTasks(project.tasks, projectId);
        
        this.emitEvent('project_created', { projectId, project });
      }

      this.emitEvent('auto_save_completed', { type: 'project' });
      
    } catch (error) {
      this.emitEvent('auto_save_failed', { 
        type: 'project', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Load project with conflict detection
   */
  async loadProject(): Promise<Project | null> {
    await this.ensureInitialized();

    try {
      const projects = await indexedDBManager.getAllProjects();
      
      if (projects.length === 0) {
        return null;
      }

      // For now, load the most recently updated project
      const latestProject = projects.sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )[0];

      this.setCurrentProject(latestProject.id);
      
      // Load tasks for this project
      const tasks = await indexedDBManager.getTasksByProject(latestProject.id);

      const project: Project = {
        title: latestProject.title,
        description: latestProject.description,
        tasks
      };

      return project;
      
    } catch (error) {
      console.error('Failed to load project:', error);
      throw error;
    }
  }

  // Task operations with auto-save

  /**
   * Save tasks with hierarchical structure and auto-save
   */
  async saveTasks(tasks: Task[], projectId?: string): Promise<void> {
    await this.ensureInitialized();
    
    const targetProjectId = projectId || this.currentProjectId;
    if (!targetProjectId) {
      throw new Error('No project ID specified for saving tasks');
    }

    try {
      // Clear existing tasks for this project
      const existingTasks = await indexedDBManager.getTasksByProject(targetProjectId);
      for (const task of existingTasks) {
        await indexedDBManager.deleteTask(task.id);
      }

      // Save new tasks recursively
      await this.saveTasksRecursively(tasks, targetProjectId);
      
      this.emitEvent('auto_save_completed', { type: 'tasks', projectId: targetProjectId });
      
    } catch (error) {
      this.emitEvent('auto_save_failed', { 
        type: 'tasks', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Recursively save tasks maintaining hierarchy
   */
  private async saveTasksRecursively(tasks: Task[], projectId: string, parentTaskId?: string): Promise<void> {
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      
      // Validate task
      const validation = this.validateTask(task);
      if (!validation.isValid) {
        console.warn(`Task validation failed for "${task.title}":`, validation.errors);
        // Continue with warnings, but log them
      }

      // Create stored task
      const storedTask: Omit<StoredTask, 'id' | 'createdAt' | 'updatedAt' | 'orderIndex'> = {
        title: task.title,
        description: task.description,
        content: task.content,
        status: task.status,
        assignees: task.assignees,
        subtasks: [], // Will be handled by recursion
        projectId,
        parentTaskId,
        isEditing: task.isEditing,
        isDescriptionEditing: task.isDescriptionEditing,
        isAiContentEditing: task.isAiContentEditing,
        aiMetrics: task.aiMetrics
      };

      const taskId = await indexedDBManager.createTask(storedTask, projectId, parentTaskId);
      
      // Recursively save subtasks
      if (task.subtasks && task.subtasks.length > 0) {
        await this.saveTasksRecursively(task.subtasks, projectId, taskId);
      }
    }
  }

  /**
   * Load tasks for current project
   */
  async loadTasks(): Promise<Task[]> {
    await this.ensureInitialized();
    
    if (!this.currentProjectId) {
      return [];
    }

    try {
      return await indexedDBManager.getTasksByProject(this.currentProjectId);
    } catch (error) {
      console.error('Failed to load tasks:', error);
      throw error;
    }
  }

  /**
   * Auto-save task with debouncing
   */
  autoSaveTask(task: Task): void {
    if (!this.currentProjectId) {
      console.warn('Cannot auto-save task: no current project');
      return;
    }

    const taskKey = `task_${task.id}`;
    
    // Clear existing timer
    const existingTimer = this.autoSaveTimers.get(taskKey);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer
    const timer = setTimeout(async () => {
      try {
        await this.saveTaskUpdate(task);
        this.autoSaveTimers.delete(taskKey);
        this.emitEvent('auto_save_completed', { type: 'task', taskId: task.id });
      } catch (error) {
        this.emitEvent('auto_save_failed', { 
          type: 'task', 
          taskId: task.id,
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }, AUTO_SAVE_DELAY);

    this.autoSaveTimers.set(taskKey, timer);
    this.emitEvent('auto_save_started', { type: 'task', taskId: task.id });
  }

  /**
   * Save individual task update
   */
  private async saveTaskUpdate(task: Task): Promise<void> {
    if (!this.currentProjectId) {
      throw new Error('No current project for task update');
    }

    // Validate task
    const validation = this.validateTask(task);
    if (!validation.isValid) {
      throw new Error(`Task validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    // Check if task exists
    const existingTask = await indexedDBManager.getTask(task.id);
    
    if (existingTask) {
      // Update existing task
      const updates: Partial<StoredTask> = {
        title: task.title,
        description: task.description,
        content: task.content,
        status: task.status,
        assignees: task.assignees,
        isEditing: task.isEditing,
        isDescriptionEditing: task.isDescriptionEditing,
        isAiContentEditing: task.isAiContentEditing,
        aiMetrics: task.aiMetrics,
        updatedAt: new Date()
      };

      await indexedDBManager.updateTask(task.id, updates);
      this.emitEvent('task_updated', { taskId: task.id, task });
    } else {
      // Create new task (this shouldn't happen in normal auto-save, but handle it)
      console.warn(`Task ${task.id} not found during auto-save, creating new task`);
      
      const storedTask: Omit<StoredTask, 'id' | 'createdAt' | 'updatedAt' | 'orderIndex'> = {
        ...task,
        projectId: this.currentProjectId,
        subtasks: []
      };

      await indexedDBManager.createTask(storedTask, this.currentProjectId);
      this.emitEvent('task_created', { taskId: task.id, task });
    }
  }

  // Settings operations

  /**
   * Save application settings
   */
  async saveSettings(settings: Partial<StoredSettings>): Promise<void> {
    await this.ensureInitialized();

    try {
      const settingsToSave: Omit<StoredSettings, 'updatedAt'> = {
        id: 'default',
        installPromptShown: false,
        offlineMode: 'auto',
        cacheSize: 50 * 1024 * 1024,
        touchFeedback: true,
        swipeActions: true,
        compactMode: false,
        preferredAIProvider: 'gemini',
        aiProviders: [],
        ...settings
      };

      await indexedDBManager.saveSettings(settingsToSave);
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  }

  /**
   * Load application settings
   */
  async loadSettings(): Promise<StoredSettings | null> {
    await this.ensureInitialized();

    try {
      return await indexedDBManager.loadSettings('default');
    } catch (error) {
      console.error('Failed to load settings:', error);
      throw error;
    }
  }

  // Data validation

  /**
   * Validate project data
   */
  private validateProject(project: Project): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Required fields
    if (!project.title || project.title.trim().length === 0) {
      errors.push({
        field: 'title',
        message: 'Project title is required',
        severity: 'error'
      });
    }

    // Title length
    if (project.title && project.title.length > 200) {
      warnings.push({
        field: 'title',
        message: 'Project title is very long (>200 characters)',
        severity: 'warning'
      });
    }

    // Description length
    if (project.description && project.description.length > 1000) {
      warnings.push({
        field: 'description',
        message: 'Project description is very long (>1000 characters)',
        severity: 'warning'
      });
    }

    // Tasks validation
    if (project.tasks && project.tasks.length > 100) {
      warnings.push({
        field: 'tasks',
        message: 'Project has many tasks (>100), consider splitting into multiple projects',
        severity: 'warning'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate task data
   */
  private validateTask(task: Task): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Required fields
    if (!task.id || task.id.trim().length === 0) {
      errors.push({
        field: 'id',
        message: 'Task ID is required',
        severity: 'error'
      });
    }

    if (!task.title || task.title.trim().length === 0) {
      errors.push({
        field: 'title',
        message: 'Task title is required',
        severity: 'error'
      });
    }

    // Title length
    if (task.title && task.title.length > 200) {
      warnings.push({
        field: 'title',
        message: 'Task title is very long (>200 characters)',
        severity: 'warning'
      });
    }

    // Content length
    if (task.content && task.content.length > 50000) {
      warnings.push({
        field: 'content',
        message: 'Task content is very large (>50KB)',
        severity: 'warning'
      });
    }

    // Status validation
    const validStatuses = ['To Do', 'In Progress', 'Done'];
    if (!validStatuses.includes(task.status)) {
      errors.push({
        field: 'status',
        message: `Invalid task status: ${task.status}`,
        severity: 'error'
      });
    }

    // Subtasks depth validation (prevent infinite nesting)
    const maxDepth = 10;
    const depth = this.calculateTaskDepth(task);
    if (depth > maxDepth) {
      warnings.push({
        field: 'subtasks',
        message: `Task nesting is very deep (>${maxDepth} levels)`,
        severity: 'warning'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Calculate task nesting depth
   */
  private calculateTaskDepth(task: Task, currentDepth = 0): number {
    if (!task.subtasks || task.subtasks.length === 0) {
      return currentDepth;
    }

    let maxDepth = currentDepth;
    for (const subtask of task.subtasks) {
      const depth = this.calculateTaskDepth(subtask, currentDepth + 1);
      maxDepth = Math.max(maxDepth, depth);
    }

    return maxDepth;
  }

  // Conflict resolution

  /**
   * Detect and handle data conflicts
   */
  async handleConflict(id: string, type: 'project' | 'task', localData: any, remoteData?: any): Promise<void> {
    const conflict: ConflictData = {
      id,
      type,
      localVersion: localData,
      remoteVersion: remoteData,
      timestamp: new Date(),
      resolved: false
    };

    this.pendingConflicts.set(id, conflict);
    this.emitEvent('conflict_detected', { conflict });

    // Auto-resolve simple conflicts (local wins for now)
    setTimeout(() => {
      if (!conflict.resolved) {
        this.resolveConflict(id, 'local');
      }
    }, CONFLICT_RESOLUTION_TIMEOUT);
  }

  /**
   * Resolve conflict with specified strategy
   */
  async resolveConflict(id: string, strategy: 'local' | 'remote' | 'merge'): Promise<void> {
    const conflict = this.pendingConflicts.get(id);
    if (!conflict) {
      throw new Error(`No conflict found for ID: ${id}`);
    }

    try {
      switch (strategy) {
        case 'local':
          // Keep local version (no action needed)
          break;
          
        case 'remote':
          if (conflict.remoteVersion) {
            // Apply remote version
            if (conflict.type === 'project') {
              await this.saveProject(conflict.remoteVersion);
            } else {
              await this.saveTaskUpdate(conflict.remoteVersion);
            }
          }
          break;
          
        case 'merge':
          // Simple merge strategy (could be enhanced)
          const merged = { ...conflict.localVersion, ...conflict.remoteVersion };
          if (conflict.type === 'project') {
            await this.saveProject(merged);
          } else {
            await this.saveTaskUpdate(merged);
          }
          break;
      }

      conflict.resolved = true;
      this.pendingConflicts.delete(id);
      this.emitEvent('conflict_resolved', { conflictId: id, strategy });
      
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      throw error;
    }
  }

  // Event system

  /**
   * Add event listener
   */
  addEventListener(type: StorageEventType, listener: (event: StorageEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(type: StorageEventType, listener: (event: StorageEvent) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit storage event
   */
  private emitEvent(type: StorageEventType, data?: any): void {
    const event: StorageEvent = {
      type,
      timestamp: new Date(),
      data
    };

    const listeners = this.eventListeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in storage event listener:', error);
        }
      });
    }
  }

  // Utility methods

  /**
   * Ensure storage manager is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Get storage statistics
   */
  async getStats(): Promise<{
    projects: number;
    tasks: number;
    pendingConflicts: number;
    autoSaveTimers: number;
  }> {
    await this.ensureInitialized();
    
    const dbStats = await indexedDBManager.getStats();
    
    return {
      projects: dbStats.projects,
      tasks: dbStats.tasks,
      pendingConflicts: this.pendingConflicts.size,
      autoSaveTimers: this.autoSaveTimers.size
    };
  }

  /**
   * Clear all auto-save timers
   */
  clearAutoSaveTimers(): void {
    this.autoSaveTimers.forEach(timer => clearTimeout(timer));
    this.autoSaveTimers.clear();
  }

  /**
   * Check if legacy data exists in localStorage
   */
  async hasLegacyData(): Promise<boolean> {
    try {
      const legacyProject = localStorage.getItem('mainProject');
      const legacyTasks = localStorage.getItem('tasks');
      return !!(legacyProject || legacyTasks);
    } catch (error) {
      console.error('Error checking for legacy data:', error);
      return false;
    }
  }

  /**
   * Export legacy data for migration
   */
  async exportData(): Promise<{ projects: Project[] }> {
    try {
      const legacyProject = localStorage.getItem('mainProject');
      const legacyProjectDescription = localStorage.getItem('mainProjectDescription');
      const legacyTasks = localStorage.getItem('tasks');

      if (!legacyProject && !legacyTasks) {
        return { projects: [] };
      }

      const project: Project = {
        id: 'legacy-project-' + Date.now(),
        title: legacyProject || 'Legacy Project',
        description: legacyProjectDescription || '',
        tasks: legacyTasks ? JSON.parse(legacyTasks) : [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isOfflineOnly: false,
        isFavorite: false
      };

      return { projects: [project] };
    } catch (error) {
      console.error('Error exporting legacy data:', error);
      return { projects: [] };
    }
  }

  /**
   * Clear legacy data after migration
   */
  async clearLegacyData(): Promise<void> {
    try {
      localStorage.removeItem('mainProject');
      localStorage.removeItem('mainProjectDescription');
      localStorage.removeItem('tasks');
    } catch (error) {
      console.error('Error clearing legacy data:', error);
    }
  }

  /**
   * Save project to localStorage (for compatibility)
   */
  async saveProject(project: Project): Promise<void> {
    try {
      localStorage.setItem('currentProject', JSON.stringify(project));
    } catch (error) {
      console.error('Error saving project to localStorage:', error);
    }
  }

  /**
   * Load project from localStorage (for compatibility)
   */
  async loadProject(): Promise<Project | null> {
    try {
      const projectData = localStorage.getItem('currentProject');
      return projectData ? JSON.parse(projectData) : null;
    } catch (error) {
      console.error('Error loading project from localStorage:', error);
      return null;
    }
  }

  /**
   * Clear project from localStorage
   */
  async clearProject(): Promise<void> {
    try {
      localStorage.removeItem('currentProject');
    } catch (error) {
      console.error('Error clearing project from localStorage:', error);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.clearAutoSaveTimers();
    this.eventListeners.clear();
    this.pendingConflicts.clear();
    indexedDBManager.close();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const localStorageManager = new LocalStorageManager();