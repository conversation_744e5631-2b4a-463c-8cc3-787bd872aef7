/**
 * Storage module exports
 * Provides unified access to IndexedDB, migration, and local storage management
 */

// Core storage managers
export { indexedDBManager } from './indexeddb';
export { dataMigrationManager } from './migration';
export { localStorageManager } from './localStorageManager';

// Types and interfaces
export type {
  StoredProject,
  StoredTask,
  StoredSettings
} from './indexeddb';

export type {
  MigrationResult,
  BackupData
} from './migration';

export type {
  StorageEventType,
  StorageEvent,
  ConflictData,
  ValidationError,
  ValidationResult
} from './localStorageManager';

// Convenience functions for common operations
export const storage = {
  /**
   * Initialize all storage systems
   */
  async initialize() {
    await localStorageManager.initialize();
  },

  /**
   * Save project with auto-persistence
   */
  async saveProject(project: import('@/lib/types').Project) {
    return localStorageManager.saveProject(project);
  },

  /**
   * Load current project
   */
  async loadProject() {
    return localStorageManager.loadProject();
  },

  /**
   * Auto-save task changes
   */
  autoSaveTask(task: import('@/lib/types').Task) {
    localStorageManager.autoSaveTask(task);
  },

  /**
   * Load tasks for current project
   */
  async loadTasks() {
    return localStorageManager.loadTasks();
  },

  /**
   * Save application settings
   */
  async saveSettings(settings: Partial<StoredSettings>) {
    return localStorageManager.saveSettings(settings);
  },

  /**
   * Load application settings
   */
  async loadSettings() {
    return localStorageManager.loadSettings();
  },

  /**
   * Create backup of all data
   */
  async createBackup() {
    return dataMigrationManager.createBackup();
  },

  /**
   * Restore from backup
   */
  async restoreFromBackup(backupData: BackupData) {
    return dataMigrationManager.restoreFromBackup(backupData);
  },

  /**
   * Get storage statistics
   */
  async getStats() {
    return localStorageManager.getStats();
  },

  /**
   * Cleanup resources
   */
  cleanup() {
    localStorageManager.cleanup();
  }
};