/**
 * IndexedDB wrapper for project and task storage
 * Provides CRUD operations with proper error handling and hierarchical relationship support
 */

import type { Project, Task, User } from '@/lib/types';

// Database configuration
const DB_NAME = 'KIProjectPlannerDB';
const DB_VERSION = 1;

// Object store names
const STORES = {
  PROJECTS: 'projects',
  TASKS: 'tasks',
  USERS: 'users',
  SETTINGS: 'settings'
} as const;

// Enhanced interfaces for IndexedDB storage
export interface StoredProject extends Omit<Project, 'tasks'> {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  lastSyncAt?: Date;
  isOfflineOnly: boolean;
  isFavorite?: boolean;
  color?: string;
  icon?: string;
}

export interface StoredTask extends Task {
  projectId: string;
  parentTaskId?: string;
  createdAt: Date;
  updatedAt: Date;
  lastAIInteraction?: Date;
  isCollapsed?: boolean;
  priority?: 'low' | 'medium' | 'high';
  pendingSync?: boolean;
  conflictResolution?: any;
  orderIndex: number; // For maintaining task order
}

export interface StoredSettings {
  id: string;
  installPromptShown: boolean;
  installDate?: Date;
  offlineMode: 'auto' | 'manual' | 'disabled';
  cacheSize: number;
  touchFeedback: boolean;
  swipeActions: boolean;
  compactMode: boolean;
  preferredAIProvider: string;
  aiProviders: any[];
  updatedAt: Date;
}

// Database schema and initialization
class IndexedDBManager {
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  /**
   * Initialize the IndexedDB database with proper schema setup
   */
  async initDatabase(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = new Promise((resolve, reject) => {
      if (typeof window === 'undefined' || !window.indexedDB) {
        reject(new Error('IndexedDB is not supported in this environment'));
        return;
      }

      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        reject(new Error(`Failed to open database: ${request.error?.message}`));
      };

      request.onsuccess = () => {
        this.db = request.result;
        
        // Handle database errors after opening
        this.db.onerror = (event) => {
          console.error('Database error:', (event.target as any)?.error);
        };

        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        try {
          // Create projects store
          if (!db.objectStoreNames.contains(STORES.PROJECTS)) {
            const projectStore = db.createObjectStore(STORES.PROJECTS, { keyPath: 'id' });
            projectStore.createIndex('title', 'title', { unique: false });
            projectStore.createIndex('createdAt', 'createdAt', { unique: false });
            projectStore.createIndex('updatedAt', 'updatedAt', { unique: false });
            projectStore.createIndex('isFavorite', 'isFavorite', { unique: false });
          }

          // Create tasks store with hierarchical support
          if (!db.objectStoreNames.contains(STORES.TASKS)) {
            const taskStore = db.createObjectStore(STORES.TASKS, { keyPath: 'id' });
            taskStore.createIndex('projectId', 'projectId', { unique: false });
            taskStore.createIndex('parentTaskId', 'parentTaskId', { unique: false });
            taskStore.createIndex('status', 'status', { unique: false });
            taskStore.createIndex('priority', 'priority', { unique: false });
            taskStore.createIndex('orderIndex', 'orderIndex', { unique: false });
            taskStore.createIndex('createdAt', 'createdAt', { unique: false });
            taskStore.createIndex('updatedAt', 'updatedAt', { unique: false });
            // Compound index for efficient hierarchical queries
            taskStore.createIndex('projectParent', ['projectId', 'parentTaskId'], { unique: false });
          }

          // Create users store
          if (!db.objectStoreNames.contains(STORES.USERS)) {
            const userStore = db.createObjectStore(STORES.USERS, { keyPath: 'id' });
            userStore.createIndex('name', 'name', { unique: false });
          }

          // Create settings store
          if (!db.objectStoreNames.contains(STORES.SETTINGS)) {
            const settingsStore = db.createObjectStore(STORES.SETTINGS, { keyPath: 'id' });
          }
        } catch (error) {
          reject(new Error(`Failed to create database schema: ${error}`));
        }
      };
    });

    return this.initPromise;
  }

  /**
   * Ensure database is initialized before operations
   */
  private async ensureInitialized(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.initDatabase();
    }
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    return this.db;
  }

  /**
   * Generic transaction helper with proper error handling
   */
  private async executeTransaction<T>(
    storeNames: string | string[],
    mode: IDBTransactionMode,
    operation: (transaction: IDBTransaction, stores: IDBObjectStore[]) => Promise<T> | T
  ): Promise<T> {
    const db = await this.ensureInitialized();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(storeNames, mode);
      const stores = Array.isArray(storeNames) 
        ? storeNames.map(name => transaction.objectStore(name))
        : [transaction.objectStore(storeNames)];

      transaction.onerror = () => {
        reject(new Error(`Transaction failed: ${transaction.error?.message}`));
      };

      transaction.onabort = () => {
        reject(new Error('Transaction was aborted'));
      };

      transaction.oncomplete = () => {
        // Transaction completed successfully
      };

      try {
        const result = operation(transaction, stores);
        if (result instanceof Promise) {
          result.then(resolve).catch(reject);
        } else {
          resolve(result);
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  // Project CRUD operations

  /**
   * Create a new project with proper error handling
   */
  async createProject(project: Omit<StoredProject, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date();
    const newProject: StoredProject = {
      ...project,
      id: crypto.randomUUID(),
      createdAt: now,
      updatedAt: now,
      isOfflineOnly: true
    };

    await this.executeTransaction(STORES.PROJECTS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const request = store.add(newProject);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`Failed to create project: ${request.error?.message}`));
      });
    });

    return newProject.id;
  }

  /**
   * Update an existing project
   */
  async updateProject(id: string, updates: Partial<Omit<StoredProject, 'id' | 'createdAt'>>): Promise<void> {
    await this.executeTransaction(STORES.PROJECTS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const getRequest = store.get(id);
        
        getRequest.onsuccess = () => {
          const existingProject = getRequest.result;
          if (!existingProject) {
            reject(new Error(`Project with id ${id} not found`));
            return;
          }

          const updatedProject: StoredProject = {
            ...existingProject,
            ...updates,
            updatedAt: new Date()
          };

          const putRequest = store.put(updatedProject);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(new Error(`Failed to update project: ${putRequest.error?.message}`));
        };

        getRequest.onerror = () => reject(new Error(`Failed to get project: ${getRequest.error?.message}`));
      });
    });
  }

  /**
   * Delete a project and all its tasks
   */
  async deleteProject(id: string): Promise<void> {
    await this.executeTransaction([STORES.PROJECTS, STORES.TASKS], 'readwrite', (transaction, [projectStore, taskStore]) => {
      return new Promise<void>((resolve, reject) => {
        // First delete all tasks for this project
        const taskIndex = taskStore.index('projectId');
        const taskRequest = taskIndex.openCursor(IDBKeyRange.only(id));
        
        taskRequest.onsuccess = () => {
          const cursor = taskRequest.result;
          if (cursor) {
            cursor.delete();
            cursor.continue();
          } else {
            // All tasks deleted, now delete the project
            const projectRequest = projectStore.delete(id);
            projectRequest.onsuccess = () => resolve();
            projectRequest.onerror = () => reject(new Error(`Failed to delete project: ${projectRequest.error?.message}`));
          }
        };

        taskRequest.onerror = () => reject(new Error(`Failed to delete project tasks: ${taskRequest.error?.message}`));
      });
    });
  }

  /**
   * Get all projects
   */
  async getAllProjects(): Promise<StoredProject[]> {
    return this.executeTransaction(STORES.PROJECTS, 'readonly', (transaction, [store]) => {
      return new Promise<StoredProject[]>((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(new Error(`Failed to get projects: ${request.error?.message}`));
      });
    });
  }

  /**
   * Get a single project by ID
   */
  async getProject(id: string): Promise<StoredProject | null> {
    return this.executeTransaction(STORES.PROJECTS, 'readonly', (transaction, [store]) => {
      return new Promise<StoredProject | null>((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(new Error(`Failed to get project: ${request.error?.message}`));
      });
    });
  }

  // Task CRUD operations with hierarchical relationship support

  /**
   * Create a new task with hierarchical relationship support
   */
  async createTask(task: Omit<StoredTask, 'id' | 'createdAt' | 'updatedAt' | 'orderIndex'>, projectId: string, parentTaskId?: string): Promise<string> {
    const now = new Date();
    
    // Get the next order index for this level
    const orderIndex = await this.getNextOrderIndex(projectId, parentTaskId);
    
    const newTask: StoredTask = {
      ...task,
      id: crypto.randomUUID(),
      projectId,
      parentTaskId,
      createdAt: now,
      updatedAt: now,
      orderIndex,
      subtasks: [] // Will be populated by getTasksByProject
    };

    await this.executeTransaction(STORES.TASKS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const request = store.add(newTask);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`Failed to create task: ${request.error?.message}`));
      });
    });

    return newTask.id;
  }

  /**
   * Get the next order index for tasks at a specific level
   */
  private async getNextOrderIndex(projectId: string, parentTaskId?: string): Promise<number> {
    return this.executeTransaction(STORES.TASKS, 'readonly', (transaction, [store]) => {
      return new Promise<number>((resolve, reject) => {
        const index = store.index('projectParent');
        const keyRange = IDBKeyRange.only([projectId, parentTaskId || null]);
        const request = index.openCursor(keyRange, 'prev'); // Get highest orderIndex first
        
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            const task = cursor.value as StoredTask;
            resolve((task.orderIndex || 0) + 1);
          } else {
            resolve(0); // First task at this level
          }
        };

        request.onerror = () => reject(new Error(`Failed to get next order index: ${request.error?.message}`));
      });
    });
  }

  /**
   * Update an existing task
   */
  async updateTask(id: string, updates: Partial<Omit<StoredTask, 'id' | 'createdAt' | 'projectId'>>): Promise<void> {
    await this.executeTransaction(STORES.TASKS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const getRequest = store.get(id);
        
        getRequest.onsuccess = () => {
          const existingTask = getRequest.result;
          if (!existingTask) {
            reject(new Error(`Task with id ${id} not found`));
            return;
          }

          const updatedTask: StoredTask = {
            ...existingTask,
            ...updates,
            updatedAt: new Date()
          };

          const putRequest = store.put(updatedTask);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(new Error(`Failed to update task: ${putRequest.error?.message}`));
        };

        getRequest.onerror = () => reject(new Error(`Failed to get task: ${getRequest.error?.message}`));
      });
    });
  }

  /**
   * Delete a task and all its subtasks recursively
   */
  async deleteTask(id: string): Promise<void> {
    await this.executeTransaction(STORES.TASKS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        // First get all subtasks recursively
        this.getAllSubtaskIds(id, store).then(subtaskIds => {
          // Delete all subtasks and the task itself
          const allIds = [id, ...subtaskIds];
          let deletedCount = 0;

          const deleteNext = () => {
            if (deletedCount >= allIds.length) {
              resolve();
              return;
            }

            const deleteRequest = store.delete(allIds[deletedCount]);
            deleteRequest.onsuccess = () => {
              deletedCount++;
              deleteNext();
            };
            deleteRequest.onerror = () => reject(new Error(`Failed to delete task: ${deleteRequest.error?.message}`));
          };

          deleteNext();
        }).catch(reject);
      });
    });
  }

  /**
   * Recursively get all subtask IDs for a given task
   */
  private async getAllSubtaskIds(parentId: string, store: IDBObjectStore): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const subtaskIds: string[] = [];
      const index = store.index('parentTaskId');
      const request = index.openCursor(IDBKeyRange.only(parentId));

      request.onsuccess = async () => {
        const cursor = request.result;
        if (cursor) {
          const task = cursor.value as StoredTask;
          subtaskIds.push(task.id);
          
          // Recursively get subtasks of this task
          try {
            const nestedSubtasks = await this.getAllSubtaskIds(task.id, store);
            subtaskIds.push(...nestedSubtasks);
          } catch (error) {
            reject(error);
            return;
          }
          
          cursor.continue();
        } else {
          resolve(subtaskIds);
        }
      };

      request.onerror = () => reject(new Error(`Failed to get subtasks: ${request.error?.message}`));
    });
  }

  /**
   * Get all tasks for a project with hierarchical structure
   */
  async getTasksByProject(projectId: string): Promise<Task[]> {
    const storedTasks = await this.executeTransaction(STORES.TASKS, 'readonly', (transaction, [store]) => {
      return new Promise<StoredTask[]>((resolve, reject) => {
        const index = store.index('projectId');
        const request = index.getAll(IDBKeyRange.only(projectId));
        
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(new Error(`Failed to get tasks: ${request.error?.message}`));
      });
    });

    // Build hierarchical structure
    return this.buildTaskHierarchy(storedTasks);
  }

  /**
   * Build hierarchical task structure from flat stored tasks
   */
  private buildTaskHierarchy(storedTasks: StoredTask[]): Task[] {
    const taskMap = new Map<string, Task>();
    const rootTasks: Task[] = [];

    // Sort by orderIndex to maintain order
    storedTasks.sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0));

    // Convert stored tasks to Task format and create map
    storedTasks.forEach(storedTask => {
      const task: Task = {
        id: storedTask.id,
        title: storedTask.title,
        description: storedTask.description,
        content: storedTask.content,
        status: storedTask.status,
        assignees: storedTask.assignees,
        subtasks: [],
        isEditing: storedTask.isEditing,
        isDescriptionEditing: storedTask.isDescriptionEditing,
        isAiContentEditing: storedTask.isAiContentEditing,
        aiMetrics: storedTask.aiMetrics
      };
      taskMap.set(task.id, task);
    });

    // Build hierarchy
    storedTasks.forEach(storedTask => {
      const task = taskMap.get(storedTask.id)!;
      
      if (storedTask.parentTaskId) {
        const parent = taskMap.get(storedTask.parentTaskId);
        if (parent) {
          parent.subtasks.push(task);
        }
      } else {
        rootTasks.push(task);
      }
    });

    return rootTasks;
  }

  /**
   * Get a single task by ID
   */
  async getTask(id: string): Promise<StoredTask | null> {
    return this.executeTransaction(STORES.TASKS, 'readonly', (transaction, [store]) => {
      return new Promise<StoredTask | null>((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(new Error(`Failed to get task: ${request.error?.message}`));
      });
    });
  }

  // Settings operations

  /**
   * Save application settings
   */
  async saveSettings(settings: Omit<StoredSettings, 'updatedAt'>): Promise<void> {
    const settingsWithTimestamp: StoredSettings = {
      ...settings,
      updatedAt: new Date()
    };

    await this.executeTransaction(STORES.SETTINGS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const request = store.put(settingsWithTimestamp);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`Failed to save settings: ${request.error?.message}`));
      });
    });
  }

  /**
   * Load application settings
   */
  async loadSettings(id: string = 'default'): Promise<StoredSettings | null> {
    return this.executeTransaction(STORES.SETTINGS, 'readonly', (transaction, [store]) => {
      return new Promise<StoredSettings | null>((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(new Error(`Failed to load settings: ${request.error?.message}`));
      });
    });
  }

  // User operations

  /**
   * Save user data
   */
  async saveUser(user: User): Promise<void> {
    await this.executeTransaction(STORES.USERS, 'readwrite', (transaction, [store]) => {
      return new Promise<void>((resolve, reject) => {
        const request = store.put(user);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error(`Failed to save user: ${request.error?.message}`));
      });
    });
  }

  /**
   * Get user by ID
   */
  async getUser(id: string): Promise<User | null> {
    return this.executeTransaction(STORES.USERS, 'readonly', (transaction, [store]) => {
      return new Promise<User | null>((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(new Error(`Failed to get user: ${request.error?.message}`));
      });
    });
  }

  // Utility methods

  /**
   * Clear all data (for testing or reset purposes)
   */
  async clearAllData(): Promise<void> {
    const storeNames = [STORES.PROJECTS, STORES.TASKS, STORES.USERS, STORES.SETTINGS];
    
    await this.executeTransaction(storeNames, 'readwrite', (transaction, stores) => {
      return Promise.all(stores.map(store => 
        new Promise<void>((resolve, reject) => {
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(new Error(`Failed to clear store: ${request.error?.message}`));
        })
      ));
    });
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    projects: number;
    tasks: number;
    users: number;
    dbSize?: number;
  }> {
    const storeNames = [STORES.PROJECTS, STORES.TASKS, STORES.USERS];
    
    return this.executeTransaction(storeNames, 'readonly', (transaction, stores) => {
      return Promise.all(stores.map(store => 
        new Promise<number>((resolve, reject) => {
          const request = store.count();
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(new Error(`Failed to count records: ${request.error?.message}`));
        })
      )).then(([projects, tasks, users]) => ({
        projects,
        tasks,
        users
      }));
    });
  }

  /**
   * Close database connection
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.initPromise = null;
    }
  }
}

// Export singleton instance
export const indexedDBManager = new IndexedDBManager();