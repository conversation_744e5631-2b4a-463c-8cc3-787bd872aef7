/**
 * Data migration utility for transitioning from localStorage to IndexedDB
 * Handles backup, restore, and version management for database schema updates
 */

import type { Project, Task } from '@/lib/types';
import { indexedDBManager, type StoredProject, type StoredTask, type StoredSettings } from './indexeddb';

// Migration version tracking
const MIGRATION_VERSION_KEY = 'migration_version';
const CURRENT_MIGRATION_VERSION = 1;

// Legacy data keys (if any localStorage data exists)
const LEGACY_KEYS = {
  THEME: 'theme',
  AI_CONTENT_VISIBLE: 'ai-content-visible-',
  INSERTION_SOUND: 'insertion-sound-enabled',
  INSERTION_HAPTIC: 'insertion-haptic-enabled',
  INSERTION_TOAST: 'insertion-toast-enabled',
  PWA_PROMPT_DISMISSED: 'pwa-prompt-dismissed',
  APP_ERRORS: 'app_errors'
} as const;

export interface MigrationResult {
  success: boolean;
  migratedProjects: number;
  migratedTasks: number;
  migratedSettings: boolean;
  errors: string[];
  warnings: string[];
}

export interface BackupData {
  version: number;
  timestamp: Date;
  projects: StoredProject[];
  tasks: StoredTask[];
  settings: StoredSettings[];
  metadata: {
    totalProjects: number;
    totalTasks: number;
    exportedBy: string;
  };
}

class DataMigrationManager {
  /**
   * Check if migration is needed
   */
  async isMigrationNeeded(): Promise<boolean> {
    try {
      // Check if IndexedDB has been initialized
      await indexedDBManager.initDatabase();
      
      // Check current migration version
      const currentVersion = this.getCurrentMigrationVersion();
      
      // If no migration version is set, we need to migrate
      if (currentVersion === null) {
        return true;
      }
      
      // If version is outdated, we need to migrate
      return currentVersion < CURRENT_MIGRATION_VERSION;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Get current migration version from localStorage
   */
  private getCurrentMigrationVersion(): number | null {
    if (typeof window === 'undefined' || !window.localStorage) {
      return null;
    }
    
    const version = localStorage.getItem(MIGRATION_VERSION_KEY);
    return version ? parseInt(version, 10) : null;
  }

  /**
   * Set migration version in localStorage
   */
  private setMigrationVersion(version: number): void {
    if (typeof window !== 'undefined' && window.localStorage) {
      localStorage.setItem(MIGRATION_VERSION_KEY, version.toString());
    }
  }

  /**
   * Migrate existing localStorage data to IndexedDB
   */
  async migrateFromLocalStorage(): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedProjects: 0,
      migratedTasks: 0,
      migratedSettings: false,
      errors: [],
      warnings: []
    };

    try {
      // Initialize IndexedDB
      await indexedDBManager.initDatabase();

      // Create backup before migration
      const backupData = await this.createBackup();
      
      // Migrate settings from localStorage
      await this.migrateSettings(result);

      // Since the current app doesn't persist project data in localStorage,
      // we'll create a default project structure if none exists
      await this.createDefaultProjectIfNeeded(result);

      // Mark migration as complete
      this.setMigrationVersion(CURRENT_MIGRATION_VERSION);
      
      result.success = true;
      
      console.log('Migration completed successfully:', result);
      
    } catch (error) {
      console.error('Migration failed:', error);
      result.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Migrate settings from localStorage to IndexedDB
   */
  private async migrateSettings(result: MigrationResult): Promise<void> {
    try {
      if (typeof window === 'undefined' || !window.localStorage) {
        result.warnings.push('localStorage not available, using default settings');
        return;
      }

      // Collect settings from localStorage
      const theme = localStorage.getItem(LEGACY_KEYS.THEME) || 'dark';
      const soundEnabled = localStorage.getItem(LEGACY_KEYS.INSERTION_SOUND) !== 'false';
      const hapticEnabled = localStorage.getItem(LEGACY_KEYS.INSERTION_HAPTIC) !== 'false';
      const toastEnabled = localStorage.getItem(LEGACY_KEYS.INSERTION_TOAST) !== 'false';
      const pwaPromptShown = localStorage.getItem(LEGACY_KEYS.PWA_PROMPT_DISMISSED) === 'true';

      const settings: Omit<StoredSettings, 'updatedAt'> = {
        id: 'default',
        installPromptShown: pwaPromptShown,
        offlineMode: 'auto',
        cacheSize: 50 * 1024 * 1024, // 50MB default
        touchFeedback: hapticEnabled,
        swipeActions: true,
        compactMode: false,
        preferredAIProvider: 'gemini',
        aiProviders: [{
          id: 'gemini',
          name: 'Google Gemini',
          enabled: true,
          config: {}
        }]
      };

      await indexedDBManager.saveSettings(settings);
      result.migratedSettings = true;
      
    } catch (error) {
      result.errors.push(`Failed to migrate settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a default project if no projects exist after migration
   */
  private async createDefaultProjectIfNeeded(result: MigrationResult): Promise<void> {
    try {
      const existingProjects = await indexedDBManager.getAllProjects();
      
      if (existingProjects.length === 0) {
        // Create a default project
        const defaultProject: Omit<StoredProject, 'id' | 'createdAt' | 'updatedAt'> = {
          title: 'Mein erstes Projekt',
          description: 'Willkommen bei KI Projekt-Planer! Hier können Sie Ihre Projekte verwalten.',
          isOfflineOnly: true,
          isFavorite: false
        };

        const projectId = await indexedDBManager.createProject(defaultProject);
        result.migratedProjects = 1;

        // Create a welcome task
        const welcomeTask: Omit<StoredTask, 'id' | 'createdAt' | 'updatedAt' | 'orderIndex'> = {
          title: 'Willkommen bei KI Projekt-Planer',
          description: 'Erste Schritte mit der App',
          content: `
            <h2>Willkommen bei KI Projekt-Planer!</h2>
            <p>Diese App hilft Ihnen dabei, große Projekte in kleinere, managbare Aufgaben zu unterteilen.</p>
            
            <h3>Erste Schritte:</h3>
            <ul>
              <li>Erstellen Sie ein neues Projekt über das Eingabefeld oben</li>
              <li>Nutzen Sie die KI-Funktionen, um Aufgaben zu generieren und zu elaborieren</li>
              <li>Bearbeiten Sie Aufgaben durch Klicken auf den Text</li>
              <li>Exportieren Sie Ihre Projekte als PDF, Markdown oder CSV</li>
            </ul>
            
            <h3>Offline-Funktionen:</h3>
            <p>Die App funktioniert auch offline! Alle Ihre Daten werden lokal gespeichert.</p>
          `,
          status: 'To Do',
          assignees: [],
          subtasks: [],
          projectId: projectId
        };

        await indexedDBManager.createTask(welcomeTask, projectId);
        result.migratedTasks = 1;
      }
    } catch (error) {
      result.errors.push(`Failed to create default project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a backup of current IndexedDB data
   */
  async createBackup(): Promise<BackupData> {
    try {
      await indexedDBManager.initDatabase();
      
      const [projects, settings] = await Promise.all([
        indexedDBManager.getAllProjects(),
        indexedDBManager.loadSettings().then(s => s ? [s] : [])
      ]);

      // Get all tasks for all projects
      const allTasks: StoredTask[] = [];
      for (const project of projects) {
        const projectTasks = await indexedDBManager.getTasksByProject(project.id);
        // Convert Task[] back to StoredTask[] format for backup
        const storedTasks = await this.convertTasksToStoredFormat(projectTasks, project.id);
        allTasks.push(...storedTasks);
      }

      const backup: BackupData = {
        version: CURRENT_MIGRATION_VERSION,
        timestamp: new Date(),
        projects,
        tasks: allTasks,
        settings,
        metadata: {
          totalProjects: projects.length,
          totalTasks: allTasks.length,
          exportedBy: 'KI Projekt-Planer Migration'
        }
      };

      return backup;
    } catch (error) {
      console.error('Failed to create backup:', error);
      throw new Error(`Backup creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Convert hierarchical Task[] to flat StoredTask[] format
   */
  private async convertTasksToStoredFormat(tasks: Task[], projectId: string, parentTaskId?: string): Promise<StoredTask[]> {
    const storedTasks: StoredTask[] = [];
    
    for (let i = 0; i < tasks.length; i++) {
      const task = tasks[i];
      
      const storedTask: StoredTask = {
        ...task,
        projectId,
        parentTaskId,
        createdAt: new Date(),
        updatedAt: new Date(),
        orderIndex: i
      };
      
      storedTasks.push(storedTask);
      
      // Recursively convert subtasks
      if (task.subtasks && task.subtasks.length > 0) {
        const subStoredTasks = await this.convertTasksToStoredFormat(task.subtasks, projectId, task.id);
        storedTasks.push(...subStoredTasks);
      }
    }
    
    return storedTasks;
  }

  /**
   * Restore data from backup
   */
  async restoreFromBackup(backupData: BackupData): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      migratedProjects: 0,
      migratedTasks: 0,
      migratedSettings: false,
      errors: [],
      warnings: []
    };

    try {
      // Initialize IndexedDB
      await indexedDBManager.initDatabase();

      // Clear existing data
      await indexedDBManager.clearAllData();

      // Restore projects
      for (const project of backupData.projects) {
        try {
          await indexedDBManager.createProject(project);
          result.migratedProjects++;
        } catch (error) {
          result.errors.push(`Failed to restore project ${project.title}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Restore tasks
      for (const task of backupData.tasks) {
        try {
          await indexedDBManager.createTask(task, task.projectId, task.parentTaskId);
          result.migratedTasks++;
        } catch (error) {
          result.errors.push(`Failed to restore task ${task.title}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Restore settings
      for (const settings of backupData.settings) {
        try {
          await indexedDBManager.saveSettings(settings);
          result.migratedSettings = true;
        } catch (error) {
          result.errors.push(`Failed to restore settings: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.success = result.errors.length === 0;
      
    } catch (error) {
      result.errors.push(`Restore failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Export backup data as JSON for download
   */
  exportBackupAsJson(backupData: BackupData): string {
    return JSON.stringify(backupData, null, 2);
  }

  /**
   * Import backup data from JSON string
   */
  importBackupFromJson(jsonString: string): BackupData {
    try {
      const data = JSON.parse(jsonString);
      
      // Validate backup data structure
      if (!data.version || !data.timestamp || !Array.isArray(data.projects) || !Array.isArray(data.tasks)) {
        throw new Error('Invalid backup data format');
      }

      // Convert timestamp strings back to Date objects
      data.timestamp = new Date(data.timestamp);
      data.projects.forEach((project: any) => {
        project.createdAt = new Date(project.createdAt);
        project.updatedAt = new Date(project.updatedAt);
        if (project.lastSyncAt) {
          project.lastSyncAt = new Date(project.lastSyncAt);
        }
      });
      
      data.tasks.forEach((task: any) => {
        task.createdAt = new Date(task.createdAt);
        task.updatedAt = new Date(task.updatedAt);
        if (task.lastAIInteraction) {
          task.lastAIInteraction = new Date(task.lastAIInteraction);
        }
      });

      return data as BackupData;
    } catch (error) {
      throw new Error(`Failed to parse backup data: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
    }
  }

  /**
   * Get migration status and statistics
   */
  async getMigrationStatus(): Promise<{
    migrationVersion: number | null;
    isMigrationNeeded: boolean;
    stats: {
      projects: number;
      tasks: number;
      users: number;
    };
  }> {
    try {
      const migrationVersion = this.getCurrentMigrationVersion();
      const isMigrationNeeded = await this.isMigrationNeeded();
      const stats = await indexedDBManager.getStats();

      return {
        migrationVersion,
        isMigrationNeeded,
        stats
      };
    } catch (error) {
      console.error('Failed to get migration status:', error);
      return {
        migrationVersion: null,
        isMigrationNeeded: true,
        stats: { projects: 0, tasks: 0, users: 0 }
      };
    }
  }

  /**
   * Perform database schema updates for version changes
   */
  async performSchemaUpdate(fromVersion: number, toVersion: number): Promise<void> {
    console.log(`Performing schema update from version ${fromVersion} to ${toVersion}`);
    
    // Future schema updates can be handled here
    // For now, we only have version 1
    if (fromVersion < 1 && toVersion >= 1) {
      // Initial schema setup is handled by IndexedDB manager
      console.log('Initial schema setup completed');
    }
    
    // Update migration version
    this.setMigrationVersion(toVersion);
  }

  /**
   * Clean up old localStorage data after successful migration
   */
  cleanupLegacyData(): void {
    if (typeof window === 'undefined' || !window.localStorage) {
      return;
    }

    try {
      // Remove legacy keys that are no longer needed
      const keysToRemove = [
        LEGACY_KEYS.PWA_PROMPT_DISMISSED, // This will be managed by IndexedDB settings
        LEGACY_KEYS.APP_ERRORS // Error logging will be handled differently
      ];

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });

      console.log('Legacy data cleanup completed');
    } catch (error) {
      console.warn('Failed to clean up legacy data:', error);
    }
  }
}

// Export singleton instance
export const dataMigrationManager = new DataMigrationManager();