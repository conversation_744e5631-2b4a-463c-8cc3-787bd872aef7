/**
 * Error handling and user feedback for insertion validation failures
 */

import type { ValidationResult, InsertionPosition, Task } from '@/lib/types';
import { validateInsertionPosition } from './insertionValidation';

export interface InsertionErrorHandlerOptions {
  showToasts?: boolean;
  logErrors?: boolean;
  fallbackToAlternatives?: boolean;
  maxAlternatives?: number;
}

const DEFAULT_OPTIONS: Required<InsertionErrorHandlerOptions> = {
  showToasts: true,
  logErrors: true,
  fallbackToAlternatives: true,
  maxAlternatives: 3
};

/**
 * Handle insertion validation errors with user-friendly feedback
 */
export class InsertionErrorHandler {
  private options: Required<InsertionErrorHandlerOptions>;

  constructor(options: InsertionErrorHandlerOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  /**
   * Process validation result and provide appropriate feedback
   */
  handleValidationResult(
    validation: ValidationResult,
    position: InsertionPosition,
    tasks: Task[],
    toastManager?: any
  ): {
    shouldProceed: boolean;
    alternativePosition?: InsertionPosition;
    userMessage?: string;
  } {
    if (validation.isValid) {
      // Show warnings if any
      if (validation.warnings && validation.warnings.length > 0) {
        const warningMessage = this.formatWarnings(validation.warnings);
        if (this.options.showToasts && toastManager) {
          toastManager.warning({
            title: 'Insertion Warning',
            description: warningMessage,
            duration: 4000
          });
        }
        if (this.options.logErrors) {
          console.warn('Insertion warnings:', validation.warnings);
        }
      }
      
      return { shouldProceed: true };
    }

    // Handle validation errors
    const errorMessage = this.formatErrors(validation.errors);
    
    if (this.options.logErrors) {
      console.error('Insertion validation failed:', {
        position,
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // Try to find alternatives
    if (this.options.fallbackToAlternatives && validation.suggestedAlternatives) {
      const bestAlternative = this.selectBestAlternative(
        validation.suggestedAlternatives,
        position,
        tasks
      );

      if (bestAlternative) {
        const fallbackMessage = this.formatFallbackMessage(errorMessage, bestAlternative);
        
        if (this.options.showToasts && toastManager) {
          toastManager.warning({
            title: 'Insertion Position Adjusted',
            description: fallbackMessage,
            duration: 5000
          });
        }

        return {
          shouldProceed: true,
          alternativePosition: bestAlternative,
          userMessage: fallbackMessage
        };
      }
    }

    // No alternatives available
    const finalMessage = this.formatFinalErrorMessage(errorMessage);
    
    if (this.options.showToasts && toastManager) {
      toastManager.error({
        title: 'Cannot Insert Task',
        description: finalMessage,
        duration: 6000
      });
    }

    return {
      shouldProceed: false,
      userMessage: finalMessage
    };
  }

  /**
   * Select the best alternative from suggested alternatives
   */
  private selectBestAlternative(
    alternatives: InsertionPosition[],
    originalPosition: InsertionPosition,
    tasks: Task[]
  ): InsertionPosition | null {
    // Limit the number of alternatives to consider
    const limitedAlternatives = alternatives.slice(0, this.options.maxAlternatives);

    // Score alternatives based on similarity to original position
    const scoredAlternatives = limitedAlternatives.map(alt => ({
      position: alt,
      score: this.scoreAlternative(alt, originalPosition, tasks)
    }));

    // Sort by score (higher is better)
    scoredAlternatives.sort((a, b) => b.score - a.score);

    // Validate the best alternative
    const bestCandidate = scoredAlternatives[0];
    if (bestCandidate && bestCandidate.score > 0) {
      const validation = validateInsertionPosition(bestCandidate.position, tasks);
      if (validation.isValid) {
        return bestCandidate.position;
      }
    }

    return null;
  }

  /**
   * Score an alternative position based on similarity to original
   */
  private scoreAlternative(
    alternative: InsertionPosition,
    original: InsertionPosition,
    tasks: Task[]
  ): number {
    let score = 0;

    // Prefer same target task
    if (alternative.targetTaskId === original.targetTaskId) {
      score += 10;
    }

    // Prefer same parent
    if (alternative.parentId === original.parentId) {
      score += 8;
    }

    // Prefer same level
    if (alternative.level === original.level) {
      score += 6;
    }

    // Prefer same insertion type
    if (alternative.type === original.type) {
      score += 5;
    }

    // Prefer positions that don't require major structural changes
    if (alternative.type === 'after' || alternative.type === 'before') {
      score += 3;
    }

    // Penalize very deep insertions
    if (alternative.level > 5) {
      score -= 2;
    }

    return score;
  }

  /**
   * Format error messages for user display
   */
  private formatErrors(errors: string[]): string {
    if (errors.length === 0) return '';
    
    if (errors.length === 1) {
      return this.humanizeError(errors[0]);
    }

    return `Multiple issues found: ${errors.map(this.humanizeError).join('; ')}`;
  }

  /**
   * Format warning messages for user display
   */
  private formatWarnings(warnings: string[]): string {
    if (warnings.length === 0) return '';
    
    if (warnings.length === 1) {
      return this.humanizeWarning(warnings[0]);
    }

    return warnings.map(this.humanizeWarning).join('; ');
  }

  /**
   * Format fallback message when using alternative position
   */
  private formatFallbackMessage(originalError: string, alternative: InsertionPosition): string {
    const typeDescriptions = {
      'before': 'before the selected task',
      'after': 'after the selected task',
      'between_parent_child': 'as a subtask'
    };

    const alternativeDescription = typeDescriptions[alternative.type] || 'at an alternative position';
    
    return `The requested position was not available (${originalError}). The task will be inserted ${alternativeDescription} instead.`;
  }

  /**
   * Format final error message when no alternatives are available
   */
  private formatFinalErrorMessage(errorMessage: string): string {
    return `${errorMessage}. Please try a different position or reorganize your task structure.`;
  }

  /**
   * Convert technical error messages to user-friendly text
   */
  private humanizeError(error: string): string {
    const errorMappings: Record<string, string> = {
      'exceeds maximum allowed depth': 'the task hierarchy is too deep',
      'maximum sibling count': 'there are too many tasks at this level',
      'not found': 'the target task could not be found',
      'restricted parent task': 'insertion is not allowed under this parent task',
      'circular parent-child relationship': 'this would create an invalid task relationship',
      'Target task ID is required': 'no target task was specified',
      'Insertion type is required': 'no insertion type was specified',
      'Level must be a non-negative number': 'invalid hierarchy level specified'
    };

    for (const [technical, friendly] of Object.entries(errorMappings)) {
      if (error.toLowerCase().includes(technical.toLowerCase())) {
        return friendly;
      }
    }

    return error; // Return original if no mapping found
  }

  /**
   * Convert technical warning messages to user-friendly text
   */
  private humanizeWarning(warning: string): string {
    const warningMappings: Record<string, string> = {
      'may impact performance': 'deep nesting may slow down the interface',
      'Level mismatch': 'the task level may not match its position in the hierarchy',
      'High sibling count': 'consider organizing tasks into smaller groups'
    };

    for (const [technical, friendly] of Object.entries(warningMappings)) {
      if (warning.toLowerCase().includes(technical.toLowerCase())) {
        return friendly;
      }
    }

    return warning; // Return original if no mapping found
  }
}

/**
 * Create a default error handler instance
 */
export function createInsertionErrorHandler(options?: InsertionErrorHandlerOptions): InsertionErrorHandler {
  return new InsertionErrorHandler(options);
}

/**
 * Quick utility function for handling validation results
 */
export function handleInsertionValidation(
  position: InsertionPosition,
  tasks: Task[],
  toastManager?: any,
  options?: InsertionErrorHandlerOptions
): {
  shouldProceed: boolean;
  alternativePosition?: InsertionPosition;
  userMessage?: string;
} {
  const validation = validateInsertionPosition(position, tasks);
  const handler = createInsertionErrorHandler(options);
  
  return handler.handleValidationResult(validation, position, tasks, toastManager);
}

/**
 * Batch validate multiple insertion positions
 */
export function handleMultipleInsertionValidations(
  positions: InsertionPosition[],
  tasks: Task[],
  toastManager?: any,
  options?: InsertionErrorHandlerOptions
): {
  validPositions: InsertionPosition[];
  invalidPositions: { position: InsertionPosition; reason: string }[];
  alternatives: { original: InsertionPosition; alternative: InsertionPosition }[];
} {
  const handler = createInsertionErrorHandler(options);
  const validPositions: InsertionPosition[] = [];
  const invalidPositions: { position: InsertionPosition; reason: string }[] = [];
  const alternatives: { original: InsertionPosition; alternative: InsertionPosition }[] = [];

  for (const position of positions) {
    const result = handler.handleValidationResult(
      validateInsertionPosition(position, tasks),
      position,
      tasks,
      toastManager
    );

    if (result.shouldProceed) {
      if (result.alternativePosition) {
        alternatives.push({
          original: position,
          alternative: result.alternativePosition
        });
        validPositions.push(result.alternativePosition);
      } else {
        validPositions.push(position);
      }
    } else {
      invalidPositions.push({
        position,
        reason: result.userMessage || 'Unknown validation error'
      });
    }
  }

  return { validPositions, invalidPositions, alternatives };
}