/**
 * Motivation message management system
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

export interface MotivationState {
  message: string;
  type: 'default' | 'loading' | 'success' | 'warning' | 'error';
  timestamp: number;
}

export type MotivationContext = 
  | 'initial'
  | 'project_starting'
  | 'project_started'
  | 'task_breakdown_starting'
  | 'task_breakdown_success'
  | 'task_breakdown_error'
  | 'ai_content_generating'
  | 'ai_content_success'
  | 'ai_content_error'
  | 'text_elaborating'
  | 'text_elaboration_success'
  | 'text_elaboration_error'
  | 'export_starting'
  | 'export_success'
  | 'export_error'
  | 'network_error'
  | 'api_error'
  | 'general_error';

export class MotivationManager {
  private static instance: MotivationManager;
  private currentState: MotivationState;
  private listeners: Set<(state: MotivationState) => void> = new Set();
  private messageHistory: MotivationState[] = [];
  private maxHistorySize = 10;

  private encouragingMessages: string[] = [
    'Sie machen großartige Fortschritte!',
    'Wei<PERSON> so! Jeder Schritt bringt Sie näher zum Ziel.',
    'Fantastisch! Ihr Plan nimmt Form an.',
    'Ausgezeichnet! Sie sind auf dem richtigen Weg.',
    'Großartig! Ihre Ideen werden Realität.',
    'Perfekt! Schritt für Schritt zum Erfolg.',
    'Wunderbar! Ihr Projekt entwickelt sich prächtig.',
    'Hervorragend! Sie meistern das wie ein Profi.'
  ];

  private messages: Record<MotivationContext, { message: string; type: MotivationState['type'] }> = {
    initial: {
      message: 'Beginnen Sie, indem Sie Ihr Hauptziel oben eingeben oder ein Beispiel auswählen!',
      type: 'default'
    },
    project_starting: {
      message: 'Die KI analysiert Ihr großes Ziel und erstellt einen detaillierten Plan...',
      type: 'loading'
    },
    project_started: {
      message: 'Großartig! Hier ist ein detaillierter Plan. Wählen Sie eine Aufgabe aus, um sie weiter zu zerlegen.',
      type: 'success'
    },
    task_breakdown_starting: {
      message: 'Die KI zerlegt die Aufgabe in praktische Unteraufgaben...',
      type: 'loading'
    },
    task_breakdown_success: {
      message: 'Aufgabe erfolgreich zerlegt! Sie können weitere Aufgaben auswählen oder diese weiter verfeinern.',
      type: 'success'
    },
    task_breakdown_error: {
      message: 'Aufgabenzerlegung hatte Probleme. Sie können die Aufgabe manuell bearbeiten oder es erneut versuchen.',
      type: 'warning'
    },
    ai_content_generating: {
      message: 'Die KI arbeitet an einer detaillierten Lösung für Sie...',
      type: 'loading'
    },
    ai_content_success: {
      message: 'Die KI hat einen Vorschlag erarbeitet. Sie können ihn nun bearbeiten oder weiter verfeinern.',
      type: 'success'
    },
    ai_content_error: {
      message: 'KI-Inhaltserstellung hatte Probleme. Sie können den Inhalt manuell bearbeiten.',
      type: 'warning'
    },
    text_elaborating: {
      message: 'Die KI überarbeitet den ausgewählten Text für bessere Klarheit...',
      type: 'loading'
    },
    text_elaboration_success: {
      message: 'Der ausgewählte Text wurde erfolgreich überarbeitet und verbessert.',
      type: 'success'
    },
    text_elaboration_error: {
      message: 'Textverbesserung hatte Probleme. Der ursprüngliche Text wurde beibehalten.',
      type: 'warning'
    },
    export_starting: {
      message: 'Ihr Projekt wird für den Export vorbereitet...',
      type: 'loading'
    },
    export_success: {
      message: 'Export erfolgreich! Ihre Datei wurde heruntergeladen.',
      type: 'success'
    },
    export_error: {
      message: 'Export fehlgeschlagen. Bitte versuchen Sie es erneut.',
      type: 'error'
    },
    network_error: {
      message: 'Netzwerkproblem erkannt. Bitte überprüfen Sie Ihre Internetverbindung.',
      type: 'error'
    },
    api_error: {
      message: 'KI-Service temporär nicht verfügbar. Fallback-Funktionen werden verwendet.',
      type: 'warning'
    },
    general_error: {
      message: 'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
      type: 'error'
    }
  };

  private encouragingMessages = [
    'Sie machen großartige Fortschritte! Weiter so!',
    'Ihr Projekt nimmt Form an. Jeder Schritt bringt Sie näher zum Ziel.',
    'Exzellente Arbeit! Die Struktur wird immer klarer.',
    'Sie sind auf dem richtigen Weg. Lassen Sie uns weitermachen!',
    'Fantastisch! Ihr Plan wird immer detaillierter.',
    'Großartig! Sie verwandeln Ihre Idee systematisch in die Realität.',
    'Perfekt! Jede Aufgabe bringt Sie Ihrem Ziel näher.',
    'Ausgezeichnet! Ihr Projekt gewinnt an Klarheit und Struktur.'
  ];

  private static getInstance(): MotivationManager {
    if (!MotivationManager.instance) {
      MotivationManager.instance = new MotivationManager();
    }
    return MotivationManager.instance;
  }

  constructor() {
    this.currentState = {
      message: this.messages.initial.message,
      type: this.messages.initial.type,
      timestamp: Date.now()
    };
  }

  static setMessage(context: MotivationContext, customMessage?: string): void {
    const instance = this.getInstance();
    instance.updateMessage(context, customMessage);
  }

  static setCustomMessage(message: string, type: MotivationState['type'] = 'default'): void {
    const instance = this.getInstance();
    instance.updateCustomMessage(message, type);
  }

  static getCurrentState(): MotivationState {
    const instance = this.getInstance();
    return { ...instance.currentState };
  }

  static subscribe(listener: (state: MotivationState) => void): () => void {
    const instance = this.getInstance();
    instance.listeners.add(listener);
    return () => instance.listeners.delete(listener);
  }

  static getRandomEncouragement(): string {
    const instance = this.getInstance();
    const randomIndex = Math.floor(Math.random() * instance.encouragingMessages.length);
    return instance.encouragingMessages[randomIndex];
  }

  static getMessageHistory(): MotivationState[] {
    const instance = this.getInstance();
    return [...instance.messageHistory];
  }

  private updateMessage(context: MotivationContext, customMessage?: string): void {
    const messageConfig = this.messages[context];
    const message = customMessage || messageConfig.message;
    
    // Add some variety to success messages
    const finalMessage = messageConfig.type === 'success' && Math.random() < 0.3
      ? MotivationManager.getRandomEncouragement()
      : message;

    this.updateState(finalMessage, messageConfig.type);
  }

  private updateCustomMessage(message: string, type: MotivationState['type']): void {
    this.updateState(message, type);
  }

  private updateState(message: string, type: MotivationState['type']): void {
    // Add current state to history
    this.messageHistory.unshift({ ...this.currentState });
    
    // Limit history size
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(0, this.maxHistorySize);
    }

    // Update current state
    this.currentState = {
      message,
      type,
      timestamp: Date.now()
    };

    // Notify listeners
    this.listeners.forEach(listener => {
      listener({ ...this.currentState });
    });
  }
}

/**
 * React hook for using motivation manager
 */
export function useMotivation() {
  return {
    setMessage: (context: MotivationContext, customMessage?: string) => 
      MotivationManager.setMessage(context, customMessage),
    setCustomMessage: (message: string, type: MotivationState['type'] = 'default') => 
      MotivationManager.setCustomMessage(message, type),
    getCurrentState: () => 
      MotivationManager.getCurrentState(),
    getRandomEncouragement: () => 
      MotivationManager.getRandomEncouragement(),
    getHistory: () => 
      MotivationManager.getMessageHistory()
  };
}

/**
 * Get CSS classes for motivation message styling
 */
export function getMotivationClasses(type: MotivationState['type']): string {
  const baseClasses = 'transition-colors duration-300';
  
  switch (type) {
    case 'loading':
      return `${baseClasses} text-blue-600 dark:text-blue-400`;
    case 'success':
      return `${baseClasses} text-green-600 dark:text-green-400`;
    case 'warning':
      return `${baseClasses} text-orange-600 dark:text-orange-400`;
    case 'error':
      return `${baseClasses} text-red-600 dark:text-red-400`;
    default:
      return `${baseClasses} text-slate-600 dark:text-slate-400`;
  }
}