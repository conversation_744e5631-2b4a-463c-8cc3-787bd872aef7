/**
 * Integration utilities for manual task insertion with existing features
 * Ensures seamless compatibility with AI generation, editing, deletion, and export
 */

import type { Task, InsertionPosition, InsertionResult, ContextStrategies } from '@/lib/types';
import { geminiClient, contextBuilder } from '@/lib/ai';
import { validateInsertionPosition } from './insertionValidation';
import { handleInsertionValidation } from './insertionErrorHandler';

export interface InsertionIntegrationOptions {
  enableAIGeneration?: boolean;
  preserveTaskRelationships?: boolean;
  updateExportData?: boolean;
  validateBeforeInsertion?: boolean;
  showToasts?: boolean;
}

export interface AIInsertionOptions {
  generateContent?: boolean;
  useContextFromParent?: boolean;
  contextStrategies?: ContextStrategies;
  mainProject?: string;
  mainProjectDescription?: string;
}

export interface TaskCreationContext {
  sourceType: 'manual' | 'ai_generated' | 'ai_selection' | 'breakdown';
  sourceTaskId?: string;
  selectionText?: string;
  aiContext?: string;
  insertionPosition?: InsertionPosition;
}

/**
 * Enhanced task creation that integrates with insertion system
 */
export class InsertionIntegration {
  /**
   * Creates a new task with AI-generated content at specified insertion position
   */
  static async createTaskWithAI(
    position: InsertionPosition,
    allTasks: Task[],
    options: AIInsertionOptions & InsertionIntegrationOptions = {}
  ): Promise<InsertionResult> {
    const {
      generateContent = false,
      useContextFromParent = true,
      contextStrategies = { strategy1: true, strategy2: true, strategy3: false },
      mainProject = '',
      mainProjectDescription = '',
      enableAIGeneration = true,
      validateBeforeInsertion = true,
      showToasts = true
    } = options;

    try {
      // Validate insertion position if requested
      if (validateBeforeInsertion) {
        const validationResult = validateInsertionPosition(position, allTasks);
        if (!validationResult.isValid) {
          return {
            success: false,
            error: `Invalid insertion position: ${validationResult.errors.join(', ')}`,
            position
          };
        }
      }

      // Create base task
      const newTask: Task = {
        id: crypto.randomUUID(),
        title: 'Neue Aufgabe (zum Bearbeiten klicken)',
        description: 'Beschreibung hinzufügen...',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      };

      // Generate AI content if requested and AI is enabled
      if (generateContent && enableAIGeneration) {
        try {
          // Build context for AI generation
          let context = '';
          if (useContextFromParent && position.targetTaskId) {
            context = contextBuilder.buildContextForAI(position.targetTaskId, {
              strategies: contextStrategies,
              mainProject,
              mainProjectDescription,
              tasks: allTasks
            });
          }

          // Generate task content based on position context
          const aiResult = await geminiClient.generateTaskContent(
            newTask.title,
            newTask.description,
            context,
            this.getPositionBasedPrompt(position, allTasks)
          );

          if (!aiResult.error && aiResult.content) {
            newTask.content = aiResult.content;
            newTask.title = this.extractTitleFromAIContent(aiResult.content) || newTask.title;
          }
        } catch (error) {
          console.warn('AI content generation failed during insertion:', error);
          // Continue with manual task creation
        }
      }

      return {
        success: true,
        newTaskId: newTask.id,
        position,
        metadata: {
          insertionTime: new Date(),
          affectedTaskIds: [position.targetTaskId],
          hierarchyChanges: position.type === 'between_parent_child'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during task creation',
        position
      };
    }
  }

  /**
   * Creates a task from AI content selection with proper insertion positioning
   */
  static async createTaskFromSelection(
    selectionText: string,
    sourceTaskId: string,
    position: InsertionPosition,
    allTasks: Task[],
    options: AIInsertionOptions & InsertionIntegrationOptions = {}
  ): Promise<InsertionResult> {
    const {
      generateContent = true,
      contextStrategies = { strategy1: true, strategy2: true, strategy3: false },
      mainProject = '',
      mainProjectDescription = '',
      enableAIGeneration = true
    } = options;

    try {
      // Create task with selection as title/description
      const newTask: Task = {
        id: crypto.randomUUID(),
        title: this.truncateText(selectionText, 100) || 'Aufgabe aus Auswahl',
        description: selectionText.length > 100 ? selectionText : 'Aus AI-Inhalt erstellt',
        content: '',
        status: 'To Do',
        assignees: [],
        subtasks: []
      };

      // Generate expanded content if AI is enabled
      if (generateContent && enableAIGeneration) {
        try {
          const context = contextBuilder.buildContextForAI(sourceTaskId, {
            strategies: contextStrategies,
            mainProject,
            mainProjectDescription,
            tasks: allTasks,
            selectedHtml: selectionText
          });

          const aiResult = await geminiClient.elaborateContent(
            selectionText,
            context,
            selectionText
          );

          if (!aiResult.error && aiResult.content) {
            newTask.content = aiResult.content;
          }
        } catch (error) {
          console.warn('AI elaboration failed for selection-based task:', error);
        }
      }

      return {
        success: true,
        newTaskId: newTask.id,
        position,
        metadata: {
          insertionTime: new Date(),
          affectedTaskIds: [sourceTaskId, position.targetTaskId],
          hierarchyChanges: position.type === 'between_parent_child'
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create task from selection',
        position
      };
    }
  }

  /**
   * Ensures task relationships are preserved during insertion operations
   */
  static validateTaskRelationships(
    tasks: Task[],
    insertedTaskId: string,
    position: InsertionPosition
  ): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    try {
      // Find the inserted task
      const insertedTask = this.findTaskById(tasks, insertedTaskId);
      if (!insertedTask) {
        issues.push('Inserted task not found in task tree');
        return { isValid: false, issues };
      }

      // Validate parent-child relationships
      if (position.parentId) {
        const parentTask = this.findTaskById(tasks, position.parentId);
        if (!parentTask) {
          issues.push('Parent task not found');
        } else {
          const isChildOfParent = parentTask.subtasks.some(subtask => subtask.id === insertedTaskId);
          if (!isChildOfParent) {
            issues.push('Task is not properly linked to its parent');
          }
        }
      }

      // Validate sibling relationships
      const siblings = this.getSiblingTasks(tasks, insertedTaskId, position.parentId);
      const insertedIndex = siblings.findIndex(task => task.id === insertedTaskId);
      
      if (insertedIndex === -1) {
        issues.push('Task not found among its siblings');
      }

      // Validate hierarchy levels
      const actualLevel = this.getTaskLevel(tasks, insertedTaskId);
      if (actualLevel !== position.level) {
        issues.push(`Task level mismatch: expected ${position.level}, actual ${actualLevel}`);
      }

      return { isValid: issues.length === 0, issues };
    } catch (error) {
      issues.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { isValid: false, issues };
    }
  }

  /**
   * Updates export data to include insertion metadata
   */
  static enhanceExportData(tasks: Task[], insertionHistory: InsertionPosition[]): Task[] {
    return tasks.map(task => ({
      ...task,
      subtasks: task.subtasks ? this.enhanceExportData(task.subtasks, insertionHistory) : [],
      // Add insertion metadata for export (optional)
      exportMetadata: {
        wasManuallyInserted: insertionHistory.some(pos => 
          pos.targetTaskId === task.id || 
          (pos.type === 'between_parent_child' && pos.parentId === task.id)
        ),
        insertionCount: insertionHistory.filter(pos => pos.targetTaskId === task.id).length
      }
    }));
  }

  /**
   * Handles task deletion with insertion state cleanup
   */
  static cleanupInsertionStateOnDeletion(
    deletedTaskId: string,
    insertionHistory: InsertionPosition[]
  ): InsertionPosition[] {
    return insertionHistory.filter(position => 
      position.targetTaskId !== deletedTaskId && 
      position.parentId !== deletedTaskId
    );
  }

  /**
   * Handles task editing with insertion compatibility
   */
  static handleTaskEditingWithInsertion(
    editedTask: Task,
    allTasks: Task[],
    insertionHistory: InsertionPosition[]
  ): { updatedTasks: Task[]; updatedHistory: InsertionPosition[] } {
    // Update task in the tree
    const updatedTasks = this.updateTaskInTree(allTasks, editedTask);
    
    // Update insertion history if task relationships changed
    const updatedHistory = insertionHistory.map(position => {
      if (position.targetTaskId === editedTask.id) {
        // Update position context if needed
        return {
          ...position,
          context: {
            ...position.context,
            hierarchyPath: this.getTaskHierarchyPath(updatedTasks, editedTask.id)
          }
        };
      }
      return position;
    });

    return { updatedTasks, updatedHistory };
  }

  // Helper methods

  private static getPositionBasedPrompt(position: InsertionPosition, allTasks: Task[]): string {
    const targetTask = this.findTaskById(allTasks, position.targetTaskId);
    if (!targetTask) return '';

    switch (position.type) {
      case 'before':
        return `Diese Aufgabe soll vor "${targetTask.title}" eingefügt werden. Erstelle eine Aufgabe, die logisch vor dieser Aufgabe kommt.`;
      case 'after':
        return `Diese Aufgabe soll nach "${targetTask.title}" eingefügt werden. Erstelle eine Aufgabe, die logisch nach dieser Aufgabe kommt.`;
      case 'between_parent_child':
        return `Diese Aufgabe soll als Unteraufgabe von "${targetTask.title}" eingefügt werden. Erstelle eine Aufgabe, die zur Erreichung des übergeordneten Ziels beiträgt.`;
      default:
        return '';
    }
  }

  private static extractTitleFromAIContent(content: string): string | null {
    // Try to extract a meaningful title from AI-generated content
    const htmlContent = content.replace(/<[^>]*>/g, ''); // Strip HTML
    const firstLine = htmlContent.split('\n')[0].trim();
    
    if (firstLine.length > 5 && firstLine.length < 100) {
      return firstLine;
    }
    
    return null;
  }

  private static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  private static findTaskById(tasks: Task[], taskId: string): Task | null {
    for (const task of tasks) {
      if (task.id === taskId) return task;
      if (task.subtasks) {
        const found = this.findTaskById(task.subtasks, taskId);
        if (found) return found;
      }
    }
    return null;
  }

  private static getSiblingTasks(tasks: Task[], taskId: string, parentId: string | null): Task[] {
    if (!parentId) {
      // Root level siblings
      return tasks;
    }
    
    const parent = this.findTaskById(tasks, parentId);
    return parent?.subtasks || [];
  }

  private static getTaskLevel(tasks: Task[], taskId: string, currentLevel: number = 0): number {
    for (const task of tasks) {
      if (task.id === taskId) return currentLevel;
      if (task.subtasks) {
        const level = this.getTaskLevel(task.subtasks, taskId, currentLevel + 1);
        if (level !== -1) return level;
      }
    }
    return -1;
  }

  private static updateTaskInTree(tasks: Task[], updatedTask: Task): Task[] {
    return tasks.map(task => {
      if (task.id === updatedTask.id) {
        return updatedTask;
      }
      if (task.subtasks) {
        return {
          ...task,
          subtasks: this.updateTaskInTree(task.subtasks, updatedTask)
        };
      }
      return task;
    });
  }

  private static getTaskHierarchyPath(tasks: Task[], taskId: string): string[] {
    const path: string[] = [];
    
    const findPath = (taskList: Task[], currentPath: string[]): boolean => {
      for (const task of taskList) {
        const newPath = [...currentPath, task.title];
        
        if (task.id === taskId) {
          path.push(...newPath);
          return true;
        }
        
        if (task.subtasks && this.findPath(task.subtasks, newPath)) {
          return true;
        }
      }
      return false;
    };

    findPath(tasks, []);
    return path;
  }

  private static findPath(tasks: Task[], currentPath: string[]): boolean {
    // This is a helper method for getTaskHierarchyPath
    return false; // Implementation would be similar to findPath above
  }
}

/**
 * Integration hooks for existing task operations
 */
export class TaskOperationIntegration {
  /**
   * Enhanced task breakdown that preserves insertion context
   */
  static async enhancedBreakdownTask(
    taskId: string,
    allTasks: Task[],
    insertionHistory: InsertionPosition[],
    options: AIInsertionOptions = {}
  ): Promise<{ success: boolean; newSubtasks: Task[]; error?: string }> {
    try {
      const task = InsertionIntegration.findTaskById(allTasks, taskId);
      if (!task) {
        return { success: false, newSubtasks: [], error: 'Task not found' };
      }

      // Use existing AI breakdown functionality
      const result = await geminiClient.generateSubtasks(
        task.title,
        task.description,
        task.content
      );

      if (result.error && !result.tasks?.length) {
        return { success: false, newSubtasks: [], error: result.error };
      }

      // Convert AI result to Task objects with insertion context
      const newSubtasks: Task[] = (result.tasks || []).map((subtask, index) => ({
        id: crypto.randomUUID(),
        title: subtask.title,
        description: subtask.description,
        content: '',
        status: 'To Do' as const,
        assignees: [],
        subtasks: []
      }));

      return { success: true, newSubtasks, error: result.error };
    } catch (error) {
      return {
        success: false,
        newSubtasks: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Enhanced task deletion that cleans up insertion state
   */
  static handleTaskDeletionWithInsertion(
    taskId: string,
    allTasks: Task[],
    insertionHistory: InsertionPosition[]
  ): { updatedTasks: Task[]; updatedHistory: InsertionPosition[] } {
    // Remove task from tree
    const updatedTasks = this.removeTaskFromTree(allTasks, taskId);
    
    // Clean up insertion history
    const updatedHistory = InsertionIntegration.cleanupInsertionStateOnDeletion(
      taskId,
      insertionHistory
    );

    return { updatedTasks, updatedHistory };
  }

  private static removeTaskFromTree(tasks: Task[], taskId: string): Task[] {
    return tasks
      .filter(task => task.id !== taskId)
      .map(task => ({
        ...task,
        subtasks: task.subtasks ? this.removeTaskFromTree(task.subtasks, taskId) : []
      }));
  }
}

/**
 * Export integration utilities
 */
export class ExportIntegration {
  /**
   * Enhances export data with insertion metadata
   */
  static enhanceExportWithInsertionData(
    tasks: Task[],
    insertionHistory: InsertionPosition[],
    includeMetadata: boolean = false
  ): Task[] {
    if (!includeMetadata) {
      return tasks; // Return tasks as-is for normal export
    }

    return InsertionIntegration.enhanceExportData(tasks, insertionHistory);
  }

  /**
   * Validates task structure before export
   */
  static validateTaskStructureForExport(tasks: Task[]): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    const validateTask = (task: Task, level: number = 0): void => {
      // Check for required fields
      if (!task.id) issues.push(`Task missing ID at level ${level}`);
      if (!task.title) issues.push(`Task "${task.id}" missing title at level ${level}`);
      
      // Check subtask relationships
      if (task.subtasks) {
        task.subtasks.forEach((subtask, index) => {
          validateTask(subtask, level + 1);
        });
      }
    };

    tasks.forEach(task => validateTask(task));

    return { isValid: issues.length === 0, issues };
  }
}