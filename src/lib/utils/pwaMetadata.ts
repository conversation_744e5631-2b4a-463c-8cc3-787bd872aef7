import type { PWAMetadata } from '@/lib/export';

/**
 * Generate PWA metadata for export functions
 */
export function generatePWAMetadata(): PWAMetadata {
  const now = new Date();
  
  return {
    isOfflineOnly: true, // Since this is a PWA, assume offline-first
    createdAt: now,
    updatedAt: now,
    deviceInfo: {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language
    },
    appVersion: '1.0.0' // Could be read from package.json or environment
  };
}

/**
 * Get PWA installation status and metadata
 */
export function getPWAInstallationMetadata(): Partial<PWAMetadata> {
  const metadata: Partial<PWAMetadata> = {};
  
  // Check if app is installed (PWA)
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    // App is running in standalone mode (installed)
    metadata.installDate = getInstallDate();
  }
  
  // Check for last sync date from localStorage
  const lastSync = localStorage.getItem('lastSyncDate');
  if (lastSync) {
    metadata.lastSyncAt = new Date(lastSync);
  }
  
  return metadata;
}

/**
 * Get app installation date from localStorage or estimate
 */
function getInstallDate(): Date {
  const stored = localStorage.getItem('pwaInstallDate');
  if (stored) {
    return new Date(stored);
  }
  
  // If not stored, estimate based on first app usage
  const firstUsage = localStorage.getItem('firstAppUsage');
  if (firstUsage) {
    return new Date(firstUsage);
  }
  
  // Fallback to current date
  return new Date();
}

/**
 * Store PWA installation date
 */
export function storePWAInstallDate(): void {
  const now = new Date().toISOString();
  localStorage.setItem('pwaInstallDate', now);
  
  // Also store first usage if not already stored
  if (!localStorage.getItem('firstAppUsage')) {
    localStorage.setItem('firstAppUsage', now);
  }
}

/**
 * Update last sync date
 */
export function updateLastSyncDate(): void {
  localStorage.setItem('lastSyncDate', new Date().toISOString());
}

/**
 * Get comprehensive PWA metadata combining generated and stored data
 */
export function getComprehensivePWAMetadata(): PWAMetadata {
  const base = generatePWAMetadata();
  const installation = getPWAInstallationMetadata();
  
  return {
    ...base,
    ...installation
  };
}

/**
 * Check if the app is running as a PWA
 */
export function isPWAMode(): boolean {
  // Check if running in standalone mode
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return true;
  }
  
  // Check for iOS Safari PWA
  if ((window.navigator as any).standalone === true) {
    return true;
  }
  
  // Check for Android PWA indicators
  if (document.referrer.includes('android-app://')) {
    return true;
  }
  
  return false;
}

/**
 * Get PWA display mode
 */
export function getPWADisplayMode(): 'browser' | 'standalone' | 'minimal-ui' | 'fullscreen' {
  if (window.matchMedia && window.matchMedia('(display-mode: fullscreen)').matches) {
    return 'fullscreen';
  }
  
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    return 'standalone';
  }
  
  if (window.matchMedia && window.matchMedia('(display-mode: minimal-ui)').matches) {
    return 'minimal-ui';
  }
  
  return 'browser';
}

/**
 * Get device and browser information for PWA metadata
 */
export function getDeviceInfo() {
  const ua = navigator.userAgent;
  
  // Detect mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
  
  // Detect OS
  let os = 'Unknown';
  if (ua.includes('Windows')) os = 'Windows';
  else if (ua.includes('Mac')) os = 'macOS';
  else if (ua.includes('Linux')) os = 'Linux';
  else if (ua.includes('Android')) os = 'Android';
  else if (ua.includes('iOS') || ua.includes('iPhone') || ua.includes('iPad')) os = 'iOS';
  
  // Detect browser
  let browser = 'Unknown';
  if (ua.includes('Chrome') && !ua.includes('Edg')) browser = 'Chrome';
  else if (ua.includes('Firefox')) browser = 'Firefox';
  else if (ua.includes('Safari') && !ua.includes('Chrome')) browser = 'Safari';
  else if (ua.includes('Edg')) browser = 'Edge';
  
  return {
    userAgent: ua,
    platform: navigator.platform,
    language: navigator.language,
    isMobile,
    os,
    browser,
    displayMode: getPWADisplayMode(),
    isPWA: isPWAMode()
  };
}