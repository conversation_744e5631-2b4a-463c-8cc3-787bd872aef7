/**
 * Insertion feedback system for visual and auditory feedback
 * Implements requirements 4.4, 4.5 for polished animations and visual feedback
 */

import { ToastManager } from './toastManager';
import type { InsertionPosition } from '@/lib/types';

export interface InsertionFeedbackOptions {
  position: InsertionPosition;
  taskTitle?: string;
  showToast?: boolean;
  playSound?: boolean;
  hapticFeedback?: boolean;
}

export class InsertionFeedback {
  private static instance: InsertionFeedback;
  private audioContext: AudioContext | null = null;
  private successSound: AudioBuffer | null = null;

  static getInstance(): InsertionFeedback {
    if (!InsertionFeedback.instance) {
      InsertionFeedback.instance = new InsertionFeedback();
    }
    return InsertionFeedback.instance;
  }

  /**
   * Initialize audio context and load success sound
   */
  private async initializeAudio(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      
      // Create a simple success sound using Web Audio API
      const sampleRate = this.audioContext.sampleRate;
      const duration = 0.2; // 200ms
      const buffer = this.audioContext.createBuffer(1, sampleRate * duration, sampleRate);
      const data = buffer.getChannelData(0);

      // Generate a pleasant success sound (two-tone chime)
      for (let i = 0; i < buffer.length; i++) {
        const t = i / sampleRate;
        const freq1 = 800; // First tone
        const freq2 = 1200; // Second tone (higher)
        
        let sample = 0;
        if (t < 0.1) {
          // First tone
          sample = Math.sin(2 * Math.PI * freq1 * t) * Math.exp(-t * 10);
        } else {
          // Second tone
          sample = Math.sin(2 * Math.PI * freq2 * (t - 0.1)) * Math.exp(-(t - 0.1) * 15);
        }
        
        data[i] = sample * 0.1; // Keep volume low
      }

      this.successSound = buffer;
    } catch (error) {
      console.warn('Could not initialize audio for insertion feedback:', error);
    }
  }

  /**
   * Play success sound
   */
  private async playSuccessSound(): Promise<void> {
    if (!this.audioContext || !this.successSound) {
      await this.initializeAudio();
    }

    if (this.audioContext && this.successSound) {
      try {
        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        
        source.buffer = this.successSound;
        source.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        // Fade in/out for smoother sound
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
        gainNode.gain.linearRampToValueAtTime(0, this.audioContext.currentTime + 0.2);
        
        source.start();
      } catch (error) {
        console.warn('Could not play success sound:', error);
      }
    }
  }

  /**
   * Trigger haptic feedback on supported devices
   */
  private triggerHapticFeedback(): void {
    if (typeof navigator !== 'undefined' && 'vibrate' in navigator) {
      // Short, pleasant vibration pattern
      navigator.vibrate([50, 30, 50]);
    }
  }

  /**
   * Get user-friendly description for insertion type
   */
  private getInsertionDescription(type: InsertionPosition['type']): string {
    switch (type) {
      case 'before':
        return 'vor der aktuellen Aufgabe';
      case 'after':
        return 'nach der aktuellen Aufgabe';
      case 'between_parent_child':
        return 'als Unteraufgabe';
      default:
        return 'an der gewählten Position';
    }
  }

  /**
   * Show success feedback for task insertion
   */
  static async showSuccess(options: InsertionFeedbackOptions): Promise<void> {
    const instance = this.getInstance();
    const { position, taskTitle, showToast = true, playSound = true, hapticFeedback = true } = options;

    // Show toast notification
    if (showToast) {
      const insertionDescription = instance.getInsertionDescription(position.type);
      const taskInfo = taskTitle ? ` "${taskTitle}"` : '';
      
      ToastManager.success({
        title: 'Aufgabe erfolgreich eingefügt',
        description: `Neue Aufgabe${taskInfo} wurde ${insertionDescription} eingefügt.`,
        duration: 3000
      });
    }

    // Play success sound
    if (playSound) {
      try {
        await instance.playSuccessSound();
      } catch (error) {
        console.warn('Failed to play success sound:', error);
      }
    }

    // Trigger haptic feedback
    if (hapticFeedback) {
      try {
        instance.triggerHapticFeedback();
      } catch (error) {
        console.warn('Failed to trigger haptic feedback:', error);
      }
    }
  }

  /**
   * Show error feedback for failed insertion
   */
  static showError(options: { 
    position: InsertionPosition; 
    error: string; 
    retryAction?: () => void;
  }): void {
    const instance = this.getInstance();
    const insertionDescription = instance.getInsertionDescription(options.position.type);

    ToastManager.error({
      title: 'Aufgabe konnte nicht eingefügt werden',
      description: `Einfügen ${insertionDescription} fehlgeschlagen: ${options.error}`,
      duration: 6000,
      action: options.retryAction ? {
        label: 'Erneut versuchen',
        onClick: options.retryAction
      } : undefined
    });

    // Error haptic feedback (different pattern)
    if (typeof navigator !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100, 50, 100]);
    }
  }

  /**
   * Show loading feedback for insertion in progress
   */
  static showLoading(options: { position: InsertionPosition; taskTitle?: string }): string {
    const instance = this.getInstance();
    const insertionDescription = instance.getInsertionDescription(options.position.type);
    const taskInfo = options.taskTitle ? ` "${options.taskTitle}"` : '';

    return ToastManager.loading({
      title: 'Aufgabe wird eingefügt...',
      description: `Neue Aufgabe${taskInfo} wird ${insertionDescription} eingefügt.`
    });
  }

  /**
   * Dismiss loading feedback
   */
  static dismissLoading(toastId: string): void {
    ToastManager.dismiss(toastId);
  }

  /**
   * Create visual ripple effect at insertion point
   */
  static createRippleEffect(element: HTMLElement, color: string = 'hsl(var(--primary))'): void {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
    ripple.style.top = (rect.height / 2 - size / 2) + 'px';
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.backgroundColor = color;
    ripple.style.opacity = '0.6';
    ripple.style.pointerEvents = 'none';
    ripple.style.transform = 'scale(0)';
    ripple.style.transition = 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.6s ease-out';
    ripple.style.zIndex = '1000';

    element.style.position = 'relative';
    element.appendChild(ripple);

    // Trigger animation
    requestAnimationFrame(() => {
      ripple.style.transform = 'scale(4)';
      ripple.style.opacity = '0';
    });

    // Clean up
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  }

  /**
   * Animate element with success bounce
   */
  static animateSuccessBounce(element: HTMLElement): void {
    element.style.animation = 'none';
    element.offsetHeight; // Trigger reflow
    element.style.animation = 'buttonBounce 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
    
    setTimeout(() => {
      element.style.animation = '';
    }, 600);
  }

  /**
   * Check if user prefers reduced motion
   */
  static prefersReducedMotion(): boolean {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Check if user has sound enabled (based on localStorage preference)
   */
  static isSoundEnabled(): boolean {
    if (typeof localStorage === 'undefined') return true;
    const preference = localStorage.getItem('insertion-sound-enabled');
    return preference !== 'false';
  }

  /**
   * Check if user has haptic feedback enabled
   */
  static isHapticEnabled(): boolean {
    if (typeof localStorage === 'undefined') return true;
    const preference = localStorage.getItem('insertion-haptic-enabled');
    return preference !== 'false';
  }

  /**
   * Set user preferences for feedback
   */
  static setFeedbackPreferences(options: {
    sound?: boolean;
    haptic?: boolean;
    toast?: boolean;
  }): void {
    if (typeof localStorage === 'undefined') return;
    
    if (options.sound !== undefined) {
      localStorage.setItem('insertion-sound-enabled', options.sound.toString());
    }
    if (options.haptic !== undefined) {
      localStorage.setItem('insertion-haptic-enabled', options.haptic.toString());
    }
    if (options.toast !== undefined) {
      localStorage.setItem('insertion-toast-enabled', options.toast.toString());
    }
  }
}

// Export convenience functions
export const showInsertionSuccess = InsertionFeedback.showSuccess;
export const showInsertionError = InsertionFeedback.showError;
export const showInsertionLoading = InsertionFeedback.showLoading;
export const dismissInsertionLoading = InsertionFeedback.dismissLoading;
export const createInsertionRipple = InsertionFeedback.createRippleEffect;
export const animateInsertionSuccess = InsertionFeedback.animateSuccessBounce;