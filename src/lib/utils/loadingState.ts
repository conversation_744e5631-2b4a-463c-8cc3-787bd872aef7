/**
 * Loading state management utilities
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

export type LoadingOperation = 'breakdown' | 'elaborate' | 'solve' | 'questions' | 'content' | false;

export interface LoadingStates {
  [taskId: string]: LoadingOperation;
}

export interface GlobalLoadingState {
  isProjectStarting: boolean;
  isExporting: boolean;
  activeOperations: Set<string>;
}

export class LoadingStateManager {
  private static instance: LoadingStateManager;
  private loadingStates: LoadingStates = {};
  private globalState: GlobalLoadingState = {
    isProjectStarting: false,
    isExporting: false,
    activeOperations: new Set()
  };
  private listeners: Set<(states: LoadingStates, global: GlobalLoadingState) => void> = new Set();

  static getInstance(): LoadingStateManager {
    if (!LoadingStateManager.instance) {
      LoadingStateManager.instance = new LoadingStateManager();
    }
    return LoadingStateManager.instance;
  }

  /**
   * Subscribe to loading state changes
   */
  subscribe(listener: (states: LoadingStates, global: GlobalLoadingState) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state changes
   */
  private notify(): void {
    this.listeners.forEach(listener => {
      listener({ ...this.loadingStates }, { ...this.globalState });
    });
  }

  /**
   * Set loading state for a specific task
   */
  setTaskLoading(taskId: string, operation: LoadingOperation): void {
    if (operation === false) {
      delete this.loadingStates[taskId];
      this.globalState.activeOperations.delete(taskId);
    } else {
      this.loadingStates[taskId] = operation;
      this.globalState.activeOperations.add(taskId);
    }
    this.notify();
  }

  /**
   * Get loading state for a specific task
   */
  getTaskLoading(taskId: string): LoadingOperation {
    return this.loadingStates[taskId] || false;
  }

  /**
   * Check if any task is loading
   */
  isAnyTaskLoading(): boolean {
    return Object.keys(this.loadingStates).length > 0;
  }

  /**
   * Check if a specific operation type is running
   */
  isOperationRunning(operation: Exclude<LoadingOperation, false>): boolean {
    return Object.values(this.loadingStates).includes(operation);
  }

  /**
   * Set global loading state
   */
  setGlobalLoading(key: keyof GlobalLoadingState, value: boolean): void {
    if (key === 'activeOperations') return; // This is managed internally
    
    (this.globalState as any)[key] = value;
    this.notify();
  }

  /**
   * Get current loading states
   */
  getStates(): { loading: LoadingStates; global: GlobalLoadingState } {
    return {
      loading: { ...this.loadingStates },
      global: { ...this.globalState }
    };
  }

  /**
   * Clear all loading states (useful for cleanup)
   */
  clearAll(): void {
    this.loadingStates = {};
    this.globalState.activeOperations.clear();
    this.globalState.isProjectStarting = false;
    this.globalState.isExporting = false;
    this.notify();
  }

  /**
   * Clear loading states for a specific task and all its subtasks
   */
  clearTaskAndSubtasks(taskId: string, allTasks: any[]): void {
    const clearRecursively = (tasks: any[]) => {
      tasks.forEach(task => {
        if (task.id === taskId || this.isDescendantOf(task.id, taskId, allTasks)) {
          this.setTaskLoading(task.id, false);
        }
        if (task.subtasks && task.subtasks.length > 0) {
          clearRecursively(task.subtasks);
        }
      });
    };

    clearRecursively(allTasks);
  }

  /**
   * Helper to check if a task is a descendant of another task
   */
  private isDescendantOf(childId: string, parentId: string, allTasks: any[]): boolean {
    const findParent = (tasks: any[], targetId: string): string | null => {
      for (const task of tasks) {
        if (task.subtasks) {
          for (const subtask of task.subtasks) {
            if (subtask.id === targetId) {
              return task.id;
            }
          }
          const found = findParent(task.subtasks, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    let currentParent = findParent(allTasks, childId);
    while (currentParent) {
      if (currentParent === parentId) return true;
      currentParent = findParent(allTasks, currentParent);
    }
    return false;
  }
}

/**
 * React hook for using loading state manager
 */
export function useLoadingState() {
  const manager = LoadingStateManager.getInstance();
  return {
    setTaskLoading: (taskId: string, operation: LoadingOperation) => 
      manager.setTaskLoading(taskId, operation),
    getTaskLoading: (taskId: string) => 
      manager.getTaskLoading(taskId),
    setGlobalLoading: (key: keyof GlobalLoadingState, value: boolean) => 
      manager.setGlobalLoading(key, value),
    isAnyTaskLoading: () => 
      manager.isAnyTaskLoading(),
    isOperationRunning: (operation: Exclude<LoadingOperation, false>) => 
      manager.isOperationRunning(operation),
    clearAll: () => 
      manager.clearAll(),
    clearTaskAndSubtasks: (taskId: string, allTasks: any[]) => 
      manager.clearTaskAndSubtasks(taskId, allTasks),
    getStates: () => 
      manager.getStates()
  };
}

/**
 * Loading indicator component props
 */
export interface LoadingIndicatorProps {
  operation: LoadingOperation;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Get loading message based on operation type
 */
export function getLoadingMessage(operation: LoadingOperation): string {
  switch (operation) {
    case 'breakdown':
      return 'Zerlege Aufgabe...';
    case 'elaborate':
      return 'Verbessere Text...';
    case 'solve':
      return 'Generiere Inhalt...';
    case 'questions':
      return 'Erstelle Fragen...';
    case 'content':
      return 'Erstelle Inhalt...';
    default:
      return 'Lädt...';
  }
}

/**
 * Get loading icon rotation class based on operation
 */
export function getLoadingIconClass(operation: LoadingOperation): string {
  const baseClass = 'animate-spin';
  
  switch (operation) {
    case 'breakdown':
      return `${baseClass} text-blue-500`;
    case 'elaborate':
      return `${baseClass} text-purple-500`;
    case 'solve':
      return `${baseClass} text-green-500`;
    case 'questions':
      return `${baseClass} text-orange-500`;
    case 'content':
      return `${baseClass} text-indigo-500`;
    default:
      return `${baseClass} text-gray-500`;
  }
}