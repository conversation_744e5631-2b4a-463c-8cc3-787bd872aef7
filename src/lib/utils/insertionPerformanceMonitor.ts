/**
 * Performance monitoring specifically for insertion operations
 * Tracks zone calculations, hover events, and rendering performance
 */

interface InsertionMetrics {
  zoneCalculationTime: number;
  hoverEventCount: number;
  renderCount: number;
  cacheHitRate: number;
  memoryUsage: number;
  timestamp: number;
  taskCount: number;
  level: number;
}

interface ComponentMetrics {
  componentName: string;
  totalRenderTime: number;
  renderCount: number;
  averageRenderTime: number;
  lastRenderTime: number;
  peakRenderTime: number;
  zoneCalculations: number;
  cacheHits: number;
  cacheMisses: number;
}

interface PerformanceAlert {
  type: 'slow_calculation' | 'excessive_renders' | 'memory_leak' | 'cache_miss_rate';
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: number;
  metrics: Partial<InsertionMetrics>;
}

class InsertionPerformanceMonitor {
  private metrics: InsertionMetrics[] = [];
  private componentMetrics = new Map<string, ComponentMetrics>();
  private alerts: PerformanceAlert[] = [];
  private activeOperations = new Map<string, number>();
  private hoverEventBuffer: number[] = [];
  private renderBuffer: number[] = [];
  
  // Performance thresholds
  private readonly SLOW_CALCULATION_THRESHOLD = 16; // 16ms (one frame)
  private readonly EXCESSIVE_RENDERS_THRESHOLD = 10; // renders per second
  private readonly HIGH_MEMORY_THRESHOLD = 50 * 1024 * 1024; // 50MB
  private readonly LOW_CACHE_HIT_RATE = 0.7; // 70%

  /**
   * Start timing a zone calculation operation
   */
  startZoneCalculation(taskId: string, level: number): string {
    const operationId = `zone-calc-${taskId}-${Date.now()}`;
    this.activeOperations.set(operationId, performance.now());
    return operationId;
  }

  /**
   * End timing a zone calculation operation
   */
  endZoneCalculation(
    operationId: string, 
    taskCount: number, 
    level: number,
    cacheHit: boolean = false
  ): number {
    const startTime = this.activeOperations.get(operationId);
    if (!startTime) return 0;

    const endTime = performance.now();
    const calculationTime = endTime - startTime;
    this.activeOperations.delete(operationId);

    // Update component metrics
    const componentName = `TaskItem-Level${level}`;
    this.updateComponentMetrics(componentName, calculationTime, cacheHit);

    // Check for slow calculations
    if (calculationTime > this.SLOW_CALCULATION_THRESHOLD) {
      this.addAlert({
        type: 'slow_calculation',
        message: `Slow zone calculation: ${calculationTime.toFixed(2)}ms for ${taskCount} tasks at level ${level}`,
        severity: calculationTime > 32 ? 'high' : 'medium',
        timestamp: Date.now(),
        metrics: { zoneCalculationTime: calculationTime, taskCount, level }
      });
    }

    return calculationTime;
  }

  /**
   * Record a hover event
   */
  recordHoverEvent(): void {
    const now = Date.now();
    this.hoverEventBuffer.push(now);

    // Keep only events from the last second
    const oneSecondAgo = now - 1000;
    this.hoverEventBuffer = this.hoverEventBuffer.filter(time => time > oneSecondAgo);

    // Check for excessive hover events
    if (this.hoverEventBuffer.length > 60) { // More than 60 events per second
      this.addAlert({
        type: 'excessive_renders',
        message: `Excessive hover events: ${this.hoverEventBuffer.length} events in the last second`,
        severity: 'medium',
        timestamp: now,
        metrics: { hoverEventCount: this.hoverEventBuffer.length }
      });
    }
  }

  /**
   * Record a render operation
   */
  recordRender(componentName: string, renderTime: number): void {
    const now = Date.now();
    this.renderBuffer.push(now);

    // Keep only renders from the last second
    const oneSecondAgo = now - 1000;
    this.renderBuffer = this.renderBuffer.filter(time => time > oneSecondAgo);

    // Update component metrics
    this.updateComponentMetrics(componentName, renderTime, false, true);

    // Check for excessive renders
    if (this.renderBuffer.length > this.EXCESSIVE_RENDERS_THRESHOLD) {
      this.addAlert({
        type: 'excessive_renders',
        message: `Excessive renders: ${this.renderBuffer.length} renders in the last second for ${componentName}`,
        severity: this.renderBuffer.length > 20 ? 'high' : 'medium',
        timestamp: now,
        metrics: { renderCount: this.renderBuffer.length }
      });
    }
  }

  /**
   * Record memory usage
   */
  recordMemoryUsage(): number {
    let memoryUsage = 0;
    
    if ('memory' in performance) {
      // @ts-ignore - Chrome specific API
      memoryUsage = performance.memory.usedJSHeapSize;
      
      if (memoryUsage > this.HIGH_MEMORY_THRESHOLD) {
        this.addAlert({
          type: 'memory_leak',
          message: `High memory usage detected: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`,
          severity: 'high',
          timestamp: Date.now(),
          metrics: { memoryUsage }
        });
      }
    }

    return memoryUsage;
  }

  /**
   * Record cache performance
   */
  recordCachePerformance(hitRate: number, hits: number, misses: number): void {
    if (hitRate < this.LOW_CACHE_HIT_RATE && (hits + misses) > 20) {
      this.addAlert({
        type: 'cache_miss_rate',
        message: `Low cache hit rate: ${(hitRate * 100).toFixed(1)}% (${hits} hits, ${misses} misses)`,
        severity: hitRate < 0.5 ? 'high' : 'medium',
        timestamp: Date.now(),
        metrics: { cacheHitRate: hitRate }
      });
    }
  }

  /**
   * Update component-specific metrics
   */
  private updateComponentMetrics(
    componentName: string, 
    operationTime: number, 
    cacheHit: boolean = false,
    isRender: boolean = false
  ): void {
    const existing = this.componentMetrics.get(componentName);
    
    if (existing) {
      const newRenderCount = isRender ? existing.renderCount + 1 : existing.renderCount;
      const newTotalTime = existing.totalRenderTime + operationTime;
      
      this.componentMetrics.set(componentName, {
        ...existing,
        totalRenderTime: newTotalTime,
        renderCount: newRenderCount,
        averageRenderTime: newTotalTime / Math.max(newRenderCount, 1),
        lastRenderTime: operationTime,
        peakRenderTime: Math.max(existing.peakRenderTime, operationTime),
        zoneCalculations: existing.zoneCalculations + (isRender ? 0 : 1),
        cacheHits: existing.cacheHits + (cacheHit ? 1 : 0),
        cacheMisses: existing.cacheMisses + (cacheHit ? 0 : 1)
      });
    } else {
      this.componentMetrics.set(componentName, {
        componentName,
        totalRenderTime: operationTime,
        renderCount: isRender ? 1 : 0,
        averageRenderTime: operationTime,
        lastRenderTime: operationTime,
        peakRenderTime: operationTime,
        zoneCalculations: isRender ? 0 : 1,
        cacheHits: cacheHit ? 1 : 0,
        cacheMisses: cacheHit ? 0 : 1
      });
    }
  }

  /**
   * Add a performance alert
   */
  private addAlert(alert: PerformanceAlert): void {
    this.alerts.push(alert);
    
    // Keep only last 50 alerts
    if (this.alerts.length > 50) {
      this.alerts.shift();
    }

    // Log high severity alerts immediately
    if (alert.severity === 'high' && process.env.NODE_ENV === 'development') {
      console.warn('Insertion Performance Alert:', alert);
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): {
    components: ComponentMetrics[];
    alerts: PerformanceAlert[];
    summary: {
      totalCalculations: number;
      averageCalculationTime: number;
      totalRenders: number;
      averageRenderTime: number;
      cacheEfficiency: number;
      memoryUsage: number;
    };
  } {
    const components = Array.from(this.componentMetrics.values());
    const totalCalculations = components.reduce((sum, c) => sum + c.zoneCalculations, 0);
    const totalRenderTime = components.reduce((sum, c) => sum + c.totalRenderTime, 0);
    const totalRenders = components.reduce((sum, c) => sum + c.renderCount, 0);
    const totalCacheHits = components.reduce((sum, c) => sum + c.cacheHits, 0);
    const totalCacheMisses = components.reduce((sum, c) => sum + c.cacheMisses, 0);
    
    return {
      components,
      alerts: [...this.alerts],
      summary: {
        totalCalculations,
        averageCalculationTime: totalCalculations > 0 ? totalRenderTime / totalCalculations : 0,
        totalRenders,
        averageRenderTime: totalRenders > 0 ? totalRenderTime / totalRenders : 0,
        cacheEfficiency: (totalCacheHits + totalCacheMisses) > 0 
          ? totalCacheHits / (totalCacheHits + totalCacheMisses) 
          : 0,
        memoryUsage: this.recordMemoryUsage()
      }
    };
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const metrics = this.getMetrics();
    const recommendations: string[] = [];

    // Check average calculation time
    if (metrics.summary.averageCalculationTime > 10) {
      recommendations.push('Zone calculations are slow. Consider implementing more aggressive caching or reducing calculation complexity.');
    }

    // Check cache efficiency
    if (metrics.summary.cacheEfficiency < 0.7) {
      recommendations.push('Cache hit rate is low. Consider increasing cache TTL or optimizing cache key generation.');
    }

    // Check for components with high render times
    const slowComponents = metrics.components.filter(c => c.averageRenderTime > 16);
    if (slowComponents.length > 0) {
      recommendations.push(`Slow rendering components detected: ${slowComponents.map(c => c.componentName).join(', ')}. Consider optimizing render logic.`);
    }

    // Check for excessive renders
    const highRenderComponents = metrics.components.filter(c => c.renderCount > 100);
    if (highRenderComponents.length > 0) {
      recommendations.push(`Components with excessive renders: ${highRenderComponents.map(c => c.componentName).join(', ')}. Consider memoization or reducing re-renders.`);
    }

    // Check recent alerts
    const recentAlerts = this.alerts.filter(a => Date.now() - a.timestamp < 60000); // Last minute
    if (recentAlerts.length > 5) {
      recommendations.push('Multiple performance issues detected recently. Consider reviewing insertion logic and optimizations.');
    }

    return recommendations;
  }

  /**
   * Clear all metrics and alerts
   */
  clear(): void {
    this.metrics = [];
    this.componentMetrics.clear();
    this.alerts = [];
    this.activeOperations.clear();
    this.hoverEventBuffer = [];
    this.renderBuffer = [];
  }

  /**
   * Get component-specific metrics
   */
  getComponentMetrics(componentName: string): ComponentMetrics | undefined {
    return this.componentMetrics.get(componentName);
  }

  /**
   * Start performance monitoring with automatic reporting
   */
  startMonitoring(intervalMs: number = 30000): NodeJS.Timeout {
    return setInterval(() => {
      const metrics = this.getMetrics();
      
      if (process.env.NODE_ENV === 'development') {
        console.group('Insertion Performance Report');
        console.log('Summary:', metrics.summary);
        console.log('Recent Alerts:', metrics.alerts.slice(-5));
        console.log('Recommendations:', this.getRecommendations());
        console.groupEnd();
      }

      // Auto-cleanup old data
      this.cleanup();
    }, intervalMs);
  }

  /**
   * Cleanup old data to prevent memory leaks
   */
  private cleanup(): void {
    const now = Date.now();
    const oneHourAgo = now - 60 * 60 * 1000;

    // Remove old alerts
    this.alerts = this.alerts.filter(alert => alert.timestamp > oneHourAgo);

    // Reset component metrics if they're getting too large
    if (this.componentMetrics.size > 50) {
      // Keep only the most active components
      const sortedComponents = Array.from(this.componentMetrics.entries())
        .sort(([, a], [, b]) => b.renderCount - a.renderCount)
        .slice(0, 25);
      
      this.componentMetrics.clear();
      sortedComponents.forEach(([name, metrics]) => {
        this.componentMetrics.set(name, metrics);
      });
    }
  }

  /**
   * Export performance data for analysis
   */
  exportData(): {
    timestamp: number;
    metrics: ReturnType<typeof this.getMetrics>;
    recommendations: string[];
  } {
    return {
      timestamp: Date.now(),
      metrics: this.getMetrics(),
      recommendations: this.getRecommendations()
    };
  }
}

// Global instance
export const insertionPerformanceMonitor = new InsertionPerformanceMonitor();

/**
 * React hook for insertion performance monitoring
 */
export function useInsertionPerformanceMonitor(componentName: string) {
  const startZoneCalculation = (taskId: string, level: number) => 
    insertionPerformanceMonitor.startZoneCalculation(taskId, level);
  
  const endZoneCalculation = (operationId: string, taskCount: number, level: number, cacheHit?: boolean) =>
    insertionPerformanceMonitor.endZoneCalculation(operationId, taskCount, level, cacheHit);
  
  const recordHoverEvent = () => insertionPerformanceMonitor.recordHoverEvent();
  
  const recordRender = (renderTime: number) => 
    insertionPerformanceMonitor.recordRender(componentName, renderTime);

  return {
    startZoneCalculation,
    endZoneCalculation,
    recordHoverEvent,
    recordRender,
    getMetrics: () => insertionPerformanceMonitor.getMetrics(),
    getComponentMetrics: () => insertionPerformanceMonitor.getComponentMetrics(componentName),
    getRecommendations: () => insertionPerformanceMonitor.getRecommendations()
  };
}

export default insertionPerformanceMonitor;