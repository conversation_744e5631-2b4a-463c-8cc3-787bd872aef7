/**
 * Task operation utilities for consistent task tree manipulation
 * Provides reusable functions for finding, updating, and manipulating tasks
 */

import type { Task } from '../types';

/**
 * Recursively applies an operation to all tasks in a task tree
 * @param tasks - Array of tasks to operate on
 * @param operation - Function to apply to each task
 * @returns Modified array of tasks
 */
export function recursiveTaskOperation<T>(
  tasks: Task[],
  operation: (task: Task) => T
): T[] {
  return tasks.map(task => {
    const result = operation(task);
    
    // If the operation returns a Task and it has subtasks, apply recursively
    if (result && typeof result === 'object' && 'subtasks' in result && Array.isArray(result.subtasks)) {
      return {
        ...result,
        subtasks: recursiveTaskOperation(result.subtasks, operation)
      } as T;
    }
    
    return result;
  });
}

/**
 * Finds a task by ID in a task tree
 * @param tasks - Array of tasks to search in
 * @param taskId - ID of the task to find
 * @returns Found task or null if not found
 */
export function findTask(tasks: Task[], taskId: string): Task | null {
  for (const task of tasks) {
    if (task.id === taskId) {
      return task;
    }
    
    if (task.subtasks && task.subtasks.length > 0) {
      const found = findTask(task.subtasks, taskId);
      if (found) {
        return found;
      }
    }
  }
  
  return null;
}

/**
 * Finds the path to a task (array of parent tasks leading to the target)
 * @param tasks - Array of tasks to search in
 * @param taskId - ID of the target task
 * @param currentPath - Current path being built (used internally)
 * @returns Array of tasks representing the path to the target task
 */
export function findTaskPath(
  tasks: Task[],
  taskId: string,
  currentPath: Task[] = []
): Task[] | null {
  for (const task of tasks) {
    const newPath = [...currentPath, task];
    
    if (task.id === taskId) {
      return newPath;
    }
    
    if (task.subtasks && task.subtasks.length > 0) {
      const found = findTaskPath(task.subtasks, taskId, newPath);
      if (found) {
        return found;
      }
    }
  }
  
  return null;
}

/**
 * Updates a specific task in a task tree
 * @param tasks - Array of tasks to update
 * @param updatedTask - The updated task
 * @returns New array with the updated task
 */
export function updateTask(tasks: Task[], updatedTask: Task): Task[] {
  return tasks.map(task => {
    if (task.id === updatedTask.id) {
      return updatedTask;
    }
    
    if (task.subtasks && task.subtasks.length > 0) {
      return {
        ...task,
        subtasks: updateTask(task.subtasks, updatedTask)
      };
    }
    
    return task;
  });
}

/**
 * Deletes a task from a task tree
 * @param tasks - Array of tasks to delete from
 * @param taskId - ID of the task to delete
 * @returns New array without the deleted task
 */
export function deleteTask(tasks: Task[], taskId: string): Task[] {
  return tasks
    .filter(task => task.id !== taskId)
    .map(task => {
      if (task.subtasks && task.subtasks.length > 0) {
        return {
          ...task,
          subtasks: deleteTask(task.subtasks, taskId)
        };
      }
      return task;
    });
}

/**
 * Adds a new task to a specific parent task
 * @param tasks - Array of tasks to add to
 * @param newTask - The new task to add
 * @param parentId - ID of the parent task (null for root level)
 * @returns New array with the added task
 */
export function addTask(
  tasks: Task[],
  newTask: Task,
  parentId: string | null = null
): Task[] {
  if (!parentId) {
    // Add to root level
    return [...tasks, newTask];
  }
  
  return tasks.map(task => {
    if (task.id === parentId) {
      return {
        ...task,
        subtasks: [...(task.subtasks || []), newTask]
      };
    }
    
    if (task.subtasks && task.subtasks.length > 0) {
      return {
        ...task,
        subtasks: addTask(task.subtasks, newTask, parentId)
      };
    }
    
    return task;
  });
}

/**
 * Adds a new task after a specific task
 * @param tasks - Array of tasks to add to
 * @param newTask - The new task to add
 * @param afterId - ID of the task to add after
 * @returns New array with the added task
 */
export function addTaskAfter(
  tasks: Task[],
  newTask: Task,
  afterId: string
): Task[] {
  const index = tasks.findIndex(task => task.id === afterId);
  
  if (index !== -1) {
    // Found at this level, insert after
    const newTasks = [...tasks];
    newTasks.splice(index + 1, 0, newTask);
    return newTasks;
  }
  
  // Search in subtasks
  return tasks.map(task => {
    if (task.subtasks && task.subtasks.length > 0) {
      return {
        ...task,
        subtasks: addTaskAfter(task.subtasks, newTask, afterId)
      };
    }
    return task;
  });
}

/**
 * Moves a task to a new position in the task tree
 * @param tasks - Array of tasks to modify
 * @param taskId - ID of the task to move
 * @param newParentId - ID of the new parent (null for root level)
 * @param position - Position in the new parent's children (optional)
 * @returns New array with the moved task
 */
export function moveTask(
  tasks: Task[],
  taskId: string,
  newParentId: string | null,
  position?: number
): Task[] {
  // First, find and remove the task
  const taskToMove = findTask(tasks, taskId);
  if (!taskToMove) {
    return tasks;
  }
  
  const tasksWithoutMoved = deleteTask(tasks, taskId);
  
  // Then add it to the new location
  if (!newParentId) {
    // Moving to root level
    if (position !== undefined) {
      const newTasks = [...tasksWithoutMoved];
      newTasks.splice(position, 0, taskToMove);
      return newTasks;
    }
    return [...tasksWithoutMoved, taskToMove];
  }
  
  return tasksWithoutMoved.map(task => {
    if (task.id === newParentId) {
      const newSubtasks = [...(task.subtasks || [])];
      if (position !== undefined) {
        newSubtasks.splice(position, 0, taskToMove);
      } else {
        newSubtasks.push(taskToMove);
      }
      return {
        ...task,
        subtasks: newSubtasks
      };
    }
    
    if (task.subtasks && task.subtasks.length > 0) {
      return {
        ...task,
        subtasks: moveTask(task.subtasks, taskId, newParentId, position)
      };
    }
    
    return task;
  });
}

/**
 * Counts the total number of tasks in a task tree
 * @param tasks - Array of tasks to count
 * @returns Total number of tasks including subtasks
 */
export function countTasks(tasks: Task[]): number {
  let count = 0;
  
  for (const task of tasks) {
    count++;
    if (task.subtasks && task.subtasks.length > 0) {
      count += countTasks(task.subtasks);
    }
  }
  
  return count;
}

/**
 * Counts tasks by status in a task tree
 * @param tasks - Array of tasks to count
 * @param status - Status to count
 * @returns Number of tasks with the specified status
 */
export function countTasksByStatus(tasks: Task[], status: string): number {
  let count = 0;
  
  for (const task of tasks) {
    if (task.status === status) {
      count++;
    }
    if (task.subtasks && task.subtasks.length > 0) {
      count += countTasksByStatus(task.subtasks, status);
    }
  }
  
  return count;
}

/**
 * Gets all tasks in a flat array (depth-first traversal)
 * @param tasks - Array of tasks to flatten
 * @returns Flat array of all tasks
 */
export function flattenTasks(tasks: Task[]): Task[] {
  const result: Task[] = [];
  
  for (const task of tasks) {
    result.push(task);
    if (task.subtasks && task.subtasks.length > 0) {
      result.push(...flattenTasks(task.subtasks));
    }
  }
  
  return result;
}

/**
 * Gets the depth of a task tree
 * @param tasks - Array of tasks to measure
 * @returns Maximum depth of the task tree
 */
export function getTaskTreeDepth(tasks: Task[]): number {
  if (tasks.length === 0) {
    return 0;
  }
  
  let maxDepth = 1;
  
  for (const task of tasks) {
    if (task.subtasks && task.subtasks.length > 0) {
      const subtaskDepth = getTaskTreeDepth(task.subtasks);
      maxDepth = Math.max(maxDepth, 1 + subtaskDepth);
    }
  }
  
  return maxDepth;
}

/**
 * Validates a task tree structure
 * @param tasks - Array of tasks to validate
 * @returns Object with validation results
 */
export function validateTaskTree(tasks: Task[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const seenIds = new Set<string>();
  
  function validateTask(task: Task, path: string = '') {
    const currentPath = path ? `${path} > ${task.title}` : task.title;
    
    // Check for required fields
    if (!task.id) {
      errors.push(`Task at ${currentPath} is missing an ID`);
    } else if (seenIds.has(task.id)) {
      errors.push(`Duplicate task ID found: ${task.id} at ${currentPath}`);
    } else {
      seenIds.add(task.id);
    }
    
    if (!task.title || task.title.trim() === '') {
      warnings.push(`Task at ${currentPath} has an empty title`);
    }
    
    if (!task.status) {
      errors.push(`Task at ${currentPath} is missing a status`);
    }
    
    if (!Array.isArray(task.assignees)) {
      errors.push(`Task at ${currentPath} has invalid assignees field`);
    }
    
    if (!Array.isArray(task.subtasks)) {
      errors.push(`Task at ${currentPath} has invalid subtasks field`);
    } else if (task.subtasks.length > 0) {
      task.subtasks.forEach(subtask => validateTask(subtask, currentPath));
    }
  }
  
  tasks.forEach(task => validateTask(task));
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Creates a new task with default values
 * @param overrides - Properties to override defaults
 * @returns New task with default values
 */
export function createTask(overrides: Partial<Task> = {}): Task {
  return {
    id: crypto.randomUUID(),
    title: 'Neue Aufgabe (zum Bearbeiten klicken)',
    description: 'Beschreibung hinzufügen...',
    content: '',
    status: 'To Do',
    assignees: [],
    subtasks: [],
    isEditing: false,
    isDescriptionEditing: false,
    isAiContentEditing: false,
    ...overrides
  };
}

/**
 * Clones a task tree (deep copy)
 * @param tasks - Array of tasks to clone
 * @returns Deep copy of the task tree
 */
export function cloneTaskTree(tasks: Task[]): Task[] {
  return tasks.map(task => ({
    ...task,
    assignees: [...task.assignees],
    subtasks: task.subtasks ? cloneTaskTree(task.subtasks) : []
  }));
}