/**
 * Lazy loading utilities for performance optimization
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';
import { performanceMonitor } from './performanceMonitor';

/**
 * Enhanced lazy loading with performance monitoring
 */
export function lazyWithPerformance<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  componentName: string
): LazyExoticComponent<T> {
  return lazy(async () => {
    const endTiming = performanceMonitor.startTiming(`lazy-${componentName}`);
    
    try {
      const module = await importFn();
      return module;
    } finally {
      endTiming();
    }
  });
}

/**
 * Preload a lazy component for better UX
 */
export function preloadComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): Promise<{ default: T }> {
  return importFn();
}

/**
 * Lazy load with retry mechanism
 */
export function lazyWithRetry<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  componentName: string,
  maxRetries: number = 3
): LazyExoticComponent<T> {
  return lazy(async () => {
    let lastError: Error | null = null;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        const endTiming = performanceMonitor.startTiming(`lazy-${componentName}-attempt-${i + 1}`);
        const module = await importFn();
        endTiming();
        return module;
      } catch (error) {
        lastError = error as Error;
        console.warn(`Failed to load ${componentName}, attempt ${i + 1}/${maxRetries}:`, error);
        
        // Wait before retry (exponential backoff)
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }
    
    throw lastError || new Error(`Failed to load ${componentName} after ${maxRetries} attempts`);
  });
}

/**
 * Intersection Observer based lazy loading for components
 */
export function useLazyIntersection(
  callback: () => void,
  options: IntersectionObserverInit = {}
) {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return (element: HTMLElement | null) => {
    if (!element || typeof window === 'undefined') return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            callback();
            observer.unobserve(entry.target);
          }
        });
      },
      defaultOptions
    );

    observer.observe(element);

    return () => observer.unobserve(element);
  };
}

/**
 * Progressive image loading with WebP support
 */
export interface ProgressiveImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  webpSrc?: string;
}

export function getOptimizedImageSrc(src: string, width?: number, quality: number = 75): string {
  // For Next.js Image optimization
  if (src.startsWith('/') || src.startsWith('./')) {
    const params = new URLSearchParams();
    if (width) params.set('w', width.toString());
    params.set('q', quality.toString());
    return `/_next/image?url=${encodeURIComponent(src)}&${params.toString()}`;
  }
  
  return src;
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return;

  // Preload critical fonts
  const fontLink = document.createElement('link');
  fontLink.rel = 'preload';
  fontLink.as = 'font';
  fontLink.type = 'font/woff2';
  fontLink.crossOrigin = 'anonymous';
  fontLink.href = '/fonts/inter-var.woff2'; // Adjust path as needed
  document.head.appendChild(fontLink);

  // Preload critical CSS
  const cssLink = document.createElement('link');
  cssLink.rel = 'preload';
  cssLink.as = 'style';
  cssLink.href = '/styles/critical.css'; // Adjust path as needed
  document.head.appendChild(cssLink);
}

/**
 * Module federation for micro-frontends (future enhancement)
 */
export interface ModuleFederationConfig {
  name: string;
  remotes: Record<string, string>;
  shared: Record<string, any>;
}

export function loadFederatedModule(
  remoteName: string,
  moduleName: string
): Promise<any> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = `/remotes/${remoteName}/remoteEntry.js`;
    script.onload = () => {
      // @ts-ignore
      const container = window[remoteName];
      container.init({});
      container.get(moduleName).then((factory: any) => {
        const module = factory();
        resolve(module);
      }).catch(reject);
    };
    script.onerror = reject;
    document.head.appendChild(script);
  });
}