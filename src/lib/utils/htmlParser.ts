/**
 * HTML entity decoding utility functions
 */

// HTML entity mapping for common entities
const HTML_ENTITIES: Record<string, string> = {
  '&amp;': '&',
  '&lt;': '<',
  '&gt;': '>',
  '&quot;': '"',
  '&#39;': "'",
  '&apos;': "'",
  '&nbsp;': ' ',
  '&copy;': '©',
  '&reg;': '®',
  '&trade;': '™',
  '&hellip;': '…',
  '&mdash;': '—',
  '&ndash;': '–',
  '&lsquo;': '\u2018',
  '&rsquo;': '\u2019',
  '&ldquo;': '\u201C',
  '&rdquo;': '\u201D',
  '&bull;': '•',
  '&middot;': '·',
  '&sect;': '§',
  '&para;': '¶',
  '&dagger;': '†',
  '&Dagger;': '‡',
  '&permil;': '‰',
  '&lsaquo;': '‹',
  '&rsaquo;': '›',
  '&euro;': '€',
  '&pound;': '£',
  '&yen;': '¥',
  '&cent;': '¢',
  '&curren;': '¤'
};

/**
 * Decodes HTML entities in a string
 * Handles both named entities (&amp;) and numeric entities (&#39; and &#x27;)
 */
export function decodeHtmlEntities(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  let decoded = text;

  // First, handle named entities
  Object.entries(HTML_ENTITIES).forEach(([entity, char]) => {
    const regex = new RegExp(entity, 'g');
    decoded = decoded.replace(regex, char);
  });

  // Handle numeric entities (decimal)
  decoded = decoded.replace(/&#(\d+);/g, (match, dec) => {
    const num = parseInt(dec, 10);
    if (num >= 0 && num <= 1114111) { // Valid Unicode range
      return String.fromCharCode(num);
    }
    return match; // Return original if invalid
  });

  // Handle numeric entities (hexadecimal)
  decoded = decoded.replace(/&#x([0-9a-fA-F]+);/g, (match, hex) => {
    const num = parseInt(hex, 16);
    if (num >= 0 && num <= 1114111) { // Valid Unicode range
      return String.fromCharCode(num);
    }
    return match; // Return original if invalid
  });

  return decoded;
}

/**
 * Strips HTML tags from a string while preserving the text content
 * Also decodes HTML entities
 */
export function stripHtmlTags(html: string): string {
  if (!html || typeof html !== 'string') {
    return html;
  }

  // Remove HTML tags
  let text = html.replace(/<[^>]*>/g, '');
  
  // Decode HTML entities
  text = decodeHtmlEntities(text);
  
  // Clean up extra whitespace
  text = text.replace(/\s+/g, ' ').trim();
  
  return text;
}

/**
 * Converts HTML to plain text while preserving some structure
 * Useful for export functions
 */
export function htmlToText(html: string): string {
  if (!html || typeof html !== 'string') {
    return html;
  }

  let text = html;

  // Convert common block elements to line breaks
  text = text.replace(/<\/?(div|p|br|h[1-6]|li)[^>]*>/gi, '\n');
  
  // Convert list items to bullet points
  text = text.replace(/<li[^>]*>/gi, '\n• ');
  
  // Remove remaining HTML tags
  text = text.replace(/<[^>]*>/g, '');
  
  // Decode HTML entities
  text = decodeHtmlEntities(text);
  
  // Clean up multiple line breaks and whitespace
  text = text.replace(/\n\s*\n/g, '\n\n');
  text = text.replace(/[ \t]+/g, ' ');
  text = text.trim();
  
  return text;
}

/**
 * Escapes HTML special characters to prevent XSS
 */
export function escapeHtml(text: string): string {
  if (!text || typeof text !== 'string') {
    return text;
  }

  const escapeMap: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;'
  };

  return text.replace(/[&<>"']/g, (char) => escapeMap[char] || char);
}

/**
 * Safely parses HTML content and extracts text
 * Uses browser's DOMParser if available, falls back to regex
 */
export function safeHtmlToText(html: string): string {
  if (!html || typeof html !== 'string') {
    return html;
  }

  // Try using DOMParser if available (browser environment)
  if (typeof window !== 'undefined' && window.DOMParser) {
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      return doc.body.textContent || doc.body.innerText || '';
    } catch (error) {
      console.warn('DOMParser failed, falling back to regex method:', error);
    }
  }

  // Fallback to regex-based method
  return htmlToText(html);
}

/**
 * Wraps selected text with a unique identifier for later replacement
 * Used for text selection and elaboration features
 */
export function wrapSelectedText(html: string, selectedText: string, wrapperId: string): string {
  if (!html || !selectedText || !wrapperId) {
    return html;
  }

  // Escape special regex characters in the selected text
  const escapedText = selectedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  // Create the wrapper with unique ID
  const wrapper = `<span id="${wrapperId}" class="ai-elaboration-target">${selectedText}</span>`;
  
  // Replace the first occurrence of the selected text
  return html.replace(new RegExp(escapedText), wrapper);
}

/**
 * Replaces wrapped text with new content
 * Used after AI elaboration is complete
 */
export function replaceWrappedText(html: string, wrapperId: string, newContent: string): string {
  if (!html || !wrapperId) {
    return html;
  }

  // Create regex to match the wrapper span
  const wrapperRegex = new RegExp(`<span id="${wrapperId}"[^>]*>.*?</span>`, 'g');
  
  // Replace with new content
  return html.replace(wrapperRegex, newContent);
}

/**
 * Extracts text content from a wrapped element
 */
export function extractWrappedText(html: string, wrapperId: string): string {
  if (!html || !wrapperId) {
    return '';
  }

  const wrapperRegex = new RegExp(`<span id="${wrapperId}"[^>]*>(.*?)</span>`, 'g');
  const match = wrapperRegex.exec(html);
  
  return match ? match[1] : '';
}