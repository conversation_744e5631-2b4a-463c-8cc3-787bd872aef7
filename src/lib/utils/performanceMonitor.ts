/**
 * Performance monitoring utilities for PWA metrics
 * Tracks load times, interactions, and app health
 */

export interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  
  // PWA-specific metrics
  appLoadTime: number;
  taskListRenderTime: number;
  aiResponseTime?: number;
  storageOperationTime: number;
  
  // User interaction metrics
  taskCreationTime: number;
  taskEditTime: number;
  exportTime?: number;
  
  // Resource metrics
  bundleSize: number;
  cacheHitRate: number;
  memoryUsage: number;
  
  // Timestamps
  timestamp: number;
  sessionId: string;
}

export interface PerformanceThresholds {
  fcp: number; // 1.8s
  lcp: number; // 2.5s
  fid: number; // 100ms
  cls: number; // 0.1
  appLoadTime: number; // 3s
  taskListRenderTime: number; // 500ms
  aiResponseTime: number; // 10s
  storageOperationTime: number; // 200ms
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private sessionId: string;
  private thresholds: PerformanceThresholds;
  private observers: Map<string, PerformanceObserver> = new Map();
  private reportingInterval: NodeJS.Timeout | null = null;
  private autoReportingEnabled: boolean = false;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.thresholds = {
      fcp: 1800,
      lcp: 2500,
      fid: 100,
      cls: 0.1,
      appLoadTime: 3000,
      taskListRenderTime: 500,
      aiResponseTime: 10000,
      storageOperationTime: 200
    };
    
    this.initializeObservers();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    // Core Web Vitals observers
    this.observeWebVitals();
    
    // Navigation timing
    this.observeNavigationTiming();
    
    // Resource timing
    this.observeResourceTiming();
  }

  private observeWebVitals(): void {
    // First Contentful Paint
    if ('PerformanceObserver' in window) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
        if (fcpEntry) {
          this.recordMetric('fcp', fcpEntry.startTime);
        }
      });
      fcpObserver.observe({ entryTypes: ['paint'] });
      this.observers.set('fcp', fcpObserver);

      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          this.recordMetric('lcp', lastEntry.startTime);
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.set('lcp', lcpObserver);

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            const fid = entry.processingStart - entry.startTime;
            this.recordMetric('fid', fid);
          }
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.set('fid', fidObserver);

      // Cumulative Layout Shift
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.recordMetric('cls', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.set('cls', clsObserver);
    }
  }

  private observeNavigationTiming(): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      window.addEventListener('load', () => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const appLoadTime = navigation.loadEventEnd - navigation.navigationStart;
          this.recordMetric('appLoadTime', appLoadTime);
        }
      });
    }
  }

  private observeResourceTiming(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        let totalSize = 0;
        
        entries.forEach((entry: any) => {
          if (entry.transferSize) {
            totalSize += entry.transferSize;
          }
        });
        
        this.recordMetric('bundleSize', totalSize);
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.set('resource', resourceObserver);
    }
  }

  // Public methods for manual metric recording
  public startTiming(label: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      switch (label) {
        case 'taskListRender':
          this.recordMetric('taskListRenderTime', duration);
          break;
        case 'aiResponse':
          this.recordMetric('aiResponseTime', duration);
          break;
        case 'storageOperation':
          this.recordMetric('storageOperationTime', duration);
          break;
        case 'taskCreation':
          this.recordMetric('taskCreationTime', duration);
          break;
        case 'taskEdit':
          this.recordMetric('taskEditTime', duration);
          break;
        case 'export':
          this.recordMetric('exportTime', duration);
          break;
      }
    };
  }

  public recordCacheHitRate(hits: number, total: number): void {
    const rate = total > 0 ? (hits / total) * 100 : 0;
    this.recordMetric('cacheHitRate', rate);
  }

  public recordMemoryUsage(): void {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (performance as any)) {
      const memory = (performance as any).memory;
      const usage = memory.usedJSHeapSize / memory.totalJSHeapSize * 100;
      this.recordMetric('memoryUsage', usage);
    }
  }

  private recordMetric(key: keyof PerformanceMetrics, value: number): void {
    const currentMetric = this.getCurrentMetric();
    (currentMetric as any)[key] = value;
    
    // Check thresholds and trigger alerts if needed
    this.checkThresholds(key, value);
  }

  private getCurrentMetric(): PerformanceMetrics {
    const now = Date.now();
    let currentMetric = this.metrics.find(m => now - m.timestamp < 60000); // 1 minute window
    
    if (!currentMetric) {
      currentMetric = {
        appLoadTime: 0,
        taskListRenderTime: 0,
        storageOperationTime: 0,
        taskCreationTime: 0,
        taskEditTime: 0,
        bundleSize: 0,
        cacheHitRate: 0,
        memoryUsage: 0,
        timestamp: now,
        sessionId: this.sessionId
      };
      this.metrics.push(currentMetric);
    }
    
    return currentMetric;
  }

  private checkThresholds(key: keyof PerformanceMetrics, value: number): void {
    const threshold = this.thresholds[key as keyof PerformanceThresholds];
    if (threshold && value > threshold) {
      this.triggerPerformanceAlert(key, value, threshold);
    }
  }

  private triggerPerformanceAlert(metric: keyof PerformanceMetrics, value: number, threshold: number): void {
    console.warn(`Performance threshold exceeded for ${metric}: ${value}ms > ${threshold}ms`);
    
    // Dispatch custom event for UI components to handle
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('performance-alert', {
        detail: { metric, value, threshold }
      }));
    }
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getLatestMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  public getAverageMetrics(): Partial<PerformanceMetrics> {
    if (this.metrics.length === 0) return {};

    const averages: any = {};
    const keys: (keyof PerformanceMetrics)[] = [
      'fcp', 'lcp', 'fid', 'cls', 'appLoadTime', 'taskListRenderTime',
      'aiResponseTime', 'storageOperationTime', 'taskCreationTime',
      'taskEditTime', 'exportTime', 'bundleSize', 'cacheHitRate', 'memoryUsage'
    ];

    keys.forEach(key => {
      const values = this.metrics
        .map(m => m[key])
        .filter(v => v !== undefined && v !== null) as number[];
      
      if (values.length > 0) {
        averages[key] = values.reduce((sum, val) => sum + val, 0) / values.length;
      }
    });

    return averages;
  }

  public generatePerformanceReport(): string {
    const latest = this.getLatestMetrics();
    const averages = this.getAverageMetrics();
    const suggestions = this.generateOptimizationSuggestions();
    
    if (!latest) return 'No performance data available';

    const report = [
      '=== Performance Report ===',
      `Session: ${this.sessionId}`,
      `Timestamp: ${new Date(latest.timestamp).toISOString()}`,
      '',
      '--- Core Web Vitals ---',
      `FCP: ${latest.fcp?.toFixed(2) || 'N/A'}ms (avg: ${averages.fcp?.toFixed(2) || 'N/A'}ms)`,
      `LCP: ${latest.lcp?.toFixed(2) || 'N/A'}ms (avg: ${averages.lcp?.toFixed(2) || 'N/A'}ms)`,
      `FID: ${latest.fid?.toFixed(2) || 'N/A'}ms (avg: ${averages.fid?.toFixed(2) || 'N/A'}ms)`,
      `CLS: ${latest.cls?.toFixed(3) || 'N/A'} (avg: ${averages.cls?.toFixed(3) || 'N/A'})`,
      '',
      '--- App Performance ---',
      `App Load Time: ${latest.appLoadTime.toFixed(2)}ms (avg: ${averages.appLoadTime?.toFixed(2) || 'N/A'}ms)`,
      `Task List Render: ${latest.taskListRenderTime.toFixed(2)}ms (avg: ${averages.taskListRenderTime?.toFixed(2) || 'N/A'}ms)`,
      `AI Response Time: ${latest.aiResponseTime?.toFixed(2) || 'N/A'}ms (avg: ${averages.aiResponseTime?.toFixed(2) || 'N/A'}ms)`,
      `Storage Operations: ${latest.storageOperationTime.toFixed(2)}ms (avg: ${averages.storageOperationTime?.toFixed(2) || 'N/A'}ms)`,
      '',
      '--- Resource Usage ---',
      `Bundle Size: ${(latest.bundleSize / 1024).toFixed(2)}KB`,
      `Cache Hit Rate: ${latest.cacheHitRate.toFixed(1)}%`,
      `Memory Usage: ${latest.memoryUsage.toFixed(1)}%`,
      '',
      '--- Optimization Suggestions ---',
      ...suggestions.map(s => `• ${s}`)
    ].join('\n');

    return report;
  }

  public generateOptimizationSuggestions(): string[] {
    const latest = this.getLatestMetrics();
    const suggestions: string[] = [];
    
    if (!latest) return suggestions;

    // Core Web Vitals suggestions
    if (latest.fcp && latest.fcp > this.thresholds.fcp) {
      suggestions.push('First Contentful Paint ist langsam. Reduzieren Sie kritische Ressourcen und optimieren Sie Server-Response-Zeit.');
    }
    
    if (latest.lcp && latest.lcp > this.thresholds.lcp) {
      suggestions.push('Largest Contentful Paint ist langsam. Optimieren Sie Bilder und verwenden Sie Lazy Loading.');
    }
    
    if (latest.fid && latest.fid > this.thresholds.fid) {
      suggestions.push('First Input Delay ist hoch. Reduzieren Sie JavaScript-Ausführungszeit und verwenden Sie Code Splitting.');
    }
    
    if (latest.cls && latest.cls > this.thresholds.cls) {
      suggestions.push('Cumulative Layout Shift ist hoch. Definieren Sie Größen für Bilder und vermeiden Sie dynamische Inhalte.');
    }

    // App-specific suggestions
    if (latest.appLoadTime > this.thresholds.appLoadTime) {
      suggestions.push('App-Ladezeit ist langsam. Implementieren Sie Service Worker Caching und reduzieren Sie Bundle-Größe.');
    }
    
    if (latest.taskListRenderTime > this.thresholds.taskListRenderTime) {
      suggestions.push('Task-Liste rendert langsam. Verwenden Sie Virtualisierung für große Listen und optimieren Sie Re-Renders.');
    }
    
    if (latest.aiResponseTime && latest.aiResponseTime > this.thresholds.aiResponseTime) {
      suggestions.push('AI-Antwortzeit ist langsam. Implementieren Sie Request-Caching und optimieren Sie Prompt-Größe.');
    }
    
    if (latest.storageOperationTime > this.thresholds.storageOperationTime) {
      suggestions.push('Storage-Operationen sind langsam. Verwenden Sie Batch-Updates und optimieren Sie IndexedDB-Queries.');
    }

    // Resource usage suggestions
    if (latest.bundleSize > 500 * 1024) { // 500KB threshold
      suggestions.push('Bundle-Größe ist groß. Implementieren Sie Code Splitting und Tree Shaking.');
    }
    
    if (latest.cacheHitRate < 80) {
      suggestions.push('Cache-Hit-Rate ist niedrig. Optimieren Sie Caching-Strategien und Service Worker-Konfiguration.');
    }
    
    if (latest.memoryUsage > 80) {
      suggestions.push('Speicherverbrauch ist hoch. Überprüfen Sie Memory Leaks und optimieren Sie Datenstrukturen.');
    }

    // General suggestions if performance is good
    if (suggestions.length === 0) {
      suggestions.push('Performance ist gut! Überwachen Sie weiterhin die Metriken für konsistente Leistung.');
    }

    return suggestions;
  }

  public enableAutoReporting(intervalMs: number = 60000): void {
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
    }
    
    this.autoReportingEnabled = true;
    this.reportingInterval = setInterval(() => {
      this.generateAutomaticReport();
    }, intervalMs);
  }

  public disableAutoReporting(): void {
    if (this.reportingInterval) {
      clearInterval(this.reportingInterval);
      this.reportingInterval = null;
    }
    this.autoReportingEnabled = false;
  }

  private generateAutomaticReport(): void {
    const latest = this.getLatestMetrics();
    if (!latest) return;

    const suggestions = this.generateOptimizationSuggestions();
    const criticalIssues = this.getCriticalPerformanceIssues();

    // Only report if there are critical issues or suggestions
    if (criticalIssues.length > 0 || suggestions.length > 1) {
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('performance-report', {
          detail: {
            metrics: latest,
            suggestions,
            criticalIssues,
            timestamp: Date.now()
          }
        }));
      }
    }
  }

  public getCriticalPerformanceIssues(): string[] {
    const latest = this.getLatestMetrics();
    const issues: string[] = [];
    
    if (!latest) return issues;

    // Critical thresholds (worse than normal thresholds)
    if (latest.fcp && latest.fcp > this.thresholds.fcp * 1.5) {
      issues.push('Kritisch langsame First Contentful Paint');
    }
    
    if (latest.lcp && latest.lcp > this.thresholds.lcp * 1.5) {
      issues.push('Kritisch langsame Largest Contentful Paint');
    }
    
    if (latest.appLoadTime > this.thresholds.appLoadTime * 1.5) {
      issues.push('Kritisch langsame App-Ladezeit');
    }
    
    if (latest.memoryUsage > 90) {
      issues.push('Kritisch hoher Speicherverbrauch');
    }
    
    if (latest.cacheHitRate < 50) {
      issues.push('Kritisch niedrige Cache-Hit-Rate');
    }

    return issues;
  }

  public getPerformanceScore(): number {
    const latest = this.getLatestMetrics();
    if (!latest) return 0;

    let score = 100;
    
    // Deduct points for poor metrics
    if (latest.fcp && latest.fcp > this.thresholds.fcp) {
      score -= Math.min(20, (latest.fcp - this.thresholds.fcp) / 100);
    }
    
    if (latest.lcp && latest.lcp > this.thresholds.lcp) {
      score -= Math.min(20, (latest.lcp - this.thresholds.lcp) / 100);
    }
    
    if (latest.fid && latest.fid > this.thresholds.fid) {
      score -= Math.min(15, (latest.fid - this.thresholds.fid) / 10);
    }
    
    if (latest.cls && latest.cls > this.thresholds.cls) {
      score -= Math.min(15, (latest.cls - this.thresholds.cls) * 100);
    }
    
    if (latest.appLoadTime > this.thresholds.appLoadTime) {
      score -= Math.min(20, (latest.appLoadTime - this.thresholds.appLoadTime) / 100);
    }
    
    if (latest.memoryUsage > 80) {
      score -= Math.min(10, (latest.memoryUsage - 80) / 2);
    }

    return Math.max(0, Math.round(score));
  }

  public cleanup(): void {
    this.disableAutoReporting();
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();