/**
 * Content metrics utility functions for calculating character, word, and token counts
 * Supports HTML content stripping for accurate metrics calculation
 */

export interface ContentMetrics {
  characterCount: number;
  wordCount: number;
  estimatedTokenCount: number;
}

/**
 * Strips HTML tags from content to get plain text for accurate counting
 * @param content - Content that may contain HTML tags
 * @returns Plain text content without HTML tags
 */
export function stripHtmlTags(content: string): string {
  if (!content) return '';
  
  // Remove HTML tags using regex
  return content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/&amp;/g, '&') // Replace HTML entities
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .trim();
}

/**
 * Counts characters in content (excluding HTML tags)
 * @param content - Content to count characters in
 * @returns Number of characters
 */
export function countCharacters(content: string): number {
  if (!content) return 0;
  
  const plainText = stripHtmlTags(content);
  return plainText.length;
}

/**
 * Counts words in content (excluding HTML tags)
 * Words are separated by whitespace
 * @param content - Content to count words in
 * @returns Number of words
 */
export function countWords(content: string): number {
  if (!content) return 0;
  
  const plainText = stripHtmlTags(content);
  
  // Split by whitespace and filter out empty strings
  const words = plainText
    .split(/\s+/)
    .filter(word => word.length > 0);
  
  return words.length;
}

/**
 * Estimates token count based on character count
 * Uses rough estimation of 4 characters per token (GPT token estimation)
 * @param content - Content to estimate tokens for
 * @returns Estimated number of tokens
 */
export function estimateTokenCount(content: string): number {
  if (!content) return 0;
  
  const characterCount = countCharacters(content);
  
  // Rough estimation: 4 characters per token
  // This is a conservative estimate for German text
  return Math.ceil(characterCount / 4);
}

/**
 * Calculates all content metrics for given content
 * @param content - Content to analyze (may contain HTML)
 * @returns Object containing all metrics
 */
export function calculateContentMetrics(content: string): ContentMetrics {
  if (!content) {
    return {
      characterCount: 0,
      wordCount: 0,
      estimatedTokenCount: 0,
    };
  }
  
  const characterCount = countCharacters(content);
  const wordCount = countWords(content);
  const estimatedTokenCount = estimateTokenCount(content);
  
  return {
    characterCount,
    wordCount,
    estimatedTokenCount,
  };
}

/**
 * Formats content metrics for display in German
 * @param metrics - Content metrics to format
 * @returns Formatted string for display
 */
export function formatContentMetrics(metrics: ContentMetrics): string {
  const { characterCount, wordCount, estimatedTokenCount } = metrics;
  
  return `${characterCount.toLocaleString('de-DE')} Zeichen | ${wordCount.toLocaleString('de-DE')} Wörter | ~${estimatedTokenCount.toLocaleString('de-DE')} Token`;
}