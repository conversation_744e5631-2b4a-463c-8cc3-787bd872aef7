/**
 * Advanced debouncing utilities for performance optimization
 */

export interface DebounceOptions {
  leading?: boolean;
  trailing?: boolean;
  maxWait?: number;
}

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds
 * have elapsed since the last time the debounced function was invoked.
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: DebounceOptions = {}
): T & { cancel: () => void; flush: () => ReturnType<T> | undefined } {
  const { leading = false, trailing = true, maxWait } = options;
  
  let lastArgs: Parameters<T> | undefined;
  let lastThis: any;
  let maxTimeoutId: NodeJS.Timeout | undefined;
  let result: ReturnType<T> | undefined;
  let timerId: NodeJS.Timeout | undefined;
  let lastCallTime: number | undefined;
  let lastInvokeTime = 0;

  function invokeFunc(time: number): ReturnType<T> {
    const args = lastArgs!;
    const thisArg = lastThis;

    lastArgs = undefined;
    lastThis = undefined;
    lastInvokeTime = time;
    result = func.apply(thisArg, args);
    return result;
  }

  function leadingEdge(time: number): ReturnType<T> {
    // Reset any `maxWait` timer.
    lastInvokeTime = time;
    // Start the timer for the trailing edge.
    timerId = setTimeout(timerExpired, wait);
    // Invoke the leading edge.
    return leading ? invokeFunc(time) : result!;
  }

  function remainingWait(time: number): number {
    const timeSinceLastCall = time - lastCallTime!;
    const timeSinceLastInvoke = time - lastInvokeTime;
    const timeWaiting = wait - timeSinceLastCall;

    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting;
  }

  function shouldInvoke(time: number): boolean {
    const timeSinceLastCall = time - lastCallTime!;
    const timeSinceLastInvoke = time - lastInvokeTime;

    // Either this is the first call, activity has stopped and we're at the
    // trailing edge, the system time has gone backwards and we're treating
    // it as the trailing edge, or we've hit the `maxWait` limit.
    return (
      lastCallTime === undefined ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    );
  }

  function timerExpired(): ReturnType<T> | undefined {
    const time = Date.now();
    if (shouldInvoke(time)) {
      return trailingEdge(time);
    }
    // Restart the timer.
    timerId = setTimeout(timerExpired, remainingWait(time));
    return undefined;
  }

  function trailingEdge(time: number): ReturnType<T> | undefined {
    timerId = undefined;

    // Only invoke if we have `lastArgs` which means `func` has been
    // debounced at least once.
    if (trailing && lastArgs) {
      return invokeFunc(time);
    }
    lastArgs = undefined;
    lastThis = undefined;
    return result;
  }

  function cancel(): void {
    if (timerId !== undefined) {
      clearTimeout(timerId);
    }
    if (maxTimeoutId !== undefined) {
      clearTimeout(maxTimeoutId);
    }
    lastInvokeTime = 0;
    lastArgs = undefined;
    lastCallTime = undefined;
    lastThis = undefined;
    timerId = undefined;
    maxTimeoutId = undefined;
  }

  function flush(): ReturnType<T> | undefined {
    return timerId === undefined ? result : trailingEdge(Date.now());
  }

  function debounced(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const time = Date.now();
    const isInvoking = shouldInvoke(time);

    lastArgs = args;
    lastThis = this;
    lastCallTime = time;

    if (isInvoking) {
      if (timerId === undefined) {
        return leadingEdge(lastCallTime);
      }
      if (maxWait !== undefined) {
        // Handle invocations in a tight loop.
        timerId = setTimeout(timerExpired, wait);
        return invokeFunc(lastCallTime);
      }
    }
    if (timerId === undefined) {
      timerId = setTimeout(timerExpired, wait);
    }
    return result;
  }

  debounced.cancel = cancel;
  debounced.flush = flush;
  return debounced as T & { cancel: () => void; flush: () => ReturnType<T> | undefined };
}

/**
 * Creates a throttled function that only invokes func at most once per every wait milliseconds.
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel: () => void; flush: () => ReturnType<T> | undefined } {
  return debounce(func, wait, {
    leading: true,
    trailing: true,
    maxWait: wait,
    ...options
  });
}

/**
 * Specialized debounce for mouse events with optimized performance
 */
export function debounceMouseEvent<T extends MouseEvent>(
  callback: (event: T) => void,
  delay: number = 16 // ~60fps
): (event: T) => void {
  let timeoutId: NodeJS.Timeout | undefined;
  let lastEvent: T | undefined;

  return (event: T) => {
    lastEvent = event;
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      if (lastEvent) {
        callback(lastEvent);
        lastEvent = undefined;
      }
    }, delay);
  };
}

/**
 * Specialized debounce for resize events
 */
export function debounceResize(
  callback: () => void,
  delay: number = 250
): () => void {
  return debounce(callback, delay, { leading: false, trailing: true });
}

/**
 * Specialized debounce for scroll events with RAF optimization
 */
export function debounceScroll(
  callback: () => void,
  useRAF: boolean = true
): () => void {
  if (!useRAF) {
    return debounce(callback, 16, { leading: false, trailing: true });
  }

  let rafId: number | undefined;
  let scheduled = false;

  return () => {
    if (scheduled) return;
    
    scheduled = true;
    rafId = requestAnimationFrame(() => {
      callback();
      scheduled = false;
    });
  };
}

/**
 * Advanced debounce with priority queue for different event types
 */
export class PriorityDebouncer {
  private queues = new Map<string, {
    callback: () => void;
    timeout: NodeJS.Timeout | undefined;
    priority: number;
  }>();

  debounce(
    key: string,
    callback: () => void,
    delay: number,
    priority: number = 0
  ): void {
    const existing = this.queues.get(key);
    
    // Clear existing timeout
    if (existing?.timeout) {
      clearTimeout(existing.timeout);
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      callback();
      this.queues.delete(key);
    }, delay);

    this.queues.set(key, { callback, timeout, priority });
  }

  flush(key?: string): void {
    if (key) {
      const item = this.queues.get(key);
      if (item) {
        if (item.timeout) clearTimeout(item.timeout);
        item.callback();
        this.queues.delete(key);
      }
    } else {
      // Flush all in priority order
      const sorted = Array.from(this.queues.entries())
        .sort(([, a], [, b]) => b.priority - a.priority);
      
      for (const [key, item] of sorted) {
        if (item.timeout) clearTimeout(item.timeout);
        item.callback();
        this.queues.delete(key);
      }
    }
  }

  cancel(key?: string): void {
    if (key) {
      const item = this.queues.get(key);
      if (item?.timeout) {
        clearTimeout(item.timeout);
        this.queues.delete(key);
      }
    } else {
      for (const [key, item] of this.queues) {
        if (item.timeout) clearTimeout(item.timeout);
        this.queues.delete(key);
      }
    }
  }

  size(): number {
    return this.queues.size;
  }
}

/**
 * React hook for debounced values
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * React hook for debounced callbacks
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  options?: DebounceOptions
): T & { cancel: () => void; flush: () => ReturnType<T> | undefined } {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay, options),
    [callback, delay, options]
  );

  useEffect(() => {
    return () => {
      debouncedCallback.cancel();
    };
  }, [debouncedCallback]);

  return debouncedCallback;
}

// Import React hooks
import { useState, useEffect, useMemo } from 'react';