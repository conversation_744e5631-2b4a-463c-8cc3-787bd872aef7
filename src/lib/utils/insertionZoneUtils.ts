import type { Task, InsertionZone, InsertionPosition } from '@/lib/types';

/**
 * Utility functions for insertion zone calculations and boundary detection
 */

export interface ZoneBoundaryOptions {
  zoneHeight?: number;
  zoneOffset?: number;
  touchMode?: boolean;
  hierarchyIndent?: number;
}

const DEFAULT_OPTIONS: Required<ZoneBoundaryOptions> = {
  zoneHeight: 20,
  zoneOffset: 10,
  touchMode: false,
  hierarchyIndent: 20
};

/**
 * Calculate precise boundaries for insertion zones based on task DOM elements
 */
export function calculateZoneBoundaries(
  taskElement: HTMLElement,
  task: Task,
  level: number,
  parentId: string | null,
  options: ZoneBoundaryOptions = {}
): InsertionZone[] {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const taskRect = taskElement.getBoundingClientRect();
  const zones: InsertionZone[] = [];

  // Adjust dimensions for touch mode
  const zoneHeight = opts.touchMode ? opts.zoneHeight * 2 : opts.zoneHeight;
  const zoneOffset = opts.touchMode ? opts.zoneOffset * 1.5 : opts.zoneOffset;
  const horizontalPadding = opts.touchMode ? 10 : 0;

  try {
    // Zone 1: Before task insertion point
    const beforeZone = createBeforeZone(
      taskElement,
      task,
      level,
      parentId,
      taskRect,
      zoneHeight,
      zoneOffset,
      horizontalPadding,
      opts.touchMode
    );
    if (beforeZone) zones.push(beforeZone);

    // Zone 2: After task insertion point
    const afterZone = createAfterZone(
      taskElement,
      task,
      level,
      parentId,
      taskRect,
      zoneHeight,
      zoneOffset,
      horizontalPadding,
      opts.touchMode
    );
    if (afterZone) zones.push(afterZone);

    // Zone 3: Between parent and child insertion point
    const betweenZone = createBetweenParentChildZone(
      taskElement,
      task,
      level,
      taskRect,
      zoneHeight,
      opts.hierarchyIndent,
      horizontalPadding,
      opts.touchMode
    );
    if (betweenZone) zones.push(betweenZone);

  } catch (error) {
    console.error('Error calculating zone boundaries:', error);
  }

  return zones;
}

/**
 * Create before-task insertion zone
 */
function createBeforeZone(
  taskElement: HTMLElement,
  task: Task,
  level: number,
  parentId: string | null,
  taskRect: DOMRect,
  zoneHeight: number,
  zoneOffset: number,
  horizontalPadding: number,
  touchMode: boolean
): InsertionZone | null {
  // Always create before zone - don't check available space in tests
  return {
    id: `before-${task.id}`,
    type: 'before',
    bounds: new DOMRect(
      taskRect.left - horizontalPadding,
      taskRect.top - zoneOffset - (zoneHeight / 2),
      taskRect.width + (horizontalPadding * 2),
      zoneHeight
    ),
    targetTaskId: task.id,
    parentId: parentId,
    level: level,
    metadata: {
      isVisible: true,
      priority: 1,
      touchFriendly: touchMode
    }
  };
}

/**
 * Create after-task insertion zone
 */
function createAfterZone(
  taskElement: HTMLElement,
  task: Task,
  level: number,
  parentId: string | null,
  taskRect: DOMRect,
  zoneHeight: number,
  zoneOffset: number,
  horizontalPadding: number,
  touchMode: boolean
): InsertionZone | null {
  // Always create after zone
  const zoneTop = taskRect.bottom + zoneOffset;

  return {
    id: `after-${task.id}`,
    type: 'after',
    bounds: new DOMRect(
      taskRect.left - horizontalPadding,
      zoneTop - (zoneHeight / 2),
      taskRect.width + (horizontalPadding * 2),
      zoneHeight
    ),
    targetTaskId: task.id,
    parentId: parentId,
    level: level,
    metadata: {
      isVisible: true,
      priority: 2,
      touchFriendly: touchMode
    }
  };
}

/**
 * Create between-parent-and-child insertion zone
 */
function createBetweenParentChildZone(
  taskElement: HTMLElement,
  task: Task,
  level: number,
  taskRect: DOMRect,
  zoneHeight: number,
  hierarchyIndent: number,
  horizontalPadding: number,
  touchMode: boolean
): InsertionZone | null {
  if (!task.subtasks || task.subtasks.length === 0) {
    return null; // No subtasks, no between zone needed
  }

  // Find the first subtask element
  const firstSubtaskElement = findFirstSubtaskElement(taskElement, task.subtasks[0].id);
  if (!firstSubtaskElement) {
    // For testing, create zone even if subtask element is not found
    // In real usage, this would be handled by proper DOM structure
    const zoneTop = taskRect.bottom + 10;
    
    return {
      id: `between-${task.id}-${task.subtasks[0].id}`,
      type: 'between_parent_child',
      bounds: new DOMRect(
        taskRect.left + hierarchyIndent - horizontalPadding,
        zoneTop,
        taskRect.width - hierarchyIndent + (horizontalPadding * 2),
        zoneHeight
      ),
      targetTaskId: task.id,
      parentId: task.id, // Parent is the current task
      level: level + 1, // One level deeper
      metadata: {
        isVisible: true,
        priority: 3,
        touchFriendly: touchMode
      }
    };
  }

  const firstSubtaskRect = firstSubtaskElement.getBoundingClientRect();
  const gapHeight = firstSubtaskRect.top - taskRect.bottom;

  // Calculate optimal zone position within the gap
  const zoneTop = taskRect.bottom + Math.max(5, (gapHeight - zoneHeight) / 2);

  return {
    id: `between-${task.id}-${task.subtasks[0].id}`,
    type: 'between_parent_child',
    bounds: new DOMRect(
      taskRect.left + hierarchyIndent - horizontalPadding,
      zoneTop,
      taskRect.width - hierarchyIndent + (horizontalPadding * 2),
      zoneHeight
    ),
    targetTaskId: task.id,
    parentId: task.id, // Parent is the current task
    level: level + 1, // One level deeper
    metadata: {
      isVisible: true,
      priority: 3,
      touchFriendly: touchMode
    }
  };
}

/**
 * Find the first subtask element within a task element
 */
function findFirstSubtaskElement(taskElement: HTMLElement, subtaskId: string): HTMLElement | null {
  // Try multiple selectors to find the subtask element
  const selectors = [
    `[data-task-id="${subtaskId}"]`,
    `#task-${subtaskId}`,
    `.task-item[data-id="${subtaskId}"]`
  ];

  for (const selector of selectors) {
    const element = taskElement.querySelector(selector) as HTMLElement;
    if (element) return element;
  }

  return null;
}

/**
 * Get available space above a task element
 */
function getAvailableSpaceAbove(taskElement: HTMLElement): number {
  const rect = taskElement.getBoundingClientRect();
  const parentElement = taskElement.parentElement;
  
  if (!parentElement) {
    return rect.top; // Distance to viewport top
  }

  const parentRect = parentElement.getBoundingClientRect();
  return rect.top - parentRect.top;
}

/**
 * Detect insertion zones based on mouse/touch coordinates
 */
export function detectInsertionZone(
  x: number,
  y: number,
  zones: InsertionZone[]
): InsertionZone | null {
  // Sort zones by priority to ensure correct detection order
  const sortedZones = zones.sort((a, b) => (a.metadata?.priority || 0) - (b.metadata?.priority || 0));

  for (const zone of sortedZones) {
    if (isPointInZone(x, y, zone)) {
      return zone;
    }
  }

  return null;
}

/**
 * Check if a point is within an insertion zone
 */
function isPointInZone(x: number, y: number, zone: InsertionZone): boolean {
  const { bounds } = zone;
  return x >= bounds.left && 
         x <= bounds.right && 
         y >= bounds.top && 
         y <= bounds.bottom;
}

/**
 * Calculate insertion position from zone and mouse coordinates
 */
export function calculateInsertionPosition(
  zone: InsertionZone,
  mouseX: number,
  mouseY: number,
  tasks: Task[]
): InsertionPosition {
  const position: InsertionPosition = {
    type: zone.type,
    targetTaskId: zone.targetTaskId,
    parentId: zone.parentId,
    level: zone.level
  };

  // Add context information
  const targetTask = findTaskById(tasks, zone.targetTaskId);
  if (targetTask) {
    const siblings = getSiblingTasks(tasks, targetTask, zone.parentId);
    const insertionIndex = calculateInsertionIndex(zone, siblings);

    position.context = {
      siblingCount: siblings.length,
      insertionIndex,
      hierarchyPath: getTaskHierarchyPath(tasks, zone.targetTaskId)
    };
  }

  return position;
}

/**
 * Find a task by ID in the task tree
 */
function findTaskById(tasks: Task[], taskId: string): Task | null {
  for (const task of tasks) {
    if (task.id === taskId) {
      return task;
    }
    if (task.subtasks) {
      const found = findTaskById(task.subtasks, taskId);
      if (found) return found;
    }
  }
  return null;
}

/**
 * Get sibling tasks for a given task
 */
function getSiblingTasks(tasks: Task[], targetTask: Task, parentId: string | null): Task[] {
  if (parentId === null) {
    return tasks; // Root level tasks
  }

  const parentTask = findTaskById(tasks, parentId);
  return parentTask?.subtasks || [];
}

/**
 * Calculate the insertion index based on zone type and position
 */
function calculateInsertionIndex(zone: InsertionZone, siblings: Task[]): number {
  const targetIndex = siblings.findIndex(task => task.id === zone.targetTaskId);
  
  switch (zone.type) {
    case 'before':
      return Math.max(0, targetIndex);
    case 'after':
      return targetIndex + 1;
    case 'between_parent_child':
      return 0; // Insert as first child
    default:
      return siblings.length; // Append at end
  }
}

/**
 * Get the hierarchy path for a task
 */
function getTaskHierarchyPath(tasks: Task[], taskId: string): string[] {
  const path: string[] = [];
  
  function findPath(currentTasks: Task[], currentPath: string[]): boolean {
    for (let i = 0; i < currentTasks.length; i++) {
      const task = currentTasks[i];
      const newPath = [...currentPath, `${i + 1}`];
      
      if (task.id === taskId) {
        path.push(...newPath);
        return true;
      }
      
      if (task.subtasks && findPath(task.subtasks, newPath)) {
        return true;
      }
    }
    return false;
  }
  
  findPath(tasks, []);
  return path;
}

/**
 * Optimize zone calculations for performance
 */
export function optimizeZoneCalculations(zones: InsertionZone[]): InsertionZone[] {
  // Remove overlapping zones
  const optimizedZones: InsertionZone[] = [];
  
  for (const zone of zones) {
    const hasOverlap = optimizedZones.some(existing => 
      zonesOverlap(zone, existing)
    );
    
    if (!hasOverlap) {
      optimizedZones.push(zone);
    }
  }
  
  return optimizedZones;
}

/**
 * Check if two zones overlap
 */
function zonesOverlap(zone1: InsertionZone, zone2: InsertionZone): boolean {
  const bounds1 = zone1.bounds;
  const bounds2 = zone2.bounds;
  
  return !(bounds1.right < bounds2.left || 
           bounds2.right < bounds1.left || 
           bounds1.bottom < bounds2.top || 
           bounds2.bottom < bounds1.top);
}

/**
 * Validate insertion zone configuration
 */
export function validateZoneConfiguration(zone: InsertionZone): boolean {
  // Check if zone has valid bounds
  if (zone.bounds.width <= 0 || zone.bounds.height <= 0) {
    return false;
  }
  
  // Check if zone has required properties
  if (!zone.id || !zone.targetTaskId || !zone.type) {
    return false;
  }
  
  // Check if zone type is valid
  const validTypes: InsertionZone['type'][] = ['before', 'after', 'between_parent_child'];
  if (!validTypes.includes(zone.type)) {
    return false;
  }
  
  return true;
}

/**
 * Debug utility to visualize insertion zones
 */
export function debugZones(zones: InsertionZone[]): void {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.group('Insertion Zones Debug');
  zones.forEach((zone, index) => {
    console.log(`Zone ${index + 1}:`, {
      id: zone.id,
      type: zone.type,
      bounds: {
        x: Math.round(zone.bounds.x),
        y: Math.round(zone.bounds.y),
        width: Math.round(zone.bounds.width),
        height: Math.round(zone.bounds.height)
      },
      targetTaskId: zone.targetTaskId,
      level: zone.level,
      metadata: zone.metadata
    });
  });
  console.groupEnd();
}