import type { InsertionZone } from '@/lib/types';

/**
 * Performance-optimized caching system for insertion zones
 * Implements LRU cache with automatic cleanup and memory management
 */

interface CacheEntry {
  zones: InsertionZone[];
  timestamp: number;
  elementBounds: DOMRect;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  size: number;
  hitRate: number;
  totalRequests: number;
  totalHits: number;
  oldestEntry: number | null;
  newestEntry: number | null;
  memoryUsage: number; // Estimated memory usage in bytes
}

class InsertionZoneCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize: number;
  private ttl: number; // Time to live in milliseconds
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  // Statistics tracking
  private stats = {
    totalRequests: 0,
    totalHits: 0,
    totalMisses: 0
  };

  constructor(maxSize = 100, ttl = 5000) {
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.startCleanupTimer();
  }

  /**
   * Generate cache key from task and element properties
   */
  generateKey(
    taskId: string,
    level: number,
    elementBounds: DOMRect,
    subtaskCount: number = 0
  ): string {
    // Create a stable key that changes when layout changes
    const boundsKey = `${Math.round(elementBounds.x)}-${Math.round(elementBounds.y)}-${Math.round(elementBounds.width)}-${Math.round(elementBounds.height)}`;
    return `${taskId}-${level}-${boundsKey}-${subtaskCount}`;
  }

  /**
   * Get zones from cache
   */
  get(key: string, elementBounds?: DOMRect): InsertionZone[] | null {
    this.stats.totalRequests++;
    
    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.totalMisses++;
      return null;
    }

    // Check if entry has expired
    const now = Date.now();
    if (now - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.stats.totalMisses++;
      return null;
    }

    // Validate element bounds if provided
    if (elementBounds && !this.boundsMatch(entry.elementBounds, elementBounds)) {
      this.cache.delete(key);
      this.stats.totalMisses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    
    this.stats.totalHits++;
    return entry.zones;
  }

  /**
   * Set zones in cache
   */
  set(key: string, zones: InsertionZone[], elementBounds: DOMRect): void {
    const now = Date.now();
    
    // If cache is full, remove least recently used entry
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry = {
      zones: this.cloneZones(zones),
      timestamp: now,
      elementBounds: this.cloneBounds(elementBounds),
      accessCount: 1,
      lastAccessed: now
    };

    this.cache.set(key, entry);
  }

  /**
   * Check if cached bounds match current bounds
   */
  private boundsMatch(cached: DOMRect, current: DOMRect, tolerance = 1): boolean {
    return Math.abs(cached.x - current.x) < tolerance &&
           Math.abs(cached.y - current.y) < tolerance &&
           Math.abs(cached.width - current.width) < tolerance &&
           Math.abs(cached.height - current.height) < tolerance;
  }

  /**
   * Clone zones to prevent mutation
   */
  private cloneZones(zones: InsertionZone[]): InsertionZone[] {
    return zones.map(zone => ({
      ...zone,
      bounds: this.cloneBounds(zone.bounds),
      metadata: zone.metadata ? { ...zone.metadata } : undefined
    }));
  }

  /**
   * Clone DOMRect to prevent mutation
   */
  private cloneBounds(bounds: DOMRect): DOMRect {
    return new DOMRect(bounds.x, bounds.y, bounds.width, bounds.height);
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clear expired entries
   */
  private clearExpired(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.clearExpired();
    }, this.ttl / 2); // Clean up twice per TTL period
  }

  /**
   * Stop cleanup timer
   */
  private stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Clear all cached entries
   */
  clear(): void {
    this.cache.clear();
    this.stats = {
      totalRequests: 0,
      totalHits: 0,
      totalMisses: 0
    };
  }

  /**
   * Delete specific cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Check if cache has entry
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check if expired
    const now = Date.now();
    if (now - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(entry => entry.timestamp);
    
    // Estimate memory usage (rough calculation)
    const memoryUsage = this.cache.size * 500; // Approximate bytes per entry

    return {
      size: this.cache.size,
      hitRate: this.stats.totalRequests > 0 ? this.stats.totalHits / this.stats.totalRequests : 0,
      totalRequests: this.stats.totalRequests,
      totalHits: this.stats.totalHits,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : null,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : null,
      memoryUsage
    };
  }

  /**
   * Get cache keys for debugging
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Optimize cache by removing least accessed entries
   */
  optimize(): void {
    if (this.cache.size <= this.maxSize * 0.8) return; // Only optimize if cache is 80% full

    // Sort entries by access count and last accessed time
    const entries = Array.from(this.cache.entries()).sort((a, b) => {
      const [, entryA] = a;
      const [, entryB] = b;
      
      // Prioritize by access count, then by recency
      if (entryA.accessCount !== entryB.accessCount) {
        return entryA.accessCount - entryB.accessCount;
      }
      return entryA.lastAccessed - entryB.lastAccessed;
    });

    // Remove bottom 25% of entries
    const removeCount = Math.floor(this.cache.size * 0.25);
    for (let i = 0; i < removeCount; i++) {
      const [key] = entries[i];
      this.cache.delete(key);
    }
  }

  /**
   * Preload zones for a set of tasks (optional optimization)
   */
  preload(taskElements: Array<{ key: string; zones: InsertionZone[]; bounds: DOMRect }>): void {
    taskElements.forEach(({ key, zones, bounds }) => {
      if (!this.has(key)) {
        this.set(key, zones, bounds);
      }
    });
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopCleanupTimer();
    this.clear();
  }
}

// Global cache instance
const globalZoneCache = new InsertionZoneCache();

// Export cache instance and utilities
export { InsertionZoneCache };
export default globalZoneCache;

/**
 * Hook-friendly cache interface
 */
export const zoneCacheUtils = {
  get: (key: string, elementBounds?: DOMRect) => globalZoneCache.get(key, elementBounds),
  set: (key: string, zones: InsertionZone[], elementBounds: DOMRect) => 
    globalZoneCache.set(key, zones, elementBounds),
  delete: (key: string) => globalZoneCache.delete(key),
  clear: () => globalZoneCache.clear(),
  has: (key: string) => globalZoneCache.has(key),
  getStats: () => globalZoneCache.getStats(),
  generateKey: (taskId: string, level: number, elementBounds: DOMRect, subtaskCount?: number) =>
    globalZoneCache.generateKey(taskId, level, elementBounds, subtaskCount),
  optimize: () => globalZoneCache.optimize()
};

/**
 * Performance monitoring utilities with enhanced metrics
 */
class CachePerformanceMonitor {
  private performanceHistory: Array<{
    timestamp: number;
    hitRate: number;
    size: number;
    memoryUsage: number;
    avgResponseTime: number;
  }> = [];

  /**
   * Monitor cache performance over time with detailed metrics
   */
  startMonitoring(intervalMs = 10000): NodeJS.Timeout {
    return setInterval(() => {
      const stats = globalZoneCache.getStats();
      const timestamp = Date.now();
      
      // Calculate average response time (simulated based on cache hits)
      const avgResponseTime = stats.hitRate > 0 ? 1 : 15; // 1ms for cache hit, 15ms for calculation
      
      // Store performance history
      this.performanceHistory.push({
        timestamp,
        hitRate: stats.hitRate,
        size: stats.size,
        memoryUsage: stats.memoryUsage,
        avgResponseTime
      });

      // Keep only last 100 entries
      if (this.performanceHistory.length > 100) {
        this.performanceHistory.shift();
      }
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Insertion Zone Cache Stats:', {
          size: stats.size,
          hitRate: `${(stats.hitRate * 100).toFixed(1)}%`,
          memoryUsage: `${(stats.memoryUsage / 1024).toFixed(1)}KB`,
          requests: stats.totalRequests,
          hits: stats.totalHits,
          avgResponseTime: `${avgResponseTime}ms`,
          trend: this.getPerformanceTrend()
        });
      }

      // Auto-optimize based on performance metrics
      this.autoOptimize(stats);
    }, intervalMs);
  }

  /**
   * Auto-optimization based on performance metrics
   */
  autoOptimize(stats: CacheStats): void {
    // Optimize if hit rate is low and we have enough data
    if (stats.hitRate < 0.5 && stats.totalRequests > 50) {
      globalZoneCache.optimize();
    }

    // Clear cache if memory usage is too high
    if (stats.memoryUsage > 10 * 1024 * 1024) { // 10MB
      globalZoneCache.clear();
      console.warn('Insertion zone cache cleared due to high memory usage');
    }

    // Optimize if cache is getting too large
    if (stats.size > 200) {
      globalZoneCache.optimize();
    }
  }

  /**
   * Get performance trend analysis
   */
  getPerformanceTrend(): 'improving' | 'declining' | 'stable' | 'insufficient_data' {
    if (this.performanceHistory.length < 5) {
      return 'insufficient_data';
    }

    const recent = this.performanceHistory.slice(-3);
    const older = this.performanceHistory.slice(-6, -3);

    const recentAvgHitRate = recent.reduce((sum, entry) => sum + entry.hitRate, 0) / recent.length;
    const olderAvgHitRate = older.reduce((sum, entry) => sum + entry.hitRate, 0) / older.length;

    const difference = recentAvgHitRate - olderAvgHitRate;

    if (Math.abs(difference) < 0.05) return 'stable';
    return difference > 0 ? 'improving' : 'declining';
  }

  /**
   * Get detailed performance metrics
   */
  getMetrics() {
    const stats = globalZoneCache.getStats();
    return {
      ...stats,
      history: [...this.performanceHistory],
      trend: this.getPerformanceTrend(),
      avgResponseTime: stats.hitRate > 0 ? 1 : 15,
      efficiency: this.calculateEfficiency(stats)
    };
  }

  /**
   * Calculate cache efficiency score (0-100)
   */
  calculateEfficiency(stats: CacheStats): number {
    if (stats.totalRequests === 0) return 0;
    
    const hitRateScore = stats.hitRate * 40; // 40% weight
    const memoryEfficiencyScore = Math.max(0, 100 - (stats.memoryUsage / (1024 * 1024)) * 10) * 0.3; // 30% weight
    const sizeEfficiencyScore = Math.max(0, 100 - (stats.size / 100) * 100) * 0.3; // 30% weight
    
    return Math.min(100, hitRateScore + memoryEfficiencyScore + sizeEfficiencyScore);
  }

  /**
   * Reset performance counters and history
   */
  resetMetrics() {
    globalZoneCache.clear();
    this.performanceHistory = [];
  }

  /**
   * Get cache performance recommendations
   */
  getRecommendations(): string[] {
    const stats = globalZoneCache.getStats();
    const recommendations: string[] = [];

    if (stats.hitRate < 0.3) {
      recommendations.push('Consider increasing cache TTL or reducing cache invalidation frequency');
    }

    if (stats.memoryUsage > 5 * 1024 * 1024) { // 5MB
      recommendations.push('Memory usage is high, consider reducing cache size or implementing more aggressive cleanup');
    }

    if (stats.size > 150) {
      recommendations.push('Cache size is large, consider implementing LRU eviction or reducing max cache size');
    }

    if (this.getPerformanceTrend() === 'declining') {
      recommendations.push('Performance is declining, consider cache optimization or clearing');
    }

    return recommendations;
  }
}

export const cachePerformance = new CachePerformanceMonitor();

/**
 * Cleanup function for application shutdown
 */
export function cleanupZoneCache(): void {
  globalZoneCache.destroy();
}