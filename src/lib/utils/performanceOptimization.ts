/**
 * Performance optimization utilities for PWA
 */

import { performanceMonitor } from './performanceMonitor';

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Memoization for expensive computations
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  getKey?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = fn(...args);
    cache.set(key, result);
    
    // Limit cache size to prevent memory leaks
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return result;
  }) as T;
}

/**
 * Virtual scrolling for large lists
 */
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export function calculateVirtualScrollItems<T>(
  items: T[],
  scrollTop: number,
  options: VirtualScrollOptions
): {
  visibleItems: Array<{ item: T; index: number; top: number }>;
  totalHeight: number;
  startIndex: number;
  endIndex: number;
} {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  
  const totalHeight = items.length * itemHeight;
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  const visibleItems = [];
  for (let i = startIndex; i <= endIndex; i++) {
    visibleItems.push({
      item: items[i],
      index: i,
      top: i * itemHeight
    });
  }
  
  return {
    visibleItems,
    totalHeight,
    startIndex,
    endIndex
  };
}

/**
 * Batch DOM updates for better performance
 */
export function batchDOMUpdates(callback: () => void): void {
  if (typeof window === 'undefined') {
    callback();
    return;
  }
  
  if ('requestIdleCallback' in window) {
    requestIdleCallback(callback, { timeout: 100 });
  } else {
    requestAnimationFrame(callback);
  }
}

/**
 * Optimize re-renders with shallow comparison
 */
export function shallowEqual(obj1: any, obj2: any): boolean {
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) {
    return false;
  }
  
  for (let key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }
  
  return true;
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage(): {
  used: number;
  total: number;
  percentage: number;
} | null {
  if (typeof window === 'undefined' || !('performance' in window) || !('memory' in (performance as any))) {
    return null;
  }
  
  const memory = (performance as any).memory;
  return {
    used: memory.usedJSHeapSize,
    total: memory.totalJSHeapSize,
    percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
  };
}

/**
 * Cleanup unused resources
 */
export function cleanupResources(): void {
  // Force garbage collection if available (Chrome DevTools)
  if (typeof window !== 'undefined' && 'gc' in window) {
    (window as any).gc();
  }
  
  // Clear performance entries to free memory
  if (typeof performance !== 'undefined' && performance.clearResourceTimings) {
    performance.clearResourceTimings();
  }
}

/**
 * Optimize images for better performance
 */
export function optimizeImageLoading(): void {
  if (typeof window === 'undefined') return;
  
  // Add loading="lazy" to images that don't have it
  const images = document.querySelectorAll('img:not([loading])');
  images.forEach((img) => {
    img.setAttribute('loading', 'lazy');
  });
  
  // Add decoding="async" for better performance
  const allImages = document.querySelectorAll('img:not([decoding])');
  allImages.forEach((img) => {
    img.setAttribute('decoding', 'async');
  });
}

/**
 * Preconnect to external domains
 */
export function preconnectToDomains(domains: string[]): void {
  if (typeof window === 'undefined') return;
  
  domains.forEach((domain) => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    document.head.appendChild(link);
  });
}

/**
 * Critical CSS inlining
 */
export function inlineCriticalCSS(css: string): void {
  if (typeof window === 'undefined') return;
  
  const style = document.createElement('style');
  style.textContent = css;
  style.setAttribute('data-critical', 'true');
  document.head.insertBefore(style, document.head.firstChild);
}

/**
 * Performance budget checker
 */
export interface PerformanceBudget {
  maxBundleSize: number; // in KB
  maxLoadTime: number; // in ms
  maxMemoryUsage: number; // in percentage
}

export function checkPerformanceBudget(budget: PerformanceBudget): {
  passed: boolean;
  violations: string[];
} {
  const violations: string[] = [];
  const metrics = performanceMonitor.getLatestMetrics();
  
  if (!metrics) {
    return { passed: false, violations: ['No performance metrics available'] };
  }
  
  // Check bundle size
  if (metrics.bundleSize / 1024 > budget.maxBundleSize) {
    violations.push(`Bundle size (${(metrics.bundleSize / 1024).toFixed(2)}KB) exceeds budget (${budget.maxBundleSize}KB)`);
  }
  
  // Check load time
  if (metrics.appLoadTime > budget.maxLoadTime) {
    violations.push(`Load time (${metrics.appLoadTime.toFixed(2)}ms) exceeds budget (${budget.maxLoadTime}ms)`);
  }
  
  // Check memory usage
  if (metrics.memoryUsage > budget.maxMemoryUsage) {
    violations.push(`Memory usage (${metrics.memoryUsage.toFixed(1)}%) exceeds budget (${budget.maxMemoryUsage}%)`);
  }
  
  return {
    passed: violations.length === 0,
    violations
  };
}

/**
 * Initialize performance optimizations
 */
export function initializePerformanceOptimizations(): () => void {
  if (typeof window === 'undefined') return () => {};
  
  // Optimize images
  optimizeImageLoading();
  
  // Preconnect to known external domains
  preconnectToDomains([
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com'
  ]);
  
  // Monitor performance budget
  const budget: PerformanceBudget = {
    maxBundleSize: 500, // 500KB
    maxLoadTime: 3000, // 3 seconds
    maxMemoryUsage: 80 // 80%
  };
  
  const checkBudget = () => {
    const result = checkPerformanceBudget(budget);
    if (!result.passed) {
      console.warn('Performance budget violations:', result.violations);
      
      // Dispatch event for monitoring
      window.dispatchEvent(new CustomEvent('performance-budget-violation', {
        detail: result.violations
      }));
    }
  };
  
  // Check budget periodically
  const budgetInterval = setInterval(checkBudget, 30000); // Every 30 seconds
  
  // Cleanup function
  return () => {
    clearInterval(budgetInterval);
  };
}