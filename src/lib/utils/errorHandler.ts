/**
 * Comprehensive error handling utilities for AI operations
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

export enum AIErrorType {
  API_KEY_MISSING = 'API_KEY_MISSING',
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  RATE_LIMIT = 'RATE_LIMIT',
  INVALID_RESPONSE = 'INVALID_RESPONSE',
  TIMEOUT = 'TIMEOUT',
  PARSE_ERROR = 'PARSE_ERROR',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AIError {
  type: AIErrorType;
  message: string;
  userFriendlyMessage: string;
  originalError?: Error;
  retryable: boolean;
  retryAfter?: number; // seconds
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
}

export class AIErrorHandler {
  private static readonly DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  };

  /**
   * Categorizes and creates user-friendly error messages
   */
  static categorizeError(error: any): AIError {
    if (!error) {
      return {
        type: AIErrorType.UNKNOWN_ERROR,
        message: 'Unknown error occurred',
        userFriendlyMessage: 'Ein unbekannter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
        retryable: true
      };
    }

    // Handle string errors
    if (typeof error === 'string') {
      if (error.includes('API key')) {
        return {
          type: AIErrorType.API_KEY_MISSING,
          message: error,
          userFriendlyMessage: 'KI-Funktionen sind nicht verfügbar. Bitte API-Schlüssel konfigurieren.',
          retryable: false
        };
      }
      
      if (error.includes('Rate limit') || error.includes('429')) {
        return {
          type: AIErrorType.RATE_LIMIT,
          message: error,
          userFriendlyMessage: 'Zu viele Anfragen. Bitte warten Sie einen Moment und versuchen Sie es erneut.',
          retryable: true,
          retryAfter: 60
        };
      }
    }

    // Handle Error objects
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase();
      
      if (error.name === 'AbortError' || errorMessage.includes('timeout')) {
        return {
          type: AIErrorType.TIMEOUT,
          message: error.message,
          userFriendlyMessage: 'Die Anfrage dauerte zu lange. Bitte versuchen Sie es erneut.',
          originalError: error,
          retryable: true
        };
      }
      
      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        return {
          type: AIErrorType.NETWORK_ERROR,
          message: error.message,
          userFriendlyMessage: 'Netzwerkfehler. Bitte Internetverbindung prüfen und erneut versuchen.',
          originalError: error,
          retryable: true
        };
      }
      
      if (errorMessage.includes('json') || errorMessage.includes('parse')) {
        return {
          type: AIErrorType.PARSE_ERROR,
          message: error.message,
          userFriendlyMessage: 'Fehler beim Verarbeiten der KI-Antwort. Bitte versuchen Sie es erneut.',
          originalError: error,
          retryable: true
        };
      }
      
      if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
        return {
          type: AIErrorType.AUTHENTICATION_ERROR,
          message: error.message,
          userFriendlyMessage: 'Authentifizierungsfehler. Bitte API-Schlüssel überprüfen.',
          originalError: error,
          retryable: false
        };
      }
      
      if (errorMessage.includes('403') || errorMessage.includes('quota')) {
        return {
          type: AIErrorType.QUOTA_EXCEEDED,
          message: error.message,
          userFriendlyMessage: 'KI-Kontingent erschöpft. Bitte versuchen Sie es später erneut.',
          originalError: error,
          retryable: true,
          retryAfter: 3600 // 1 hour
        };
      }
      
      if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
        return {
          type: AIErrorType.RATE_LIMIT,
          message: error.message,
          userFriendlyMessage: 'Zu viele Anfragen. Bitte warten Sie einen Moment und versuchen Sie es erneut.',
          originalError: error,
          retryable: true,
          retryAfter: 60
        };
      }
      
      if (errorMessage.includes('invalid response') || errorMessage.includes('malformed')) {
        return {
          type: AIErrorType.INVALID_RESPONSE,
          message: error.message,
          userFriendlyMessage: 'Unerwartete Antwort von der KI. Bitte versuchen Sie es erneut.',
          originalError: error,
          retryable: true
        };
      }
    }

    // Default to API error for unhandled cases
    return {
      type: AIErrorType.API_ERROR,
      message: error instanceof Error ? error.message : String(error),
      userFriendlyMessage: 'KI-Service temporär nicht verfügbar. Bitte versuchen Sie es später erneut.',
      originalError: error instanceof Error ? error : undefined,
      retryable: true
    };
  }

  /**
   * Implements exponential backoff retry mechanism
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    onRetry?: (attempt: number, error: AIError) => void
  ): Promise<T> {
    const finalConfig = { ...this.DEFAULT_RETRY_CONFIG, ...config };
    let lastError: AIError;

    for (let attempt = 0; attempt <= finalConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.categorizeError(error);
        
        // Don't retry if error is not retryable
        if (!lastError.retryable) {
          throw lastError;
        }
        
        // Don't retry on last attempt
        if (attempt === finalConfig.maxRetries) {
          throw lastError;
        }
        
        // Calculate delay with exponential backoff
        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier, attempt),
          finalConfig.maxDelay
        );
        
        // Use custom retry delay if specified in error
        const actualDelay = lastError.retryAfter ? lastError.retryAfter * 1000 : delay;
        
        // Notify about retry attempt
        if (onRetry) {
          onRetry(attempt + 1, lastError);
        }
        
        // Wait before retrying
        await this.delay(actualDelay);
      }
    }

    // This should never be reached, but TypeScript requires it
    throw lastError!;
  }

  /**
   * Utility function to create a delay
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Logs errors with appropriate level based on error type
   */
  static logError(error: AIError, context: string): void {
    const logData = {
      type: error.type,
      message: error.message,
      context,
      timestamp: new Date().toISOString(),
      retryable: error.retryable,
      ...(error.originalError && { stack: error.originalError.stack })
    };

    // Log at different levels based on error severity
    switch (error.type) {
      case AIErrorType.API_KEY_MISSING:
      case AIErrorType.AUTHENTICATION_ERROR:
      case AIErrorType.QUOTA_EXCEEDED:
        console.error('Critical AI Error:', logData);
        break;
      
      case AIErrorType.RATE_LIMIT:
      case AIErrorType.TIMEOUT:
        console.warn('Recoverable AI Error:', logData);
        break;
      
      case AIErrorType.NETWORK_ERROR:
      case AIErrorType.PARSE_ERROR:
      case AIErrorType.INVALID_RESPONSE:
        console.info('Transient AI Error:', logData);
        break;
      
      default:
        console.error('Unknown AI Error:', logData);
    }
  }

  /**
   * Creates a fallback response for failed AI operations
   */
  static createFallbackResponse<T>(
    operationType: 'tasks' | 'content' | 'questions' | 'elaboration',
    context?: any
  ): T {
    switch (operationType) {
      case 'tasks':
        return {
          tasks: [
            {
              id: Date.now().toString(),
              title: "Planung und Recherche",
              description: "Anforderungen definieren und Lösungen recherchieren",
              content: "",
              status: "To Do" as const,
              assignees: [],
              subtasks: []
            },
            {
              id: (Date.now() + 1).toString(),
              title: "Implementierung",
              description: "Kernfunktionalität entwickeln",
              content: "",
              status: "To Do" as const,
              assignees: [],
              subtasks: []
            }
          ]
        } as T;
      
      case 'content':
        return {
          content: `<div class="fallback-content">
            <p><strong>KI-Inhalt konnte nicht generiert werden.</strong></p>
            <p>Bitte versuchen Sie es später erneut oder bearbeiten Sie den Inhalt manuell.</p>
            <ul>
              <li>Überprüfen Sie Ihre Internetverbindung</li>
              <li>Stellen Sie sicher, dass der API-Schlüssel konfiguriert ist</li>
              <li>Versuchen Sie es in wenigen Minuten erneut</li>
            </ul>
          </div>`
        } as T;
      
      case 'questions':
        return {
          questions: [
            "Was sind die Hauptanforderungen für diese Aufgabe?",
            "Gibt es technische Einschränkungen zu beachten?",
            "Wie würde Erfolg für diese Aufgabe aussehen?",
            "Welche Ressourcen werden benötigt?"
          ]
        } as T;
      
      case 'elaboration':
        return {
          content: context?.originalText || "Text konnte nicht verbessert werden. Bitte versuchen Sie es erneut."
        } as T;
      
      default:
        return {} as T;
    }
  }
}