/**
 * Enhanced toast notification system
 * Implements requirements 8.1, 8.2, 8.3, 8.4, 8.5
 */

import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';
import React from 'react';

export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface ToastOptions {
  title: string;
  description?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface AIOperationToastOptions extends ToastOptions {
  operation: 'breakdown' | 'elaborate' | 'solve' | 'questions' | 'content' | 'export';
  taskTitle?: string;
  retryAction?: () => void;
}

export class ToastManager {
  private static instance: ToastManager;
  private activeToasts: Set<string> = new Set();
  private toastQueue: Array<{ id: string; options: ToastOptions & { type: ToastType } }> = [];
  private maxConcurrentToasts = 3;

  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  /**
   * Show a success toast
   */
  static success(options: ToastOptions): void {
    const instance = this.getInstance();
    instance.showToast('success', options);
  }

  /**
   * Show an error toast
   */
  static error(options: ToastOptions): void {
    const instance = this.getInstance();
    instance.showToast('error', options);
  }

  /**
   * Show a warning toast
   */
  static warning(options: ToastOptions): void {
    const instance = this.getInstance();
    instance.showToast('warning', options);
  }

  /**
   * Show an info toast
   */
  static info(options: ToastOptions): void {
    const instance = this.getInstance();
    instance.showToast('info', options);
  }

  /**
   * Show a loading toast
   */
  static loading(options: ToastOptions): string {
    const instance = this.getInstance();
    return instance.showToast('loading', { ...options, duration: 0 }); // Loading toasts don't auto-dismiss
  }

  /**
   * Dismiss a specific toast
   */
  static dismiss(toastId: string): void {
    const instance = this.getInstance();
    instance.dismissToast(toastId);
  }

  /**
   * Show AI operation specific toast
   */
  static aiOperation(type: ToastType, options: AIOperationToastOptions): void {
    const instance = this.getInstance();
    const enhancedOptions = instance.enhanceAIOperationToast(type, options);
    instance.showToast(type, enhancedOptions);
  }

  /**
   * Show AI operation success toast
   */
  static aiSuccess(options: AIOperationToastOptions): void {
    this.aiOperation('success', options);
  }

  /**
   * Show AI operation error toast with retry option
   */
  static aiError(options: AIOperationToastOptions): void {
    this.aiOperation('error', options);
  }

  /**
   * Show AI operation warning toast
   */
  static aiWarning(options: AIOperationToastOptions): void {
    this.aiOperation('warning', options);
  }

  /**
   * Show network error toast
   */
  static networkError(retryAction?: () => void): void {
    this.error({
      title: 'Netzwerkfehler',
      description: 'Bitte überprüfen Sie Ihre Internetverbindung.',
      action: retryAction ? {
        label: 'Erneut versuchen',
        onClick: retryAction
      } : undefined,
      duration: 8000
    });
  }

  /**
   * Show API error toast
   */
  static apiError(operation: string, retryAction?: () => void): void {
    this.error({
      title: 'KI-Service Fehler',
      description: `${operation} fehlgeschlagen. Der Service ist möglicherweise temporär nicht verfügbar.`,
      action: retryAction ? {
        label: 'Erneut versuchen',
        onClick: retryAction
      } : undefined,
      duration: 10000
    });
  }

  /**
   * Show export success toast
   */
  static exportSuccess(format: string): void {
    this.success({
      title: 'Export erfolgreich',
      description: `Ihr Projekt wurde als ${format.toUpperCase()} exportiert.`,
      duration: 4000
    });
  }

  /**
   * Show export error toast
   */
  static exportError(format: string, retryAction?: () => void): void {
    this.error({
      title: 'Export fehlgeschlagen',
      description: `${format.toUpperCase()}-Export konnte nicht abgeschlossen werden.`,
      action: retryAction ? {
        label: 'Erneut versuchen',
        onClick: retryAction
      } : undefined,
      duration: 8000
    });
  }

  private showToast(type: ToastType, options: ToastOptions): string {
    const toastId = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // If we have too many active toasts, queue this one
    if (this.activeToasts.size >= this.maxConcurrentToasts) {
      this.toastQueue.push({ id: toastId, options: { ...options, type } });
      return toastId;
    }

    this.displayToast(toastId, type, options);
    return toastId;
  }

  private displayToast(toastId: string, type: ToastType, options: ToastOptions): void {
    this.activeToasts.add(toastId);

    const variant = this.getToastVariant(type);
    const duration = options.duration ?? this.getDefaultDuration(type);

    toast({
      variant,
      title: options.title,
      description: options.description,
      duration,
      action: options.action ? React.createElement(ToastAction, {
        altText: options.action.label,
        onClick: options.action.onClick
      }, options.action.label) : undefined,
    });

    // Auto-remove from active set after duration (if not loading)
    if (type !== 'loading' && duration > 0) {
      setTimeout(() => {
        this.activeToasts.delete(toastId);
        this.processQueue();
      }, duration);
    }
  }

  private dismissToast(toastId: string): void {
    this.activeToasts.delete(toastId);
    this.processQueue();
  }

  private processQueue(): void {
    if (this.toastQueue.length > 0 && this.activeToasts.size < this.maxConcurrentToasts) {
      const nextToast = this.toastQueue.shift();
      if (nextToast) {
        const { id, options } = nextToast;
        const { type, ...toastOptions } = options;
        this.displayToast(id, type, toastOptions);
      }
    }
  }

  private getToastVariant(type: ToastType): 'default' | 'destructive' {
    switch (type) {
      case 'error':
        return 'destructive';
      default:
        return 'default';
    }
  }

  private getDefaultDuration(type: ToastType): number {
    switch (type) {
      case 'success':
        return 4000;
      case 'info':
        return 5000;
      case 'warning':
        return 6000;
      case 'error':
        return 8000;
      case 'loading':
        return 0; // Loading toasts don't auto-dismiss
      default:
        return 5000;
    }
  }

  private enhanceAIOperationToast(type: ToastType, options: AIOperationToastOptions): ToastOptions {
    const operationNames = {
      breakdown: 'Aufgabenzerlegung',
      elaborate: 'Textverbesserung',
      solve: 'KI-Lösung',
      questions: 'Fragenerstellung',
      content: 'Inhaltserstellung',
      export: 'Export'
    };

    const operationName = operationNames[options.operation] || 'KI-Operation';
    const taskInfo = options.taskTitle ? ` für "${options.taskTitle}"` : '';

    let enhancedTitle = options.title;
    let enhancedDescription = options.description;

    // Enhance based on type
    switch (type) {
      case 'success':
        enhancedTitle = enhancedTitle || `${operationName} erfolgreich`;
        enhancedDescription = enhancedDescription || `${operationName}${taskInfo} wurde erfolgreich abgeschlossen.`;
        break;
      
      case 'error':
        enhancedTitle = enhancedTitle || `${operationName} fehlgeschlagen`;
        enhancedDescription = enhancedDescription || `${operationName}${taskInfo} konnte nicht abgeschlossen werden.`;
        break;
      
      case 'warning':
        enhancedTitle = enhancedTitle || `${operationName} teilweise erfolgreich`;
        enhancedDescription = enhancedDescription || `${operationName}${taskInfo} wurde mit Einschränkungen abgeschlossen.`;
        break;
      
      case 'loading':
        enhancedTitle = enhancedTitle || `${operationName} läuft`;
        enhancedDescription = enhancedDescription || `${operationName}${taskInfo} wird verarbeitet...`;
        break;
    }

    return {
      title: enhancedTitle,
      description: enhancedDescription,
      duration: options.duration,
      // Temporarily disable retry actions to fix rendering issue
      action: undefined
    };
  }
}

/**
 * React hook for using toast manager
 */
export function useEnhancedToast() {
  return {
    success: (options: ToastOptions) => ToastManager.success(options),
    error: (options: ToastOptions) => ToastManager.error(options),
    warning: (options: ToastOptions) => ToastManager.warning(options),
    info: (options: ToastOptions) => ToastManager.info(options),
    loading: (options: ToastOptions) => ToastManager.loading(options),
    dismiss: (toastId: string) => ToastManager.dismiss(toastId),
    aiSuccess: (options: AIOperationToastOptions) => ToastManager.aiSuccess(options),
    aiError: (options: AIOperationToastOptions) => ToastManager.aiError(options),
    aiWarning: (options: AIOperationToastOptions) => ToastManager.aiWarning(options),
    networkError: (retryAction?: () => void) => ToastManager.networkError(retryAction),
    apiError: (operation: string, retryAction?: () => void) => ToastManager.apiError(operation, retryAction),
    exportSuccess: (format: string) => ToastManager.exportSuccess(format),
    exportError: (format: string, retryAction?: () => void) => ToastManager.exportError(format, retryAction)
  };
}