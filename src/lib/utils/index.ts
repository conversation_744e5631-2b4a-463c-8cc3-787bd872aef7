// Utility Functions Module
export {
  decodeHtmlEntities,
  stripHtmlTags,
  htmlToText,
  escapeHtml,
  safeHtmlToText,
  wrapSelectedText,
  replaceWrappedText,
  extractWrappedText
} from './htmlParser';

export {
  calculateContentMetrics,
  countCharacters,
  countWords,
  estimateTokenCount,
  formatContentMetrics,
  stripHtmlTags as stripHtmlTagsFromContent
} from './contentMetrics';

export {
  recursiveTaskOperation,
  findTask,
  findTaskPath,
  updateTask,
  deleteTask,
  addTask,
  addTaskAfter,
  moveTask,
  countTasks,
  countTasksByStatus,
  flattenTasks,
  getTaskTreeDepth,
  validateTaskTree,
  createTask,
  cloneTaskTree
} from './taskOperations';