/**
 * Insertion Position Validation System
 * 
 * Provides comprehensive validation for task insertion positions,
 * including constraint checking, error handling, and fallback suggestions.
 */

import type { 
  Task, 
  InsertionPosition, 
  ValidationResult, 
  InsertionConstraints,
  InsertionValidator 
} from '@/lib/types';
import { findTask, getTaskTreeDepth, flattenTasks } from './taskOperations';

/**
 * Default insertion constraints
 */
const DEFAULT_CONSTRAINTS: InsertionConstraints = {
  maxDepth: 10,
  allowedPositions: ['before', 'after', 'between_parent_child'],
  parentRestrictions: [],
  maxSiblings: 50
};

/**
 * Configuration for insertion validation
 */
export interface ValidationConfig {
  maxDepth?: number;
  maxSiblings?: number;
  allowedPositions?: ('before' | 'after' | 'between_parent_child')[];
  parentRestrictions?: string[];
  strictMode?: boolean;
}

/**
 * Main insertion validator implementation
 */
export class TaskInsertionValidator implements InsertionValidator {
  private config: Required<ValidationConfig>;

  constructor(config: ValidationConfig = {}) {
    this.config = {
      maxDepth: config.maxDepth ?? DEFAULT_CONSTRAINTS.maxDepth,
      maxSiblings: config.maxSiblings ?? DEFAULT_CONSTRAINTS.maxSiblings ?? 50,
      allowedPositions: config.allowedPositions ?? DEFAULT_CONSTRAINTS.allowedPositions,
      parentRestrictions: config.parentRestrictions ?? DEFAULT_CONSTRAINTS.parentRestrictions,
      strictMode: config.strictMode ?? false
    };
  }

  /**
   * Validates if insertion is allowed at a specific position
   */
  validatePosition(position: InsertionPosition, tasks: Task[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestedAlternatives: InsertionPosition[] = [];

    try {
      // Basic position validation
      const basicValidation = this.validateBasicPosition(position);
      if (!basicValidation.isValid) {
        errors.push(...basicValidation.errors);
      }

      // Target task existence validation
      const targetTask = findTask(tasks, position.targetTaskId);
      if (!targetTask) {
        errors.push(`Target task with ID '${position.targetTaskId}' not found`);
        return { isValid: false, errors, warnings, suggestedAlternatives };
      }

      // Position type validation
      if (!this.config.allowedPositions.includes(position.type)) {
        errors.push(`Insertion type '${position.type}' is not allowed`);
        const alternatives = this.generatePositionTypeAlternatives(position, targetTask);
        suggestedAlternatives.push(...alternatives);
      }

      // Depth validation
      const depthValidation = this.validateDepth(position, tasks, targetTask);
      if (!depthValidation.isValid) {
        errors.push(...depthValidation.errors);
        warnings.push(...depthValidation.warnings);
        if (depthValidation.alternatives) {
          suggestedAlternatives.push(...depthValidation.alternatives);
        }
      }

      // Sibling count validation
      const siblingValidation = this.validateSiblingCount(position, tasks, targetTask);
      if (!siblingValidation.isValid) {
        errors.push(...siblingValidation.errors);
        warnings.push(...siblingValidation.warnings);
      }

      // Parent restrictions validation
      const parentValidation = this.validateParentRestrictions(position, tasks, targetTask);
      if (!parentValidation.isValid) {
        errors.push(...parentValidation.errors);
        if (parentValidation.alternatives) {
          suggestedAlternatives.push(...parentValidation.alternatives);
        }
      }

      // Hierarchy consistency validation
      const hierarchyValidation = this.validateHierarchyConsistency(position, tasks, targetTask);
      if (!hierarchyValidation.isValid) {
        errors.push(...hierarchyValidation.errors);
        warnings.push(...hierarchyValidation.warnings);
      }

      // Generate additional alternatives if validation failed
      if (errors.length > 0 && suggestedAlternatives.length === 0) {
        const fallbackAlternatives = this.generateFallbackAlternatives(position, tasks, targetTask);
        suggestedAlternatives.push(...fallbackAlternatives);
      }

    } catch (error) {
      errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestedAlternatives: suggestedAlternatives.length > 0 ? suggestedAlternatives : undefined
    };
  }

  /**
   * Quick check if insertion is allowed at a position
   */
  canInsertAtPosition(position: InsertionPosition): boolean {
    // Basic validation without full task tree analysis
    return this.validateBasicPosition(position).isValid &&
           this.config.allowedPositions.includes(position.type);
  }

  /**
   * Get insertion constraints for a specific task
   */
  getInsertionConstraints(taskId: string): InsertionConstraints {
    return {
      maxDepth: this.config.maxDepth,
      allowedPositions: [...this.config.allowedPositions],
      parentRestrictions: [...this.config.parentRestrictions],
      maxSiblings: this.config.maxSiblings
    };
  }

  /**
   * Validate basic position properties
   */
  private validateBasicPosition(position: InsertionPosition): ValidationResult {
    const errors: string[] = [];

    if (!position.targetTaskId || position.targetTaskId.trim() === '') {
      errors.push('Target task ID is required');
    }

    if (!position.type) {
      errors.push('Insertion type is required');
    }

    if (typeof position.level !== 'number' || position.level < 0) {
      errors.push('Level must be a non-negative number');
    }

    return { isValid: errors.length === 0, errors, warnings: [] };
  }

  /**
   * Validate insertion depth constraints
   */
  private validateDepth(
    position: InsertionPosition, 
    tasks: Task[], 
    targetTask: Task
  ): ValidationResult & { alternatives?: InsertionPosition[] } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const alternatives: InsertionPosition[] = [];

    let insertionDepth = position.level;

    // For between_parent_child, depth increases by 1
    if (position.type === 'between_parent_child') {
      insertionDepth = position.level + 1;
    }

    if (insertionDepth > this.config.maxDepth) {
      errors.push(`Insertion depth ${insertionDepth} exceeds maximum allowed depth of ${this.config.maxDepth}`);
      
      // Suggest alternative positions at allowed depth
      const maxAllowedLevel = this.config.maxDepth - (position.type === 'between_parent_child' ? 1 : 0);
      if (maxAllowedLevel >= 0) {
        // Suggest inserting at a higher level
        alternatives.push({
          ...position,
          type: position.type === 'between_parent_child' ? 'after' : position.type,
          level: Math.min(position.level, maxAllowedLevel)
        });
      }
    }

    // Warning for deep nesting (use actual insertion depth, not just position level)
    const warningThreshold = Math.floor(this.config.maxDepth * 0.8);
    if (insertionDepth >= warningThreshold && insertionDepth <= this.config.maxDepth) {
      warnings.push(`Insertion at depth ${insertionDepth} may impact performance and usability`);
    }

    return { 
      isValid: errors.length === 0, 
      errors, 
      warnings,
      alternatives: alternatives.length > 0 ? alternatives : undefined
    };
  }

  /**
   * Validate sibling count constraints
   */
  private validateSiblingCount(
    position: InsertionPosition, 
    tasks: Task[], 
    targetTask: Task
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      let siblingCount = 0;

      if (position.type === 'between_parent_child') {
        // Count existing subtasks of the target task
        siblingCount = targetTask.subtasks?.length || 0;
      } else {
        // Count siblings at the same level
        if (position.parentId) {
          const parentTask = findTask(tasks, position.parentId);
          siblingCount = parentTask?.subtasks?.length || 0;
        } else {
          // Root level tasks
          siblingCount = tasks.length;
        }
      }

      if (siblingCount >= this.config.maxSiblings) {
        errors.push(`Cannot insert task: maximum sibling count of ${this.config.maxSiblings} reached`);
      } else if (siblingCount > this.config.maxSiblings * 0.8) {
        warnings.push(`High sibling count (${siblingCount}): consider reorganizing task structure`);
      }

    } catch (error) {
      errors.push(`Error validating sibling count: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate parent restriction constraints
   */
  private validateParentRestrictions(
    position: InsertionPosition, 
    tasks: Task[], 
    targetTask: Task
  ): ValidationResult & { alternatives?: InsertionPosition[] } {
    const errors: string[] = [];
    const alternatives: InsertionPosition[] = [];

    if (this.config.parentRestrictions.length === 0) {
      return { isValid: true, errors, warnings: [] };
    }

    try {
      let parentTask: Task | null = null;

      if (position.type === 'between_parent_child') {
        parentTask = targetTask; // Target task becomes the parent
      } else if (position.parentId) {
        parentTask = findTask(tasks, position.parentId);
      }

      if (parentTask && this.config.parentRestrictions.includes(parentTask.id)) {
        errors.push(`Cannot insert under restricted parent task '${parentTask.title}'`);
        
        // Suggest alternative positions not under restricted parents
        const rootAlternative: InsertionPosition = {
          ...position,
          parentId: null,
          level: 0,
          type: 'after'
        };
        alternatives.push(rootAlternative);
      }

    } catch (error) {
      errors.push(`Error validating parent restrictions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { 
      isValid: errors.length === 0, 
      errors, 
      warnings: [],
      alternatives: alternatives.length > 0 ? alternatives : undefined
    };
  }

  /**
   * Validate hierarchy consistency
   */
  private validateHierarchyConsistency(
    position: InsertionPosition, 
    tasks: Task[], 
    targetTask: Task
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check for circular references
      if (position.type === 'between_parent_child' && position.parentId === position.targetTaskId) {
        errors.push('Cannot create circular parent-child relationship');
      }

      // Validate level consistency
      if (position.parentId) {
        const parentTask = findTask(tasks, position.parentId);
        if (parentTask) {
          const parentLevel = this.calculateTaskLevel(tasks, position.parentId);
          if (parentLevel !== -1) {
            const expectedLevel = parentLevel + 1;
            
            if (Math.abs(position.level - expectedLevel) > 0) {
              warnings.push(`Level mismatch: expected ${expectedLevel}, got ${position.level}`);
            }
          }
        }
      } else if (position.level !== 0) {
        warnings.push(`Root level tasks should have level 0, got ${position.level}`);
      }

    } catch (error) {
      errors.push(`Error validating hierarchy consistency: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Generate alternative positions for different insertion types
   */
  private generatePositionTypeAlternatives(
    position: InsertionPosition, 
    targetTask: Task
  ): InsertionPosition[] {
    const alternatives: InsertionPosition[] = [];

    // Generate alternatives for each allowed position type
    for (const allowedType of this.config.allowedPositions) {
      if (allowedType !== position.type) {
        const alternative: InsertionPosition = {
          ...position,
          type: allowedType
        };

        // Adjust level for between_parent_child
        if (allowedType === 'between_parent_child') {
          alternative.level = position.level + 1;
          alternative.parentId = position.targetTaskId;
        }

        alternatives.push(alternative);
      }
    }

    return alternatives;
  }

  /**
   * Generate fallback alternatives when validation fails
   */
  private generateFallbackAlternatives(
    position: InsertionPosition, 
    tasks: Task[], 
    targetTask: Task
  ): InsertionPosition[] {
    const alternatives: InsertionPosition[] = [];

    try {
      // Fallback 1: Insert after target task at same level
      if (position.type !== 'after') {
        alternatives.push({
          ...position,
          type: 'after'
        });
      }

      // Fallback 2: Insert at root level
      if (position.level > 0) {
        alternatives.push({
          ...position,
          parentId: null,
          level: 0,
          type: 'after'
        });
      }

      // Fallback 3: Insert as last child of parent (if parent exists and has capacity)
      if (position.parentId && position.type !== 'between_parent_child') {
        const parentTask = findTask(tasks, position.parentId);
        if (parentTask && (parentTask.subtasks?.length || 0) < this.config.maxSiblings) {
          alternatives.push({
            ...position,
            type: 'between_parent_child',
            targetTaskId: position.parentId,
            parentId: position.parentId,
            level: position.level
          });
        }
      }

    } catch (error) {
      console.warn('Error generating fallback alternatives:', error);
    }

    return alternatives;
  }

  /**
   * Calculate the level of a task in the hierarchy
   */
  private calculateTaskLevel(tasks: Task[], taskId: string, currentLevel: number = 0): number {
    for (const task of tasks) {
      if (task.id === taskId) {
        return currentLevel;
      }
      
      if (task.subtasks && task.subtasks.length > 0) {
        const foundLevel = this.calculateTaskLevel(task.subtasks, taskId, currentLevel + 1);
        if (foundLevel !== -1) {
          return foundLevel;
        }
      }
    }
    
    return -1; // Not found
  }
}

/**
 * Create a validator instance with default configuration
 */
export function createInsertionValidator(config?: ValidationConfig): TaskInsertionValidator {
  return new TaskInsertionValidator(config);
}

/**
 * Quick validation function for simple use cases
 */
export function validateInsertionPosition(
  position: InsertionPosition, 
  tasks: Task[], 
  config?: ValidationConfig
): ValidationResult {
  const validator = createInsertionValidator(config);
  return validator.validatePosition(position, tasks);
}

/**
 * Check if insertion is allowed (simple boolean check)
 */
export function canInsertAtPosition(
  position: InsertionPosition, 
  tasks: Task[], 
  config?: ValidationConfig
): boolean {
  const validation = validateInsertionPosition(position, tasks, config);
  return validation.isValid;
}

/**
 * Get suggested alternatives for a failed insertion
 */
export function getInsertionAlternatives(
  position: InsertionPosition, 
  tasks: Task[], 
  config?: ValidationConfig
): InsertionPosition[] {
  const validation = validateInsertionPosition(position, tasks, config);
  return validation.suggestedAlternatives || [];
}

/**
 * Validate multiple insertion positions at once
 */
export function validateMultiplePositions(
  positions: InsertionPosition[], 
  tasks: Task[], 
  config?: ValidationConfig
): { position: InsertionPosition; result: ValidationResult }[] {
  const validator = createInsertionValidator(config);
  
  return positions.map(position => ({
    position,
    result: validator.validatePosition(position, tasks)
  }));
}

/**
 * Get insertion constraints for the entire task tree
 */
export function getGlobalInsertionConstraints(
  tasks: Task[], 
  config?: ValidationConfig
): {
  currentDepth: number;
  maxDepth: number;
  totalTasks: number;
  canInsertMore: boolean;
  recommendations: string[];
} {
  const validator = createInsertionValidator(config);
  const currentDepth = getTaskTreeDepth(tasks);
  const totalTasks = flattenTasks(tasks).length;
  const maxDepth = config?.maxDepth ?? DEFAULT_CONSTRAINTS.maxDepth;
  
  const recommendations: string[] = [];
  
  if (currentDepth > maxDepth * 0.8) {
    recommendations.push('Consider flattening deep task hierarchies for better usability');
  }
  
  if (totalTasks > 100) {
    recommendations.push('Large task trees may impact performance - consider breaking into smaller projects');
  }
  
  return {
    currentDepth,
    maxDepth,
    totalTasks,
    canInsertMore: currentDepth < maxDepth,
    recommendations
  };
}