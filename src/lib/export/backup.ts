import type { Task } from '../types';
import type { PWAMetadata } from './index';
import { indexedDBManager, type StoredProject, type StoredTask, type StoredSettings } from '../storage/indexeddb';

export interface BackupData {
  metadata: {
    backupDate: Date;
    backupVersion: '1.0';
    appVersion: string;
    deviceInfo: {
      userAgent: string;
      platform: string;
      language: string;
    };
    pwa?: PWAMetadata;
  };
  projects: StoredProject[];
  tasks: StoredTask[];
  settings?: StoredSettings;
  appState: {
    currentProjectId?: string;
    lastActiveDate: Date;
    totalExports: number;
  };
}

/**
 * Create backup export format with complete app state
 */
export const exportToBackup = async (
  project: { mainProject: string; mainProjectDescription?: string; tasks: Task[] },
  pwaMetadata?: PWAMetadata
) => {
  try {
    // Get all data from IndexedDB for complete backup
    const [allProjects, settings, stats] = await Promise.all([
      indexedDBManager.getAllProjects(),
      indexedDBManager.loadSettings().catch(() => null),
      indexedDBManager.getStats()
    ]);

    // Get all tasks for all projects
    const allTasks: StoredTask[] = [];
    for (const proj of allProjects) {
      const projectTasks = await indexedDBManager.getTasksByProject(proj.id);
      // Convert back to StoredTask format for backup
      const storedTasks = await convertTasksToStoredFormat(projectTasks, proj.id);
      allTasks.push(...storedTasks);
    }

    const backupData: BackupData = {
      metadata: {
        backupDate: new Date(),
        backupVersion: '1.0',
        appVersion: '1.0.0',
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        },
        ...(pwaMetadata && { pwa: pwaMetadata })
      },
      projects: allProjects,
      tasks: allTasks,
      ...(settings && { settings }),
      appState: {
        lastActiveDate: new Date(),
        totalExports: (settings?.id ? 1 : 0) // Simple counter, could be enhanced
      }
    };

    const backupString = JSON.stringify(backupData, null, 2);
    const blob = new Blob([backupString], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    link.setAttribute("href", url);
    link.setAttribute("download", `KI_Projekt_Planer_Backup_${timestamp}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error creating backup:', error);
    throw new Error('Failed to create backup. Please try again.');
  }
};

/**
 * Convert Task[] back to StoredTask[] format for backup
 */
async function convertTasksToStoredFormat(tasks: Task[], projectId: string, parentTaskId?: string): Promise<StoredTask[]> {
  const storedTasks: StoredTask[] = [];
  
  for (let i = 0; i < tasks.length; i++) {
    const task = tasks[i];
    
    const storedTask: StoredTask = {
      ...task,
      projectId,
      parentTaskId,
      createdAt: new Date(), // In real implementation, this would come from storage
      updatedAt: new Date(),
      orderIndex: i,
      // Add PWA-specific fields with defaults
      isCollapsed: task.isEditing || false,
      priority: 'medium',
      pendingSync: false
    };

    storedTasks.push(storedTask);

    // Process subtasks recursively
    if (task.subtasks && task.subtasks.length > 0) {
      const subtasks = await convertTasksToStoredFormat(task.subtasks, projectId, task.id);
      storedTasks.push(...subtasks);
    }
  }

  return storedTasks;
}

/**
 * Validate backup data structure
 */
export function validateBackupData(data: any): data is BackupData {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Check required metadata
  if (!data.metadata || !data.metadata.backupDate || !data.metadata.backupVersion) {
    return false;
  }

  // Check projects array
  if (!Array.isArray(data.projects)) {
    return false;
  }

  // Check tasks array
  if (!Array.isArray(data.tasks)) {
    return false;
  }

  // Check app state
  if (!data.appState || !data.appState.lastActiveDate) {
    return false;
  }

  return true;
}

/**
 * Get backup file info without full parsing
 */
export function getBackupInfo(backupData: BackupData): {
  backupDate: Date;
  projectCount: number;
  taskCount: number;
  appVersion: string;
  deviceInfo: string;
} {
  return {
    backupDate: new Date(backupData.metadata.backupDate),
    projectCount: backupData.projects.length,
    taskCount: backupData.tasks.length,
    appVersion: backupData.metadata.appVersion,
    deviceInfo: `${backupData.metadata.deviceInfo.platform} - ${backupData.metadata.deviceInfo.userAgent.split(' ')[0]}`
  };
}