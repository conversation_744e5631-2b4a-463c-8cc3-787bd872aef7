import type { Task, Project } from '../types';
import type { BackupData } from './backup';
import { validateBackupData, getBackupInfo } from './backup';
import { indexedDBManager } from '../storage/indexeddb';

export type ImportFormat = 'json' | 'backup' | 'markdown' | 'csv';

export interface ImportOptions {
  format: ImportFormat;
  mergeStrategy: 'replace' | 'merge' | 'append';
  validateData?: boolean;
  preserveIds?: boolean;
}

export interface ImportResult {
  success: boolean;
  error?: string;
  imported: {
    projects: number;
    tasks: number;
  };
  warnings: string[];
  conflicts?: ConflictInfo[];
}

export interface ConflictInfo {
  type: 'project' | 'task';
  existingId: string;
  existingTitle: string;
  newTitle: string;
  resolution: 'skip' | 'overwrite' | 'rename';
}

export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

/**
 * Main import function that handles various project file formats
 */
export async function importProject(
  file: File,
  options: ImportOptions
): Promise<ImportResult> {
  try {
    const fileContent = await readFileContent(file);
    
    // Detect format if not specified
    const detectedFormat = options.format || detectFileFormat(file, fileContent);
    
    switch (detectedFormat) {
      case 'json':
        return await importFromJson(fileContent, options);
      case 'backup':
        return await importFromBackup(fileContent, options);
      case 'markdown':
        return await importFromMarkdown(fileContent, options);
      case 'csv':
        return await importFromCsv(fileContent, options);
      default:
        throw new Error(`Unsupported import format: ${detectedFormat}`);
    }
  } catch (error) {
    console.error('Import failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown import error',
      imported: { projects: 0, tasks: 0 },
      warnings: []
    };
  }
}

/**
 * Read file content as text
 */
async function readFileContent(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result;
      if (typeof content === 'string') {
        resolve(content);
      } else {
        reject(new Error('Failed to read file content'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
}

/**
 * Detect file format based on file extension and content
 */
function detectFileFormat(file: File, content: string): ImportFormat {
  const extension = file.name.toLowerCase().split('.').pop();
  
  // Check by extension first
  switch (extension) {
    case 'json':
      // Distinguish between regular JSON and backup
      try {
        const parsed = JSON.parse(content);
        if (validateBackupData(parsed)) {
          return 'backup';
        }
        return 'json';
      } catch {
        return 'json';
      }
    case 'md':
    case 'markdown':
      return 'markdown';
    case 'csv':
      return 'csv';
    default:
      // Try to detect by content
      if (content.trim().startsWith('{')) {
        try {
          const parsed = JSON.parse(content);
          return validateBackupData(parsed) ? 'backup' : 'json';
        } catch {
          throw new Error('Invalid JSON format');
        }
      } else if (content.includes('#') || content.includes('*')) {
        return 'markdown';
      } else if (content.includes(',') && content.includes('\n')) {
        return 'csv';
      }
      
      throw new Error('Unable to detect file format');
  }
}

/**
 * Import from JSON export format
 */
async function importFromJson(content: string, options: ImportOptions): Promise<ImportResult> {
  try {
    const data = JSON.parse(content);
    const validationErrors = validateJsonData(data);
    
    if (validationErrors.length > 0 && options.validateData !== false) {
      const errors = validationErrors.filter(e => e.severity === 'error');
      if (errors.length > 0) {
        throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`);
      }
    }

    const warnings = validationErrors
      .filter(e => e.severity === 'warning')
      .map(e => e.message);

    // Create project from JSON data
    const projectId = await createProjectFromData({
      title: data.project?.title || 'Imported Project',
      description: data.project?.description,
      tasks: data.tasks || []
    }, options);

    return {
      success: true,
      imported: {
        projects: 1,
        tasks: countTasks(data.tasks || [])
      },
      warnings
    };
  } catch (error) {
    throw new Error(`JSON import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Import from backup format
 */
async function importFromBackup(content: string, options: ImportOptions): Promise<ImportResult> {
  try {
    const backupData: BackupData = JSON.parse(content);
    
    if (!validateBackupData(backupData)) {
      throw new Error('Invalid backup file format');
    }

    const info = getBackupInfo(backupData);
    let importedProjects = 0;
    let importedTasks = 0;
    const warnings: string[] = [];

    // Import settings if available
    if (backupData.settings && options.mergeStrategy !== 'append') {
      try {
        await indexedDBManager.saveSettings(backupData.settings);
        warnings.push('Settings imported successfully');
      } catch (error) {
        warnings.push('Failed to import settings');
      }
    }

    // Import projects and tasks
    for (const project of backupData.projects) {
      try {
        const projectTasks = backupData.tasks.filter(t => t.projectId === project.id);
        const hierarchicalTasks = buildTaskHierarchy(projectTasks);
        
        const newProjectId = await createProjectFromData({
          title: project.title,
          description: project.title, // StoredProject doesn't have description
          tasks: hierarchicalTasks
        }, options);

        importedProjects++;
        importedTasks += projectTasks.length;
      } catch (error) {
        warnings.push(`Failed to import project "${project.title}": ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      success: true,
      imported: {
        projects: importedProjects,
        tasks: importedTasks
      },
      warnings
    };
  } catch (error) {
    throw new Error(`Backup import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Import from Markdown format (basic parsing)
 */
async function importFromMarkdown(content: string, options: ImportOptions): Promise<ImportResult> {
  try {
    const lines = content.split('\n');
    let projectTitle = 'Imported Markdown Project';
    let projectDescription = '';
    const tasks: Task[] = [];
    
    // Parse markdown content
    let currentTask: Partial<Task> | null = null;
    let taskCounter = 0;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // Project title (first # heading)
      if (trimmed.startsWith('# ') && !projectTitle.includes('Imported')) {
        projectTitle = trimmed.substring(2);
        continue;
      }
      
      // Project description (blockquote after title)
      if (trimmed.startsWith('> ') && !projectDescription) {
        projectDescription = trimmed.substring(2);
        continue;
      }
      
      // Task items (list items with task indicators)
      if (trimmed.match(/^-\s+\*\*\d+\./)) {
        // Save previous task
        if (currentTask && currentTask.title) {
          tasks.push(createTaskFromPartial(currentTask, taskCounter++));
        }
        
        // Start new task
        const titleMatch = trimmed.match(/\*\*(.+?)\*\*/);
        if (titleMatch) {
          currentTask = {
            title: titleMatch[1].replace(/^\d+\.\s*/, ''),
            description: '',
            content: '',
            status: trimmed.includes('✅') ? 'Done' : 
                   trimmed.includes('🔄') ? 'In Progress' : 'To Do',
            assignees: [],
            subtasks: []
          };
        }
      }
      
      // Task description or content
      if (currentTask && trimmed.startsWith('- *') && !trimmed.includes('**')) {
        currentTask.description = trimmed.substring(3, trimmed.length - 1);
      }
    }
    
    // Add last task
    if (currentTask && currentTask.title) {
      tasks.push(createTaskFromPartial(currentTask, taskCounter++));
    }

    const projectId = await createProjectFromData({
      title: projectTitle,
      description: projectDescription,
      tasks
    }, options);

    return {
      success: true,
      imported: {
        projects: 1,
        tasks: tasks.length
      },
      warnings: ['Markdown import is basic - some formatting may be lost']
    };
  } catch (error) {
    throw new Error(`Markdown import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Import from CSV format (basic parsing)
 */
async function importFromCsv(content: string, options: ImportOptions): Promise<ImportResult> {
  try {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('CSV file must have at least a header and one data row');
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const tasks: Task[] = [];
    let taskCounter = 0;

    // Find required columns
    const titleIndex = headers.findIndex(h => h.toLowerCase().includes('title'));
    const descriptionIndex = headers.findIndex(h => h.toLowerCase().includes('description'));
    const statusIndex = headers.findIndex(h => h.toLowerCase().includes('status'));

    if (titleIndex === -1) {
      throw new Error('CSV must contain a title column');
    }

    // Parse data rows
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      
      if (values.length >= headers.length && values[titleIndex]) {
        const task: Task = {
          id: crypto.randomUUID(),
          title: values[titleIndex],
          description: descriptionIndex >= 0 ? values[descriptionIndex] || '' : '',
          content: '',
          status: statusIndex >= 0 && values[statusIndex] ? 
                 (values[statusIndex] as any) : 'To Do',
          assignees: [],
          subtasks: []
        };
        
        tasks.push(task);
        taskCounter++;
      }
    }

    const projectId = await createProjectFromData({
      title: 'Imported CSV Project',
      description: 'Project imported from CSV file',
      tasks
    }, options);

    return {
      success: true,
      imported: {
        projects: 1,
        tasks: tasks.length
      },
      warnings: ['CSV import is basic - hierarchical structure is not preserved']
    };
  } catch (error) {
    throw new Error(`CSV import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create project from imported data with merge strategies
 */
async function createProjectFromData(
  projectData: { title: string; description?: string; tasks: Task[] },
  options: ImportOptions
): Promise<string> {
  // Check for existing project with same title
  const existingProjects = await indexedDBManager.getAllProjects();
  const existingProject = existingProjects.find(p => p.title === projectData.title);

  if (existingProject && options.mergeStrategy === 'replace') {
    // Delete existing project and create new one
    await indexedDBManager.deleteProject(existingProject.id);
  } else if (existingProject && options.mergeStrategy === 'merge') {
    // Merge tasks into existing project
    const existingTasks = await indexedDBManager.getTasksByProject(existingProject.id);
    const mergedTasks = [...existingTasks, ...projectData.tasks];
    
    // Save new tasks to existing project
    for (const task of projectData.tasks) {
      await saveTaskRecursively(task, existingProject.id);
    }
    
    return existingProject.id;
  } else if (existingProject && options.mergeStrategy === 'append') {
    // Create new project with modified name
    projectData.title = `${projectData.title} (Imported ${new Date().toLocaleDateString()})`;
  }

  // Create new project
  const projectId = await indexedDBManager.createProject({
    title: projectData.title,
    isOfflineOnly: true
  });

  // Save all tasks
  for (const task of projectData.tasks) {
    await saveTaskRecursively(task, projectId);
  }

  return projectId;
}

/**
 * Recursively save task and its subtasks
 */
async function saveTaskRecursively(task: Task, projectId: string, parentTaskId?: string): Promise<void> {
  const taskId = await indexedDBManager.createTask({
    ...task,
    projectId,
    parentTaskId,
    subtasks: [] // Will be handled by recursion
  }, projectId, parentTaskId);

  // Save subtasks
  for (const subtask of task.subtasks) {
    await saveTaskRecursively(subtask, projectId, taskId);
  }
}

/**
 * Validate JSON data structure
 */
function validateJsonData(data: any): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!data || typeof data !== 'object') {
    errors.push({
      field: 'root',
      message: 'Invalid JSON structure',
      severity: 'error'
    });
    return errors;
  }

  if (!data.project || !data.project.title) {
    errors.push({
      field: 'project.title',
      message: 'Project title is required',
      severity: 'error'
    });
  }

  if (!Array.isArray(data.tasks)) {
    errors.push({
      field: 'tasks',
      message: 'Tasks must be an array',
      severity: 'error'
    });
  } else {
    data.tasks.forEach((task: any, index: number) => {
      if (!task.title) {
        errors.push({
          field: `tasks[${index}].title`,
          message: `Task ${index + 1} is missing a title`,
          severity: 'error'
        });
      }
      
      if (task.status && !['To Do', 'In Progress', 'Done'].includes(task.status)) {
        errors.push({
          field: `tasks[${index}].status`,
          message: `Task ${index + 1} has invalid status: ${task.status}`,
          severity: 'warning'
        });
      }
    });
  }

  return errors;
}

/**
 * Build hierarchical task structure from flat stored tasks
 */
function buildTaskHierarchy(storedTasks: any[]): Task[] {
  const taskMap = new Map<string, Task>();
  const rootTasks: Task[] = [];

  // Convert stored tasks to Task format
  storedTasks.forEach(storedTask => {
    const task: Task = {
      id: storedTask.id,
      title: storedTask.title,
      description: storedTask.description || '',
      content: storedTask.content || '',
      status: storedTask.status || 'To Do',
      assignees: storedTask.assignees || [],
      subtasks: []
    };
    taskMap.set(task.id, task);
  });

  // Build hierarchy
  storedTasks.forEach(storedTask => {
    const task = taskMap.get(storedTask.id)!;
    
    if (storedTask.parentTaskId) {
      const parent = taskMap.get(storedTask.parentTaskId);
      if (parent) {
        parent.subtasks.push(task);
      }
    } else {
      rootTasks.push(task);
    }
  });

  return rootTasks;
}

/**
 * Create Task from partial data
 */
function createTaskFromPartial(partial: Partial<Task>, index: number): Task {
  return {
    id: crypto.randomUUID(),
    title: partial.title || `Task ${index + 1}`,
    description: partial.description || '',
    content: partial.content || '',
    status: partial.status || 'To Do',
    assignees: partial.assignees || [],
    subtasks: partial.subtasks || []
  };
}

/**
 * Count total tasks recursively
 */
function countTasks(tasks: Task[]): number {
  let count = tasks.length;
  tasks.forEach(task => {
    if (task.subtasks) {
      count += countTasks(task.subtasks);
    }
  });
  return count;
}

/**
 * Validate import file before processing
 */
export function validateImportFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 50MB)
  if (file.size > 50 * 1024 * 1024) {
    return { valid: false, error: 'File size too large (max 50MB)' };
  }

  // Check file type
  const allowedTypes = [
    'application/json',
    'text/plain',
    'text/markdown',
    'text/csv',
    'application/csv'
  ];

  const allowedExtensions = ['json', 'md', 'markdown', 'txt', 'csv'];
  const extension = file.name.toLowerCase().split('.').pop();

  if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(extension || '')) {
    return { valid: false, error: 'Unsupported file type. Supported: JSON, Markdown, CSV' };
  }

  return { valid: true };
}