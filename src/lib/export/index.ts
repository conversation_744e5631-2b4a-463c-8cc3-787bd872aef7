/**
 * Centralized export and import manager with dynamic library loading integration
 */

import type { Task } from '../types';
import type { LibraryConfig } from '../../hooks/useLibraryLoader';
import { exportToPdf } from './pdf';
import { exportToCsv } from './csv';
import { exportToMarkdown } from './markdown';

// Re-export import functionality
export { 
  importProject, 
  validateImportFile,
  type ImportOptions,
  type ImportResult,
  type ImportFormat,
  type ConflictInfo,
  type ValidationError
} from './import';

// Export library configurations
export const EXPORT_LIBRARIES: LibraryConfig[] = [
  {
    id: 'pdfmake',
    src: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.10/pdfmake.min.js',
    name: 'PDFMake'
  },
  {
    id: 'pdfmake-fonts',
    src: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.10/vfs_fonts.min.js',
    name: 'PDFMake Fonts'
  },
  {
    id: 'papaparse',
    src: 'https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js',
    name: 'PapaParse'
  }
];

export type ExportFormat = 'pdf' | 'csv' | 'markdown' | 'json' | 'backup';

export interface PWAMetadata {
  isOfflineOnly: boolean;
  lastSyncAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  installDate?: Date;
  deviceInfo?: {
    userAgent: string;
    platform: string;
    language: string;
  };
  appVersion?: string;
}

export interface ExportOptions {
  format: ExportFormat;
  project: {
    mainProject: string;
    mainProjectDescription?: string;
    tasks: Task[];
  };
  libsLoaded?: boolean;
  // PWA-specific options
  pwaMetadata?: PWAMetadata;
  includeTimestamps?: boolean;
  includeOfflineIndicators?: boolean;
}

export interface ExportResult {
  success: boolean;
  error?: string;
}

/**
 * Main export function that handles all export formats
 */
export async function exportProject(options: ExportOptions): Promise<ExportResult> {
  const { format, project, libsLoaded = true } = options;

  try {
    switch (format) {
      case 'pdf':
        await exportToPdf(project, libsLoaded, options.pwaMetadata, options.includeOfflineIndicators);
        break;
      case 'csv':
        await exportToCsv(project, libsLoaded, options.includeTimestamps);
        break;
      case 'markdown':
        await exportToMarkdown(project, libsLoaded, options.pwaMetadata);
        break;
      case 'json':
        const { exportToJson } = await import('./json');
        await exportToJson(project, options.pwaMetadata, options.includeTimestamps);
        break;
      case 'backup':
        const { exportToBackup } = await import('./backup');
        await exportToBackup(project, options.pwaMetadata);
        break;
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown export error';
    console.error(`Export failed for format ${format}:`, error);
    return { success: false, error: errorMessage };
  }
}

/**
 * Check if required libraries are available for a specific export format
 */
export function checkLibraryAvailability(format: ExportFormat): boolean {
  switch (format) {
    case 'pdf':
      return typeof window !== 'undefined' && 
             typeof (window as any).pdfMake !== 'undefined';
    case 'csv':
      return typeof window !== 'undefined' && 
             typeof (window as any).Papa !== 'undefined';
    case 'markdown':
    case 'json':
    case 'backup':
      return true; // These formats don't require external libraries
    default:
      return false;
  }
}

/**
 * Get user-friendly error message for library loading failures
 */
export function getLibraryErrorMessage(format: ExportFormat): string {
  switch (format) {
    case 'pdf':
      return 'PDF export requires PDFMake library. Please wait for libraries to load or refresh the page.';
    case 'csv':
      return 'CSV export requires PapaParse library. Please wait for libraries to load or refresh the page.';
    case 'markdown':
      return 'Markdown export is always available.';
    case 'json':
      return 'JSON export is always available.';
    case 'backup':
      return 'Backup export is always available.';
    default:
      return 'Unknown export format.';
  }
}

/**
 * Get the libraries required for a specific export format
 */
export function getRequiredLibraries(format: ExportFormat): LibraryConfig[] {
  switch (format) {
    case 'pdf':
      return EXPORT_LIBRARIES.filter(lib => lib.id.startsWith('pdfmake'));
    case 'csv':
      return EXPORT_LIBRARIES.filter(lib => lib.id === 'papaparse');
    case 'markdown':
      return []; // No external libraries required
    default:
      return [];
  }
}

/**
 * Graceful degradation when libraries are not available
 */
export function getAvailableExportFormats(libsLoaded: boolean): ExportFormat[] {
  const formats: ExportFormat[] = ['markdown', 'json', 'backup']; // Always available

  if (libsLoaded) {
    if (checkLibraryAvailability('pdf')) {
      formats.push('pdf');
    }
    if (checkLibraryAvailability('csv')) {
      formats.push('csv');
    }
  }

  return formats;
}

/**
 * Export with automatic fallback to available formats
 */
export async function exportWithFallback(options: ExportOptions): Promise<ExportResult> {
  const { format, libsLoaded = true } = options;
  
  // First try the requested format
  if (libsLoaded && checkLibraryAvailability(format)) {
    return exportProject(options);
  }

  // If the requested format is not available, suggest alternatives
  const availableFormats = getAvailableExportFormats(libsLoaded);
  
  if (availableFormats.length === 0) {
    return {
      success: false,
      error: 'No export formats are currently available. Please refresh the page and try again.'
    };
  }

  // If markdown is available and requested format is not, offer markdown as fallback
  if (format !== 'markdown' && availableFormats.includes('markdown')) {
    return {
      success: false,
      error: `${format.toUpperCase()} export is not available. Markdown export is available as an alternative.`
    };
  }

  return {
    success: false,
    error: getLibraryErrorMessage(format)
  };
}