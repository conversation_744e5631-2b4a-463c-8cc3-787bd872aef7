import type { Task } from '../types';
import type { PWAMetadata } from './index';

export interface JSONExportData {
  metadata: {
    exportDate: Date;
    exportFormat: 'json';
    appVersion: string;
    pwa?: PWAMetadata;
  };
  project: {
    title: string;
    description?: string;
    createdAt?: Date;
    updatedAt?: Date;
  };
  tasks: EnhancedTask[];
  statistics: {
    totalTasks: number;
    completedTasks: number;
    inProgressTasks: number;
    maxDepth: number;
  };
}

interface EnhancedTask extends Task {
  createdAt?: Date;
  updatedAt?: Date;
  lastAIInteraction?: Date;
  priority?: 'low' | 'medium' | 'high';
  isCollapsed?: boolean;
  pendingSync?: boolean;
  level: number;
  path: string;
  orderIndex?: number;
}

/**
 * Enhanced JSON export with full project data including timestamps and PWA metadata
 */
export const exportToJson = (
  project: { mainProject: string; mainProjectDescription?: string; tasks: Task[] },
  pwaMetadata?: PWAMetadata,
  includeTimestamps: boolean = true
) => {
  try {
    const enhancedTasks = enhanceTasksWithMetadata(project.tasks, includeTimestamps);
    const statistics = calculateStatistics(enhancedTasks);

    const exportData: JSONExportData = {
      metadata: {
        exportDate: new Date(),
        exportFormat: 'json',
        appVersion: '1.0.0', // Could be read from package.json
        ...(pwaMetadata && { pwa: pwaMetadata })
      },
      project: {
        title: project.mainProject,
        description: project.mainProjectDescription,
        ...(includeTimestamps && {
          createdAt: pwaMetadata?.createdAt || new Date(),
          updatedAt: pwaMetadata?.updatedAt || new Date()
        })
      },
      tasks: enhancedTasks,
      statistics
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    
    link.setAttribute("href", url);
    link.setAttribute("download", `${project.mainProject.replace(/ /g, '_')}_full_export.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error exporting to JSON:', error);
    throw new Error('Failed to export JSON. Please try again.');
  }
};

/**
 * Enhance tasks with metadata for export
 */
function enhanceTasksWithMetadata(tasks: Task[], includeTimestamps: boolean, level: number = 0, parentPath: string = ''): EnhancedTask[] {
  const enhancedTasks: EnhancedTask[] = [];
  
  tasks.forEach((task, index) => {
    const currentPath = parentPath ? `${parentPath}.${index + 1}` : `${index + 1}`;
    
    const enhancedTask: EnhancedTask = {
      ...task,
      level,
      path: currentPath,
      orderIndex: index,
      ...(includeTimestamps && {
        createdAt: new Date(), // In real implementation, this would come from storage
        updatedAt: new Date(),
        ...(task.content && { lastAIInteraction: new Date() })
      })
    };

    // Process subtasks recursively
    if (task.subtasks && task.subtasks.length > 0) {
      enhancedTask.subtasks = enhanceTasksWithMetadata(task.subtasks, includeTimestamps, level + 1, currentPath);
    }

    enhancedTasks.push(enhancedTask);
  });

  return enhancedTasks;
}

/**
 * Calculate project statistics
 */
function calculateStatistics(tasks: EnhancedTask[]): JSONExportData['statistics'] {
  let totalTasks = 0;
  let completedTasks = 0;
  let inProgressTasks = 0;
  let maxDepth = 0;

  const countTasks = (taskList: EnhancedTask[], currentDepth: number = 0) => {
    maxDepth = Math.max(maxDepth, currentDepth);
    
    taskList.forEach(task => {
      totalTasks++;
      
      if (task.status === 'Done') {
        completedTasks++;
      } else if (task.status === 'In Progress') {
        inProgressTasks++;
      }

      if (task.subtasks && task.subtasks.length > 0) {
        countTasks(task.subtasks as EnhancedTask[], currentDepth + 1);
      }
    });
  };

  countTasks(tasks);

  return {
    totalTasks,
    completedTasks,
    inProgressTasks,
    maxDepth
  };
}