
"use client";

import type { Project, Task } from '../types';
import { decodeHtmlEntities } from '../utils/htmlParser';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

if (pdfFonts.pdfMake && pdfMake.vfs !== pdfFonts.pdfMake.vfs) {
  pdfMake.vfs = pdfFonts.pdfMake.vfs;
}

// Sicherstellen, dass Standard-Schriftarten definiert sind (Browser-Build liefert Roboto aus vfs_fonts)
// Vermeidet Fehler wie: "Font 'Courier' in style 'normal' is not defined"
if (!pdfMake.fonts || !pdfMake.fonts.Roboto) {
  (pdfMake as any).fonts = {
    Roboto: {
      normal: 'Roboto-Regular.ttf',
      bold: 'Roboto-Medium.ttf',
      italics: 'Roboto-Italic.ttf',
      bolditalics: 'Roboto-MediumItalic.ttf',
    },
  };
}

// Enhanced HTML-to-PDFMake parser for correct formatting (from monolithic version)
const htmlToPdfmakeParser = (htmlString: string) => {
    if (typeof document === 'undefined') return [];
    
    // Helper function for recursive array flattening
    const flatten = (arr: any[]): any[] => arr.reduce((acc, val) => Array.isArray(val) ? acc.concat(flatten(val)) : acc.concat(val), []);

    // Lightweight language detection from element/class or content
    const detectLanguage = (element?: HTMLElement, text?: string): string => {
        const classAttr = element?.getAttribute('class') || '';
        const langFromClass = /language-([a-z0-9+#-]+)/i.exec(classAttr) || /lang-([a-z0-9+#-]+)/i.exec(classAttr);
        if (langFromClass) return (langFromClass[1] || '').toLowerCase();
        const sample = (text || '').trim();
        if (sample.startsWith('{') || sample.startsWith('[')) return 'json';
        if (/\bclass\s+|\bfunction\s+|=>|const\s+|let\s+/.test(sample)) return 'js';
        if (/^\s*#!/.test(sample) || /\b(npm|yarn|pnpm)\b|\b(cd|ls|cat|echo)\b/.test(sample)) return 'bash';
        if (/\bflutter\b|\bdart\b/.test(sample)) return 'dart';
        if (/\bversion:\s|\bdependencies:\s/.test(sample)) return 'yaml';
        return 'text';
    };

    // Minimal Syntax-Highlighting → erzeugt ein Array aus pdfmake Text-Snippets mit Farben
    const highlightCodeToPdfMake = (codeText: string, lang: string): any[] => {
        const chunks: any[] = [];
        const push = (text: string, color?: string, bold?: boolean) => {
            if (!text) return;
            const chunk: any = { text };
            if (color) chunk.color = color;
            if (bold) chunk.bold = true;
            chunks.push(chunk);
        };

        const lines = codeText.replace(/\r\n?/g, '\n').split('\n');
        // Dunklere, druckfreundliche Farben für weißen Hintergrund
        const keywordColor = '#1d4ed8'; // blue-700
        const stringColor = '#b45309';  // orange-700
        const numberColor = '#065f46';  // emerald-800
        const commentColor = '#6b7280'; // gray-500
        const plainColor = '#111827';   // slate-900

        const jsKeywords = /(\b(?:const|let|var|function|return|if|else|for|while|switch|case|break|continue|try|catch|finally|new|class|extends|super|import|from|export|default|throw|async|await|yield|typeof|instanceof|in|of)\b)/g;

        lines.forEach((line, idx) => {
            // Comments
            if (/^\s*\/\//.test(line)) {
                push(line + (idx < lines.length - 1 ? '\n' : ''), commentColor);
                return;
            }
            // Strings
            const parts: any[] = [];
            let rest = line;
            const stringRegex = /(\"[^\"]*\"|'[^']*'|`[^`]*`)/g;
            let match: RegExpExecArray | null;
            let lastIndex = 0;
            while ((match = stringRegex.exec(line)) !== null) {
                const pre = line.slice(lastIndex, match.index);
                if (pre) parts.push({ kind: 'code', text: pre });
                parts.push({ kind: 'string', text: match[0] });
                lastIndex = match.index + match[0].length;
            }
            if (lastIndex < line.length) {
                parts.push({ kind: 'code', text: line.slice(lastIndex) });
            }

            parts.forEach((p) => {
                if (p.kind === 'string') {
                    push(p.text, stringColor);
                } else {
                    let segment = p.text;
                    if (lang === 'js' || lang === 'ts' || lang === 'dart') {
                        // Numbers
                        segment = segment.replace(/(\b\d+(?:\.\d+)?\b)/g, (m) => `\u0000N${m}\u0000`);
                        // Keywords
                        segment = segment.replace(jsKeywords, (m) => `\u0000K${m}\u0000`);
                        // Emit with colors
                        segment.split(/\u0000/).forEach((token) => {
                            if (!token) return;
                            if (token.startsWith('N')) {
                                push(token.slice(1), numberColor);
                            } else if (token.startsWith('K')) {
                                push(token.slice(1), keywordColor, true);
                            } else {
                                push(token, plainColor);
                            }
                        });
                    } else if (lang === 'json' || lang === 'yaml') {
                        // Numbers
                        segment = segment.replace(/(\b\d+(?:\.\d+)?\b)/g, (m) => `\u0000N${m}\u0000`);
                        segment.split(/\u0000/).forEach((token) => {
                            if (!token) return;
                            if (token.startsWith('N')) push(token.slice(1), numberColor);
                            else push(token, plainColor);
                        });
                    } else {
                        push(segment, plainColor);
                    }
                }
            });
            if (idx < lines.length - 1) push('\n');
        });
        return chunks;
    };

    const buildCodeBlock = (element: HTMLElement): any => {
        const codeElement = element.querySelector('code');
        const text = codeElement ? codeElement.textContent || '' : element.textContent || '';
        const lang = detectLanguage(codeElement || element, text);
        const highlighted = highlightCodeToPdfMake(text, lang);
        const lineCount = (text.match(/\n/g) || []).length + 1;
        if (lineCount <= 1) {
            // Einzeiliger Code → inline/ohne zusätzlichen Blockumbruch
            return { text: highlighted, style: 'codeInline', preserveLeadingSpaces: true };
        }
        // Mehrzeilig → eigener Block
        return { text: highlighted, style: 'codeText', preserveLeadingSpaces: true, margin: [0, 4, 0, 10] };
    };

    // Build a pdfmake table object from an HTML <table>
    const buildTable = (tableEl: HTMLTableElement) => {
        // Collect header/body rows
        const thead = tableEl.querySelector('thead');
        const tbody = tableEl.querySelector('tbody');

        const headRows = thead ? Array.from(thead.querySelectorAll('tr')) : [];
        const bodyRows = tbody ? Array.from(tbody.querySelectorAll('tr')) : Array.from(tableEl.querySelectorAll(':scope > tr'));

        const headerRowsCount = headRows.length;

        // Determine column count considering colSpan on the first available row
        const firstRow = (headRows[0] || bodyRows[0]) as HTMLTableRowElement | undefined;
        let columnCount = 0;
        if (firstRow) {
            Array.from(firstRow.cells).forEach((c) => {
                columnCount += Math.max(1, c.colSpan || 1);
            });
        }
        const widths = Array.from({ length: columnCount }).map(() => '*');

        // Helper to map a row's cells to pdfmake cells (with colspan/rowspan)
        const mapRow = (tr: HTMLTableRowElement, isHeader: boolean) => {
            const row: any[] = [];
            Array.from(tr.cells).forEach((cell) => {
                const contentText = (cell.textContent || '').replace(/\s+$/g, '').replace(/^\s+/g, '');
                const cellObj: any = isHeader
                    ? { text: contentText, style: 'tableHeader' }
                    : { text: contentText, style: 'tableCell' };
                if (cell.colSpan && cell.colSpan > 1) cellObj.colSpan = cell.colSpan;
                if (cell.rowSpan && cell.rowSpan > 1) cellObj.rowSpan = cell.rowSpan;
                row.push(cellObj);

                // pdfmake requires placeholders for colSpans
                for (let i = 1; i < (cell.colSpan || 1); i++) {
                    row.push({ text: '' });
                }
            });
            return row;
        };

        const body: any[] = [];
        headRows.forEach((tr) => body.push(mapRow(tr as HTMLTableRowElement, true)));
        bodyRows.forEach((tr) => body.push(mapRow(tr as HTMLTableRowElement, false)));

        return {
            table: {
                headerRows: headerRowsCount,
                widths,
                body
            },
            layout: 'lightHorizontalLines',
            margin: [0, 6, 0, 10]
        };
    };

    const convertNode = (node: Node, inList = false): any => {
        // Text Node
        if (node.nodeType === 3) {
            // CORRECTION: Ignore empty or whitespace-only text nodes
            if (node.textContent?.trim().length === 0) {
                return null;
            }
            return { text: node.textContent };
        }

        // Element Node
        if (node.nodeType === 1) {
            const element = node as HTMLElement;
            const nodeName = element.nodeName.toLowerCase();
            const isListContext = inList || nodeName === 'li' || nodeName === 'ul' || nodeName === 'ol';
            const children = Array.from(element.childNodes).map(child => convertNode(child, isListContext)).filter(n => n);

            const processedChildren = flatten(children);

            switch (nodeName) {
                // INLINE ELEMENTS
                case 'strong':
                case 'b':
                    return processedChildren.map(child => ({ ...child, bold: true }));
                case 'em':
                case 'i':
                    return processedChildren.map(child => ({ ...child, italics: true }));
                case 'u':
                    return processedChildren.map(child => ({ ...child, decoration: 'underline' }));
                case 'code':
                    // Inline Code
                    return { text: element.textContent, style: 'codeInline', preserveLeadingSpaces: true };
                case 'a':
                    return processedChildren.map(child => ({ ...child, link: element.getAttribute('href'), style: 'link' }));
                case 'br':
                    return { text: '\n' };
                case 'pre':
                    return buildCodeBlock(element);

                // BLOCK ELEMENTS
                case 'h1': return { text: processedChildren, style: 'h1' };
                case 'h2': return { text: processedChildren, style: 'h2' };
                case 'h3': return { text: processedChildren, style: 'h3' };
                case 'h4': return { text: processedChildren, style: 'h4' };
                
                // CORRECTION: Handle paragraphs in lists differently to avoid misalignment
                case 'p':
                    if (inList) {
                        // Keep list content compact and inline on one line where possible
                        return { text: processedChildren, margin: [0, 0, 0, 0] };
                    }
                    return { text: processedChildren, style: 'paragraph' };

                case 'blockquote': return { stack: processedChildren, style: 'quote' };

                case 'ul':
                    // Wrap each LI content as a single pdfmake text node to keep inline elements on one line
                    return { ul: processedChildren.filter((c: any) => c.isLi).map((c: any) => c.content), style: 'list' };
                case 'ol':
                    return { ol: processedChildren.filter((c: any) => c.isLi).map((c: any) => c.content), style: 'list' };
                case 'li':
                    // Keep inline children on the same line by wrapping into a text node
                    return { isLi: true, content: { text: processedChildren } };

                case 'table':
                    return buildTable(element as HTMLTableElement);

                // OTHER CONTAINER ELEMENTS (div, span, etc.)
                default:
                    return processedChildren;
            }
        }
        return null;
    };

    if (!htmlString) return [];
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = decodeHtmlEntities(htmlString);
    const result = Array.from(tempDiv.childNodes).map(node => convertNode(node)).filter(n => n);
    return flatten(result);
};


const generateFlatData = (tasks: Task[]): any[] => {
    const flatData: any[] = [];
    const recurse = (tasks: Task[], prefix: string, level: number) => {
        tasks.forEach((task, index) => {
            const currentNumber = `${prefix}${index + 1}`;
            flatData.push({
                number: `${currentNumber}.`,
                level: level,
                title: task.title,
                description: task.description || '',
                ai_content: task.content || ''
            });
            if (task.subtasks && task.subtasks.length > 0) {
                recurse(task.subtasks, `${currentNumber}.`, level + 1);
            }
        });
    };
    recurse(tasks, '', 1);
    return flatData;
};

export const exportToPdf = (
  project: { mainProject: string; mainProjectDescription?: string; tasks: Task[] }, 
  libsLoaded: boolean = true,
  pwaMetadata?: import('./index').PWAMetadata,
  includeOfflineIndicators: boolean = false
) => {
    // Check if required libraries are loaded
    if (!libsLoaded) {
        throw new Error('PDF export libraries are not loaded. Please wait for libraries to load and try again.');
    }

    // Check if pdfMake is available
    if (typeof pdfMake === 'undefined') {
        throw new Error('PDFMake library is not available. Please reload the page and try again.');
    }

    try {
        const flatData = generateFlatData(project.tasks);

        // Build PWA metadata section
        const pwaMetadataContent = [];
        if (pwaMetadata) {
            pwaMetadataContent.push(
                { text: 'PWA Informationen', style: 'h4', margin: [0, 10, 0, 5] },
                {
                    ul: [
                        `Offline-Modus: ${pwaMetadata.isOfflineOnly ? '✓ Aktiviert' : '✗ Deaktiviert'}`,
                        `Erstellt: ${pwaMetadata.createdAt.toLocaleDateString('de-DE')}`,
                        `Zuletzt aktualisiert: ${pwaMetadata.updatedAt.toLocaleDateString('de-DE')}`,
                        ...(pwaMetadata.lastSyncAt ? [`Letzte Synchronisation: ${pwaMetadata.lastSyncAt.toLocaleDateString('de-DE')}`] : []),
                        ...(pwaMetadata.installDate ? [`App installiert: ${pwaMetadata.installDate.toLocaleDateString('de-DE')}`] : []),
                        ...(pwaMetadata.deviceInfo ? [`Gerät: ${pwaMetadata.deviceInfo.platform} (${pwaMetadata.deviceInfo.language})`] : [])
                    ],
                    style: 'list'
                }
            );
        }

        const docDefinition: any = {
        content: [
            { text: project.mainProject, style: 'header', alignment: 'center' },
            { text: project.mainProjectDescription || 'Ihr Projektplan, um Großes zu erreichen.', style: 'subheader', alignment: 'center' },
            ...(includeOfflineIndicators && pwaMetadata?.isOfflineOnly ? [
                { text: '📱 Offline-PWA Export', style: 'offlineIndicator', alignment: 'center', margin: [0, 5, 0, 5] }
            ] : []),
            { canvas: [{ type: 'line', x1: 0, y1: 5, x2: 515, y2: 5, lineWidth: 0.5, lineColor: '#cccccc' }], margin: [0, 20, 0, 20] },
            { text: `Erstellt am: ${new Date().toLocaleDateString('de-DE')}`, style: 'meta', alignment: 'center' },
            ...pwaMetadataContent,
            { text: 'Projektübersicht', style: 'h2', pageBreak: 'before' },
            {
                style: 'tableExample',
                table: {
                    headerRows: 1,
                    widths: ['auto', '*', 'auto'],
                    body: [
                        [{text: 'Nummer', style: 'tableHeader'}, {text: 'Aufgabe', style: 'tableHeader'}, {text: 'Beschreibung', style: 'tableHeader'}],
                        ...flatData.map(d => [
                            { text: ' '.repeat(d.level * 2) + d.number, style: 'tableCell' },
                            { text: d.title, style: 'tableCell' },
                            { text: d.description, style: 'tableCell' }
                        ])
                    ]
                },
                layout: 'lightHorizontalLines'
            },
        ],
        styles: {
            header: { fontSize: 28, bold: true, margin: [0, 20, 0, 10] },
            subheader: { fontSize: 14, italic: true, margin: [0, 0, 0, 15] },
            h1: { fontSize: 22, bold: true, margin: [0, 10, 0, 4] },
            h2: { fontSize: 20, bold: true, margin: [0, 8, 0, 4] },
            h3: { fontSize: 16, bold: true, margin: [0, 6, 0, 3] },
            h4: { fontSize: 14, bold: true, margin: [0, 5, 0, 2] },
            paragraph: { margin: [0, 0, 0, 5], lineHeight: 1.2, fontSize: 9 },
            quote: { italics: true, margin: [10, 5, 10, 5], color: '#555555' },
            list: { margin: [10, 5, 0, 8] },
            link: { color: 'blue', decoration: 'underline' },
            // Inline-Code
            codeInline: { font: 'Roboto', fontSize: 9, color: '#111827' },
            // Codeblock-Text auf weißem Hintergrund
            codeText: { font: 'Roboto', fontSize: 9, color: '#111827', lineHeight: 1.2 },
            meta: { fontSize: 9, color: '#666666' },
            offlineIndicator: { fontSize: 11, color: '#059669', bold: true },
            tableHeader: { bold: true, fontSize: 13, color: 'black' },
            tableCell: { margin: [0, 5, 0, 5], lineHeight: 1.2 }
        },
        defaultStyle: {
            font: 'Roboto',
            fontSize: 9,
            lineHeight: 1.2
        },
        footer: (currentPage: number, pageCount: number) => ({ text: `Seite ${currentPage.toString()} von ${pageCount}`, alignment: 'center', style: 'meta', margin: [0,10,0,0] }),
        header: (currentPage: number) => {
            if (currentPage === 1) return null;
            return { text: project.mainProject, alignment: 'center', style: 'meta', margin: [0, 10, 0, 0] };
        }
    };

    const detailedContent = flatData.filter(d => d.ai_content);
    if (detailedContent.length > 0) {
        docDefinition.content.push({ text: 'Detaillierte Ausarbeitungen', style: 'h2', pageBreak: 'before' });
        detailedContent.forEach(task => {
            docDefinition.content.push({ text: `${task.number} ${task.title}`, style: 'h3' });
            const parsedContent = htmlToPdfmakeParser(task.ai_content);
            if(parsedContent && parsedContent.length > 0) {
                docDefinition.content.push(...parsedContent);
            }
        });
    }

        pdfMake.createPdf(docDefinition).download(`${project.mainProject.replace(/ /g, '_')}.pdf`);
    } catch (error) {
        console.error('Error creating PDF:', error);
        throw new Error('Failed to create PDF. Please try again.');
    }
};
