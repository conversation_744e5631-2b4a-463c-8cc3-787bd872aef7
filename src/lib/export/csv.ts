import type { Project, Task } from '../types';
import { decodeHtmlEntities, stripHtmlTags } from '../utils/htmlParser';
import { unparse } from 'papaparse';

const generateFlatData = (tasks: Task[]): any[] => {
    const flatData: any[] = [];
    const recurse = (tasks: Task[], prefix: string, level: number) => {
        tasks.forEach((task, index) => {
            const currentNumber = `${prefix}${index + 1}`;
            
            // Process HTML content properly
            const processedContent = task.content ? stripHtmlTags(task.content) : '';
            const processedDescription = task.description ? stripHtmlTags(task.description) : '';
            
            const baseData = {
                number: `${currentNumber}.`,
                level: level,
                title: task.title,
                description: processedDescription,
                ai_content: processedContent,
                status: task.status,
                assignees: task.assignees.map(user => user.name).join(', '),
                subtask_count: task.subtasks ? task.subtasks.length : 0
            };

            // Add timestamp fields if requested
            const timestampData = includeTimestamps ? {
                created_at: new Date().toISOString(), // In real implementation, this would come from storage
                updated_at: new Date().toISOString(),
                last_ai_interaction: task.content ? new Date().toISOString() : '',
                priority: 'medium', // Default priority
                is_offline_only: 'true' // PWA indicator
            } : {};

            flatData.push({
                ...baseData,
                ...timestampData
            });
            
            if (task.subtasks && task.subtasks.length > 0) {
                recurse(task.subtasks, `${currentNumber}.`, level + 1);
            }
        });
    };
    recurse(tasks, '', 1);
    return flatData;
};

export const exportToCsv = (
  project: { mainProject: string; mainProjectDescription?: string; tasks: Task[] }, 
  libsLoaded: boolean = true,
  includeTimestamps: boolean = false
) => {
    // Check if required libraries are loaded
    if (!libsLoaded) {
        throw new Error('CSV export libraries are not loaded. Please wait for libraries to load and try again.');
    }

    // Check if PapaParse is available
    if (typeof unparse === 'undefined') {
        throw new Error('PapaParse library is not available. Please reload the page and try again.');
    }

    try {
        const flatData = generateFlatData(project.tasks);
        const csv = unparse(flatData);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `${project.mainProject.replace(/ /g, '_')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL object
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error exporting to CSV:', error);
        throw new Error('Failed to export CSV. Please try again.');
    }
};
