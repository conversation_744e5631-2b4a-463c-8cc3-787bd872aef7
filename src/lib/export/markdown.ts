import type { Project, Task } from '../types';
import { decodeHtmlEntities, htmlToText } from '../utils/htmlParser';
import { countTasks, countTasksByStatus } from '../utils/taskOperations';

export const exportToMarkdown = (
  project: { mainProject: string; mainProjectDescription?: string; tasks: Task[] }, 
  libsLoaded: boolean = true,
  pwaMetadata?: import('./index').PWAMetadata
) => {
    // Markdown export doesn't require external libraries, but we keep the parameter for consistency
    try {
        let mdContent = `# ${project.mainProject}\n\n`;
        
        if (project.mainProjectDescription) {
            mdContent += `> ${project.mainProjectDescription}\n\n`;
        }
        
        mdContent += `***\n*"Der beste Weg, die Zukunft vorauszusagen, ist, sie zu gestalten."*\n***\n\n`;
        mdContent += `*Erstellt am: ${new Date().toLocaleDateString('de-DE')}*\n\n`;

        // Add PWA metadata section if available
        if (pwaMetadata) {
            mdContent += `## 📱 PWA Informationen\n\n`;
            mdContent += `- **Offline-Modus:** ${pwaMetadata.isOfflineOnly ? '✅ Aktiviert' : '❌ Deaktiviert'}\n`;
            mdContent += `- **Erstellt:** ${pwaMetadata.createdAt.toLocaleDateString('de-DE')}\n`;
            mdContent += `- **Zuletzt aktualisiert:** ${pwaMetadata.updatedAt.toLocaleDateString('de-DE')}\n`;
            
            if (pwaMetadata.lastSyncAt) {
                mdContent += `- **Letzte Synchronisation:** ${pwaMetadata.lastSyncAt.toLocaleDateString('de-DE')}\n`;
            }
            
            if (pwaMetadata.installDate) {
                mdContent += `- **App installiert:** ${pwaMetadata.installDate.toLocaleDateString('de-DE')}\n`;
            }
            
            if (pwaMetadata.deviceInfo) {
                mdContent += `- **Gerät:** ${pwaMetadata.deviceInfo.platform} (${pwaMetadata.deviceInfo.language})\n`;
            }
            
            if (pwaMetadata.appVersion) {
                mdContent += `- **App Version:** ${pwaMetadata.appVersion}\n`;
            }
            
            mdContent += `\n---\n\n`;
        }

        const recurse = (tasks: Task[], prefix: string, level: number) => {
            tasks.forEach((task, index) => {
                const currentNumber = `${prefix}${index + 1}`;
                const indent = '  '.repeat(level - 1);
                
                // Task title with status indicator
                const statusEmoji = task.status === 'Done' ? '✅' : task.status === 'In Progress' ? '🔄' : '📋';
                mdContent += `${indent}- **${currentNumber}. ${task.title}** ${statusEmoji}\n`;
                
                // Task description
                if (task.description) {
                    const processedDescription = htmlToText(task.description);
                    mdContent += `${indent}  - *${processedDescription}*\n`;
                }
                
                // Assignees
                if (task.assignees && task.assignees.length > 0) {
                    const assigneeNames = task.assignees.map(user => user.name).join(', ');
                    mdContent += `${indent}  - 👤 **Zugewiesen an:** ${assigneeNames}\n`;
                }
                
                // AI content/elaboration
                if (task.content) {
                    const processedContent = decodeHtmlEntities(task.content);
                    const textContent = htmlToText(processedContent);
                    mdContent += `\n${indent}  **Ausarbeitung:**\n${indent}  > ${textContent.replace(/\n/g, `\n${indent}  > `)}\n\n`;
                }
                
                // Recurse for subtasks
                if (task.subtasks && task.subtasks.length > 0) {
                    recurse(task.subtasks, `${currentNumber}.`, level + 1);
                }
            });
        };

        recurse(project.tasks, '', 1);

        // Add project statistics
        const totalTasks = countTasks(project.tasks);
        const completedTasks = countTasksByStatus(project.tasks, 'Done');
        const inProgressTasks = countTasksByStatus(project.tasks, 'In Progress');
        
        mdContent += `\n---\n\n## Projektstatistiken\n\n`;
        mdContent += `- **Gesamtaufgaben:** ${totalTasks}\n`;
        mdContent += `- **Abgeschlossen:** ${completedTasks} (${Math.round((completedTasks / totalTasks) * 100)}%)\n`;
        mdContent += `- **In Bearbeitung:** ${inProgressTasks}\n`;
        mdContent += `- **Offen:** ${totalTasks - completedTasks - inProgressTasks}\n`;

        const blob = new Blob([mdContent], { type: 'text/markdown;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `${project.mainProject.replace(/ /g, '_')}.md`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL object
        URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error exporting to Markdown:', error);
        throw new Error('Failed to export Markdown. Please try again.');
    }
};

// Helper functions are now imported from taskOperations utility
