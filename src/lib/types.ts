export interface User {
  id: string;
  name: string;
  avatarUrl: string;
}

export type TaskStatus = 'To Do' | 'In Progress' | 'Done';

export interface Task {
  id: string;
  title: string;
  description: string;
  content: string;
  status: TaskStatus;
  assignees: User[];
  subtasks: Task[];
  // New editing states for enhanced functionality
  isEditing?: boolean;
  isDescriptionEditing?: boolean;
  isAiContentEditing?: boolean;
  // Optional AI-Metriken
  aiMetrics?: {
    lastSolveMs?: number;
    lastElaborateMs?: number;
    lastBreakdownMs?: number;
    // AI output diagnostics
    lastFinishReason?: string;
    promptTokens?: number;
    candidateTokens?: number;
    totalTokens?: number;
    lastContentChars?: number;
    likelyTruncated?: boolean;
    lastHeadingSnippet?: string;
  };
}

export interface Project {
  title: string;
  tasks: Task[];
}

// AI Integration Types
export interface ContextStrategies {
  strategy1: boolean; // Task path
  strategy2: boolean; // Intelligent summary (default: true)
  strategy3: boolean; // Complete project tree
}

// Loading states per task
export interface LoadingStates {
  [taskId: string]: 'breakdown' | 'elaborate' | 'solve' | false;
}

// Solve modal state
export interface SolveModalState {
  step: 'closed' | 'choice' | 'autopilot_prompt' | 'copilot_asking' | 'copilot_answering';
  task: Task | null;
  questions: string[];
  answers: Record<string, string>;
  additionalPrompt: string;
  isRefinement: boolean;
  selectionWrapperId: string | null;
}

// Manual Task Insertion Types

export interface InsertionPosition {
  type: 'before' | 'after' | 'between_parent_child';
  targetTaskId: string;
  parentId: string | null;
  level: number;
  
  // Additional context for insertion operations
  context?: {
    siblingCount: number;
    insertionIndex: number;
    hierarchyPath: string[];
  };
}

export interface InsertionZone {
  id: string;
  type: 'before' | 'after' | 'between_parent_child';
  bounds: DOMRect;
  targetTaskId: string;
  parentId: string | null;
  level: number;
  
  // Metadata for hover detection and positioning
  metadata?: {
    isVisible: boolean;
    priority: number;
    touchFriendly: boolean;
  };
}

export interface InsertionState {
  activeInsertionPoint: InsertionPosition | null;
  hoveredZone: InsertionZone | null;
  keyboardMode: boolean;
  insertionHistory: InsertionPosition[];
  
  // Performance optimization caches
  lastCalculatedZones: Map<string, InsertionZone[]>;
  zoneCalculationCache: Map<string, { zones: InsertionZone[], timestamp: number }>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestedAlternatives?: InsertionPosition[];
}

export interface InsertionConstraints {
  maxDepth: number;
  allowedPositions: ('before' | 'after' | 'between_parent_child')[];
  parentRestrictions: string[];
  
  // Additional validation rules
  requiresParent?: boolean;
  allowedParentTypes?: string[];
  maxSiblings?: number;
}

export interface InsertionValidator {
  validatePosition(position: InsertionPosition, tasks: Task[]): ValidationResult;
  canInsertAtPosition(position: InsertionPosition): boolean;
  getInsertionConstraints(taskId: string): InsertionConstraints;
}

export interface InsertionResult {
  success: boolean;
  newTaskId?: string;
  error?: string;
  position: InsertionPosition;
  
  // Additional result metadata
  metadata?: {
    insertionTime: Date;
    affectedTaskIds: string[];
    hierarchyChanges: boolean;
  };
}
