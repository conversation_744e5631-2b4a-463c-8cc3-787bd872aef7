/**
 * Data management utilities for privacy controls and complete data removal
 * Provides comprehensive data deletion and privacy management features
 */

import { indexedDBManager } from '@/lib/storage/indexeddb';
import { KeyManager } from './keyManager';

export interface DataDeletionOptions {
  includeProjects: boolean;
  includeTasks: boolean;
  includeSettings: boolean;
  includeEncryptionKeys: boolean;
  includeBrowserCache: boolean;
  includeLocalStorage: boolean;
}

export interface DataDeletionResult {
  success: boolean;
  deletedItems: {
    projects: number;
    tasks: number;
    settings: number;
    cacheEntries: number;
    localStorageKeys: number;
  };
  errors: string[];
  completedAt: Date;
}

export interface PrivacySettings {
  dataRetentionDays: number;
  autoDeleteOldData: boolean;
  encryptSensitiveData: boolean;
  minimizeAIDataSharing: boolean;
  allowAnalytics: boolean;
  allowCrashReporting: boolean;
}

export class DataManager {
  private static readonly PRIVACY_SETTINGS_KEY = 'privacy_settings';
  private static readonly DATA_USAGE_LOG_KEY = 'data_usage_log';

  /**
   * Perform complete data deletion based on options
   */
  static async deleteAllData(options: DataDeletionOptions): Promise<DataDeletionResult> {
    const result: DataDeletionResult = {
      success: true,
      deletedItems: {
        projects: 0,
        tasks: 0,
        settings: 0,
        cacheEntries: 0,
        localStorageKeys: 0
      },
      errors: [],
      completedAt: new Date()
    };

    try {
      // Get statistics before deletion
      const stats = await indexedDBManager.getStats();

      // Delete IndexedDB data
      if (options.includeProjects || options.includeTasks || options.includeSettings) {
        try {
          if (options.includeProjects) {
            result.deletedItems.projects = stats.projects;
          }
          if (options.includeTasks) {
            result.deletedItems.tasks = stats.tasks;
          }
          if (options.includeSettings) {
            result.deletedItems.settings = 1; // Assuming one settings record
          }

          await indexedDBManager.clearAllData();
        } catch (error) {
          result.errors.push(`IndexedDB deletion failed: ${error}`);
          result.success = false;
        }
      }

      // Delete encryption keys
      if (options.includeEncryptionKeys) {
        try {
          await KeyManager.resetEncryption();
        } catch (error) {
          result.errors.push(`Encryption key deletion failed: ${error}`);
          result.success = false;
        }
      }

      // Clear localStorage
      if (options.includeLocalStorage) {
        try {
          const keysToDelete = this.getAppLocalStorageKeys();
          result.deletedItems.localStorageKeys = keysToDelete.length;
          
          keysToDelete.forEach(key => {
            try {
              localStorage.removeItem(key);
            } catch (error) {
              result.errors.push(`Failed to remove localStorage key ${key}: ${error}`);
            }
          });
        } catch (error) {
          result.errors.push(`localStorage deletion failed: ${error}`);
          result.success = false;
        }
      }

      // Clear browser cache (limited to what we can control)
      if (options.includeBrowserCache) {
        try {
          const cacheCount = await this.clearAppCache();
          result.deletedItems.cacheEntries = cacheCount;
        } catch (error) {
          result.errors.push(`Cache deletion failed: ${error}`);
          result.success = false;
        }
      }

      // Log the deletion for transparency
      await this.logDataDeletion(options, result);

    } catch (error) {
      result.errors.push(`General deletion error: ${error}`);
      result.success = false;
    }

    return result;
  }

  /**
   * Get all localStorage keys used by the app
   */
  private static getAppLocalStorageKeys(): string[] {
    const appKeys: string[] = [];
    const appPrefixes = [
      'app_encryption_metadata',
      'privacy_settings',
      'data_usage_log',
      'pwa_',
      'ai_provider_',
      'task_',
      'project_'
    ];

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && appPrefixes.some(prefix => key.startsWith(prefix))) {
        appKeys.push(key);
      }
    }

    return appKeys;
  }

  /**
   * Clear app-specific cache entries
   */
  private static async clearAppCache(): Promise<number> {
    let clearedCount = 0;

    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        
        for (const cacheName of cacheNames) {
          if (cacheName.includes('ki-projekt-planer') || cacheName.includes('pwa')) {
            await caches.delete(cacheName);
            clearedCount++;
          }
        }
      }
    } catch (error) {
      console.warn('Failed to clear some cache entries:', error);
    }

    return clearedCount;
  }

  /**
   * Delete old data based on retention policy
   */
  static async deleteOldData(retentionDays: number): Promise<DataDeletionResult> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const result: DataDeletionResult = {
      success: true,
      deletedItems: {
        projects: 0,
        tasks: 0,
        settings: 0,
        cacheEntries: 0,
        localStorageKeys: 0
      },
      errors: [],
      completedAt: new Date()
    };

    try {
      // Get all projects and check their age
      const projects = await indexedDBManager.getAllProjects();
      
      for (const project of projects) {
        if (project.updatedAt < cutoffDate) {
          try {
            await indexedDBManager.deleteProject(project.id);
            result.deletedItems.projects++;
          } catch (error) {
            result.errors.push(`Failed to delete old project ${project.id}: ${error}`);
          }
        }
      }

      // Log the cleanup
      await this.logDataDeletion({
        includeProjects: true,
        includeTasks: true,
        includeSettings: false,
        includeEncryptionKeys: false,
        includeBrowserCache: false,
        includeLocalStorage: false
      }, result);

    } catch (error) {
      result.errors.push(`Old data deletion failed: ${error}`);
      result.success = false;
    }

    return result;
  }

  /**
   * Export all user data for transparency/portability
   */
  static async exportAllUserData(): Promise<{
    projects: any[];
    settings: any;
    metadata: {
      exportedAt: Date;
      version: string;
      encryptionEnabled: boolean;
    };
  }> {
    const projects = await indexedDBManager.getAllProjects();
    const settings = await indexedDBManager.loadSettings();

    return {
      projects: projects.map(project => ({
        ...project,
        // Remove internal IDs and metadata for privacy
        createdAt: project.createdAt,
        updatedAt: project.updatedAt
      })),
      settings: settings ? {
        ...settings,
        // Remove sensitive settings
        aiProviders: settings.aiProviders?.map(provider => ({
          name: provider.name,
          enabled: provider.enabled
          // API keys and secrets are not exported
        }))
      } : null,
      metadata: {
        exportedAt: new Date(),
        version: '1.0',
        encryptionEnabled: KeyManager.isEncryptionEnabled()
      }
    };
  }

  /**
   * Get privacy settings
   */
  static getPrivacySettings(): PrivacySettings {
    try {
      const stored = localStorage.getItem(this.PRIVACY_SETTINGS_KEY);
      if (stored) {
        return { ...this.getDefaultPrivacySettings(), ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Failed to load privacy settings:', error);
    }
    
    return this.getDefaultPrivacySettings();
  }

  /**
   * Save privacy settings
   */
  static savePrivacySettings(settings: PrivacySettings): void {
    try {
      localStorage.setItem(this.PRIVACY_SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save privacy settings:', error);
    }
  }

  /**
   * Get default privacy settings
   */
  private static getDefaultPrivacySettings(): PrivacySettings {
    return {
      dataRetentionDays: 365, // 1 year
      autoDeleteOldData: false,
      encryptSensitiveData: true,
      minimizeAIDataSharing: true,
      allowAnalytics: false,
      allowCrashReporting: false
    };
  }

  /**
   * Log data usage for transparency
   */
  static async logDataUsage(action: string, dataType: string, details?: any): Promise<void> {
    try {
      const log = this.getDataUsageLog();
      const entry = {
        timestamp: new Date().toISOString(),
        action,
        dataType,
        details: details || {},
        id: crypto.randomUUID()
      };

      log.push(entry);

      // Keep only last 100 entries
      if (log.length > 100) {
        log.splice(0, log.length - 100);
      }

      localStorage.setItem(this.DATA_USAGE_LOG_KEY, JSON.stringify(log));
    } catch (error) {
      console.warn('Failed to log data usage:', error);
    }
  }

  /**
   * Get data usage log for transparency
   */
  static getDataUsageLog(): any[] {
    try {
      const stored = localStorage.getItem(this.DATA_USAGE_LOG_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to load data usage log:', error);
      return [];
    }
  }

  /**
   * Clear data usage log
   */
  static clearDataUsageLog(): void {
    try {
      localStorage.removeItem(this.DATA_USAGE_LOG_KEY);
    } catch (error) {
      console.warn('Failed to clear data usage log:', error);
    }
  }

  /**
   * Log data deletion for audit trail
   */
  private static async logDataDeletion(options: DataDeletionOptions, result: DataDeletionResult): Promise<void> {
    await this.logDataUsage('data_deletion', 'all', {
      options,
      result: {
        success: result.success,
        deletedItems: result.deletedItems,
        errorCount: result.errors.length
      }
    });
  }

  /**
   * Get data statistics for privacy dashboard
   */
  static async getDataStatistics(): Promise<{
    totalProjects: number;
    totalTasks: number;
    oldestProject?: Date;
    newestProject?: Date;
    encryptionEnabled: boolean;
    storageUsed: number;
    dataUsageLogEntries: number;
  }> {
    const stats = await indexedDBManager.getStats();
    const projects = await indexedDBManager.getAllProjects();
    const usageLog = this.getDataUsageLog();

    let oldestProject: Date | undefined;
    let newestProject: Date | undefined;

    if (projects.length > 0) {
      const dates = projects.map(p => p.createdAt).sort();
      oldestProject = dates[0];
      newestProject = dates[dates.length - 1];
    }

    // Estimate storage usage (rough calculation)
    let storageUsed = 0;
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        storageUsed = estimate.usage || 0;
      }
    } catch (error) {
      console.warn('Failed to estimate storage usage:', error);
    }

    return {
      totalProjects: stats.projects,
      totalTasks: stats.tasks,
      oldestProject,
      newestProject,
      encryptionEnabled: KeyManager.isEncryptionEnabled(),
      storageUsed,
      dataUsageLogEntries: usageLog.length
    };
  }

  /**
   * Check if automatic data cleanup should run
   */
  static async shouldRunAutoCleanup(): Promise<boolean> {
    const settings = this.getPrivacySettings();
    
    if (!settings.autoDeleteOldData) {
      return false;
    }

    // Check if we have old data to clean up
    const stats = await this.getDataStatistics();
    if (!stats.oldestProject) {
      return false;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - settings.dataRetentionDays);

    return stats.oldestProject < cutoffDate;
  }

  /**
   * Run automatic data cleanup if needed
   */
  static async runAutoCleanupIfNeeded(): Promise<DataDeletionResult | null> {
    if (await this.shouldRunAutoCleanup()) {
      const settings = this.getPrivacySettings();
      return await this.deleteOldData(settings.dataRetentionDays);
    }
    return null;
  }
}