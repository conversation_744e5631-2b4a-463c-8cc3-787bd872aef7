/**
 * Data sanitization utilities for AI request content
 * Removes or masks sensitive information before sending to AI providers
 */

export interface SanitizationOptions {
  removePII: boolean;
  maskEmails: boolean;
  maskPhones: boolean;
  maskUrls: boolean;
  removeCustomPatterns: string[];
  maxLength?: number;
}

export interface SanitizationResult {
  sanitizedContent: string;
  removedItems: string[];
  wasModified: boolean;
}

export class DataSanitizer {
  // Common PII patterns
  private static readonly EMAIL_PATTERN = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
  private static readonly PHONE_PATTERN = /(\+49|0049|0)\s?[1-9]\d{1,4}\s?\d{1,8}|\b\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4}\b/g;
  private static readonly URL_PATTERN = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g;
  private static readonly CREDIT_CARD_PATTERN = /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g;
  private static readonly GERMAN_ID_PATTERN = /\b\d{11}\b/g; // German ID numbers
  private static readonly IP_PATTERN = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g;

  /**
   * Sanitize content for AI requests
   */
  static sanitizeForAI(content: string, options: SanitizationOptions = this.getDefaultOptions()): SanitizationResult {
    let sanitized = content;
    const removedItems: string[] = [];
    let wasModified = false;

    // Remove or mask emails
    if (options.maskEmails) {
      const emails = content.match(this.EMAIL_PATTERN) || [];
      if (emails.length > 0) {
        sanitized = sanitized.replace(this.EMAIL_PATTERN, '[EMAIL]');
        removedItems.push(...emails);
        wasModified = true;
      }
    }

    // Remove or mask phone numbers
    if (options.maskPhones) {
      const phones = content.match(this.PHONE_PATTERN) || [];
      if (phones.length > 0) {
        sanitized = sanitized.replace(this.PHONE_PATTERN, '[TELEFON]');
        removedItems.push(...phones);
        wasModified = true;
      }
    }

    // Remove or mask URLs
    if (options.maskUrls) {
      const urls = content.match(this.URL_PATTERN) || [];
      if (urls.length > 0) {
        sanitized = sanitized.replace(this.URL_PATTERN, '[URL]');
        removedItems.push(...urls);
        wasModified = true;
      }
    }

    // Remove PII patterns
    if (options.removePII) {
      // Credit card numbers
      const creditCards = content.match(this.CREDIT_CARD_PATTERN) || [];
      if (creditCards.length > 0) {
        sanitized = sanitized.replace(this.CREDIT_CARD_PATTERN, '[KREDITKARTE]');
        removedItems.push(...creditCards);
        wasModified = true;
      }

      // German ID numbers
      const germanIds = content.match(this.GERMAN_ID_PATTERN) || [];
      if (germanIds.length > 0) {
        sanitized = sanitized.replace(this.GERMAN_ID_PATTERN, '[AUSWEISNUMMER]');
        removedItems.push(...germanIds);
        wasModified = true;
      }

      // IP addresses
      const ips = content.match(this.IP_PATTERN) || [];
      if (ips.length > 0) {
        sanitized = sanitized.replace(this.IP_PATTERN, '[IP-ADRESSE]');
        removedItems.push(...ips);
        wasModified = true;
      }
    }

    // Remove custom patterns
    if (options.removeCustomPatterns.length > 0) {
      for (const pattern of options.removeCustomPatterns) {
        try {
          const regex = new RegExp(pattern, 'gi');
          const matches = content.match(regex) || [];
          if (matches.length > 0) {
            sanitized = sanitized.replace(regex, '[ENTFERNT]');
            removedItems.push(...matches);
            wasModified = true;
          }
        } catch (error) {
          console.warn('Invalid regex pattern:', pattern, error);
        }
      }
    }

    // Truncate if too long
    if (options.maxLength && sanitized.length > options.maxLength) {
      sanitized = sanitized.substring(0, options.maxLength) + '... [GEKÜRZT]';
      wasModified = true;
    }

    return {
      sanitizedContent: sanitized,
      removedItems,
      wasModified
    };
  }

  /**
   * Validate content before AI processing
   */
  static validateAIContent(content: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check for potential sensitive data
    if (this.EMAIL_PATTERN.test(content)) {
      issues.push('E-Mail-Adressen gefunden');
    }

    if (this.PHONE_PATTERN.test(content)) {
      issues.push('Telefonnummern gefunden');
    }

    if (this.CREDIT_CARD_PATTERN.test(content)) {
      issues.push('Potenzielle Kreditkartennummern gefunden');
    }

    if (this.GERMAN_ID_PATTERN.test(content)) {
      issues.push('Potenzielle Ausweisnummern gefunden');
    }

    // Check content length
    if (content.length > 10000) {
      issues.push('Inhalt ist sehr lang (>10.000 Zeichen)');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * Get default sanitization options
   */
  static getDefaultOptions(): SanitizationOptions {
    return {
      removePII: true,
      maskEmails: true,
      maskPhones: true,
      maskUrls: false, // URLs might be relevant for project context
      removeCustomPatterns: [],
      maxLength: 8000 // Reasonable limit for AI processing
    };
  }

  /**
   * Create sanitization report for user transparency
   */
  static createSanitizationReport(result: SanitizationResult): string {
    if (!result.wasModified) {
      return 'Keine sensiblen Daten gefunden. Inhalt wird unverändert an die KI gesendet.';
    }

    const report = [
      'Folgende Änderungen wurden vor der KI-Verarbeitung vorgenommen:',
      ''
    ];

    if (result.removedItems.length > 0) {
      report.push(`- ${result.removedItems.length} sensible Datenelemente entfernt oder maskiert`);
    }

    report.push('');
    report.push('Dies dient dem Schutz Ihrer Privatsphäre. Die KI erhält nur die für die Aufgabenbearbeitung notwendigen Informationen.');

    return report.join('\n');
  }

  /**
   * Check if content needs sanitization
   */
  static needsSanitization(content: string, options: SanitizationOptions = this.getDefaultOptions()): boolean {
    const validation = this.validateAIContent(content);
    return !validation.isValid || (options.maxLength && content.length > options.maxLength);
  }
}