/**
 * Encrypted storage wrapper for IndexedDB
 * Provides transparent encryption/decryption for sensitive project data
 */

import { indexedDBManager, type StoredProject, type StoredTask, type StoredSettings } from '@/lib/storage/indexeddb';
import { EncryptionManager, type EncryptionResult } from './encryption';
import { KeyManager } from './keyManager';
import type { Task } from '@/lib/types';

export interface EncryptedData {
  encrypted: string;
  iv: string;
  isEncrypted: boolean;
  version: number;
}

export interface EncryptionSettings {
  encryptProjects: boolean;
  encryptTasks: boolean;
  encryptSettings: boolean;
  encryptionLevel: 'basic' | 'enhanced';
}

export class EncryptedStorageManager {
  private static readonly ENCRYPTION_VERSION = 1;
  private static readonly ENCRYPTED_FIELDS = {
    project: ['title', 'description'],
    task: ['title', 'description', 'content'],
    settings: ['aiProviders'] // Only encrypt sensitive settings
  };

  /**
   * Initialize encrypted storage
   */
  static async initialize(): Promise<boolean> {
    try {
      await indexedDBManager.initDatabase();
      return await KeyManager.initializeEncryption();
    } catch (error) {
      console.error('Failed to initialize encrypted storage:', error);
      return false;
    }
  }

  /**
   * Check if encryption is available and enabled
   */
  static async isEncryptionEnabled(): Promise<boolean> {
    return KeyManager.isEncryptionEnabled() && await KeyManager.getEncryptionKey() !== null;
  }

  /**
   * Encrypt sensitive fields in a project
   */
  private static async encryptProject(project: StoredProject): Promise<StoredProject> {
    if (!await this.isEncryptionEnabled()) {
      return project;
    }

    const key = await KeyManager.getEncryptionKey();
    if (!key) return project;

    const encryptedProject = { ...project };

    // Encrypt sensitive fields
    for (const field of this.ENCRYPTED_FIELDS.project) {
      const value = (project as any)[field];
      if (value && typeof value === 'string') {
        try {
          const encrypted = await EncryptionManager.encrypt(value, key);
          (encryptedProject as any)[field] = this.createEncryptedData(encrypted);
        } catch (error) {
          console.warn(`Failed to encrypt project field ${field}:`, error);
        }
      }
    }

    return encryptedProject;
  }

  /**
   * Decrypt sensitive fields in a project
   */
  private static async decryptProject(project: StoredProject): Promise<StoredProject> {
    if (!await this.isEncryptionEnabled()) {
      return project;
    }

    const key = await KeyManager.getEncryptionKey();
    if (!key) return project;

    const decryptedProject = { ...project };

    // Decrypt sensitive fields
    for (const field of this.ENCRYPTED_FIELDS.project) {
      const value = (project as any)[field];
      if (this.isEncryptedData(value)) {
        try {
          const decrypted = await EncryptionManager.decrypt({
            encryptedData: value.encrypted,
            iv: value.iv,
            salt: '',
            key: key
          });
          (decryptedProject as any)[field] = decrypted;
        } catch (error) {
          console.warn(`Failed to decrypt project field ${field}:`, error);
          // Keep encrypted data as fallback
        }
      }
    }

    return decryptedProject;
  }

  /**
   * Encrypt sensitive fields in a task
   */
  private static async encryptTask(task: StoredTask): Promise<StoredTask> {
    if (!await this.isEncryptionEnabled()) {
      return task;
    }

    const key = await KeyManager.getEncryptionKey();
    if (!key) return task;

    const encryptedTask = { ...task };

    // Encrypt sensitive fields
    for (const field of this.ENCRYPTED_FIELDS.task) {
      const value = (task as any)[field];
      if (value && typeof value === 'string') {
        try {
          const encrypted = await EncryptionManager.encrypt(value, key);
          (encryptedTask as any)[field] = this.createEncryptedData(encrypted);
        } catch (error) {
          console.warn(`Failed to encrypt task field ${field}:`, error);
        }
      }
    }

    return encryptedTask;
  }

  /**
   * Decrypt sensitive fields in a task
   */
  private static async decryptTask(task: StoredTask): Promise<StoredTask> {
    if (!await this.isEncryptionEnabled()) {
      return task;
    }

    const key = await KeyManager.getEncryptionKey();
    if (!key) return task;

    const decryptedTask = { ...task };

    // Decrypt sensitive fields
    for (const field of this.ENCRYPTED_FIELDS.task) {
      const value = (task as any)[field];
      if (this.isEncryptedData(value)) {
        try {
          const decrypted = await EncryptionManager.decrypt({
            encryptedData: value.encrypted,
            iv: value.iv,
            salt: '',
            key: key
          });
          (decryptedTask as any)[field] = decrypted;
        } catch (error) {
          console.warn(`Failed to decrypt task field ${field}:`, error);
          // Keep encrypted data as fallback
        }
      }
    }

    return decryptedTask;
  }

  /**
   * Create encrypted data wrapper
   */
  private static createEncryptedData(encrypted: EncryptionResult): EncryptedData {
    return {
      encrypted: encrypted.encryptedData,
      iv: encrypted.iv,
      isEncrypted: true,
      version: this.ENCRYPTION_VERSION
    };
  }

  /**
   * Check if data is encrypted
   */
  private static isEncryptedData(data: any): data is EncryptedData {
    return data && 
           typeof data === 'object' && 
           data.isEncrypted === true && 
           typeof data.encrypted === 'string' && 
           typeof data.iv === 'string';
  }

  // Project operations with encryption

  /**
   * Create encrypted project
   */
  static async createProject(project: Omit<StoredProject, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const encryptedProject = await this.encryptProject(project as StoredProject);
    return indexedDBManager.createProject(encryptedProject);
  }

  /**
   * Update encrypted project
   */
  static async updateProject(id: string, updates: Partial<Omit<StoredProject, 'id' | 'createdAt'>>): Promise<void> {
    const encryptedUpdates = await this.encryptProject(updates as StoredProject);
    return indexedDBManager.updateProject(id, encryptedUpdates);
  }

  /**
   * Get decrypted project
   */
  static async getProject(id: string): Promise<StoredProject | null> {
    const project = await indexedDBManager.getProject(id);
    if (!project) return null;
    return this.decryptProject(project);
  }

  /**
   * Get all decrypted projects
   */
  static async getAllProjects(): Promise<StoredProject[]> {
    const projects = await indexedDBManager.getAllProjects();
    return Promise.all(projects.map(project => this.decryptProject(project)));
  }

  /**
   * Delete project (no encryption needed)
   */
  static async deleteProject(id: string): Promise<void> {
    return indexedDBManager.deleteProject(id);
  }

  // Task operations with encryption

  /**
   * Create encrypted task
   */
  static async createTask(
    task: Omit<StoredTask, 'id' | 'createdAt' | 'updatedAt' | 'orderIndex'>, 
    projectId: string, 
    parentTaskId?: string
  ): Promise<string> {
    const encryptedTask = await this.encryptTask(task as StoredTask);
    return indexedDBManager.createTask(encryptedTask, projectId, parentTaskId);
  }

  /**
   * Update encrypted task
   */
  static async updateTask(id: string, updates: Partial<Omit<StoredTask, 'id' | 'createdAt' | 'projectId'>>): Promise<void> {
    const encryptedUpdates = await this.encryptTask(updates as StoredTask);
    return indexedDBManager.updateTask(id, encryptedUpdates);
  }

  /**
   * Get decrypted task
   */
  static async getTask(id: string): Promise<StoredTask | null> {
    const task = await indexedDBManager.getTask(id);
    if (!task) return null;
    return this.decryptTask(task);
  }

  /**
   * Get all decrypted tasks for project
   */
  static async getTasksByProject(projectId: string): Promise<Task[]> {
    // Get tasks directly from indexedDB and decrypt them
    const tasks = await indexedDBManager.getTasksByProject(projectId);
    return tasks; // Tasks are already in the correct format from indexedDBManager
  }

  /**
   * Delete task (no encryption needed)
   */
  static async deleteTask(id: string): Promise<void> {
    return indexedDBManager.deleteTask(id);
  }

  // Settings operations with encryption

  /**
   * Save encrypted settings
   */
  static async saveSettings(settings: Omit<StoredSettings, 'updatedAt'>): Promise<void> {
    // Only encrypt sensitive settings fields
    const encryptedSettings = { ...settings };
    
    if (await this.isEncryptionEnabled()) {
      const key = await KeyManager.getEncryptionKey();
      if (key) {
        for (const field of this.ENCRYPTED_FIELDS.settings) {
          const value = (settings as any)[field];
          if (value) {
            try {
              const encrypted = await EncryptionManager.encrypt(JSON.stringify(value), key);
              (encryptedSettings as any)[field] = this.createEncryptedData(encrypted);
            } catch (error) {
              console.warn(`Failed to encrypt settings field ${field}:`, error);
            }
          }
        }
      }
    }

    return indexedDBManager.saveSettings(encryptedSettings);
  }

  /**
   * Load decrypted settings
   */
  static async loadSettings(id: string = 'default'): Promise<StoredSettings | null> {
    const settings = await indexedDBManager.loadSettings(id);
    if (!settings) return null;

    // Decrypt sensitive fields
    if (await this.isEncryptionEnabled()) {
      const key = await KeyManager.getEncryptionKey();
      if (key) {
        const decryptedSettings = { ...settings };
        
        for (const field of this.ENCRYPTED_FIELDS.settings) {
          const value = (settings as any)[field];
          if (this.isEncryptedData(value)) {
            try {
              const decrypted = await EncryptionManager.decrypt({
                encryptedData: value.encrypted,
                iv: value.iv,
                salt: '',
                key: key
              });
              (decryptedSettings as any)[field] = JSON.parse(decrypted);
            } catch (error) {
              console.warn(`Failed to decrypt settings field ${field}:`, error);
            }
          }
        }
        
        return decryptedSettings;
      }
    }

    return settings;
  }

  // Utility methods

  /**
   * Get encryption status and statistics
   */
  static async getEncryptionStatus(): Promise<{
    enabled: boolean;
    keyExists: boolean;
    encryptedProjects: number;
    encryptedTasks: number;
    totalProjects: number;
    totalTasks: number;
  }> {
    const enabled = await this.isEncryptionEnabled();
    const keyExists = KeyManager.isEncryptionEnabled();
    const stats = await indexedDBManager.getStats();

    // Count encrypted items (simplified - in real implementation would check each item)
    return {
      enabled,
      keyExists,
      encryptedProjects: enabled ? stats.projects : 0,
      encryptedTasks: enabled ? stats.tasks : 0,
      totalProjects: stats.projects,
      totalTasks: stats.tasks
    };
  }

  /**
   * Migrate existing data to encrypted format
   */
  static async migrateToEncrypted(): Promise<{ success: boolean; migratedProjects: number; migratedTasks: number }> {
    if (!await this.isEncryptionEnabled()) {
      throw new Error('Encryption not enabled');
    }

    let migratedProjects = 0;
    let migratedTasks = 0;

    try {
      // Migrate projects
      const projects = await indexedDBManager.getAllProjects();
      for (const project of projects) {
        // Check if already encrypted
        const hasEncryptedFields = this.ENCRYPTED_FIELDS.project.some(field => 
          this.isEncryptedData((project as any)[field])
        );
        
        if (!hasEncryptedFields) {
          const encryptedProject = await this.encryptProject(project);
          await indexedDBManager.updateProject(project.id, encryptedProject);
          migratedProjects++;
        }
      }

      // Note: Task migration would be more complex due to hierarchy
      // For now, we'll handle it in the background

      return {
        success: true,
        migratedProjects,
        migratedTasks
      };
    } catch (error) {
      console.error('Migration failed:', error);
      return {
        success: false,
        migratedProjects,
        migratedTasks
      };
    }
  }

  /**
   * Clear encryption and decrypt all data
   */
  static async disableEncryption(): Promise<boolean> {
    try {
      // First decrypt all data
      const projects = await this.getAllProjects();
      const tasks = await indexedDBManager.getStats(); // Get task count

      // Save decrypted versions
      for (const project of projects) {
        await indexedDBManager.updateProject(project.id, project);
      }

      // Clear encryption keys
      await KeyManager.resetEncryption();

      return true;
    } catch (error) {
      console.error('Failed to disable encryption:', error);
      return false;
    }
  }
}