/**
 * Security module exports
 * Provides encryption, data sanitization, and privacy management
 */

// Encryption utilities
export { EncryptionManager, type EncryptionResult } from './encryption';
export { KeyManager } from './keyManager';
export { EncryptedStorageManager } from './encryptedStorage';

// Data sanitization
export { 
  DataSanitizer, 
  type SanitizationOptions, 
  type SanitizationResult 
} from './dataSanitization';

// Data management and privacy
export { 
  DataManager, 
  type DataDeletionOptions, 
  type DataDeletionResult, 
  type PrivacySettings 
} from './dataManager';

// Re-export components
export { PrivacySettingsPanel } from '@/components/PrivacySettingsPanel';
export { DataUsageTransparency } from '@/components/DataUsageTransparency';