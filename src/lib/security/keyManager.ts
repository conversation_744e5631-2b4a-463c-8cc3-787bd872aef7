/**
 * Secure key management for local encryption
 * Handles key generation, storage, and retrieval
 */

import { EncryptionManager } from './encryption';

export interface KeyMetadata {
  salt: string;
  created: number;
  lastUsed: number;
}

export class KeyManager {
  private static readonly KEY_STORAGE_KEY = 'app_encryption_metadata';
  private static readonly SESSION_KEY = 'session_encryption_key';
  
  private static keyCache = new Map<string, CryptoKey>();

  /**
   * Initialize encryption for the app
   * Prompts user for password if not already set
   */
  static async initializeEncryption(): Promise<boolean> {
    try {
      const metadata = this.getKeyMetadata();
      
      if (!metadata) {
        // First time setup - generate new key
        return await this.setupNewEncryption();
      } else {
        // Existing setup - verify access
        return await this.verifyExistingEncryption(metadata);
      }
    } catch (error) {
      console.error('Failed to initialize encryption:', error);
      return false;
    }
  }

  /**
   * Setup new encryption with user password
   */
  private static async setupNewEncryption(): Promise<boolean> {
    const password = await this.promptForPassword('<PERSON>rstellen Sie ein Passwort für die Datenverschlüsselung:');
    
    if (!password || password.length < 8) {
      throw new Error('Passwort muss mindestens 8 Zeichen lang sein');
    }

    const salt = EncryptionManager.generateSalt();
    const key = await EncryptionManager.deriveKey(password, salt);

    // Store metadata (not the key itself)
    const metadata: KeyMetadata = {
      salt: EncryptionManager.arrayBufferToBase64(salt),
      created: Date.now(),
      lastUsed: Date.now()
    };

    localStorage.setItem(this.KEY_STORAGE_KEY, JSON.stringify(metadata));
    
    // Cache key for session
    this.keyCache.set(this.SESSION_KEY, key);
    
    return true;
  }

  /**
   * Verify existing encryption setup
   */
  private static async verifyExistingEncryption(metadata: KeyMetadata): Promise<boolean> {
    // Check if key is already in session cache
    if (this.keyCache.has(this.SESSION_KEY)) {
      return true;
    }

    const password = await this.promptForPassword('Geben Sie Ihr Verschlüsselungspasswort ein:');
    
    if (!password) {
      return false;
    }

    try {
      const salt = EncryptionManager.base64ToArrayBuffer(metadata.salt);
      const key = await EncryptionManager.deriveKey(password, new Uint8Array(salt));
      
      // Test the key by trying to decrypt a test value
      const testResult = await this.testKey(key);
      
      if (testResult) {
        // Update last used timestamp
        metadata.lastUsed = Date.now();
        localStorage.setItem(this.KEY_STORAGE_KEY, JSON.stringify(metadata));
        
        // Cache key for session
        this.keyCache.set(this.SESSION_KEY, key);
        return true;
      } else {
        throw new Error('Falsches Passwort');
      }
    } catch (error) {
      console.error('Key verification failed:', error);
      return false;
    }
  }

  /**
   * Get the current encryption key
   */
  static async getEncryptionKey(): Promise<CryptoKey | null> {
    const key = this.keyCache.get(this.SESSION_KEY);
    
    if (!key) {
      const initialized = await this.initializeEncryption();
      if (initialized) {
        return this.keyCache.get(this.SESSION_KEY) || null;
      }
      return null;
    }
    
    return key;
  }

  /**
   * Clear encryption key from memory
   */
  static clearSessionKey(): void {
    this.keyCache.delete(this.SESSION_KEY);
  }

  /**
   * Check if encryption is enabled
   */
  static isEncryptionEnabled(): boolean {
    return !!this.getKeyMetadata();
  }

  /**
   * Reset encryption (remove all keys and metadata)
   */
  static async resetEncryption(): Promise<void> {
    const confirmed = await this.confirmAction(
      'Sind Sie sicher, dass Sie die Verschlüsselung zurücksetzen möchten? Alle verschlüsselten Daten gehen verloren!'
    );
    
    if (confirmed) {
      localStorage.removeItem(this.KEY_STORAGE_KEY);
      this.keyCache.clear();
    }
  }

  /**
   * Get key metadata from storage
   */
  private static getKeyMetadata(): KeyMetadata | null {
    try {
      const stored = localStorage.getItem(this.KEY_STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  /**
   * Test if a key is valid by encrypting/decrypting test data
   */
  private static async testKey(key: CryptoKey): Promise<boolean> {
    try {
      const testData = 'encryption_test_' + Date.now();
      const encrypted = await EncryptionManager.encrypt(testData, key);
      const decrypted = await EncryptionManager.decrypt({
        encryptedData: encrypted.encryptedData,
        iv: encrypted.iv,
        salt: encrypted.salt,
        key: key
      });
      
      return decrypted === testData;
    } catch {
      return false;
    }
  }

  /**
   * Prompt user for password (in a real app, this would be a proper UI dialog)
   */
  private static async promptForPassword(message: string): Promise<string | null> {
    // In a real implementation, this would show a proper modal dialog
    // For now, using browser prompt as placeholder
    return prompt(message);
  }

  /**
   * Confirm action with user
   */
  private static async confirmAction(message: string): Promise<boolean> {
    // In a real implementation, this would show a proper confirmation dialog
    return confirm(message);
  }
}

// Extend EncryptionManager with helper methods
declare module './encryption' {
  namespace EncryptionManager {
    function arrayBufferToBase64(buffer: ArrayBuffer): string;
    function base64ToArrayBuffer(base64: string): ArrayBuffer;
  }
}

// Add the helper methods to EncryptionManager
Object.assign(EncryptionManager, {
  arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  },

  base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }
});