// AI Integration Module
export { GeminiClient, geminiClient } from './gemini';
export { ContextBuilder, contextBuilder } from './context';
export { OfflineAwareAIClient, offlineAwareAIClient } from './offlineAwareClient';
export { AIRequestQueue, aiRequestQueue } from './requestQueue';

// New Provider System
export { UnifiedAIClient, unifiedAIClient, aiProviderRegistry } from './client';
export { AIProvider, AIProviderRegistry } from './providers/base';
export { GeminiProvider } from './providers/gemini';

// Re-export types for convenience
export type { ContextStrategies, LoadingStates, SolveModalState } from '../types';
export type { OfflineAIResponse, AIButtonState } from './offlineAwareClient';
export type { QueuedAIRequest, QueuedRequestResult } from './requestQueue';
export type { 
  AIProviderConfig, 
  AIProviderCredentials, 
  AIProviderSettings, 
  AIFeature, 
  AIRequest, 
  AIResponse,
  TaskGenerationResponse,
  ContentResponse,
  QuestionResponse
} from './providers/base';