/**
 * Unified AI Client
 * 
 * This module provides a unified interface for AI operations that works
 * with any registered AI provider, maintaining backward compatibility
 * with the existing GeminiClient interface.
 */

import { aiProviderRegistry, AIProvider, AIResponse, TaskGenerationResponse, ContentResponse, QuestionResponse } from './providers/base';
import { GeminiProvider } from './providers/gemini';
import { Task } from '../types';

export interface UnifiedAIClientOptions {
  preferredProviderId?: string;
  fallbackToAnyProvider?: boolean;
}

export class UnifiedAIClient {
  private options: UnifiedAIClientOptions;

  constructor(options: UnifiedAIClientOptions = {}) {
    this.options = {
      fallbackToAnyProvider: true,
      ...options
    };

    // Initialize with Gemini provider if no providers are registered
    this.initializeDefaultProviders();
  }

  private initializeDefaultProviders(): void {
    if (aiProviderRegistry.getAvailableProviders().length === 0) {
      // Initialize Gemini provider with environment credentials
      const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || '';
      const geminiProvider = new GeminiProvider({ apiKey: geminiApiKey });
      
      aiProviderRegistry.register(geminiProvider);
      
      // Set as active if configured
      if (geminiProvider.isConfigured()) {
        aiProviderRegistry.setActiveProvider('gemini');
      }
    }
  }

  private getActiveProvider(): AIProvider | null {
    // Try preferred provider first
    if (this.options.preferredProviderId) {
      const preferredProvider = aiProviderRegistry.getProvider(this.options.preferredProviderId);
      if (preferredProvider && preferredProvider.isConfigured()) {
        return preferredProvider;
      }
    }

    // Try currently active provider
    const activeProvider = aiProviderRegistry.getActiveProvider();
    if (activeProvider && activeProvider.isConfigured()) {
      return activeProvider;
    }

    // Fallback to any configured provider
    if (this.options.fallbackToAnyProvider) {
      const configuredProviders = aiProviderRegistry.getConfiguredProviders();
      if (configuredProviders.length > 0) {
        return configuredProviders[0];
      }
    }

    return null;
  }

  private createCompatibilityResponse<T>(response: AIResponse<T>): T & { error?: string } {
    if (response.success && response.data) {
      return response.data as T & { error?: string };
    } else {
      // Return error in the format expected by existing code
      return { error: response.userFriendlyError || response.error } as T & { error?: string };
    }
  }

  async generateTasks(projectGoal: string, description?: string): Promise<{ tasks: any[], error?: string }> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        tasks: [],
        error: 'Kein KI-Provider verfügbar. Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.'
      };
    }

    if (!provider.supportsFeature('task_generation')) {
      return {
        tasks: [],
        error: 'Der aktuelle KI-Provider unterstützt keine Aufgabengenerierung.'
      };
    }

    const response = await provider.generateTasks(projectGoal, description);
    const result = this.createCompatibilityResponse(response);

    if (response.success && response.data) {
      return {
        tasks: response.data.tasks.map((task, index) => ({
          id: (Date.now() + index).toString(),
          title: task.title,
          description: task.description,
          content: "",
          status: "To Do" as const,
          assignees: [],
          subtasks: []
        })),
        error: result.error
      };
    }

    return {
      tasks: [],
      error: result.error
    };
  }

  async generateSubtasks(taskTitle: string, taskDescription: string, context?: string): Promise<{ tasks: any[], error?: string }> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        tasks: [],
        error: 'Kein KI-Provider verfügbar. Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.'
      };
    }

    if (!provider.supportsFeature('subtask_generation')) {
      return {
        tasks: [],
        error: 'Der aktuelle KI-Provider unterstützt keine Unteraufgabengenerierung.'
      };
    }

    const response = await provider.generateSubtasks(taskTitle, taskDescription, context);
    const result = this.createCompatibilityResponse(response);

    if (response.success && response.data) {
      return {
        tasks: response.data.tasks.map((task, index) => ({
          id: (Date.now() + index).toString(),
          title: task.title,
          description: task.description,
          content: "",
          status: "To Do" as const,
          assignees: [],
          subtasks: []
        })),
        error: result.error
      };
    }

    return {
      tasks: [],
      error: result.error
    };
  }

  async generateQuestions(context: string): Promise<{ questions: string[], error?: string }> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        questions: [],
        error: 'Kein KI-Provider verfügbar. Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.'
      };
    }

    if (!provider.supportsFeature('question_generation')) {
      return {
        questions: [],
        error: 'Der aktuelle KI-Provider unterstützt keine Fragengenerierung.'
      };
    }

    const response = await provider.generateQuestions(context);
    const result = this.createCompatibilityResponse(response);

    if (response.success && response.data) {
      return {
        questions: response.data.questions,
        error: result.error
      };
    }

    return {
      questions: [],
      error: result.error
    };
  }

  async elaborateContent(content: string, context: string, selectedText?: string): Promise<{ content: string, error?: string }> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        content: selectedText || content,
        error: 'Kein KI-Provider verfügbar. Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.'
      };
    }

    if (!provider.supportsFeature('content_elaboration')) {
      return {
        content: selectedText || content,
        error: 'Der aktuelle KI-Provider unterstützt keine Inhaltserweiterung.'
      };
    }

    const response = await provider.elaborateContent(content, context, selectedText);
    const result = this.createCompatibilityResponse(response);

    if (response.success && response.data) {
      return {
        content: response.data.content,
        error: result.error
      };
    }

    return {
      content: selectedText || content,
      error: result.error
    };
  }

  async generateTaskContent(taskTitle: string, taskDescription: string, context: string, additionalPrompt?: string): Promise<{ content: string, error?: string }> {
    const provider = this.getActiveProvider();
    if (!provider) {
      return {
        content: `<div class="error-content">
          <p><strong>Inhaltserstellung fehlgeschlagen:</strong> Kein KI-Provider verfügbar.</p>
          <p>Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.</p>
        </div>`,
        error: 'Kein KI-Provider verfügbar. Bitte konfigurieren Sie einen AI-Provider in den Einstellungen.'
      };
    }

    if (!provider.supportsFeature('task_content_generation')) {
      return {
        content: `<div class="error-content">
          <p><strong>Inhaltserstellung fehlgeschlagen:</strong> Feature nicht unterstützt.</p>
          <p>Der aktuelle KI-Provider unterstützt keine Aufgabeninhalte-Generierung.</p>
        </div>`,
        error: 'Der aktuelle KI-Provider unterstützt keine Aufgabeninhalte-Generierung.'
      };
    }

    const response = await provider.generateTaskContent(taskTitle, taskDescription, context, additionalPrompt);
    const result = this.createCompatibilityResponse(response);

    if (response.success && response.data) {
      return {
        content: response.data.content,
        error: result.error
      };
    }

    return {
      content: `<div class="error-content">
        <p><strong>Inhaltserstellung fehlgeschlagen:</strong> ${result.error}</p>
      </div>`,
      error: result.error
    };
  }

  // Provider management methods
  getAvailableProviders(): AIProvider[] {
    return aiProviderRegistry.getAvailableProviders();
  }

  getConfiguredProviders(): AIProvider[] {
    return aiProviderRegistry.getConfiguredProviders();
  }

  getActiveProvider(): AIProvider | null {
    return aiProviderRegistry.getActiveProvider();
  }

  setActiveProvider(providerId: string): boolean {
    return aiProviderRegistry.setActiveProvider(providerId);
  }

  registerProvider(provider: AIProvider): void {
    aiProviderRegistry.register(provider);
  }

  // Utility methods
  isAnyProviderConfigured(): boolean {
    return aiProviderRegistry.hasConfiguredProvider();
  }

  getActiveProviderInfo(): { id: string; name: string; description: string } | null {
    const provider = this.getActiveProvider();
    if (!provider) return null;

    return {
      id: provider.getId(),
      name: provider.getName(),
      description: provider.getDescription()
    };
  }
}

// Export singleton instance for backward compatibility
export const unifiedAIClient = new UnifiedAIClient();

// Re-export for convenience
export { aiProviderRegistry } from './providers/base';
export type { AIProvider, AIProviderConfig, AIProviderCredentials, AIProviderSettings } from './providers/base';