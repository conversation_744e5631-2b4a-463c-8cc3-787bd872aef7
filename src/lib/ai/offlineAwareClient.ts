import { geminiClient, GeminiClient } from './gemini';
import { aiRequestQueue, QueuedAIRequest } from './requestQueue';
import { DataSanitizer, type SanitizationOptions } from '@/lib/security/dataSanitization';
import { DataManager } from '@/lib/security/dataManager';

export interface OfflineAIResponse<T = any> {
  success: boolean;
  data?: T & { queuedRequestId?: string };
  error?: string;
  isOffline?: boolean;
  fallbackUsed?: boolean;
}

export interface AIButtonState {
  enabled: boolean;
  loading: boolean;
  tooltip: string;
  variant: 'default' | 'disabled' | 'offline';
}

export class OfflineAwareAIClient {
  private geminiClient: GeminiClient;
  private isOnlineCache: boolean = true;
  private lastOnlineCheck: number = 0;
  private onlineCheckInterval: number = 5000; // 5 seconds
  private sanitizationOptions: SanitizationOptions;

  constructor(geminiClient: GeminiClient) {
    this.geminiClient = geminiClient;
    this.sanitizationOptions = DataSanitizer.getDefaultOptions();
    this.initializeOnlineDetection();
  }

  private initializeOnlineDetection(): void {
    if (typeof window === 'undefined') return;

    // Initial check
    this.updateOnlineStatus();

    // Listen for online/offline events
    window.addEventListener('online', () => this.updateOnlineStatus());
    window.addEventListener('offline', () => this.updateOnlineStatus());

    // Periodic connectivity check
    setInterval(() => {
      if (Date.now() - this.lastOnlineCheck > this.onlineCheckInterval) {
        this.checkConnectivity();
      }
    }, this.onlineCheckInterval);
  }

  private updateOnlineStatus(): void {
    this.isOnlineCache = navigator.onLine;
    this.lastOnlineCheck = Date.now();
  }

  private async checkConnectivity(): Promise<boolean> {
    if (typeof window === 'undefined') return true;

    try {
      // Try to fetch a small resource to verify actual connectivity
      const response = await fetch('/favicon.ico', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(3000) // 3 second timeout
      });
      
      const isOnline = response.ok;
      this.isOnlineCache = isOnline;
      this.lastOnlineCheck = Date.now();
      
      return isOnline;
    } catch (error) {
      this.isOnlineCache = false;
      this.lastOnlineCheck = Date.now();
      return false;
    }
  }

  public isOnline(): boolean {
    return this.isOnlineCache;
  }

  public getAIButtonState(isLoading: boolean = false): AIButtonState {
    if (!this.isOnline()) {
      return {
        enabled: false,
        loading: false,
        tooltip: 'AI-Funktionen sind offline nicht verfügbar. Bitte prüfen Sie Ihre Internetverbindung.',
        variant: 'offline'
      };
    }

    if (isLoading) {
      return {
        enabled: false,
        loading: true,
        tooltip: 'AI generiert Inhalt...',
        variant: 'default'
      };
    }

    return {
      enabled: true,
      loading: false,
      tooltip: 'AI-Unterstützung verfügbar',
      variant: 'default'
    };
  }

  public async generateTasks(
    projectGoal: string, 
    description?: string,
    queueIfOffline: boolean = true
  ): Promise<OfflineAIResponse<{ tasks: any[] }>> {
    if (!this.isOnline()) {
      if (queueIfOffline) {
        const requestId = aiRequestQueue.addRequest({
          type: 'generateTasks',
          params: { projectGoal, description },
          maxRetries: 3,
          taskTitle: projectGoal
        });

        return {
          success: false,
          error: 'Anfrage wurde in die Warteschlange eingereiht. Sie wird automatisch verarbeitet, sobald eine Internetverbindung verfügbar ist.',
          isOffline: true,
          fallbackUsed: false,
          data: { queuedRequestId: requestId }
        };
      }

      return {
        success: false,
        error: 'Keine Internetverbindung verfügbar. AI-Funktionen sind offline nicht verfügbar.',
        isOffline: true,
        fallbackUsed: false
      };
    }

    try {
      // Sanitize input data
      const { sanitizedContent, sanitizedContext } = await this.sanitizeContent(
        projectGoal, 
        description || ''
      );

      const result = await this.geminiClient.generateTasks(sanitizedContent, sanitizedContext);
      
      // Log the request
      await this.logAIRequest(
        'generateTasks', 
        'gemini', 
        sanitizedContent.length + sanitizedContext.length, 
        !result.error,
        result.error
      );
      
      return {
        success: !result.error,
        data: { tasks: result.tasks },
        error: result.error,
        isOffline: false,
        fallbackUsed: false
      };
    } catch (error) {
      // Check if error is network-related
      const isNetworkError = this.isNetworkError(error);
      
      if (isNetworkError) {
        // Update online status
        this.isOnlineCache = false;
        
        return {
          success: false,
          error: 'Verbindung zur AI verloren. Bitte prüfen Sie Ihre Internetverbindung.',
          isOffline: true,
          fallbackUsed: false
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler bei der AI-Generierung',
        isOffline: false,
        fallbackUsed: false
      };
    }
  }

  public async generateSubtasks(
    taskTitle: string, 
    taskDescription: string, 
    context?: string,
    queueIfOffline: boolean = true,
    taskId?: string
  ): Promise<OfflineAIResponse<{ tasks: any[] }>> {
    if (!this.isOnline()) {
      if (queueIfOffline) {
        const requestId = aiRequestQueue.addRequest({
          type: 'generateSubtasks',
          params: { taskTitle, taskDescription, context },
          maxRetries: 3,
          taskId,
          taskTitle
        });

        return {
          success: false,
          error: 'Anfrage wurde in die Warteschlange eingereiht. Sie wird automatisch verarbeitet, sobald eine Internetverbindung verfügbar ist.',
          isOffline: true,
          fallbackUsed: false,
          data: { queuedRequestId: requestId }
        };
      }

      return {
        success: false,
        error: 'Keine Internetverbindung verfügbar. AI-Funktionen sind offline nicht verfügbar.',
        isOffline: true,
        fallbackUsed: false
      };
    }

    try {
      // Sanitize input data
      const { sanitizedContent, sanitizedContext } = await this.sanitizeContent(
        `${taskTitle}\n${taskDescription}`, 
        context || ''
      );

      const result = await this.geminiClient.generateSubtasks(
        taskTitle, 
        taskDescription, 
        sanitizedContext
      );
      
      // Log the request
      await this.logAIRequest(
        'generateSubtasks', 
        'gemini', 
        sanitizedContent.length + sanitizedContext.length, 
        !result.error,
        result.error
      );
      
      return {
        success: !result.error,
        data: { tasks: result.tasks },
        error: result.error,
        isOffline: false,
        fallbackUsed: false
      };
    } catch (error) {
      const isNetworkError = this.isNetworkError(error);
      
      if (isNetworkError) {
        this.isOnlineCache = false;
        
        return {
          success: false,
          error: 'Verbindung zur AI verloren. Bitte prüfen Sie Ihre Internetverbindung.',
          isOffline: true,
          fallbackUsed: false
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler bei der AI-Generierung',
        isOffline: false,
        fallbackUsed: false
      };
    }
  }

  public async generateTaskContent(
    taskTitle: string,
    taskDescription: string,
    context: string,
    additionalPrompt?: string,
    queueIfOffline: boolean = true,
    taskId?: string
  ): Promise<OfflineAIResponse<{ content: string }>> {
    if (!this.isOnline()) {
      if (queueIfOffline) {
        const requestId = aiRequestQueue.addRequest({
          type: 'generateTaskContent',
          params: { taskTitle, taskDescription, context, additionalPrompt },
          maxRetries: 3,
          taskId,
          taskTitle
        });

        return {
          success: false,
          error: 'Anfrage wurde in die Warteschlange eingereiht. Sie wird automatisch verarbeitet, sobald eine Internetverbindung verfügbar ist.',
          isOffline: true,
          fallbackUsed: false,
          data: { queuedRequestId: requestId }
        };
      }

      return {
        success: false,
        error: 'Keine Internetverbindung verfügbar. AI-Funktionen sind offline nicht verfügbar.',
        isOffline: true,
        fallbackUsed: false
      };
    }

    try {
      // Sanitize input data
      const { sanitizedContent, sanitizedContext } = await this.sanitizeContent(
        `${taskTitle}\n${taskDescription}\n${additionalPrompt || ''}`, 
        context
      );

      const result = await this.geminiClient.generateTaskContent(
        taskTitle, 
        taskDescription, 
        sanitizedContext, 
        additionalPrompt
      );
      
      // Log the request
      await this.logAIRequest(
        'generateTaskContent', 
        'gemini', 
        sanitizedContent.length + sanitizedContext.length, 
        !result.error,
        result.error
      );
      
      return {
        success: !result.error,
        data: { content: result.content },
        error: result.error,
        isOffline: false,
        fallbackUsed: false
      };
    } catch (error) {
      const isNetworkError = this.isNetworkError(error);
      
      if (isNetworkError) {
        this.isOnlineCache = false;
        
        return {
          success: false,
          error: 'Verbindung zur AI verloren. Bitte prüfen Sie Ihre Internetverbindung.',
          isOffline: true,
          fallbackUsed: false
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler bei der AI-Generierung',
        isOffline: false,
        fallbackUsed: false
      };
    }
  }

  public async elaborateContent(
    content: string,
    context: string,
    selectedText?: string,
    queueIfOffline: boolean = true,
    taskId?: string,
    taskTitle?: string
  ): Promise<OfflineAIResponse<{ content: string }>> {
    if (!this.isOnline()) {
      if (queueIfOffline) {
        const requestId = aiRequestQueue.addRequest({
          type: 'elaborateContent',
          params: { content, context, selectedText },
          maxRetries: 3,
          taskId,
          taskTitle
        });

        return {
          success: false,
          error: 'Anfrage wurde in die Warteschlange eingereiht. Sie wird automatisch verarbeitet, sobald eine Internetverbindung verfügbar ist.',
          isOffline: true,
          fallbackUsed: false,
          data: { queuedRequestId: requestId }
        };
      }

      return {
        success: false,
        error: 'Keine Internetverbindung verfügbar. AI-Funktionen sind offline nicht verfügbar.',
        isOffline: true,
        fallbackUsed: false
      };
    }

    try {
      // Sanitize input data
      const { sanitizedContent, sanitizedContext } = await this.sanitizeContent(
        `${content}\n${selectedText || ''}`, 
        context
      );

      const result = await this.geminiClient.elaborateContent(
        sanitizedContent, 
        sanitizedContext, 
        selectedText
      );
      
      // Log the request
      await this.logAIRequest(
        'elaborateContent', 
        'gemini', 
        sanitizedContent.length + sanitizedContext.length, 
        !result.error,
        result.error
      );
      
      return {
        success: !result.error,
        data: { content: result.content },
        error: result.error,
        isOffline: false,
        fallbackUsed: false
      };
    } catch (error) {
      const isNetworkError = this.isNetworkError(error);
      
      if (isNetworkError) {
        this.isOnlineCache = false;
        
        return {
          success: false,
          error: 'Verbindung zur AI verloren. Bitte prüfen Sie Ihre Internetverbindung.',
          isOffline: true,
          fallbackUsed: false
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unbekannter Fehler bei der AI-Generierung',
        isOffline: false,
        fallbackUsed: false
      };
    }
  }

  private isNetworkError(error: any): boolean {
    if (!error) return false;
    
    const errorMessage = error.message?.toLowerCase() || '';
    const errorName = error.name?.toLowerCase() || '';
    
    // Check for common network error indicators
    return (
      errorMessage.includes('network') ||
      errorMessage.includes('fetch') ||
      errorMessage.includes('connection') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('aborted') ||
      errorName.includes('networkerror') ||
      errorName.includes('typeerror') ||
      error.code === 'NETWORK_ERROR' ||
      error.code === 'TIMEOUT'
    );
  }

  /**
   * Sanitize content before sending to AI
   */
  private async sanitizeContent(content: string, context: string): Promise<{
    sanitizedContent: string;
    sanitizedContext: string;
    sanitizationReport: string;
  }> {
    const contentResult = DataSanitizer.sanitizeForAI(content, this.sanitizationOptions);
    const contextResult = DataSanitizer.sanitizeForAI(context, this.sanitizationOptions);

    // Log data usage for transparency
    await DataManager.logDataUsage('ai_request_sanitization', 'content', {
      originalLength: content.length + context.length,
      sanitizedLength: contentResult.sanitizedContent.length + contextResult.sanitizedContent.length,
      removedItems: [...contentResult.removedItems, ...contextResult.removedItems],
      wasModified: contentResult.wasModified || contextResult.wasModified
    });

    return {
      sanitizedContent: contentResult.sanitizedContent,
      sanitizedContext: contextResult.sanitizedContent,
      sanitizationReport: DataSanitizer.createSanitizationReport(contentResult)
    };
  }

  /**
   * Log AI request for transparency
   */
  private async logAIRequest(
    operation: string, 
    provider: string, 
    contentLength: number, 
    success: boolean,
    error?: string
  ): Promise<void> {
    await DataManager.logDataUsage(`ai_${operation}`, 'ai_request', {
      provider,
      contentLength,
      success,
      error,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Update sanitization options
   */
  public setSanitizationOptions(options: SanitizationOptions): void {
    this.sanitizationOptions = options;
  }

  /**
   * Get current sanitization options
   */
  public getSanitizationOptions(): SanitizationOptions {
    return { ...this.sanitizationOptions };
  }

  // Method to show appropriate offline message
  public getOfflineMessage(operation: string): string {
    const queueLength = aiRequestQueue.getQueueLength();
    const queueInfo = queueLength > 0 ? ` (${queueLength} Anfragen in der Warteschlange)` : '';
    
    const messages = {
      'breakdown': `Aufgabenzerlegung ist offline nicht verfügbar. Bitte stellen Sie eine Internetverbindung her.${queueInfo}`,
      'solve': `AI-Inhaltsgenerierung ist offline nicht verfügbar. Bitte stellen Sie eine Internetverbindung her.${queueInfo}`,
      'elaborate': `AI-Textverbesserung ist offline nicht verfügbar. Bitte stellen Sie eine Internetverbindung her.${queueInfo}`,
      'default': `AI-Funktionen sind offline nicht verfügbar. Bitte stellen Sie eine Internetverbindung her.${queueInfo}`
    };

    return messages[operation as keyof typeof messages] || messages.default;
  }

  // Queue management methods
  public getQueueLength(): number {
    return aiRequestQueue.getQueueLength();
  }

  public getQueuedRequests(): QueuedAIRequest[] {
    return aiRequestQueue.getQueuedRequests();
  }

  public clearQueue(): void {
    aiRequestQueue.clearQueue();
  }

  public onRequestCompleted(callback: (request: QueuedAIRequest, result: any) => void): () => void {
    return aiRequestQueue.onRequestCompleted(callback);
  }

  public async processQueue(): Promise<void> {
    return aiRequestQueue.processQueue();
  }
}

// Export singleton instance
export const offlineAwareAIClient = new OfflineAwareAIClient(geminiClient);