/**
 * Gemini AI Provider Implementation
 * 
 * This module implements the AIProvider interface for Google's Gemini API,
 * refactoring the existing GeminiClient to work with the new provider system.
 */

import { AIProvider, AIProviderConfig, AIProviderCredentials, AIProviderSettings, AIResponse, TaskGenerationResponse, ContentResponse, QuestionResponse, AIFeature } from './base';
import { AIErrorHandler, AIErrorType, type AIError } from '@/lib/utils/errorHandler';
import { AIPrompts, PromptConfig, ResponseSchemas } from '../prompts';

interface GeminiAPIOptions {
  isJson?: boolean;
  isQuestionGeneration?: boolean;
  maxRetries?: number;
  timeout?: number;
}

interface GeminiTaskResponse {
  tasks: Array<{
    title: string;
    description: string;
  }>;
}

interface GeminiQuestionResponse {
  questions: string[];
}

export class GeminiProvider extends AIProvider {
  private baseUrl: string = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';

  constructor(credentials: AIProviderCredentials = {}, settings: AIProviderSettings = {}) {
    const config: AIProviderConfig = {
      id: 'gemini',
      name: 'Google Gemini',
      description: 'Google\'s Gemini AI model for task generation and content elaboration',
      apiKeyRequired: true,
      supportedFeatures: [
        'task_generation',
        'subtask_generation',
        'content_elaboration',
        'question_generation',
        'task_content_generation'
      ] as AIFeature[],
      defaultSettings: {
        temperature: PromptConfig.defaults.temperature,
        topK: PromptConfig.defaults.topK,
        topP: PromptConfig.defaults.topP,
        maxTokens: PromptConfig.defaults.maxOutputTokens,
        timeout: 30000,
        maxRetries: 3
      }
    };

    super(config, credentials, settings);
  }

  async validateCredentials(): Promise<boolean> {
    if (!this.credentials.apiKey) {
      return false;
    }

    try {
      // Make a simple test request to validate the API key
      const response = await this.makeRequest('Test', { timeout: 5000, maxRetries: 1 });
      return response.success;
    } catch (error) {
      return false;
    }
  }

  updateCredentials(credentials: AIProviderCredentials): void {
    this.credentials = { ...this.credentials, ...credentials };
  }

  updateSettings(settings: AIProviderSettings): void {
    this.settings = { ...this.settings, ...settings };
  }

  async generateTasks(projectGoal: string, description?: string): Promise<AIResponse<TaskGenerationResponse>> {
    const prompt = AIPrompts.generateTasks({
      projectGoal,
      description,
      maxEntries: PromptConfig.maxEntries.mainTasks
    });

    try {
      const response = await this.makeRequest(prompt, { isJson: true });

      if (!response.success) {
        const fallback = AIErrorHandler.createFallbackResponse<{ tasks: any[] }>('tasks');
        return this.createErrorResponse(
          response.error || 'Task generation failed',
          response.userFriendlyError
        );
      }

      const parsed: GeminiTaskResponse = JSON.parse(response.content);

      if (!parsed.tasks || !Array.isArray(parsed.tasks)) {
        throw new Error('Invalid tasks array in response');
      }

      return this.createSuccessResponse({
        tasks: parsed.tasks.map(task => ({
          title: task.title || 'Unbenannte Aufgabe',
          description: task.description || 'Keine Beschreibung verfügbar'
        }))
      }, response.metadata);

    } catch (error) {
      const aiError = AIErrorHandler.categorizeError(error);
      AIErrorHandler.logError(aiError, 'GeminiProvider.generateTasks');

      return this.createErrorResponse(
        aiError.message,
        aiError.userFriendlyMessage
      );
    }
  }

  async generateSubtasks(taskTitle: string, taskDescription: string, context?: string): Promise<AIResponse<TaskGenerationResponse>> {
    const prompt = AIPrompts.generateSubtasks({
      taskTitle,
      taskDescription,
      context,
      maxEntries: PromptConfig.maxEntries.subtasks
    });

    try {
      const response = await this.makeRequest(prompt, { isJson: true });

      if (!response.success) {
        return this.createErrorResponse(
          response.error || 'Subtask generation failed',
          response.userFriendlyError
        );
      }

      const parsed: GeminiTaskResponse = JSON.parse(response.content);

      if (!parsed.tasks || !Array.isArray(parsed.tasks)) {
        throw new Error('Invalid tasks array in response');
      }

      return this.createSuccessResponse({
        tasks: parsed.tasks.map(task => ({
          title: task.title || 'Unbenannte Unteraufgabe',
          description: task.description || 'Keine Beschreibung verfügbar'
        }))
      }, response.metadata);

    } catch (error) {
      const aiError = AIErrorHandler.categorizeError(error);
      AIErrorHandler.logError(aiError, 'GeminiProvider.generateSubtasks');

      return this.createErrorResponse(
        aiError.message,
        aiError.userFriendlyMessage
      );
    }
  }

  async generateQuestions(context: string): Promise<AIResponse<QuestionResponse>> {
    const prompt = AIPrompts.generateQuestions({ context });

    try {
      const response = await this.makeRequest(prompt, { isJson: true, isQuestionGeneration: true });

      if (!response.success) {
        const fallback = AIErrorHandler.createFallbackResponse<{ questions: string[] }>('questions');
        return this.createErrorResponse(
          response.error || 'Question generation failed',
          response.userFriendlyError
        );
      }

      const parsed: GeminiQuestionResponse = JSON.parse(response.content);

      if (!parsed.questions || !Array.isArray(parsed.questions)) {
        throw new Error('Invalid questions array in response');
      }

      return this.createSuccessResponse({
        questions: parsed.questions.filter(q => q && typeof q === 'string')
      }, response.metadata);

    } catch (error) {
      const aiError = AIErrorHandler.categorizeError(error);
      AIErrorHandler.logError(aiError, 'GeminiProvider.generateQuestions');

      return this.createErrorResponse(
        aiError.message,
        aiError.userFriendlyMessage
      );
    }
  }

  async elaborateContent(content: string, context: string, selectedText?: string): Promise<AIResponse<ContentResponse>> {
    const prompt = AIPrompts.elaborateContent({
      content,
      context,
      selectedText
    });

    try {
      const response = await this.makeRequest(prompt);

      if (!response.success) {
        return this.createErrorResponse(
          response.error || 'Content elaboration failed',
          response.userFriendlyError
        );
      }

      return this.createSuccessResponse({
        content: response.content.trim()
      }, response.metadata);

    } catch (error) {
      const aiError = AIErrorHandler.categorizeError(error);
      AIErrorHandler.logError(aiError, 'GeminiProvider.elaborateContent');

      return this.createErrorResponse(
        aiError.message,
        aiError.userFriendlyMessage
      );
    }
  }

  async generateTaskContent(taskTitle: string, taskDescription: string, context: string, additionalPrompt?: string): Promise<AIResponse<ContentResponse>> {
    const prompt = AIPrompts.generateTaskContent({
      taskTitle,
      taskDescription,
      context,
      additionalPrompt
    });

    try {
      const response = await this.makeRequest(prompt);

      if (!response.success) {
        return this.createErrorResponse(
          response.error || 'Task content generation failed',
          response.userFriendlyError
        );
      }

      return this.createSuccessResponse({
        content: response.content
      }, response.metadata);

    } catch (error) {
      const aiError = AIErrorHandler.categorizeError(error);
      AIErrorHandler.logError(aiError, 'GeminiProvider.generateTaskContent');

      return this.createErrorResponse(
        aiError.message,
        aiError.userFriendlyMessage
      );
    }
  }

  private async makeRequest(prompt: string, options: GeminiAPIOptions = {}): Promise<{ success: boolean; content: string; error?: string; userFriendlyError?: string; metadata?: any }> {
    const maxRetries = options.maxRetries ?? this.settings.maxRetries ?? 3;
    const timeout = options.timeout ?? this.settings.timeout ?? 30000;

    if (!this.credentials.apiKey) {
      return {
        success: false,
        content: '',
        error: 'API key missing',
        userFriendlyError: 'KI-Service nicht verfügbar: API-Schlüssel fehlt. Bitte konfigurieren Sie einen gültigen Gemini API-Schlüssel.'
      };
    }

    try {
      return await AIErrorHandler.withRetry(
        async () => {
          const generationConfig: any = {
            temperature: this.settings.temperature,
            topK: this.settings.topK,
            topP: this.settings.topP,
            maxOutputTokens: this.settings.maxTokens,
          };

          if (options.isJson) {
            generationConfig.responseMimeType = "application/json";
            if (options.isQuestionGeneration) {
              generationConfig.responseSchema = ResponseSchemas.questions;
            } else {
              generationConfig.responseSchema = ResponseSchemas.tasks;
            }
          }

          const requestBody = {
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig
          };

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          const response = await fetch(`${this.baseUrl}?key=${this.credentials.apiKey}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const errorMessage = `API request failed: ${response.status} ${response.statusText}. ${errorData.error?.message || ''}`;

            if (response.status === 429) {
              throw new Error(`Rate limit exceeded: ${response.status}`);
            } else if (response.status === 401) {
              throw new Error(`Authentication failed: ${response.status}`);
            } else if (response.status === 403) {
              throw new Error(`Quota exceeded: ${response.status}`);
            } else if (response.status >= 500) {
              throw new Error(`Server error: ${response.status}`);
            } else {
              throw new Error(errorMessage);
            }
          }

          const data = await response.json();

          if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response format from Gemini API');
          }

          const content = data.candidates[0].content.parts[0].text;
          const finishReason = data.candidates[0].finishReason || data.candidates[0].finish_reason || 'unknown';
          const usage = data.usageMetadata || data.usage || {};
          const promptTokens = usage.promptTokenCount || usage.prompt_tokens || 0;
          const candidateTokens = usage.candidatesTokenCount || usage.completion_tokens || 0;
          const totalTokens = usage.totalTokenCount || usage.total_tokens || (promptTokens + candidateTokens);

          return {
            success: true,
            content,
            metadata: {
              finishReason,
              tokensUsed: totalTokens,
              promptTokens,
              candidateTokens
            }
          };
        },
        {
          maxRetries: 2,
          baseDelay: 5000,
          maxDelay: 60000,
          backoffMultiplier: 3
        }
      );
    } catch (error) {
      const aiError = error instanceof Object && 'type' in error
        ? error as AIError
        : AIErrorHandler.categorizeError(error);

      AIErrorHandler.logError(aiError, 'GeminiProvider.makeRequest');

      return {
        success: false,
        content: '',
        error: aiError.message,
        userFriendlyError: aiError.userFriendlyMessage
      };
    }
  }
}