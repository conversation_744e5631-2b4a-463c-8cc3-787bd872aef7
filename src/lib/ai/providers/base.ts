/**
 * Abstract AI Provider Interface
 * 
 * This module defines the base interface and types for AI providers,
 * enabling modular AI service integration with standardized methods.
 */

export interface AIProviderConfig {
  id: string;
  name: string;
  description: string;
  apiKeyRequired: boolean;
  supportedFeatures: AIFeature[];
  defaultSettings: Record<string, any>;
}

export type AIFeature = 
  | 'task_generation'
  | 'subtask_generation' 
  | 'content_elaboration'
  | 'question_generation'
  | 'task_content_generation';

export interface AIProviderCredentials {
  apiKey?: string;
  endpoint?: string;
  additionalConfig?: Record<string, any>;
}

export interface AIProviderSettings {
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  maxRetries?: number;
  [key: string]: any;
}

export interface AIRequest {
  type: AIFeature;
  prompt: string;
  options?: {
    isJson?: boolean;
    maxRetries?: number;
    timeout?: number;
    [key: string]: any;
  };
}

export interface AIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  userFriendlyError?: string;
  metadata?: {
    tokensUsed?: number;
    responseTime?: number;
    finishReason?: string;
    [key: string]: any;
  };
}

export interface TaskGenerationResponse {
  tasks: Array<{
    title: string;
    description: string;
  }>;
}

export interface ContentResponse {
  content: string;
}

export interface QuestionResponse {
  questions: string[];
}

/**
 * Abstract base class for AI providers
 */
export abstract class AIProvider {
  protected config: AIProviderConfig;
  protected credentials: AIProviderCredentials;
  protected settings: AIProviderSettings;

  constructor(
    config: AIProviderConfig,
    credentials: AIProviderCredentials = {},
    settings: AIProviderSettings = {}
  ) {
    this.config = config;
    this.credentials = credentials;
    this.settings = { ...config.defaultSettings, ...settings };
  }

  // Configuration methods
  abstract validateCredentials(): Promise<boolean>;
  abstract updateCredentials(credentials: AIProviderCredentials): void;
  abstract updateSettings(settings: AIProviderSettings): void;

  // Core AI methods
  abstract generateTasks(projectGoal: string, description?: string): Promise<AIResponse<TaskGenerationResponse>>;
  abstract generateSubtasks(taskTitle: string, taskDescription: string, context?: string): Promise<AIResponse<TaskGenerationResponse>>;
  abstract generateQuestions(context: string): Promise<AIResponse<QuestionResponse>>;
  abstract elaborateContent(content: string, context: string, selectedText?: string): Promise<AIResponse<ContentResponse>>;
  abstract generateTaskContent(taskTitle: string, taskDescription: string, context: string, additionalPrompt?: string): Promise<AIResponse<ContentResponse>>;

  // Provider information
  getConfig(): AIProviderConfig {
    return { ...this.config };
  }

  getId(): string {
    return this.config.id;
  }

  getName(): string {
    return this.config.name;
  }

  getDescription(): string {
    return this.config.description;
  }

  getSupportedFeatures(): AIFeature[] {
    return [...this.config.supportedFeatures];
  }

  supportsFeature(feature: AIFeature): boolean {
    return this.config.supportedFeatures.includes(feature);
  }

  getSettings(): AIProviderSettings {
    return { ...this.settings };
  }

  isConfigured(): boolean {
    if (this.config.apiKeyRequired && !this.credentials.apiKey) {
      return false;
    }
    return true;
  }

  // Utility methods for error handling
  protected createErrorResponse<T>(error: string, userFriendlyError?: string): AIResponse<T> {
    return {
      success: false,
      error,
      userFriendlyError: userFriendlyError || error
    };
  }

  protected createSuccessResponse<T>(data: T, metadata?: Record<string, any>): AIResponse<T> {
    return {
      success: true,
      data,
      metadata
    };
  }
}

/**
 * Provider registry for managing multiple AI providers
 */
export class AIProviderRegistry {
  private providers = new Map<string, AIProvider>();
  private activeProviderId: string | null = null;

  register(provider: AIProvider): void {
    this.providers.set(provider.getId(), provider);
  }

  unregister(providerId: string): void {
    if (this.activeProviderId === providerId) {
      this.activeProviderId = null;
    }
    this.providers.delete(providerId);
  }

  getProvider(providerId: string): AIProvider | null {
    return this.providers.get(providerId) || null;
  }

  getActiveProvider(): AIProvider | null {
    if (!this.activeProviderId) {
      return null;
    }
    return this.getProvider(this.activeProviderId);
  }

  setActiveProvider(providerId: string): boolean {
    if (this.providers.has(providerId)) {
      this.activeProviderId = providerId;
      return true;
    }
    return false;
  }

  getAvailableProviders(): AIProvider[] {
    return Array.from(this.providers.values());
  }

  getConfiguredProviders(): AIProvider[] {
    return this.getAvailableProviders().filter(provider => provider.isConfigured());
  }

  getProviderConfigs(): AIProviderConfig[] {
    return this.getAvailableProviders().map(provider => provider.getConfig());
  }

  hasConfiguredProvider(): boolean {
    return this.getConfiguredProviders().length > 0;
  }

  getActiveProviderId(): string | null {
    return this.activeProviderId;
  }
}

// Export singleton registry instance
export const aiProviderRegistry = new AIProviderRegistry();