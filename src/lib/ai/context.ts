import { Task, ContextStrategies } from '../types';

interface ContextBuilderOptions {
  strategies: ContextStrategies;
  mainProject: string;
  mainProjectDescription: string;
  tasks: Task[];
  selectedHtml?: string;
}

export class ContextBuilder {
  /**
   * Builds context for AI based on selected strategies
   */
  buildContextForAI(taskId: string, options: ContextBuilderOptions): string {
    const { strategies, mainProject, mainProjectDescription, tasks, selectedHtml } = options;
    let context = `Projekt: ${mainProject}\n`;
    
    if (mainProjectDescription) {
      context += `Projektbeschreibung: ${mainProjectDescription}\n\n`;
    }

    // Find the target task
    const targetTask = this.findTaskInternal(tasks, taskId);
    if (!targetTask) {
      return context + "Aufgabe nicht gefunden.";
    }

    // Strategy 1: Task path
    if (strategies.strategy1) {
      const taskPath = this.findTaskPath(tasks, taskId);
      if (taskPath.length > 0) {
        context += "Aufgabenpfad:\n";
        taskPath.forEach((task, index) => {
          const indent = "  ".repeat(index);
          context += `${indent}- ${task.title}\n`;
          if (task.description) {
            context += `${indent}  Beschreibung: ${task.description}\n`;
          }
        });
        context += "\n";
      }
    }

    // Strategy 2: Intelligent summary
    if (strategies.strategy2) {
      const taskPath = this.findTaskPath(tasks, taskId);
      const summaryContext = this.buildSummaryContext(taskPath, mainProject);
      if (summaryContext) {
        context += summaryContext + "\n";
      }
    }

    // Strategy 3: Complete project tree
    if (strategies.strategy3) {
      const treeString = this.generateTaskTreeString(tasks, taskId);
      if (treeString) {
        context += "Vollständige Projektstruktur:\n";
        context += treeString + "\n";
      }
    }

    // Add selected HTML if provided
    if (selectedHtml) {
      context += `Ausgewählter Inhalt zum Erweitern:\n${selectedHtml}\n\n`;
    }

    // Add current task details
    context += `Aktuelle Aufgabe: ${targetTask.title}\n`;
    if (targetTask.description) {
      context += `Aufgabenbeschreibung: ${targetTask.description}\n`;
    }
    if (targetTask.content) {
      context += `Aktueller Inhalt: ${targetTask.content}\n`;
    }

    return context;
  }

  /**
   * Finds the path from root to target task
   */
  findTaskPath(tasks: Task[], targetId: string): Task[] {
    const path: Task[] = [];
    
    const findPath = (taskList: Task[], currentPath: Task[]): boolean => {
      for (const task of taskList) {
        const newPath = [...currentPath, task];
        
        if (task.id === targetId) {
          path.push(...newPath);
          return true;
        }
        
        if (task.subtasks && task.subtasks.length > 0) {
          if (findPath(task.subtasks, newPath)) {
            return true;
          }
        }
      }
      return false;
    };

    findPath(tasks, []);
    return path;
  }

  /**
   * Generates a string representation of the complete task tree
   */
  generateTaskTreeString(tasks: Task[], targetId: string): string {
    const generateTree = (taskList: Task[], indent: string = ""): string => {
      let result = "";
      
      taskList.forEach((task, index) => {
        const isLast = index === taskList.length - 1;
        const connector = isLast ? "└── " : "├── ";
        const isTarget = task.id === targetId;
        const marker = isTarget ? " ← AKTUELLE AUFGABE" : "";
        
        result += `${indent}${connector}${task.title}${marker}\n`;
        
        if (task.description && task.description.trim()) {
          const descIndent = indent + (isLast ? "    " : "│   ");
          result += `${descIndent}Beschreibung: ${task.description}\n`;
        }
        
        if (task.subtasks && task.subtasks.length > 0) {
          const childIndent = indent + (isLast ? "    " : "│   ");
          result += generateTree(task.subtasks, childIndent);
        }
      });
      
      return result;
    };

    return generateTree(tasks);
  }

  /**
   * Builds intelligent summary context based on task path
   */
  private buildSummaryContext(path: Task[], mainProject: string): string {
    if (path.length === 0) return "";

    let context = "Aufgabenkontext-Zusammenfassung:\n";
    
    // Add project overview
    context += `Diese Aufgabe ist Teil des "${mainProject}" Projekts.\n`;
    
    // Add parent task context if exists
    if (path.length > 1) {
      const parentTask = path[path.length - 2];
      context += `Übergeordnete Aufgabe: "${parentTask.title}"\n`;
      if (parentTask.description) {
        context += `Übergeordnete Beschreibung: ${parentTask.description}\n`;
      }
    }
    
    // Add sibling tasks context
    const currentTask = path[path.length - 1];
    if (path.length > 1) {
      const parentTask = path[path.length - 2];
      const siblings = parentTask.subtasks?.filter(t => t.id !== currentTask.id) || [];
      if (siblings.length > 0) {
        context += `Verwandte Aufgaben in diesem Bereich:\n`;
        siblings.forEach(sibling => {
          context += `- ${sibling.title}\n`;
        });
      }
    }
    
    return context;
  }

  /**
   * Utility function to find a task by ID in the task tree
   */
  findTask(tasks: Task[], taskId: string): Task | null {
    for (const task of tasks) {
      if (task.id === taskId) {
        return task;
      }
      
      if (task.subtasks && task.subtasks.length > 0) {
        const found = this.findTask(task.subtasks, taskId);
        if (found) return found;
      }
    }
    
    return null;
  }

  /**
   * Private helper for internal use
   */
  private findTaskInternal(tasks: Task[], taskId: string): Task | null {
    return this.findTask(tasks, taskId);
  }

  /**
   * Generates context for task breakdown operations
   */
  buildBreakdownContext(task: Task, allTasks: Task[], mainProject: string): string {
    let context = `Projekt: ${mainProject}\n\n`;
    
    context += `Aufzuteilende Aufgabe: ${task.title}\n`;
    if (task.description) {
      context += `Beschreibung: ${task.description}\n`;
    }
    if (task.content) {
      context += `Aktueller Inhalt: ${task.content}\n`;
    }
    
    // Add context about related tasks
    const taskPath = this.findTaskPath(allTasks, task.id);
    if (taskPath.length > 1) {
      const parentTask = taskPath[taskPath.length - 2];
      context += `\nÜbergeordnete Aufgabe: ${parentTask.title}\n`;
      
      // Add sibling tasks for context
      const siblings = parentTask.subtasks?.filter(t => t.id !== task.id) || [];
      if (siblings.length > 0) {
        context += `Verwandte Aufgaben:\n`;
        siblings.forEach(sibling => {
          context += `- ${sibling.title}\n`;
        });
      }
    }
    
    return context;
  }

  /**
   * Generates context for Co-pilot question generation
   */
  buildCopilotContext(task: Task, allTasks: Task[], mainProject: string, mainProjectDescription?: string): string {
    let context = `Projekt: ${mainProject}\n`;
    
    if (mainProjectDescription) {
      context += `Projektbeschreibung: ${mainProjectDescription}\n`;
    }
    
    context += `\nAufgabe für Co-pilot: ${task.title}\n`;
    if (task.description) {
      context += `Aufgabenbeschreibung: ${task.description}\n`;
    }
    
    // Add task hierarchy context
    const taskPath = this.findTaskPath(allTasks, task.id);
    if (taskPath.length > 1) {
      context += `\nAufgabenhierarchie:\n`;
      taskPath.forEach((pathTask, index) => {
        const indent = "  ".repeat(index);
        context += `${indent}- ${pathTask.title}\n`;
      });
    }
    
    return context;
  }

  /**
   * Generates context for Autopilot mode with example prompts
   */
  buildAutopilotContext(task: Task, allTasks: Task[], mainProject: string, additionalPrompt?: string): string {
    const baseContext = this.buildContextForAI(task.id, {
      strategies: { strategy1: true, strategy2: true, strategy3: false },
      mainProject,
      mainProjectDescription: '',
      tasks: allTasks
    });
    
    let context = baseContext;
    
    if (additionalPrompt) {
      context += `\nZusätzliche Anweisungen: ${additionalPrompt}\n`;
    }
    
    return context;
  }

  /**
   * Utility to get task statistics for context
   */
  getTaskStatistics(tasks: Task[]): { total: number, completed: number, inProgress: number, todo: number } {
    let total = 0;
    let completed = 0;
    let inProgress = 0;
    let todo = 0;

    const countTasks = (taskList: Task[]) => {
      taskList.forEach(task => {
        total++;
        switch (task.status) {
          case 'Done':
            completed++;
            break;
          case 'In Progress':
            inProgress++;
            break;
          case 'To Do':
            todo++;
            break;
        }
        
        if (task.subtasks && task.subtasks.length > 0) {
          countTasks(task.subtasks);
        }
      });
    };

    countTasks(tasks);
    return { total, completed, inProgress, todo };
  }
}

// Export a singleton instance
export const contextBuilder = new ContextBuilder();