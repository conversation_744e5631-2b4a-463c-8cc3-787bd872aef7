/**
 * Lazy loading for AI modules to reduce initial bundle size
 */

import { performanceMonitor } from '@/lib/utils/performanceMonitor';

// Lazy load AI client modules
export const lazyLoadGeminiClient = async () => {
  const endTiming = performanceMonitor.startTiming('lazy-gemini-client');
  
  try {
    const module = await import('./gemini');
    return module;
  } finally {
    endTiming();
  }
};

export const lazyLoadOfflineAwareClient = async () => {
  const endTiming = performanceMonitor.startTiming('lazy-offline-aware-client');
  
  try {
    const module = await import('./offlineAwareClient');
    return module;
  } finally {
    endTiming();
  }
};

export const lazyLoadContextBuilder = async () => {
  const endTiming = performanceMonitor.startTiming('lazy-context-builder');
  
  try {
    const module = await import('./context');
    return module;
  } finally {
    endTiming();
  }
};

export const lazyLoadAIProviders = async () => {
  const endTiming = performanceMonitor.startTiming('lazy-ai-providers');
  
  try {
    const [baseProvider, geminiProvider] = await Promise.all([
      import('./providers/base'),
      import('./providers/gemini')
    ]);
    
    return {
      baseProvider,
      geminiProvider
    };
  } finally {
    endTiming();
  }
};

// Lazy load export modules
export const lazyLoadExportModules = async () => {
  const endTiming = performanceMonitor.startTiming('lazy-export-modules');
  
  try {
    const [pdfExport, csvExport, markdownExport, jsonExport, backupExport] = await Promise.all([
      import('../export/pdf'),
      import('../export/csv'),
      import('../export/markdown'),
      import('../export/json'),
      import('../export/backup')
    ]);
    
    return {
      pdfExport,
      csvExport,
      markdownExport,
      jsonExport,
      backupExport
    };
  } finally {
    endTiming();
  }
};

// Preload AI modules when user shows intent to use AI features
export const preloadAIModules = () => {
  // Preload when user hovers over AI buttons or focuses on task content
  lazyLoadGeminiClient();
  lazyLoadContextBuilder();
};

// Preload export modules when user opens export dialog
export const preloadExportModules = () => {
  lazyLoadExportModules();
};

// Smart preloading based on user behavior
export const initializeSmartPreloading = () => {
  if (typeof window === 'undefined') return;

  // Preload AI modules on first user interaction
  let aiPreloaded = false;
  const preloadAI = () => {
    if (!aiPreloaded) {
      aiPreloaded = true;
      preloadAIModules();
    }
  };

  // Preload export modules when user scrolls to bottom (likely to export)
  let exportPreloaded = false;
  const preloadExport = () => {
    if (!exportPreloaded && window.scrollY > document.body.scrollHeight * 0.7) {
      exportPreloaded = true;
      preloadExportModules();
    }
  };

  // Add event listeners
  document.addEventListener('click', preloadAI, { once: true });
  document.addEventListener('keydown', preloadAI, { once: true });
  document.addEventListener('scroll', preloadExport, { passive: true });

  // Cleanup function
  return () => {
    document.removeEventListener('click', preloadAI);
    document.removeEventListener('keydown', preloadAI);
    document.removeEventListener('scroll', preloadExport);
  };
};