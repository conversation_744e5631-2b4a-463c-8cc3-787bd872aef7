import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AIErrorType, type AIError } from '@/lib/utils/errorHandler';
import { AIPrompts, PromptConfig, ResponseSchemas, type PromptParams } from './prompts';

interface GeminiAPIOptions {
    isJson?: boolean;
    isQuestionGeneration?: boolean;
    maxRetries?: number;
    timeout?: number;
}

interface GeminiResponse {
    content: string;
    success: boolean;
    error?: string;
    userFriendlyError?: string;
}

interface GeminiTaskResponse {
    tasks: Array<{
        title: string;
        description: string;
    }>;
}

interface GeminiQuestionResponse {
    questions: string[];
}

export class GeminiClient {
    private apiKey: string;
    private baseUrl: string = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';
    private defaultTimeout: number = 30000; // 30 seconds
    private defaultMaxRetries: number = 3;

    constructor() {
        this.apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || process.env.GEMINI_API_KEY || '';
        console.log('Gemini API Key loaded:', this.apiKey ? 'Yes (length: ' + this.apiKey.length + ')' : 'No');
        console.log('Environment check:', {
            NEXT_PUBLIC_GEMINI_API_KEY: process.env.NEXT_PUBLIC_GEMINI_API_KEY ? 'Set' : 'Not set',
            GEMINI_API_KEY: process.env.GEMINI_API_KEY ? 'Set' : 'Not set'
        });
        if (!this.apiKey) {
            console.error('Gemini API key not found. AI features will not work.');
        } else {
            console.log('API Key first 10 chars:', this.apiKey.substring(0, 10) + '...');
        }
    }

    private async makeRequest(prompt: string, options: GeminiAPIOptions = {}): Promise<GeminiResponse> {
        const maxRetries = options.maxRetries ?? this.defaultMaxRetries;
        const timeout = options.timeout ?? this.defaultTimeout;

        if (!this.apiKey) {
            console.error('Gemini API key not found');
            return {
                content: '',
                success: false,
                error: 'API key missing',
                userFriendlyError: 'KI-Service nicht verfügbar: API-Schlüssel fehlt. Bitte konfigurieren Sie einen gültigen Gemini API-Schlüssel.'
            };
        }

        try {
            return await AIErrorHandler.withRetry(
                async () => {
                    const generationConfig: any = {
                        temperature: PromptConfig.defaults.temperature,
                        topK: PromptConfig.defaults.topK,
                        topP: PromptConfig.defaults.topP,
                        maxOutputTokens: PromptConfig.defaults.maxOutputTokens,
                    };

                    if (options.isJson) {
                        generationConfig.responseMimeType = "application/json";
                        // Add JSON schema instructions to prompt for better JSON responses
                        if (options.isQuestionGeneration) {
                            generationConfig.responseSchema = ResponseSchemas.questions;
                        } else {
                            generationConfig.responseSchema = ResponseSchemas.tasks;
                        }
                    }

                    const requestBody = {
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }],
                        generationConfig
                    };

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), timeout);

                    console.log('Making fetch request to:', `${this.baseUrl}?key=${this.apiKey.substring(0, 10)}...`);
                    console.log('Request body:', JSON.stringify(requestBody, null, 2));

                    const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(requestBody),
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);
                    console.log('Response status:', response.status, response.statusText);

                    if (!response.ok) {
                        const errorData = await response.json().catch(() => ({}));
                        const errorMessage = `API request failed: ${response.status} ${response.statusText}. ${errorData.error?.message || ''}`;

                        // Create specific error based on status code
                        if (response.status === 429) {
                            throw new Error(`Rate limit exceeded: ${response.status}`);
                        } else if (response.status === 401) {
                            throw new Error(`Authentication failed: ${response.status}`);
                        } else if (response.status === 403) {
                            throw new Error(`Quota exceeded: ${response.status}`);
                        } else if (response.status >= 500) {
                            throw new Error(`Server error: ${response.status}`);
                        } else {
                            throw new Error(errorMessage);
                        }
                    }

                    const data = await response.json();
                    console.log('Response data:', JSON.stringify(data, null, 2));

                    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                        console.error('Invalid response format:', data);
                        throw new Error('Invalid response format from Gemini API');
                    }

                    const content = data.candidates[0].content.parts[0].text;
                    const finishReason = data.candidates[0].finishReason || data.candidates[0].finish_reason || 'unknown';
                    const usage = data.usageMetadata || data.usage || {};
                    const promptTokens = usage.promptTokenCount || usage.prompt_tokens || 0;
                    const candidateTokens = usage.candidatesTokenCount || usage.completion_tokens || 0;
                    const totalTokens = usage.totalTokenCount || usage.total_tokens || (promptTokens + candidateTokens);
                    console.log('Extracted content:', content);
                    console.log('Finish reason:', finishReason, 'Tokens:', { promptTokens, candidateTokens, totalTokens });

                    return Object.assign({
                        content,
                        success: true
                    }, { _diag: { finishReason, promptTokens, candidateTokens, totalTokens } });
                },
                {
                    maxRetries: 2, // Reduce retries to avoid hitting rate limit more
                    baseDelay: 5000, // Start with 5 seconds
                    maxDelay: 60000, // Max 60 seconds
                    backoffMultiplier: 3 // More aggressive backoff
                },
                (attempt, error) => {
                    console.warn(`Gemini API retry attempt ${attempt}/${maxRetries}:`, error.userFriendlyMessage);
                }
            );
        } catch (error) {
            const aiError = error instanceof Object && 'type' in error
                ? error as AIError
                : AIErrorHandler.categorizeError(error);

            AIErrorHandler.logError(aiError, 'GeminiClient.makeRequest');

            return {
                content: '',
                success: false,
                error: aiError.message,
                userFriendlyError: aiError.userFriendlyMessage
            };
        }
    }

    async generateContent(prompt: string, options?: GeminiAPIOptions): Promise<GeminiResponse> {
        return this.makeRequest(prompt, options);
    }

    async generateTasks(projectGoal: string, description?: string): Promise<{ tasks: any[], error?: string }> {
        const prompt = AIPrompts.generateTasks({
            projectGoal,
            description,
            maxEntries: PromptConfig.maxEntries.mainTasks
        });

        try {
            console.log('Making request to Gemini API for generateTasks...');
            const response = await this.makeRequest(prompt, { isJson: true });
            console.log('Gemini API response:', response);

            if (!response.success) {
                console.error('Gemini API request failed:', response.error);
                const fallback = AIErrorHandler.createFallbackResponse<{ tasks: any[] }>('tasks');
                return {
                    ...fallback,
                    error: response.userFriendlyError || response.error
                };
            }

            const parsed: GeminiTaskResponse = JSON.parse(response.content);

            if (!parsed.tasks || !Array.isArray(parsed.tasks)) {
                throw new Error('Invalid tasks array in response');
            }

            return {
                tasks: parsed.tasks.map((task, index) => ({
                    id: (Date.now() + index).toString(),
                    title: task.title || `Aufgabe ${index + 1}`,
                    description: task.description || "Keine Beschreibung verfügbar",
                    content: "",
                    status: "To Do" as const,
                    assignees: [],
                    subtasks: []
                }))
            };
        } catch (error) {
            const aiError = AIErrorHandler.categorizeError(error);
            AIErrorHandler.logError(aiError, 'GeminiClient.generateTasks');

            const fallback = AIErrorHandler.createFallbackResponse<{ tasks: any[] }>('tasks');
            return {
                ...fallback,
                error: aiError.userFriendlyMessage
            };
        }
    }

    async generateSubtasks(taskTitle: string, taskDescription: string, context?: string): Promise<{ tasks: any[], error?: string }> {
        const prompt = AIPrompts.generateSubtasks({
            taskTitle,
            taskDescription,
            context,
            maxEntries: PromptConfig.maxEntries.subtasks
        });

        try {
            const response = await this.makeRequest(prompt, { isJson: true });

            if (!response.success) {
                const fallback = AIErrorHandler.createFallbackResponse<{ tasks: any[] }>('tasks');
                return {
                    tasks: fallback.tasks.slice(0, 1), // Return only one fallback subtask
                    error: response.userFriendlyError || response.error
                };
            }

            const parsed: GeminiTaskResponse = JSON.parse(response.content);

            if (!parsed.tasks || !Array.isArray(parsed.tasks)) {
                throw new Error('Invalid tasks array in response');
            }

            return {
                tasks: parsed.tasks.map((task, index) => ({
                    id: (Date.now() + index).toString(),
                    title: task.title || `Unteraufgabe ${index + 1}`,
                    description: task.description || "Keine Beschreibung verfügbar",
                    content: "",
                    status: "To Do" as const,
                    assignees: [],
                    subtasks: []
                }))
            };
        } catch (error) {
            const aiError = AIErrorHandler.categorizeError(error);
            AIErrorHandler.logError(aiError, 'GeminiClient.generateSubtasks');

            return {
                tasks: [],
                error: aiError.userFriendlyMessage
            };
        }
    }

    async generateQuestions(context: string): Promise<{ questions: string[], error?: string }> {
        const prompt = AIPrompts.generateQuestions({ context });

        try {
            const response = await this.makeRequest(prompt, { isJson: true, isQuestionGeneration: true });

            if (!response.success) {
                const fallback = AIErrorHandler.createFallbackResponse<{ questions: string[] }>('questions');
                return {
                    ...fallback,
                    error: response.userFriendlyError || response.error
                };
            }

            const parsed: GeminiQuestionResponse = JSON.parse(response.content);

            if (!parsed.questions || !Array.isArray(parsed.questions)) {
                throw new Error('Invalid questions array in response');
            }

            return {
                questions: parsed.questions.filter(q => q && typeof q === 'string')
            };
        } catch (error) {
            const aiError = AIErrorHandler.categorizeError(error);
            AIErrorHandler.logError(aiError, 'GeminiClient.generateQuestions');

            const fallback = AIErrorHandler.createFallbackResponse<{ questions: string[] }>('questions');
            return {
                ...fallback,
                error: aiError.userFriendlyMessage
            };
        }
    }

    async elaborateContent(content: string, context: string, selectedText?: string): Promise<{ content: string, error?: string }> {
        const prompt = AIPrompts.elaborateContent({
            content,
            context,
            selectedText
        });

        try {
            const response = await this.makeRequest(prompt);

            if (!response.success) {
                const fallback = AIErrorHandler.createFallbackResponse<{ content: string }>('elaboration', { originalText: selectedText || content });
                return {
                    ...fallback,
                    error: response.userFriendlyError || response.error
                };
            }

            return {
                content: response.content.trim()
            };
        } catch (error) {
            const aiError = AIErrorHandler.categorizeError(error);
            AIErrorHandler.logError(aiError, 'GeminiClient.elaborateContent');

            return {
                content: selectedText || content, // Return original on failure
                error: aiError.userFriendlyMessage
            };
        }
    }

    async generateTaskContent(taskTitle: string, taskDescription: string, context: string, additionalPrompt?: string): Promise<{ content: string, error?: string }> {
        const prompt = AIPrompts.generateTaskContent({
            taskTitle,
            taskDescription,
            context,
            additionalPrompt
        });

        try {
            const response = await this.makeRequest(prompt);

            if (!response.success) {
                const fallback = AIErrorHandler.createFallbackResponse<{ content: string }>('content');
                return {
                    content: `<div class="error-content">
            <p><strong>Inhaltserstellung fehlgeschlagen:</strong> ${response.userFriendlyError || response.error}</p>
            ${fallback.content}
          </div>`,
                    error: response.userFriendlyError || response.error
                };
            }

            return {
                content: response.content
            };
        } catch (error) {
            const aiError = AIErrorHandler.categorizeError(error);
            AIErrorHandler.logError(aiError, 'GeminiClient.generateTaskContent');

            const fallback = AIErrorHandler.createFallbackResponse<{ content: string }>('content');
            return {
                content: `<div class="error-content">
          <p><strong>Inhaltserstellung fehlgeschlagen:</strong> ${aiError.userFriendlyMessage}</p>
          ${fallback.content}
        </div>`,
                error: aiError.userFriendlyMessage
            };
        }
    }
}

// Export a singleton instance
export const geminiClient = new GeminiClient();

// Re-export prompt utilities for convenience
export { AIPrompts, PromptConfig, ResponseSchemas } from './prompts';