/**
 * AI Prompt Templates für den KI Projekt-Planer
 * 
 * Die<PERSON>i enthält alle Prompt-Templates, die von der Gemini AI verwendet werden.
 * Jeder Prompt ist als Funktion implementiert, um dynamische Parameter zu unterstützen.
 */

export interface PromptParams {
    projectGoal?: string;
    description?: string;
    taskTitle?: string;
    taskDescription?: string;
    context?: string;
    content?: string;
    selectedText?: string;
    additionalPrompt?: string;
    maxEntries?: number;
}

/**
 * Prompt-Templates für verschiedene AI-Operationen
 */
export class AIPrompts {

    /**
     * Generiert einen Prompt für die Erstellung von Hauptaufgaben eines Projekts
     */
    static generateTasks(params: PromptParams): string {
        const { projectGoal, description, maxEntries = 5 } = params;

        return `Erstelle eine detaillierte Liste von Hauptaufgaben für das folgende Projekt:

Projektziel: ${projectGoal}
${description ? `Beschreibung: ${description}` : ''}

Zerlege das Projekt in eine umfassende Liste der elementarsten Hauptaufgaben. Die Liste sollte so vollständig wie möglich sein, aber maximal ${maxEntries} Einträge haben. Jede Aufgabe sollte einen präzisen Titel und eine kurze, aber aussagekräftige Beschreibung haben.

Antworte als JSON-Objekt mit dieser Struktur:
{
  "tasks": [
    {
      "title": "Aufgabentitel hier",
      "description": "Detaillierte Beschreibung dessen, was getan werden muss"
    }
  ]
}`;
    }

    /**
     * Generiert einen Prompt für die Erstellung von Unteraufgaben
     */
    static generateSubtasks(params: PromptParams): string {
        const { taskTitle, taskDescription, context, maxEntries = 5 } = params;

        return `Zerlege die folgende Aufgabe in eine detaillierte Liste der elementarsten Teilschritte:

Aufgabe: ${taskTitle}
Beschreibung: ${taskDescription}
${context ? `Kontext: ${context}` : ''}

Erstelle eine umfassende Liste spezifischer, umsetzbarer Unteraufgaben. Die Liste sollte so vollständig wie möglich sein, aber maximal ${maxEntries} Einträge haben. Jede Unteraufgabe sollte einen präzisen Titel und eine kurze Beschreibung haben.

Antworte als JSON-Objekt mit dieser Struktur:
{
  "tasks": [
    {
      "title": "Unteraufgaben-Titel hier",
      "description": "Detaillierte Beschreibung der Unteraufgabe"
    }
  ]
}`;
    }

    /**
     * Generiert einen Prompt für die Erstellung von Rückfragen
     */
    static generateQuestions(params: PromptParams): string {
        const { context } = params;

        return `Basierend auf dem folgenden Aufgabenkontext, erstelle 3-5 hilfreiche Fragen, die dabei helfen würden, Anforderungen und Ansatz zu klären:

Kontext: ${context}

Bitte erstelle Fragen, die dabei helfen würden zu verstehen:
- Spezifische Anforderungen
- Technische Einschränkungen
- Benutzererwartungen
- Erfolgskriterien

Antworte als JSON-Objekt mit dieser Struktur:
{
  "questions": [
    "Frage 1 hier?",
    "Frage 2 hier?",
    "Frage 3 hier?"
  ]
}`;
    }

    /**
     * Generiert einen Prompt für die Verbesserung von Inhalten
     */
    static elaborateContent(params: PromptParams): string {
        const { content, context, selectedText } = params;

        const htmlFormatInstructions = `
WICHTIGE FORMATIERUNGSANWEISUNGEN:
- Verwende moderne HTML-Tags mit Tailwind CSS-Klassen für schöne Darstellung
- Überschriften: <h2 class="text-xl font-bold text-blue-400 mb-3 mt-6">Titel</h2>
- Unterüberschriften: <h3 class="text-lg font-semibold text-blue-300 mb-2 mt-4">Untertitel</h3>
- Absätze: <p class="text-slate-300 mb-4 leading-relaxed">Text hier</p>
- Listen: <ul class="list-disc list-inside space-y-2 mb-4 pl-4"><li class="text-slate-300">Punkt</li></ul>
- Nummerierte Listen: <ol class="list-decimal list-inside space-y-2 mb-4 pl-4"><li class="text-slate-300">Punkt</li></ol>
- Wichtige Hinweise: <div class="bg-slate-800/30 border-l-4 border-blue-400 p-4 mb-4 rounded-r"><p class="text-blue-300">Hinweis</p></div>
- Code: <code class="bg-slate-700 text-amber-300 px-2 py-1 rounded text-sm">code</code>
- Zitate: <blockquote class="border-l-4 border-slate-500 pl-4 italic text-slate-400 mb-4 bg-slate-800/30 py-2 rounded-r">Zitat</blockquote>
- Trennlinien: <hr class="border-slate-600 my-6">
- Hervorhebungen: <strong class="font-semibold text-slate-200">wichtig</strong>

Gib NUR den HTML-Inhalt zurück, ohne <html>, <head> oder <body> Tags.`;

        if (selectedText) {
            return `Bitte erweitere und verbessere den folgenden ausgewählten Text: "${selectedText}"
         
Kontext: ${context}
Vollständiger Inhalt: ${content}
         
${htmlFormatInstructions}
         
Erstelle eine verbesserte Version nur des ausgewählten Textes, die detaillierter, klarer und hilfreicher ist.`;
        }

        return `Bitte erweitere und verbessere den folgenden Inhalt:

${content}
         
Kontext: ${context}
         
${htmlFormatInstructions}
         
Erstelle eine verbesserte Version, die detaillierter, klarer und hilfreicher ist, während die gleiche Struktur beibehalten wird.`;
    }

    /**
     * Generiert einen Prompt für die Erstellung von Aufgaben-Inhalten
     */
    static generateTaskContent(params: PromptParams): string {
        const { taskTitle, taskDescription, context, additionalPrompt } = params;

        return `Führe die folgende Aufgabe direkt und vollständig aus. Erstelle den Inhalt, die Antwort oder die Lösung für die gegebene Aufgabe, anstatt sie nur neu zu formulieren oder zu beschreiben.

Aufgabe: ${taskTitle}
Beschreibung: ${taskDescription}
Kontext: ${context}
${additionalPrompt ? `Zusätzliche Anforderungen: ${additionalPrompt}` : ''}

WICHTIGE FORMATIERUNGSANWEISUNGEN:
- Verwende moderne HTML-Tags mit Tailwind CSS-Klassen für schöne Darstellung
- Überschriften: <h2 class="text-xl font-bold text-blue-400 mb-3 mt-6">Titel</h2>
- Unterüberschriften: <h3 class="text-lg font-semibold text-blue-300 mb-2 mt-4">Untertitel</h3>
- Absätze: <p class="text-slate-300 mb-4 leading-relaxed">Text hier</p>
- Schritte: <ol class="list-decimal list-inside space-y-3 mb-6 pl-4"><li class="text-slate-300"><strong class="font-semibold text-slate-200">Schritt:</strong> Beschreibung</li></ol>
- Listen: <ul class="list-disc list-inside space-y-2 mb-4 pl-4"><li class="text-slate-300">Punkt</li></ul>
- Wichtige Hinweise: <div class="bg-slate-800/30 border-l-4 border-amber-400 p-4 mb-4 rounded-r"><p class="text-amber-300"><strong>Wichtig:</strong> Hinweis</p></div>
- Tipps: <div class="bg-slate-800/30 border-l-4 border-green-400 p-4 mb-4 rounded-r"><p class="text-green-300"><strong>💡 Tipp:</strong> Tipp-Text</p></div>
- Code: <code class="bg-slate-700 text-amber-300 px-2 py-1 rounded text-sm">code</code>
- Codeblöcke: <pre class="bg-slate-800 p-4 rounded-lg mb-4 overflow-x-auto"><code class="text-slate-300 text-sm">code block</code></pre>
- Tabellen: <div class="overflow-x-auto mb-4"><table class="min-w-full border border-slate-600 bg-slate-800/50"><thead class="bg-slate-700"><tr><th class="px-4 py-2 text-left text-slate-200 font-semibold">Header</th></tr></thead><tbody><tr><td class="px-4 py-2 border-t border-slate-600 text-slate-300">Data</td></tr></tbody></table></div>

Gib NUR den HTML-Inhalt zurück, ohne <html>, <head> oder <body> Tags.
Strukturiere den Inhalt logisch mit klaren Abschnitten und verwende die oben genannten Styling-Klassen.`;
    }
}

/**
 * Konfiguration für verschiedene Prompt-Typen
 */
export const PromptConfig = {
    maxEntries: {
        mainTasks: 5,
        subtasks: 30,
        questions: 5
    },

    defaults: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192
    }
} as const;

/**
 * JSON-Schemas für strukturierte Antworten
 */
export const ResponseSchemas = {
    tasks: {
        type: "object",
        properties: {
            tasks: {
                type: "array",
                items: {
                    type: "object",
                    properties: {
                        title: { type: "string" },
                        description: { type: "string" }
                    },
                    required: ["title", "description"]
                }
            }
        },
        required: ["tasks"]
    },

    questions: {
        type: "object",
        properties: {
            questions: {
                type: "array",
                items: { type: "string" }
            }
        },
        required: ["questions"]
    }
} as const;