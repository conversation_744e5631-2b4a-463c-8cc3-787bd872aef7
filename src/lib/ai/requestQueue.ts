export interface QueuedAIRequest {
  id: string;
  type: 'generateTasks' | 'generateSubtasks' | 'generateTaskContent' | 'elaborateContent';
  params: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  taskId?: string;
  taskTitle?: string;
}

export interface QueuedRequestResult {
  success: boolean;
  data?: any;
  error?: string;
}

export class AIRequestQueue {
  private queue: QueuedAIRequest[] = [];
  private processing: boolean = false;
  private listeners: Array<(request: QueuedAIRequest, result: QueuedRequestResult) => void> = [];
  private storageKey = 'ai-request-queue';

  constructor() {
    this.loadFromStorage();
    this.setupOnlineListener();
  }

  private setupOnlineListener(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('online', () => {
      console.log('Connection restored, processing queued AI requests...');
      this.processQueue();
    });
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        this.queue = JSON.parse(stored);
        console.log(`Loaded ${this.queue.length} queued AI requests from storage`);
      }
    } catch (error) {
      console.error('Failed to load AI request queue from storage:', error);
      this.queue = [];
    }
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.queue));
    } catch (error) {
      console.error('Failed to save AI request queue to storage:', error);
    }
  }

  public addRequest(request: Omit<QueuedAIRequest, 'id' | 'timestamp' | 'retryCount'>): string {
    const queuedRequest: QueuedAIRequest = {
      ...request,
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      retryCount: 0
    };

    this.queue.push(queuedRequest);
    this.saveToStorage();

    console.log(`Added AI request to queue: ${request.type} for task ${request.taskTitle || 'unknown'}`);
    
    return queuedRequest.id;
  }

  public removeRequest(id: string): void {
    this.queue = this.queue.filter(req => req.id !== id);
    this.saveToStorage();
  }

  public getQueueLength(): number {
    return this.queue.length;
  }

  public getQueuedRequests(): QueuedAIRequest[] {
    return [...this.queue];
  }

  public clearQueue(): void {
    this.queue = [];
    this.saveToStorage();
  }

  public onRequestCompleted(callback: (request: QueuedAIRequest, result: QueuedRequestResult) => void): () => void {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  private notifyListeners(request: QueuedAIRequest, result: QueuedRequestResult): void {
    this.listeners.forEach(listener => {
      try {
        listener(request, result);
      } catch (error) {
        console.error('Error in AI request queue listener:', error);
      }
    });
  }

  public async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    // Check if we're actually online
    if (!navigator.onLine) {
      console.log('Still offline, skipping queue processing');
      return;
    }

    this.processing = true;
    console.log(`Processing ${this.queue.length} queued AI requests...`);

    // Process requests one by one to avoid overwhelming the API
    while (this.queue.length > 0) {
      const request = this.queue[0];
      
      try {
        const result = await this.executeRequest(request);
        
        if (result.success) {
          console.log(`Successfully processed queued request: ${request.type}`);
          this.notifyListeners(request, result);
          this.removeRequest(request.id);
        } else {
          // Handle failure
          request.retryCount++;
          
          if (request.retryCount >= request.maxRetries) {
            console.error(`Max retries exceeded for request: ${request.type}`);
            this.notifyListeners(request, result);
            this.removeRequest(request.id);
          } else {
            console.warn(`Request failed, will retry: ${request.type} (${request.retryCount}/${request.maxRetries})`);
            // Move to end of queue for retry
            this.queue.push(this.queue.shift()!);
            this.saveToStorage();
          }
        }
      } catch (error) {
        console.error('Error processing queued request:', error);
        request.retryCount++;
        
        if (request.retryCount >= request.maxRetries) {
          this.notifyListeners(request, { 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
          this.removeRequest(request.id);
        } else {
          // Move to end of queue for retry
          this.queue.push(this.queue.shift()!);
          this.saveToStorage();
        }
      }

      // Add delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.processing = false;
    console.log('Finished processing AI request queue');
  }

  private async executeRequest(request: QueuedAIRequest): Promise<QueuedRequestResult> {
    // Import the AI client dynamically to avoid circular dependencies
    const { geminiClient } = await import('./gemini');

    switch (request.type) {
      case 'generateTasks':
        const tasksResult = await geminiClient.generateTasks(
          request.params.projectGoal,
          request.params.description
        );
        return {
          success: !tasksResult.error,
          data: tasksResult,
          error: tasksResult.error
        };

      case 'generateSubtasks':
        const subtasksResult = await geminiClient.generateSubtasks(
          request.params.taskTitle,
          request.params.taskDescription,
          request.params.context
        );
        return {
          success: !subtasksResult.error,
          data: subtasksResult,
          error: subtasksResult.error
        };

      case 'generateTaskContent':
        const contentResult = await geminiClient.generateTaskContent(
          request.params.taskTitle,
          request.params.taskDescription,
          request.params.context,
          request.params.additionalPrompt
        );
        return {
          success: !contentResult.error,
          data: contentResult,
          error: contentResult.error
        };

      case 'elaborateContent':
        const elaborateResult = await geminiClient.elaborateContent(
          request.params.content,
          request.params.context,
          request.params.selectedText
        );
        return {
          success: !elaborateResult.error,
          data: elaborateResult,
          error: elaborateResult.error
        };

      default:
        throw new Error(`Unknown request type: ${request.type}`);
    }
  }
}

// Export singleton instance
export const aiRequestQueue = new AIRequestQueue();