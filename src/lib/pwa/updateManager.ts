/**
 * PWA Update Manager
 * Handles app updates, version management, and user notifications
 */

export interface UpdateInfo {
  version: string;
  releaseDate: Date;
  releaseNotes: string[];
  size?: string;
  critical?: boolean;
}

export interface UpdateManagerConfig {
  checkInterval: number; // in milliseconds
  maxRetries: number;
  retryDelay: number;
  autoUpdate: boolean;
}

export class PWAUpdateManager {
  private registration: ServiceWorkerRegistration | null = null;
  private config: UpdateManagerConfig;
  private updateCheckTimer: NodeJS.Timeout | null = null;
  private listeners: Map<string, Function[]> = new Map();

  constructor(config: Partial<UpdateManagerConfig> = {}) {
    this.config = {
      checkInterval: 30 * 60 * 1000, // 30 minutes
      maxRetries: 3,
      retryDelay: 5000, // 5 seconds
      autoUpdate: false,
      ...config
    };
  }

  /**
   * Initialize the update manager
   */
  async initialize(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      throw new Error('Service Worker not supported');
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        updateViaCache: 'none'
      });

      this.setupEventListeners();
      this.startPeriodicUpdateChecks();
      
      // Check for updates immediately
      await this.checkForUpdates();

      console.log('PWA Update Manager initialized');
    } catch (error) {
      console.error('Failed to initialize PWA Update Manager:', error);
      throw error;
    }
  }

  /**
   * Set up event listeners for service worker events
   */
  private setupEventListeners(): void {
    if (!this.registration) return;

    // Listen for new service worker installations
    this.registration.addEventListener('updatefound', () => {
      const newWorker = this.registration!.installing;
      if (newWorker) {
        console.log('New service worker found');
        
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            this.emit('updateAvailable', {
              version: 'Latest',
              releaseDate: new Date(),
              releaseNotes: [
                'Verbesserte Performance und Stabilität',
                'Neue Features und Bugfixes',
                'Optimierte Offline-Funktionalität'
              ]
            });
          }
        });
      }
    });

    // Listen for service worker messages
    navigator.serviceWorker.addEventListener('message', (event) => {
      const { data } = event;
      
      switch (data.type) {
        case 'SW_ACTIVATED':
          this.emit('updateApplied', { version: data.version });
          break;
        case 'SW_UPDATE_APPLYING':
          this.emit('updateApplying', { version: data.version });
          break;
        case 'SW_INSTALLING':
          this.emit('updateInstalling', { version: data.version });
          break;
      }
    });

    // Listen for controller changes (update applied)
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      this.emit('controllerChanged');
    });

    // Listen for page visibility changes to check for updates
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.checkForUpdates();
      }
    });
  }

  /**
   * Start periodic update checks
   */
  private startPeriodicUpdateChecks(): void {
    if (this.updateCheckTimer) {
      clearInterval(this.updateCheckTimer);
    }

    this.updateCheckTimer = setInterval(() => {
      this.checkForUpdates();
    }, this.config.checkInterval);
  }

  /**
   * Check for available updates
   */
  async checkForUpdates(): Promise<boolean> {
    if (!this.registration) {
      console.warn('Service worker not registered');
      return false;
    }

    try {
      await this.registration.update();
      
      // Check if there's a waiting service worker
      if (this.registration.waiting) {
        this.emit('updateAvailable', {
          version: 'Latest',
          releaseDate: new Date(),
          releaseNotes: [
            'Verbesserte Performance und Stabilität',
            'Neue Features und Bugfixes',
            'Optimierte Offline-Funktionalität'
          ]
        });
        return true;
      }

      return false;
    } catch (error) {
      console.error('Update check failed:', error);
      this.emit('updateCheckFailed', { error });
      return false;
    }
  }

  /**
   * Apply available update
   */
  async applyUpdate(): Promise<void> {
    if (!this.registration?.waiting) {
      throw new Error('No update available');
    }

    return new Promise((resolve, reject) => {
      const handleControllerChange = () => {
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
        resolve();
      };

      navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);

      // Tell the waiting service worker to skip waiting
      this.registration!.waiting!.postMessage({ type: 'SKIP_WAITING' });

      // Set a timeout in case the update fails
      setTimeout(() => {
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
        reject(new Error('Update timeout'));
      }, 30000); // 30 seconds timeout
    });
  }

  /**
   * Get current version information
   */
  async getVersionInfo(): Promise<any> {
    if (!this.registration) {
      return null;
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      
      channel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      this.registration!.active?.postMessage(
        { type: 'GET_VERSION' },
        [channel.port2]
      );

      // Timeout after 5 seconds
      setTimeout(() => resolve(null), 5000);
    });
  }

  /**
   * Get cache information for debugging
   */
  async getCacheInfo(): Promise<any> {
    if (!this.registration) {
      return null;
    }

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      
      channel.port1.onmessage = (event) => {
        if (event.data.type === 'CACHE_INFO') {
          resolve(event.data.data);
        }
      };

      this.registration!.active?.postMessage(
        { type: 'GET_CACHE_INFO' },
        [channel.port2]
      );

      // Timeout after 5 seconds
      setTimeout(() => resolve(null), 5000);
    });
  }

  /**
   * Check if update is available
   */
  isUpdateAvailable(): boolean {
    return !!this.registration?.waiting;
  }

  /**
   * Get estimated update size
   */
  getUpdateSize(): string {
    // In a real implementation, this could be calculated from cache differences
    return '< 1 MB';
  }

  /**
   * Event listener management
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.updateCheckTimer) {
      clearInterval(this.updateCheckTimer);
      this.updateCheckTimer = null;
    }
    this.listeners.clear();
  }
}

// Singleton instance
export const updateManager = new PWAUpdateManager();

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  updateManager.initialize().catch(console.error);
}