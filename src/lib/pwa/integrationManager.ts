/**
 * PWA Integration Manager
 * 
 * This module provides comprehensive integration between all PWA components
 * and the existing task management application. It ensures seamless transitions
 * between online and offline modes and coordinates all PWA functionality.
 */

import { serviceWorkerManager } from './serviceWorker';
import { updateManager } from './updateManager';
import { localStorageManager } from '../storage/localStorageManager';
import { indexedDBManager } from '../storage/indexeddb';
import { offlineAwareAIClient } from '../ai/offlineAwareClient';
import type { Project, Task } from '../types';

export interface PWAIntegrationState {
  isOnline: boolean;
  isInstalled: boolean;
  updateAvailable: boolean;
  serviceWorkerReady: boolean;
  dataSync: {
    lastSync: Date | null;
    pendingChanges: number;
    syncInProgress: boolean;
  };
  performance: {
    loadTime: number;
    cacheHitRate: number;
    offlineCapability: boolean;
  };
}

export interface PWAIntegrationConfig {
  enableOfflineSync: boolean;
  enablePerformanceMonitoring: boolean;
  enableAutoUpdate: boolean;
  enableDataBackup: boolean;
  syncInterval: number; // in milliseconds
}

class PWAIntegrationManager {
  private state: PWAIntegrationState;
  private config: PWAIntegrationConfig;
  private listeners: Set<(state: PWAIntegrationState) => void> = new Set();
  private syncTimer: NodeJS.Timeout | null = null;
  private performanceObserver: PerformanceObserver | null = null;

  constructor() {
    this.state = {
      isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
      isInstalled: false,
      updateAvailable: false,
      serviceWorkerReady: false,
      dataSync: {
        lastSync: null,
        pendingChanges: 0,
        syncInProgress: false
      },
      performance: {
        loadTime: 0,
        cacheHitRate: 0,
        offlineCapability: false
      }
    };

    this.config = {
      enableOfflineSync: true,
      enablePerformanceMonitoring: true,
      enableAutoUpdate: false,
      enableDataBackup: true,
      syncInterval: 30000 // 30 seconds
    };
  }

  /**
   * Initialize PWA integration with the existing application
   */
  async initialize(): Promise<void> {
    console.log('Initializing PWA Integration Manager...');

    try {
      // Initialize storage systems
      await this.initializeStorage();

      // Initialize service worker
      await this.initializeServiceWorker();

      // Initialize network monitoring
      this.initializeNetworkMonitoring();

      // Initialize performance monitoring
      if (this.config.enablePerformanceMonitoring) {
        this.initializePerformanceMonitoring();
      }

      // Initialize data synchronization
      if (this.config.enableOfflineSync) {
        this.initializeDataSync();
      }

      // Initialize update management
      this.initializeUpdateManagement();

      // Check installation status
      await this.checkInstallationStatus();

      console.log('PWA Integration Manager initialized successfully');
      this.notifyStateChange();

    } catch (error) {
      console.error('Failed to initialize PWA Integration Manager:', error);
      throw error;
    }
  }

  /**
   * Initialize storage systems (IndexedDB and localStorage)
   */
  private async initializeStorage(): Promise<void> {
    try {
      // Initialize IndexedDB
      await indexedDBManager.initDatabase();
      
      // Migrate data from localStorage to IndexedDB if needed
      try {
        const hasLegacyData = await localStorageManager.hasLegacyData();
        if (hasLegacyData) {
          console.log('Migrating legacy data to IndexedDB...');
          await this.migrateLegacyData();
        }
      } catch (error) {
        console.warn('Legacy data migration check failed, skipping:', error);
        // Continue without migration - not critical for app functionality
      }

      console.log('Storage systems initialized');
    } catch (error) {
      console.error('Storage initialization failed:', error);
      throw error;
    }
  }

  /**
   * Initialize service worker and handle lifecycle events
   */
  private async initializeServiceWorker(): Promise<void> {
    try {
      const registration = await serviceWorkerManager.register();
      
      if (registration) {
        this.state.serviceWorkerReady = true;
        this.state.performance.offlineCapability = true;

        // Listen for service worker updates
        serviceWorkerManager.onUpdateAvailable(() => {
          this.state.updateAvailable = true;
          this.notifyStateChange();
        });

        // Listen for service worker messages
        this.setupServiceWorkerMessageHandling();
      }

      console.log('Service worker initialized');
    } catch (error) {
      console.error('Service worker initialization failed:', error);
      // Don't throw - app should work without service worker
    }
  }

  /**
   * Initialize network status monitoring
   */
  private initializeNetworkMonitoring(): void {
    if (typeof window === 'undefined') return;

    const updateOnlineStatus = () => {
      const wasOnline = this.state.isOnline;
      this.state.isOnline = navigator.onLine;

      if (wasOnline !== this.state.isOnline) {
        console.log(`Network status changed: ${this.state.isOnline ? 'online' : 'offline'}`);
        
        if (this.state.isOnline) {
          this.handleOnlineTransition();
        } else {
          this.handleOfflineTransition();
        }

        this.notifyStateChange();
      }
    };

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Initial status check
    updateOnlineStatus();
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;

    try {
      // Monitor navigation timing
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        this.state.performance.loadTime = navigationEntry.loadEventEnd - navigationEntry.fetchStart;
      }

      // Monitor resource loading for cache hit rate calculation
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        let cacheHits = 0;
        let totalRequests = 0;

        entries.forEach((entry) => {
          if (entry.entryType === 'resource') {
            totalRequests++;
            // Check if resource was served from cache (transferSize is 0 for cached resources)
            if ((entry as PerformanceResourceTiming).transferSize === 0) {
              cacheHits++;
            }
          }
        });

        if (totalRequests > 0) {
          this.state.performance.cacheHitRate = (cacheHits / totalRequests) * 100;
          this.notifyStateChange();
        }
      });

      this.performanceObserver.observe({ entryTypes: ['resource'] });

    } catch (error) {
      console.error('Performance monitoring initialization failed:', error);
    }
  }

  /**
   * Initialize data synchronization
   */
  private initializeDataSync(): void {
    if (this.config.enableOfflineSync && this.syncTimer === null) {
      this.syncTimer = setInterval(() => {
        if (this.state.isOnline && !this.state.dataSync.syncInProgress) {
          this.performDataSync();
        }
      }, this.config.syncInterval);
    }
  }

  /**
   * Initialize update management
   */
  private initializeUpdateManagement(): void {
    updateManager.onUpdateAvailable(() => {
      this.state.updateAvailable = true;
      this.notifyStateChange();

      if (this.config.enableAutoUpdate) {
        // Auto-apply updates after a delay
        setTimeout(() => {
          this.applyUpdate();
        }, 5000);
      }
    });
  }

  /**
   * Check if app is installed as PWA
   */
  private async checkInstallationStatus(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      // Check if app is running in standalone mode
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                          (window.navigator as any).standalone ||
                          document.referrer.includes('android-app://');

      this.state.isInstalled = isStandalone;
      
      // Listen for installation events
      window.addEventListener('beforeinstallprompt', () => {
        this.state.isInstalled = false;
        this.notifyStateChange();
      });

      window.addEventListener('appinstalled', () => {
        this.state.isInstalled = true;
        this.notifyStateChange();
      });

    } catch (error) {
      console.error('Installation status check failed:', error);
    }
  }

  /**
   * Handle transition from offline to online
   */
  private async handleOnlineTransition(): Promise<void> {
    console.log('Handling online transition...');

    try {
      // Process queued AI requests
      const queueLength = offlineAwareAIClient.getQueueLength();
      if (queueLength > 0) {
        console.log(`Processing ${queueLength} queued AI requests...`);
        await offlineAwareAIClient.processQueue();
      }

      // Sync data if enabled
      if (this.config.enableOfflineSync) {
        await this.performDataSync();
      }

      // Update service worker cache
      if (this.state.serviceWorkerReady) {
        await serviceWorkerManager.updateCache();
      }

    } catch (error) {
      console.error('Online transition handling failed:', error);
    }
  }

  /**
   * Handle transition from online to offline
   */
  private handleOfflineTransition(): void {
    console.log('Handling offline transition...');

    // Ensure all data is saved locally
    this.saveCurrentState();

    // Notify user about offline capabilities
    this.notifyOfflineCapabilities();
  }

  /**
   * Perform data synchronization
   */
  private async performDataSync(): Promise<void> {
    if (this.state.dataSync.syncInProgress) return;

    this.state.dataSync.syncInProgress = true;
    this.notifyStateChange();

    try {
      // Get pending changes count
      const pendingChanges = await this.getPendingChangesCount();
      this.state.dataSync.pendingChanges = pendingChanges;

      if (pendingChanges > 0) {
        console.log(`Syncing ${pendingChanges} pending changes...`);
        
        // Perform actual sync operations
        await this.syncPendingChanges();
        
        this.state.dataSync.lastSync = new Date();
        this.state.dataSync.pendingChanges = 0;
      }

    } catch (error) {
      console.error('Data sync failed:', error);
    } finally {
      this.state.dataSync.syncInProgress = false;
      this.notifyStateChange();
    }
  }

  /**
   * Get count of pending changes that need to be synced
   */
  private async getPendingChangesCount(): Promise<number> {
    try {
      // Check for unsaved changes in IndexedDB
      const projects = await indexedDBManager.getAllProjects();
      let pendingCount = 0;

      for (const project of projects) {
        if (project.pendingSync) {
          pendingCount++;
        }
        
        const tasks = await indexedDBManager.getTasksByProject(project.id);
        pendingCount += tasks.filter(task => task.pendingSync).length;
      }

      return pendingCount;
    } catch (error) {
      console.error('Failed to get pending changes count:', error);
      return 0;
    }
  }

  /**
   * Sync pending changes
   */
  private async syncPendingChanges(): Promise<void> {
    // In a real implementation, this would sync with a backend server
    // For now, we just mark items as synced
    try {
      const projects = await indexedDBManager.getAllProjects();
      
      for (const project of projects) {
        if (project.pendingSync) {
          await indexedDBManager.updateProject(project.id, { 
            ...project, 
            pendingSync: false,
            lastSyncAt: new Date()
          });
        }
        
        const tasks = await indexedDBManager.getTasksByProject(project.id);
        for (const task of tasks) {
          if (task.pendingSync) {
            await indexedDBManager.updateTask(task.id, { 
              ...task, 
              pendingSync: false 
            });
          }
        }
      }
    } catch (error) {
      console.error('Failed to sync pending changes:', error);
      throw error;
    }
  }

  /**
   * Migrate legacy data from localStorage to IndexedDB
   */
  private async migrateLegacyData(): Promise<void> {
    try {
      const legacyData = await localStorageManager.exportData();
      
      if (legacyData.projects && legacyData.projects.length > 0) {
        for (const project of legacyData.projects) {
          await indexedDBManager.createProject(project);
          
          if (project.tasks) {
            for (const task of project.tasks) {
              await indexedDBManager.createTask(task, project.id);
            }
          }
        }
      }

      // Clear legacy data after successful migration
      await localStorageManager.clearLegacyData();
      
      console.log('Legacy data migration completed');
    } catch (error) {
      console.error('Legacy data migration failed:', error);
      throw error;
    }
  }

  /**
   * Save current application state
   */
  private saveCurrentState(): void {
    try {
      // This would be called by the main application to save current state
      console.log('Saving current application state for offline use');
    } catch (error) {
      console.error('Failed to save current state:', error);
    }
  }

  /**
   * Notify user about offline capabilities
   */
  private notifyOfflineCapabilities(): void {
    // This would show a user-friendly message about what works offline
    console.log('App is now offline - basic functionality remains available');
  }

  /**
   * Setup service worker message handling
   */
  private setupServiceWorkerMessageHandling(): void {
    if (typeof navigator === 'undefined' || !navigator.serviceWorker) return;

    navigator.serviceWorker.addEventListener('message', (event) => {
      const { data } = event;
      
      switch (data.type) {
        case 'SW_ACTIVATED':
          this.state.serviceWorkerReady = true;
          this.notifyStateChange();
          break;
          
        case 'SW_UPDATE_APPLYING':
          console.log('Service worker update being applied...');
          break;
          
        case 'CACHE_INFO':
          console.log('Cache info:', data.data);
          break;
      }
    });
  }

  /**
   * Apply available update
   */
  async applyUpdate(): Promise<void> {
    try {
      await updateManager.applyUpdate();
      this.state.updateAvailable = false;
      this.notifyStateChange();
    } catch (error) {
      console.error('Failed to apply update:', error);
      throw error;
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: (state: PWAIntegrationState) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyStateChange(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('Error in PWA state listener:', error);
      }
    });
  }

  /**
   * Get current PWA integration state
   */
  getState(): PWAIntegrationState {
    return { ...this.state };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PWAIntegrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart sync timer if interval changed
    if (newConfig.syncInterval && this.syncTimer) {
      clearInterval(this.syncTimer);
      this.initializeDataSync();
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    this.listeners.clear();
  }

  /**
   * Force data sync
   */
  async forceSyncData(): Promise<void> {
    if (this.state.isOnline) {
      await this.performDataSync();
    } else {
      throw new Error('Cannot sync data while offline');
    }
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PWAIntegrationState['performance'] {
    return { ...this.state.performance };
  }

  /**
   * Test complete user workflow from project creation to export
   */
  async testCompleteWorkflow(): Promise<boolean> {
    try {
      console.log('Testing complete PWA workflow...');

      // Test 1: Create a test project
      const testProject: Project = {
        id: 'test-' + Date.now(),
        title: 'Test Project',
        description: 'PWA Integration Test',
        tasks: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        isOfflineOnly: !this.state.isOnline,
        isFavorite: false
      };

      await indexedDBManager.createProject(testProject);

      // Test 2: Create test tasks
      const testTask: Task = {
        id: 'task-' + Date.now(),
        title: 'Test Task',
        description: 'Test task for PWA integration',
        content: 'Test content',
        status: 'To Do',
        assignees: [],
        subtasks: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        pendingSync: false
      };

      await indexedDBManager.createTask(testTask, testProject.id);

      // Test 3: Verify data persistence
      const retrievedProject = await indexedDBManager.getAllProjects();
      const retrievedTasks = await indexedDBManager.getTasksByProject(testProject.id);

      const projectExists = retrievedProject.some(p => p.id === testProject.id);
      const taskExists = retrievedTasks.some(t => t.id === testTask.id);

      // Test 4: Test offline/online transitions
      const originalOnlineState = this.state.isOnline;
      
      // Simulate offline
      this.state.isOnline = false;
      this.handleOfflineTransition();
      
      // Simulate online
      this.state.isOnline = true;
      await this.handleOnlineTransition();
      
      // Restore original state
      this.state.isOnline = originalOnlineState;

      // Test 5: Cleanup test data
      await indexedDBManager.deleteProject(testProject.id);

      const success = projectExists && taskExists;
      console.log(`PWA workflow test ${success ? 'passed' : 'failed'}`);
      
      return success;

    } catch (error) {
      console.error('PWA workflow test failed:', error);
      return false;
    }
  }
}

// Create singleton instance
export const pwaIntegrationManager = new PWAIntegrationManager();

// Export types and manager
export type { PWAIntegrationState, PWAIntegrationConfig };
export { PWAIntegrationManager };