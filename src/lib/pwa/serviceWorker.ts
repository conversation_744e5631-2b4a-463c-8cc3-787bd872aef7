'use client';

// Service Worker registration and management utilities

export interface ServiceWorkerManager {
  register(): Promise<ServiceWorkerRegistration | null>;
  unregister(): Promise<boolean>;
  checkForUpdates(): Promise<boolean>;
  isSupported(): boolean;
}

class PWAServiceWorkerManager implements ServiceWorkerManager {
  private registration: ServiceWorkerRegistration | null = null;

  isSupported(): boolean {
    return typeof window !== 'undefined' && 'serviceWorker' in navigator;
  }

  async register(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported()) {
      console.warn('Service Workers are not supported in this browser');
      return null;
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none' // Always check for updates
      });

      console.log('Service Worker registered successfully:', this.registration);

      // Set up lifecycle event listeners
      this.setupLifecycleListeners();

      // Check for existing updates
      if (this.registration.waiting) {
        this.notifyUpdate();
      }

      // Check for updates immediately
      await this.registration.update();

      return this.registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  }

  private setupLifecycleListeners(): void {
    if (!this.registration) return;

    // Listen for updates
    this.registration.addEventListener('updatefound', () => {
      const newWorker = this.registration?.installing;
      if (newWorker) {
        console.log('New service worker installing...');
        
        // Dispatch installing event
        this.dispatchEvent('sw-installing', { registration: this.registration });

        newWorker.addEventListener('statechange', () => {
          console.log('Service Worker state changed to:', newWorker.state);
          
          switch (newWorker.state) {
            case 'installed':
              if (navigator.serviceWorker.controller) {
                // New content is available, notify user
                console.log('New service worker installed, update available');
                this.notifyUpdate();
              } else {
                // First install
                console.log('Service worker installed for the first time');
                this.dispatchEvent('sw-installed', { registration: this.registration });
              }
              break;
            case 'activated':
              console.log('Service worker activated');
              this.dispatchEvent('sw-activated', { registration: this.registration });
              break;
            case 'redundant':
              console.log('Service worker became redundant');
              this.dispatchEvent('sw-redundant', { registration: this.registration });
              break;
          }
        });
      }
    });

    // Listen for controller changes
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('Service worker controller changed');
      this.dispatchEvent('sw-controller-changed', { registration: this.registration });
    });
  }

  private dispatchEvent(type: string, detail: any): void {
    const event = new CustomEvent(type, { detail });
    window.dispatchEvent(event);
  }

  async unregister(): Promise<boolean> {
    if (!this.isSupported()) {
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        const result = await registration.unregister();
        console.log('Service Worker unregistered:', result);
        return result;
      }
      return false;
    } catch (error) {
      console.error('Service Worker unregistration failed:', error);
      return false;
    }
  }

  async checkForUpdates(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      await this.registration.update();
      return true;
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return false;
    }
  }

  private notifyUpdate(): void {
    // Dispatch custom event for update notification
    const event = new CustomEvent('sw-update-available', {
      detail: { registration: this.registration }
    });
    window.dispatchEvent(event);
  }

  // Get current registration
  getRegistration(): ServiceWorkerRegistration | null {
    return this.registration;
  }

  // Force update and reload
  async forceUpdate(): Promise<void> {
    if (!this.registration || !this.registration.waiting) {
      return;
    }

    // Send message to waiting service worker to skip waiting
    this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

    // Listen for controlling change and reload
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      window.location.reload();
    });
  }
}

// Singleton instance
export const serviceWorkerManager = new PWAServiceWorkerManager();

// Auto-register service worker when module is imported
if (typeof window !== 'undefined') {
  // Register after page load to not interfere with initial page performance
  window.addEventListener('load', () => {
    serviceWorkerManager.register();
  });
}