# Firebase Studio Project Weaver - Development Guide

## Essential Commands

```bash
# Development
npm run dev

# Build
npm run build

# Type checking
npm run typecheck

# Linting
npm run lint

# Testing
npm run test
npm run test:watch  # Watch mode
npm run test:ui     # UI mode

# Run a single test file
npx vitest run path/to/test-file.test.ts
```

## Code Style Guidelines

### Imports
- Use absolute imports with `@/*` alias for src directory
- Group imports in order: external libraries, internal modules, type imports
- Use destructuring for named exports
- Place type imports after regular imports

### Formatting
- Use Prettier defaults with 2-space indentation
- Semicolons required
- Trailing commas in objects and arrays
- Line width: 100 characters

### Types
- Use TypeScript for all new files
- Define interfaces for component props
- Use type aliases for complex types
- Prefer `type` over `interface` for simple object shapes

### Naming Conventions
- Components: PascalCase
- Functions: camelCase
- Constants: UPPER_SNAKE_CASE
- Files: kebab-case
- Variables: camelCase
- Hooks: use prefixed (useHookName)

### Error Handling
- Use try/catch with specific error types
- Implement error boundaries for React components
- Use consistent error messaging
- Handle async errors properly with .catch() or try/catch

### Components
- Use functional components with TypeScript interfaces
- Leverage React.memo for performance optimization
- Use useCallback for event handlers
- Implement proper cleanup in useEffect

### Hooks
- Custom hooks should start with "use"
- Follow React hooks rules
- Handle cleanup properly
- Memoize expensive calculations with useMemo

### Testing
- Use Vitest with React Testing Library
- Test component rendering and interactions
- Mock external dependencies
- Test edge cases and error states
- Use descriptive test names

## Project Structure
- `src/app/`: Main application pages
- `src/components/`: Reusable UI components
- `src/hooks/`: Custom React hooks
- `src/lib/`: Utilities, types, and business logic
- `src/test/`: Test files organized by type (unit, integration, etc.)
- `src/styles/`: Global styles and Tailwind configuration

## UI Library
- Uses Radix UI primitives for accessibility
- Tailwind CSS for styling with shadcn-inspired components
- Dark mode support with localStorage persistence
- Responsive design with mobile-first approach

## AI Integration
- Uses Gemini AI for task generation and content creation
- Context strategies for controlling AI context awareness
- Error handling for API failures with user feedback
- Performance monitoring for AI operations