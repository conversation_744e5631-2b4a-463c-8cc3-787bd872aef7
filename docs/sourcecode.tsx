import React from 'react';
// import { createRoot } from 'react-dom/client'; // KORREKTUR: <PERSON><PERSON><PERSON><PERSON>, da es in dieser Umgebung zu Fehlern führt. 
import { useState, useEffect, useCallback, useRef } from 'react';
import { ChevronDown, Plus, BrainCircuit, Sparkles, Trash2, Edit, Save, Sun, Moon, X, Info, FileDown, HelpCircle, Heart, Eye, EyeOff, User, Bot, CornerDownRight, ChevronsRight, CornerDownLeft, ListPlus } from 'lucide-react';

// HILFSFUNKTION: Dekodiert HTML-Entitäten (z.B. &lt;p&gt; zu <p>) 
const decodeHtmlEntities = (text) => {
    if (typeof text !== 'string') return '';
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
};

// NEU: HTML-zu-PDFMake-Parser für korrekte Formatierung 
const htmlToPdfmakeParser = (htmlString) => {
    // Hilfsfunktion zum rekursiven Flachklopfen von Arrays 
    const flatten = (arr) => arr.reduce((acc, val) => Array.isArray(val) ? acc.concat(flatten(val)) : acc.concat(val), []);

    const convertNode = (node, inList = false) => {
        // Text Node 
        if (node.nodeType === 3) {
            // KORREKTUR: Leere oder nur aus Leerzeichen bestehende Textknoten ignorieren 
            if (node.textContent.trim().length === 0) {
                return null;
            }
            return { text: node.textContent };
        }

        // Element Node 
        if (node.nodeType === 1) {
            const nodeName = node.nodeName.toLowerCase();
            const isListContext = inList || nodeName === 'li' || nodeName === 'ul' || nodeName === 'ol';
            const children = Array.from(node.childNodes).map(child => convertNode(child, isListContext)).filter(n => n);

            const processedChildren = flatten(children);

            switch (nodeName) {
                // INLINE ELEMENTS 
                case 'strong':
                case 'b':
                    return processedChildren.map(child => ({ ...child, bold: true }));
                case 'em':
                case 'i':
                    return processedChildren.map(child => ({ ...child, italics: true }));
                case 'u':
                    return processedChildren.map(child => ({ ...child, decoration: 'underline' }));
                case 'code':
                    return { text: node.textContent, style: 'code' };
                case 'a':
                    return processedChildren.map(child => ({ ...child, link: node.getAttribute('href'), style: 'link' }));
                case 'br':
                    return { text: '\n' };

                // BLOCK ELEMENTS 
                case 'h1': return { text: processedChildren, style: 'h1' };
                case 'h2': return { text: processedChildren, style: 'h2' };
                case 'h3': return { text: processedChildren, style: 'h3' };
                case 'h4': return { text: processedChildren, style: 'h4' };

                // KORREKTUR: Absätze in Listen anders behandeln, um Fehlausrichtung zu vermeiden 
                case 'p':
                    if (inList) {
                        // Innerhalb einer Liste wird ein <p> wie ein einfacher Text behandelt, um zusätzliche Abstände zu vermeiden. 
                        return { text: processedChildren, margin: [0, 0, 0, 2] };
                    }
                    return { text: processedChildren, style: 'paragraph' };

                case 'blockquote': return { stack: processedChildren, style: 'quote' };

                case 'ul':
                    return { ul: processedChildren.filter(c => c.isLi).map(c => c.content), style: 'list' };
                case 'ol':
                    return { ol: processedChildren.filter(c => c.isLi).map(c => c.content), style: 'list' };
                case 'li':
                    // Stellt sicher, dass der Inhalt eines <li> immer ein flaches Array ist. 
                    return { isLi: true, content: processedChildren };

                // OTHER CONTAINER ELEMENTS (div, span, etc.) 
                default:
                    return processedChildren;
            }
        }
        return null;
    };

    if (!htmlString) return [];
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = decodeHtmlEntities(htmlString);
    const result = Array.from(tempDiv.childNodes).map(node => convertNode(node)).filter(n => n);
    return flatten(result);
};


// Tooltip-Komponente für responsive Darstellung 
const Tooltip = ({ content, children }) => {
    return React.cloneElement(children, {
        'data-tippy-content': content,
    });
};

// NEU: Auto-wachsendes Textfeld 
const AutoGrowingTextarea = (props) => {
    const textareaRef = useRef(null);

    useEffect(() => {
        if (textareaRef.current) {
            textareaRef.current.style.height = "auto";
            textareaRef.current.style.height = textareaRef.current.scrollHeight + "px";
        }
    }, [props.value]); // Passt die Höhe bei jeder Wertänderung an 

    return (
        <textarea
            ref={textareaRef}
            rows="1"
            {...props}
        />
    );
};


// Haupt-App-Komponente 
const App = () => {
    // Zustand für das Hauptprojektziel 
    const [mainProject, setMainProject] = useState('');
    const [mainProjectDescription, setMainProjectDescription] = useState('');
    // Zustand für die Liste der Aufgaben 
    const [tasks, setTasks] = useState([]);
    // Zustand für Ladeindikatoren (pro Aufgabe) 
    const [loading, setLoading] = useState({});
    // Zustand für die motivierende Nachricht 
    const [motivation, setMotivation] = useState("Beginnen Sie, indem Sie Ihr Hauptziel oben eingeben oder ein Beispiel auswählen!");
    // Zustand für das Farbschema (light/dark), jetzt standardmäßig 'dark' 
    const [theme, setTheme] = useState('dark');
    // Zustand für Modals 
    const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
    const [solveModalState, setSolveModalState] = useState({
        step: 'closed', // 'closed', 'choice', 'autopilot_prompt', 'copilot_asking', 'copilot_answering' 
        task: null,
        questions: [],
        answers: {},
        additionalPrompt: '',
        isRefinement: false,
        selectionWrapperId: null // NEU: ID für den Wrapper der Auswahl 
    });

    // Zustand für die Kontext-Strategien 
    const [contextStrategies, setContextStrategies] = useState({
        strategy1: false, // Aufgaben-Pfad 
        strategy2: true, // Intelligente Zusammenfassung (Default) 
        strategy3: false, // Kompletter Projekt-Bauplan 
    });
    // Zustand für die Sichtbarkeit der Beispielprojekte 
    const [isExamplesVisible, setIsExamplesVisible] = useState(true);
    const [projectJustStarted, setProjectJustStarted] = useState(false);
    const taskContainerRef = useRef(null);
    // Zustand für geladene Export-Bibliotheken 
    const [libsLoaded, setLibsLoaded] = useState(false);
    const tippyInstanceRef = useRef(null);


    const exampleProjects = [
        "Ich möchte eine App entwickeln", "Ich möchte Trading lernen", "Ich möchte meine Musik über Social-Media promoten", "Ich möchte ein Startup gründen im Bereich Trading", "Ich möchte meine App vermarkten", "Ich möchte Schwimmen autodidaktisch lernen", "Ich möchte React lernen", "Ich möchte Englisch lernen", "Ich möchte mathematische Ableitungen praktisch einsetzen", "Ich möchte ein Grundstück in Deutschland kaufen", "Ich möchte ein Haus bauen", "Ich möchte gute Selbstenwicklungsbücher entdecken", "Ich möchte lernen, wie man mit Frauen flirtet", "Ich möchte mich auf ein Marathon vorbereiten"
    ];

    const exampleAdditionalPrompts = [
        "Erkläre es einem 10-Jährigen.", "Fasse dich so kurz wie möglich.", "Erstelle eine Schritt-für-Schritt-Anleitung.", "Sei extrem motivierend und inspirierend.", "Welche Werkzeuge brauche ich dafür?", "Erstelle eine Vergleichstabelle.", "Was sind die häufigsten Fehler?", "Gib mir ein Code-Beispiel in Python.", "Formuliere es als E-Mail an meinen Chef.", "Welche physikalischen Gesetze gelten hier?", "Erstelle einen Trainingsplan für 4 Wochen.", "Schreibe ein kurzes Gedicht darüber.", "Was sind die finanziellen Aspekte?", "Wie kann ich das sicher tun?", "Zerlege es in die kleinsten denkbaren Teile."
    ];

    // Hilfsfunktion zum rekursiven Finden einer Aufgabe anhand ihrer ID 
    const findTask = (tasksToSearch, id) => {
        for (const task of tasksToSearch) {
            if (task.id === id) return task;
            if (task.subtasks) {
                const found = findTask(task.subtasks, id);
                if (found) return found;
            }
        }
        return null;
    };

    // Effekt zum Setzen des Farbschemas beim Start und bei Änderungen 
    useEffect(() => {
        const savedTheme = localStorage.getItem('theme') || 'dark';
        setTheme(savedTheme);

        // Lade externe Skripte für Export-Funktionen 
        const loadScript = (src, id) => new Promise((resolve, reject) => {
            if (document.getElementById(id)) {
                resolve();
                return;
            }
            const script = document.createElement('script');
            script.id = id;
            script.src = src;
            script.async = true;
            script.onload = resolve;
            script.onerror = () => reject(new Error(`Script load error for ${src}`));
            document.body.appendChild(script);
        });

        const loadLibraries = async () => {
            try {
                await loadScript('https://unpkg.com/@popperjs/core@2', 'popper-script');
                await loadScript('https://unpkg.com/tippy.js@6', 'tippy-script');
                await loadScript('https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js', 'pdfmake-script');
                await loadScript('https://unpkg.com/papaparse@latest/papaparse.min.js', 'papaparse-script');
                setLibsLoaded(true);
            } catch (error) {
                console.error("Fehler beim Laden der Export-Bibliotheken:", error);
            }
        };

        loadLibraries();
    }, []);

    // Effekt zum Initialisieren von Tippy.js, sobald die Bibliothek geladen ist 
    useEffect(() => {
        if (libsLoaded && window.tippy) {
            if (tippyInstanceRef.current) {
                tippyInstanceRef.current.forEach(instance => instance.destroy());
            }
            const buttonTooltips = window.tippy('[data-tippy-content]', {
                placement: 'top',
                arrow: true,
                theme: 'light-border',
            });
            tippyInstanceRef.current = buttonTooltips;
        }
    }, [libsLoaded, tasks]);


    useEffect(() => {
        const root = window.document.documentElement;
        if (theme === 'dark') {
            root.classList.add('dark');
        } else {
            root.classList.remove('dark');
        }
        localStorage.setItem('theme', theme);
    }, [theme]);

    // Effekt für Auto-Scroll nach Projektstart 
    useEffect(() => {
        if (projectJustStarted) {
            setTimeout(() => {
                taskContainerRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                setProjectJustStarted(false);
            }, 500);
        }
    }, [projectJustStarted]);

    const toggleTheme = () => {
        setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
    };

    const handleStrategyChange = (strategy) => {
        setContextStrategies(prev => ({ ...prev, [strategy]: !prev[strategy] }));
    };

    const setTaskLoading = (taskId, loadingType) => {
        setLoading(prev => ({ ...prev, [taskId]: loadingType }));
    };

    const callGeminiAPI = async (prompt, isJson = false, isQuestionGeneration = false) => {
        const apiKey = "";
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        let payload = {
            contents: [{ role: "user", parts: [{ text: prompt }] }]
        };

        if (isJson) {
            let schema = {
                type: "ARRAY",
                items: {
                    type: "OBJECT",
                    properties: {
                        title: { type: "STRING" },
                        description: { type: "STRING" }
                    },
                    required: ["title", "description"]
                }
            };
            if (isQuestionGeneration) {
                schema = {
                    type: "ARRAY",
                    items: { type: "STRING" }
                }
            }
            payload.generationConfig = {
                responseMimeType: "application/json",
                responseSchema: schema
            };
        }

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`API-Anfrage fehlgeschlagen mit Status: ${response.status}`);
            }

            const result = await response.json();

            if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                const rawText = result.candidates[0].content.parts[0].text;
                return isJson ? JSON.parse(rawText) : rawText;
            } else {
                console.error("Unerwartete API-Antwortstruktur:", result);
                throw new Error("Konnte keine gültige Antwort von der KI erhalten.");
            }
        } catch (error) {
            console.error("Fehler bei der Kommunikation mit der Gemini-API:", error);
            return isJson ? [] : `<p><strong>Fehler:</strong> ${error.message}</p>`;
        }
    };

    const buildContextForAI = (taskId, selectedHtml = "") => {
        let contextStrings = [];
        const findTaskPath = (currentTasks, targetId, path = []) => {
            for (const task of currentTasks) {
                const newPath = [...path, task];
                if (task.id === targetId) { return newPath; }
                if (task.subtasks && task.subtasks.length > 0) {
                    const foundPath = findTaskPath(task.subtasks, targetId, newPath);
                    if (foundPath) return foundPath;
                }
            }
            return null;
        };
        const generateTaskTreeString = (currentTasks, targetId, depth = 0) => {
            let treeString = '';
            currentTasks.forEach(task => {
                const prefix = '  '.repeat(depth) + '- ';
                const marker = task.id === targetId ? '[AKTUELLE AUFGABE] ' : '';
                treeString += `${prefix}${marker}${task.title}\n`;
                if (task.subtasks && task.subtasks.length > 0) {
                    treeString += generateTaskTreeString(task.subtasks, targetId, depth + 1);
                }
            });
            return treeString;
        };
        const path = findTaskPath(tasks, taskId);
        if (contextStrategies.strategy2) {
            let summary = `Gesamtziel des Projekts: "${mainProject}".`;
            if (path && path.length > 1) { const parent = path[path.length - 2]; summary += ` Die aktuelle Aufgabe ist Teil von "${parent.title}".`; }
            contextStrings.push(summary);
        }
        if (contextStrategies.strategy1 && path) { const pathString = `Projektpfad: ${mainProject} > ${path.map(p => p.title).join(' > ')}`; contextStrings.push(pathString); }
        if (contextStrategies.strategy3) { const tree = generateTaskTreeString(tasks, taskId); contextStrings.push(`Gesamtprojektplan:\n${tree}`); }

        if (selectedHtml) {
            contextStrings.push(`Der Fokus liegt auf diesem spezifischen HTML-Ausschnitt: "${selectedHtml}"`);
        }

        if (contextStrings.length === 0) return '';
        return `Zusätzlicher Gesamtkontext für deine Aufgabe:\n---\n${contextStrings.join('\n\n')}\n---\n\n`;
    };

    const handleSolve = async (taskId, taskTitle, taskDescription, isRework = false, userAdditionalPrompt = '', selectedHtml = '', wrapperId = '') => {
        const isSelectionBasedElaboration = !!selectedHtml && !!wrapperId;

        if (!isSelectionBasedElaboration) {
            setTaskLoading(taskId, 'elaborate');
        }

        setMotivation("Die KI arbeitet an einer Lösung für Sie...");
        const aiContext = buildContextForAI(taskId, selectedHtml);
        const stylingPrompt = `Gib eine klare, detaillierte und schön formatierte Antwort direkt im HTML-Format zurück. **WICHTIG: Verwende Tailwind CSS Klassen direkt in den HTML-Tags, um ein ansprechendes Layout zu erstellen.** Beispiele für das Styling: - Überschriften: <h3 class='text-xl font-bold text-indigo-400 mt-4 mb-2'>Titel</h3> - Unterüberschriften: <h4 class='text-lg font-semibold text-indigo-300 mt-3 mb-1'>Untertitel</h4> - Absätze: <p class='text-slate-300 mb-3'>Dies ist ein Textabsatz.</p> - Listen: <ul class='list-disc list-inside space-y-1 pl-4 text-slate-300 mb-3'><li>Punkt 1</li><li>Punkt 2</li></ul> - Zitate: <blockquote class='border-l-4 border-slate-500 pl-4 my-4 text-slate-400 italic'>Ein wichtiges Zitat.</blockquote> - Code: <code class='bg-slate-700 text-amber-300 px-2 py-1 rounded'>const beispiel = 'hallo';</code> - Starker Text: <strong class='text-slate-200'>Wichtig</strong> Gib NUR den reinen HTML-Body-Inhalt zurück, ohne \`<html>\`, \`<head>\` oder \`<body>\` Tags.`;

        let finalTaskDescription = `Titel: ${taskTitle}. Beschreibung: ${taskDescription}`;
        if (userAdditionalPrompt) { finalTaskDescription += `. Zusätzliche Anweisung/Informationen vom Benutzer: ${userAdditionalPrompt}`; }

        const baseInstruction = isSelectionBasedElaboration
            ? `Arbeite den folgenden HTML-Ausschnitt detaillierter aus. Ersetze NUR den Ausschnitt durch deine verbesserte Version. HTML-Ausschnitt: "${selectedHtml}". Aufgabe: "${finalTaskDescription}"`
            : `Führe die folgende Aufgabe direkt und vollständig aus. Erstelle den Inhalt, die Antwort oder die Lösung für die gegebene Aufgabe, anstatt sie nur neu zu formulieren oder zu beschreiben. Aufgabe: "${finalTaskDescription}"`;

        const reworkInstruction = `Hier ist eine Aufgabe und eine vom Benutzer bearbeitete Version. Bitte überarbeite und verbessere sie basierend auf den Änderungen: "${finalTaskDescription}"`;

        const prompt = isRework
            ? `${aiContext}${stylingPrompt} ${reworkInstruction}`
            : `${aiContext}${stylingPrompt} ${baseInstruction}`;

        let solution = await callGeminiAPI(prompt);
        solution = solution.replace(/^```html\s*/, '').replace(/```\s*$/, '');

        setTasks(currentTasks => {
            const findTaskAndReplace = (tasksToSearch) => {
                return tasksToSearch.map(task => {
                    if (task.id === taskId) {
                        let finalAiContent;
                        if (isSelectionBasedElaboration) {
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = task.aiContent;
                            const wrapper = tempDiv.querySelector(`#${wrapperId}`);

                            if (wrapper) {
                                wrapper.innerHTML = solution;
                                // Unwrap the content from the temporary span 
                                const parent = wrapper.parentNode;
                                while (wrapper.firstChild) {
                                    parent.insertBefore(wrapper.firstChild, wrapper);
                                }
                                parent.removeChild(wrapper);
                                finalAiContent = tempDiv.innerHTML;
                            } else {
                                // Fallback if wrapper is not found (should not happen) 
                                finalAiContent = solution;
                            }
                        } else {
                            finalAiContent = solution;
                        }
                        return { ...task, aiContent: finalAiContent, isAiContentEditing: false };
                    }
                    if (task.subtasks) {
                        return { ...task, subtasks: findTaskAndReplace(task.subtasks) };
                    }
                    return task;
                });
            };
            return findTaskAndReplace(currentTasks);
        });

        setMotivation("Die KI hat einen Vorschlag erarbeitet. Sie können ihn nun bearbeiten.");
        if (!isSelectionBasedElaboration) {
            setTaskLoading(taskId, false);
        }
    };

    const recursiveTaskOperation = (tasks, targetId, operation) => { return tasks.reduce((acc, task) => { if (task.id === targetId) { return operation(acc, task); } if (task.subtasks) { const newSubtasks = recursiveTaskOperation(task.subtasks, targetId, operation); if (newSubtasks !== task.subtasks) { acc.push({ ...task, subtasks: newSubtasks }); return acc; } } acc.push(task); return acc; }, []); };
    const handleAddTask = (parentId = null, title = 'Neue Aufgabe (zum Bearbeiten klicken)', description = 'Beschreibung hinzufügen...') => { const newTask = { id: crypto.randomUUID(), title, description, subtasks: [], aiContent: '', isEditing: true, isDescriptionEditing: false, isAiContentEditing: false, }; if (!parentId) { setTasks(prev => [...prev, newTask]); } else { const operation = (acc, parentTask) => { acc.push({ ...parentTask, subtasks: [...(parentTask.subtasks || []), newTask] }); return acc; }; setTasks(currentTasks => recursiveTaskOperation(currentTasks, parentId, operation)); } };
    const handleAddTaskAfter = (afterId, parentId = null, title = 'Neue Aufgabe (zum Bearbeiten klicken)', description = 'Beschreibung hinzufügen...') => { const newTask = { id: crypto.randomUUID(), title, description, subtasks: [], aiContent: '', isEditing: true, isDescriptionEditing: false, isAiContentEditing: false, }; const addTask = (taskList) => { const index = taskList.findIndex(t => t.id === afterId); if (index !== -1) { const newTasks = [...taskList]; newTasks.splice(index + 1, 0, newTask); return newTasks; } return taskList.map(t => ({ ...t, subtasks: addTask(t.subtasks || []) })); }; if (!parentId) { setTasks(addTask); } else { setTasks(tasks => tasks.map(task => { if (task.id === parentId) { return { ...task, subtasks: addTask(task.subtasks || []) }; } return task; })); } };
    const handleDeleteTask = (targetId) => { const filterOut = (tasks, id) => { return tasks.filter(task => task.id !== id).map(task => { if (task.subtasks) { return { ...task, subtasks: filterOut(task.subtasks, id) }; } return task; }); }; setTasks(currentTasks => filterOut(currentTasks, targetId)); };
    const handleUpdateTask = (targetId, updates) => { const operation = (acc, taskToUpdate) => { acc.push({ ...taskToUpdate, ...updates }); return acc; }; setTasks(currentTasks => recursiveTaskOperation(currentTasks, targetId, operation)); };
    const handleBreakdown = async (taskId, taskTitle, taskDescription) => { setTaskLoading(taskId, 'breakdown'); setMotivation("Die KI denkt über die besten Teilschritte nach..."); const prompt = `Zerlege die folgende Aufgabe in eine detaillierte Liste der elementarsten Teilschritte. Gib für jeden Schritt einen Titel und eine kurze Beschreibung. Die Liste sollte so vollständig wie möglich sein, aber maximal 30 Einträge haben. Gib nur ein JSON-Array mit Objekten zurück, die jeweils "title" und "description" enthalten. Aufgabe: "${taskTitle}". Beschreibung: "${taskDescription}"`; const subtaskData = await callGeminiAPI(prompt, true); if (subtaskData && subtaskData.length > 0) { const newSubtasks = subtaskData.map(data => ({ id: crypto.randomUUID(), title: data.title, description: data.description, subtasks: [], aiContent: '', isEditing: false, isDescriptionEditing: false, isAiContentEditing: false, })); const operation = (acc, task) => { acc.push({ ...task, subtasks: [...(task.subtasks || []), ...newSubtasks] }); return acc; }; setTasks(currentTasks => recursiveTaskOperation(currentTasks, taskId, operation)); setMotivation("Neue Teilschritte wurden hinzugefügt. Was ist der nächste Schritt?"); } else { setMotivation("Die KI konnte diese Aufgabe nicht zerlegen. Versuchen Sie es anders zu formulieren."); } setTaskLoading(taskId, false); };
    const startProject = async () => { if (!mainProject) return; setTasks([]); setLoading({ 'main': true }); setMotivation("Die KI analysiert Ihr großes Ziel..."); const prompt = `Ich möchte das folgende Projekt/Ziel erreichen: "${mainProject}". Beschreibung: "${mainProjectDescription}". Zerlege es in eine detaillierte Liste der elementarsten Hauptaufgaben. Gib für jede Aufgabe einen Titel und eine kurze Beschreibung. Die Liste sollte so vollständig wie möglich sein, aber maximal 30 Einträge haben. Gib nur ein JSON-Array mit Objekten zurück, die jeweils "title" und "description" enthalten.`; const initialTaskData = await callGeminiAPI(prompt, true); if (initialTaskData && initialTaskData.length > 0) { setTasks(initialTaskData.map(data => ({ id: crypto.randomUUID(), title: data.title, description: data.description, subtasks: [], aiContent: '', isEditing: false, isDescriptionEditing: false, isAiContentEditing: false, }))); setMotivation("Großartig! Hier ist ein detaillierter Plan. Wählen Sie eine Aufgabe aus, um sie weiter zu zerlegen."); setIsExamplesVisible(false); setProjectJustStarted(true); } else { setMotivation("Konnte das Projekt nicht starten. Bitte versuchen Sie, Ihr Ziel anders zu formulieren."); } setLoading({ 'main': false }); };

    const handleOpenSolveModal = (task, isRefinement = false, selectionWrapperId = '') => {
        setSolveModalState({
            step: 'choice',
            task: { id: task.id, title: task.title, description: task.description },
            questions: [],
            answers: {},
            additionalPrompt: '',
            isRefinement,
            selectionWrapperId,
        });
    };

    const handleCloseSolveModal = () => {
        setSolveModalState({ step: 'closed', task: null, questions: [], answers: {}, additionalPrompt: '' });
    };

    const handleAutopilotSelect = () => {
        setSolveModalState(prev => ({ ...prev, step: 'autopilot_prompt' }));
    };

    const handleConfirmAutopilot = async () => {
        const { task, additionalPrompt, selectionWrapperId } = solveModalState;
        if (!task) return;

        let selectedHtml = '';

        // Synchronously find the task and its content to extract the selected HTML 
        // and replace it with a loading indicator. 
        if (selectionWrapperId) {
            const currentTask = findTask(tasks, task.id);
            if (currentTask) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = currentTask.aiContent;
                const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
                if (wrapper) {
                    selectedHtml = wrapper.innerHTML; // Capture the HTML to be reworked 
                    // Replace the wrapper's content with a loading placeholder 
                    wrapper.innerHTML = `<div class="flex items-center justify-center p-4"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div><span class="ml-2 text-slate-500 dark:text-slate-400">Wird überarbeitet...</span></div>`;
                    const newAiContentWithLoader = tempDiv.innerHTML;
                    // Update the task immediately to show the loader 
                    handleUpdateTask(task.id, { aiContent: newAiContentWithLoader });
                }
            }
        }

        handleCloseSolveModal();

        try {
            const autopilotPrompt = `Führe die Aufgabe im Autopilot-Modus aus. Wenn dir für eine spezifische Antwort Details fehlen, triff plausible, kreative Annahmen, die zum Gesamtziel passen, und erwähne diese Annahmen am Anfang deiner Antwort. ${additionalPrompt}`;
            // Now call handleSolve with the captured HTML. 
            await handleSolve(task.id, task.title, task.description, false, autopilotPrompt, selectedHtml, selectionWrapperId);
        } catch (error) {
            console.error("Fehler während des Autopilot-Modus:", error);
            setTaskLoading(task.id, false);
            // Optional: Revert the change if the API call fails 
        }
    };

    const handleCopilotSelect = async () => {
        setSolveModalState(prev => ({ ...prev, step: 'copilot_asking' }));
        const { task, selectionWrapperId } = solveModalState;
        let selectedHtml = '';

        if (selectionWrapperId) {
            const currentTask = findTask(tasks, task.id);
            if (currentTask) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = currentTask.aiContent;
                const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
                if (wrapper) {
                    selectedHtml = wrapper.innerHTML;
                }
            }
        }

        const prompt = `Du bist ein Projekt-Assistent. Das übergeordnete Projektziel lautet: "${mainProject}". Die Beschreibung dazu ist: "${mainProjectDescription}". Die aktuelle Aufgabe lautet: "${task.title}". ${selectedHtml ? `Der Benutzer möchte folgenden Textabschnitt verfeinern: "${selectedHtml}".` : ''} Um diese Aufgabe bestmöglich zu lösen, stelle dem Benutzer 3-6 entscheidende, spezifische Rückfragen, die auf den bereits bekannten Informationen aufbauen und fehlende Details erfragen. Gib nur ein JSON-Array mit den Fragen als Strings zurück.`;
        const questions = await callGeminiAPI(prompt, true, true);
        setSolveModalState(prev => ({ ...prev, questions: questions.length > 0 ? questions : ["Konnte keine Fragen generieren. Bitte beschreiben Sie Ihr Ziel genauer."], step: 'copilot_answering' }));
    };

    const handleConfirmCopilot = async () => {
        const { task, answers, selectionWrapperId } = solveModalState;
        if (!task) return;

        let selectedHtml = '';

        // Synchronously find the task and its content to extract the selected HTML 
        // and replace it with a loading indicator. 
        if (selectionWrapperId) {
            const currentTask = findTask(tasks, task.id);
            if (currentTask) {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = currentTask.aiContent;
                const wrapper = tempDiv.querySelector(`#${selectionWrapperId}`);
                if (wrapper) {
                    selectedHtml = wrapper.innerHTML; // Capture the HTML to be reworked 
                    // Replace the wrapper's content with a loading placeholder 
                    wrapper.innerHTML = `<div class="flex items-center justify-center p-4"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div><span class="ml-2 text-slate-500 dark:text-slate-400">Wird überarbeitet...</span></div>`;
                    const newAiContentWithLoader = tempDiv.innerHTML;
                    // Update the task immediately to show the loader 
                    handleUpdateTask(task.id, { aiContent: newAiContentWithLoader });
                }
            }
        }

        handleCloseSolveModal();

        try {
            const answersString = Object.entries(answers).map(([q, a]) => `Frage: "${q}" - Antwort: "${a}"`).join('; ');
            // Now call handleSolve with the captured HTML. 
            await handleSolve(task.id, task.title, task.description, false, `Hier sind die Antworten auf deine Rückfragen: ${answersString}`, selectedHtml, selectionWrapperId);
        } catch (error) {
            console.error("Fehler während des Copilot-Modus:", error);
            setTaskLoading(task.id, false);
        }
    };

    const generateFlatData = () => {
        const flatData = [];
        const recurse = (tasks, prefix, level) => {
            tasks.forEach((task, index) => {
                const currentNumber = `${prefix}${index + 1}`;
                flatData.push({
                    number: `${currentNumber}.`,
                    level: level,
                    title: task.title,
                    description: task.description || '',
                    ai_content: task.aiContent || ''
                });
                if (task.subtasks && task.subtasks.length > 0) {
                    recurse(task.subtasks, `${currentNumber}.`, level + 1);
                }
            });
        };
        recurse(tasks, '', 1);
        return flatData;
    };

    const handleExportPDF = () => {
        // Verbesserte Prüfung und Fehlerbehandlung für den PDF-Export 
        if (typeof window.pdfMake === 'undefined') {
            console.error("pdfmake.min.js ist nicht geladen.");
            setMotivation("Fehler: Die PDF-Hauptbibliothek ist nicht bereit. Bitte warten Sie einen Moment.");
            return;
        }

        try {
            // KORREKTUR: Integrieren Sie die Schriftarten direkt, um Race Conditions zu vermeiden. 
            window.pdfMake.fonts = {
                Roboto: {
                    normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
                    bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
                    italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
                    bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
                }
            };

            const flatData = generateFlatData();

            const docDefinition = {
                content: [
                    { text: mainProject, style: 'header', alignment: 'center' },
                    { text: mainProjectDescription || 'Ihr Projektplan, um Großes zu erreichen.', style: 'subheader', alignment: 'center' },
                    { canvas: [{ type: 'line', x1: 0, y1: 5, x2: 515, y2: 5, lineWidth: 0.5, lineColor: '#cccccc' }], margin: [0, 20, 0, 20] },
                    { text: `Erstellt am: ${new Date().toLocaleDateString('de-DE')}`, style: 'meta', alignment: 'center' },
                    { text: 'Projektübersicht', style: 'h2', pageBreak: 'before' },
                    {
                        style: 'tableExample',
                        table: {
                            headerRows: 1,
                            widths: ['auto', '*', 'auto'],
                            body: [
                                [{ text: 'Nummer', style: 'tableHeader' }, { text: 'Aufgabe', style: 'tableHeader' }, { text: 'Beschreibung', style: 'tableHeader' }],
                                ...flatData.map(d => [
                                    { text: ' '.repeat(d.level * 2) + d.number, style: 'tableCell' },
                                    { text: d.title, style: 'tableCell' },
                                    { text: d.description, style: 'tableCell' }
                                ])
                            ]
                        },
                        layout: 'lightHorizontalLines'
                    },
                ],
                // Angepasste Stile für bessere Abstände und Lesbarkeit 
                styles: {
                    header: { fontSize: 28, bold: true, margin: [0, 20, 0, 10] },
                    subheader: { fontSize: 14, italic: true, margin: [0, 0, 0, 15] },
                    h1: { fontSize: 22, bold: true, margin: [0, 10, 0, 4] },
                    h2: { fontSize: 20, bold: true, margin: [0, 8, 0, 4] },
                    h3: { fontSize: 16, bold: true, margin: [0, 6, 0, 3] },
                    h4: { fontSize: 14, bold: true, margin: [0, 5, 0, 2] },
                    paragraph: { margin: [0, 0, 0, 5], lineHeight: 1.15 },
                    quote: { italics: true, margin: [10, 5, 10, 5], color: '#555555' },
                    list: { margin: [10, 5, 0, 8] },
                    link: { color: 'blue', decoration: 'underline' },
                    code: { font: 'Courier', background: '#f0f0f0', margin: [0, 5, 0, 5] },
                    meta: { fontSize: 9, color: '#666666' },
                    tableHeader: { bold: true, fontSize: 13, color: 'black' },
                    tableCell: { margin: [0, 5, 0, 5], lineHeight: 1.2 }
                },
                defaultStyle: {
                    font: 'Roboto'
                },
                footer: function (currentPage, pageCount) {
                    return { text: `Seite ${currentPage.toString()} von ${pageCount}`, alignment: 'center', style: 'meta', margin: [0, 10, 0, 0] };
                },
                header: function (currentPage, pageCount, pageSize) {
                    if (currentPage === 1) return null;
                    return { text: mainProject, alignment: 'center', style: 'meta', margin: [0, 10, 0, 0] };
                }
            };

            const detailedContent = flatData.filter(d => d.ai_content);
            if (detailedContent.length > 0) {
                docDefinition.content.push({ text: 'Detaillierte Ausarbeitungen', style: 'h2', pageBreak: 'before' });
                detailedContent.forEach(task => {
                    docDefinition.content.push({ text: `${task.number} ${task.title}`, style: 'h3' });
                    const parsedContent = htmlToPdfmakeParser(task.ai_content);
                    if (parsedContent && parsedContent.length > 0) {
                        docDefinition.content.push(...parsedContent);
                    }
                });
            }

            window.pdfMake.createPdf(docDefinition).download(`${mainProject.replace(/ /g, '_')}.pdf`);

        } catch (error) {
            console.error("Fehler bei der PDF-Erstellung:", error);
            setMotivation(`PDF-Export fehlgeschlagen: ${error.message}. Bitte Konsole prüfen.`);
        }
    };

    const handleExportCSV = () => {
        if (!window.Papa) { console.error("PapaParse ist nicht geladen."); return; }
        const flatData = generateFlatData();
        const csv = window.Papa.unparse(flatData.map(d => ({ ...d, ai_content: d.ai_content ? decodeHtmlEntities(d.ai_content).replace(/<[^>]*>?/gm, '') : '' })));
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `${mainProject.replace(/ /g, '_')}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleExportMD = () => {
        let mdContent = `# ${mainProject}\n\n`;
        if (mainProjectDescription) {
            mdContent += `> ${mainProjectDescription}\n\n`;
        }
        mdContent += `***\n*"Der beste Weg, die Zukunft vorauszusagen, ist, sie zu gestalten."*\n***\n\n`;

        const recurse = (tasks, prefix, level) => {
            tasks.forEach((task, index) => {
                const currentNumber = `${prefix}${index + 1}`;
                mdContent += `${'  '.repeat(level - 1)}- **${currentNumber}. ${task.title}**\n`;
                if (task.description) {
                    mdContent += `${'  '.repeat(level - 1)}  - *${task.description}*\n`;
                }
                if (task.aiContent) {
                    mdContent += `\n${'  '.repeat(level - 1)}  **Ausarbeitung:**\n${'  '.repeat(level - 1)}  >${decodeHtmlEntities(task.aiContent).replace(/<[^>]*>?/gm, ' ').replace(/\n/g, `\n${'  '.repeat(level - 1)}  > `)}\n\n`;
                }
                if (task.subtasks && task.subtasks.length > 0) {
                    recurse(task.subtasks, `${currentNumber}.`, level + 1);
                }
            });
        };
        recurse(tasks, '', 1);

        const blob = new Blob([mdContent], { type: 'text/markdown;charset=utf-8;' });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `${mainProject.replace(/ /g, '_')}.md`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const TippyMenu = ({ onCreateSubtask, onCreateTaskAfter, onElaborate, onDelete, onHide }) => {
        const createSubtask = () => { onCreateSubtask(); onHide(); };
        const createTaskAfter = () => { onCreateTaskAfter(); onHide(); };
        const elaborate = () => { onElaborate(); onHide(); };
        const deleteText = () => { onDelete(); onHide(); };

        return (
            <div className="bg-slate-800 border border-slate-700 rounded-lg shadow-2xl p-2 w-64 text-white">
                <button onClick={createSubtask} className="w-full text-left p-2 rounded-md hover:bg-slate-700 transition-colors flex items-center gap-3">
                    <CornerDownRight size={18} className="text-indigo-400 shrink-0" />
                    <div>
                        <p className="font-semibold text-sm">Unteraufgabe erstellen</p>
                        <p className="text-xs text-slate-400">Wandelt den Text in einen neuen Unterschritt um.</p>
                    </div>
                </button>
                <button onClick={createTaskAfter} className="w-full text-left p-2 rounded-md hover:bg-slate-700 transition-colors flex items-center gap-3">
                    <ListPlus size={18} className="text-indigo-400 shrink-0" />
                    <div>
                        <p className="font-semibold text-sm">Neue Aufgabe danach</p>
                        <p className="text-xs text-slate-400">Erstellt eine neue Aufgabe auf gleicher Ebene.</p>
                    </div>
                </button>
                <button onClick={elaborate} className="w-full text-left p-2 rounded-md hover:bg-slate-700 transition-colors flex items-center gap-3">
                    <Sparkles size={18} className="text-indigo-400 shrink-0" />
                    <div>
                        <p className="font-semibold text-sm">Detailliert ausarbeiten</p>
                        <p className="text-xs text-slate-400">Lässt die KI den markierten Text verfeinern.</p>
                    </div>
                </button>
                <div className="w-full h-px my-1 bg-slate-700"></div>
                <button onClick={deleteText} className="w-full text-left p-2 rounded-md hover:bg-slate-700 transition-colors flex items-center gap-3">
                    <Trash2 size={18} className="text-red-400 shrink-0" />
                    <div>
                        <p className="font-semibold text-sm text-red-400">Markierung löschen</p>
                        <p className="text-xs text-slate-400">Entfernt den ausgewählten Text.</p>
                    </div>
                </button>
            </div>
        );
    };

    const TaskItem = ({ task, level, parentId, onAddTask, onAddTaskAfter, onOpenSolveModal, numbering }) => {
        const [tempTitle, setTempTitle] = useState(task.title);
        const [tempDescription, setTempDescription] = useState(task.description);
        const [tempAiContent, setTempAiContent] = useState(task.aiContent);
        const [isAiContentVisible, setIsAiContentVisible] = useState(true);
        const aiContentRef = useRef(null);
        const tippyInstance = useRef(null);
        const tippyRoot = useRef(null);
        const loadingState = loading[task.id];
        const isBreakingDown = loadingState === 'breakdown';
        const isElaborating = loadingState === 'elaborate';

        const handleTitleBlur = () => handleUpdateTask(task.id, { title: tempTitle, isEditing: false });
        const handleDescriptionBlur = () => handleUpdateTask(task.id, { description: tempDescription, isDescriptionEditing: false });
        const handleSaveAndRework = () => { handleUpdateTask(task.id, { aiContent: tempAiContent }); handleSolve(task.id, tempAiContent, task.description, true); };

        useEffect(() => {
            if (task.aiContent || isElaborating) {
                setIsAiContentVisible(true);
            }
        }, [task.aiContent, isElaborating]);

        const handleSelectionChange = useCallback(() => {
            setTimeout(() => {
                if (!window.tippy || !window.ReactDOM || !window.ReactDOM.createRoot) return;

                const selection = window.getSelection();
                if (!selection || selection.rangeCount === 0) return;

                const selectedText = selection.toString().trim();

                if (tippyInstance.current) {
                    tippyInstance.current.hide();
                }

                if (selectedText.length > 0 && aiContentRef.current && aiContentRef.current.contains(selection.anchorNode)) {
                    const range = selection.getRangeAt(0);

                    const handleCreateSubtask = () => {
                        onAddTask(task.id, selectedText);
                    };

                    const handleCreateTaskAfter = () => {
                        onAddTaskAfter(task.id, parentId, selectedText);
                    };

                    const handleElaborate = () => {
                        const wrapperId = `wrapper-${crypto.randomUUID()}`;
                        const wrapper = document.createElement('span');
                        wrapper.id = wrapperId;

                        try {
                            const selectionContents = range.extractContents();
                            wrapper.appendChild(selectionContents);
                            range.insertNode(wrapper);

                            handleUpdateTask(task.id, { aiContent: aiContentRef.current.innerHTML });
                            onOpenSolveModal(task, true, wrapperId);
                        } catch (e) {
                            console.error("Fehler bei der Textauswahl-Verarbeitung.", e);
                        }
                    };

                    const handleDelete = () => {
                        range.deleteContents();
                        const newAiContent = aiContentRef.current.innerHTML;
                        handleUpdateTask(task.id, { aiContent: newAiContent });
                    };

                    const rect = range.getBoundingClientRect();
                    const container = document.createElement('div');

                    tippyInstance.current = window.tippy(document.body, {
                        content: container,
                        placement: 'bottom-start',
                        trigger: 'manual',
                        interactive: true,
                        arrow: false,
                        appendTo: () => document.body,
                        getReferenceClientRect: () => rect,
                        onHidden: (instance) => {
                            if (tippyRoot.current) {
                                tippyRoot.current.unmount();
                                tippyRoot.current = null;
                            }
                            instance.destroy();
                            tippyInstance.current = null;
                        }
                    });

                    tippyRoot.current = window.ReactDOM.createRoot(container);
                    tippyRoot.current.render(
                        <TippyMenu
                            onCreateSubtask={handleCreateSubtask}
                            onCreateTaskAfter={handleCreateTaskAfter}
                            onElaborate={handleElaborate}
                            onDelete={handleDelete}
                            onHide={() => tippyInstance.current?.hide()}
                        />
                    );

                    tippyInstance.current.show();
                }
            }, 100);
        }, [task, parentId, onAddTask, onAddTaskAfter, onOpenSolveModal, handleUpdateTask]);

        useEffect(() => {
            const contentDiv = aiContentRef.current;
            if (!contentDiv) return;

            contentDiv.addEventListener('mouseup', handleSelectionChange);
            contentDiv.addEventListener('touchend', handleSelectionChange);
            return () => {
                contentDiv.removeEventListener('mouseup', handleSelectionChange);
                contentDiv.removeEventListener('touchend', handleSelectionChange);
                if (tippyInstance.current && tippyInstance.current.state.isShown) {
                    tippyInstance.current.hide();
                }
            };
        }, [handleSelectionChange]);

        return (<div className="ml-4 pl-4 border-l-2 border-slate-200 dark:border-slate-700 relative group"> <button onClick={() => onAddTaskAfter(task.id, parentId)} className="absolute -bottom-3 left-1/2 -translate-x-1/2 w-6 h-6 flex items-center justify-center bg-slate-200 dark:bg-slate-700 text-slate-500 dark:text-slate-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity z-10 hover:bg-indigo-500 hover:text-white" aria-label="Aufgabe danach hinzufügen"> <Plus size={16} /> </button> <div className="bg-white dark:bg-slate-800 p-4 rounded-lg shadow-sm mb-4 transition-all hover:shadow-md hover:border-slate-300 dark:hover:border-slate-600 border border-transparent"> <div className="flex items-start justify-between"> <div className="flex-grow"> {task.isEditing ? (<input type="text" value={tempTitle} onChange={(e) => setTempTitle(e.target.value)} onBlur={handleTitleBlur} onKeyDown={(e) => e.key === 'Enter' && handleTitleBlur()} autoFocus className="w-full bg-transparent text-lg font-semibold text-slate-800 dark:text-slate-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md px-2 py-1" />) : (<h3 onClick={() => handleUpdateTask(task.id, { isEditing: true })} className="text-lg font-semibold text-slate-800 dark:text-slate-100 cursor-pointer w-full"><span className="text-indigo-500 dark:text-indigo-400 mr-2">{numbering}</span>{task.title}</h3>)} </div> <div className="flex items-center space-x-2 flex-shrink-0 ml-4"> {isBreakingDown ? (<div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>) : (<> <button onClick={() => handleBreakdown(task.id, task.title, task.description)} title="In Teilschritte zerlegen" className="p-1 text-slate-500 dark:text-slate-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"><ChevronDown size={20} /></button> <button onClick={() => onOpenSolveModal(task)} title="Mit KI lösen" className="p-1 text-slate-500 dark:text-slate-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"><Sparkles size={20} /></button> <button onClick={() => handleDeleteTask(task.id)} title="Löschen" className="p-1 text-slate-500 dark:text-slate-400 hover:text-red-600 dark:hover:text-red-500 transition-colors"><Trash2 size={20} /></button> </>)} </div> </div> <div className="mt-1 pl-8"> {task.isDescriptionEditing ? (<textarea value={tempDescription} onChange={(e) => setTempDescription(e.target.value)} onBlur={handleDescriptionBlur} autoFocus className="w-full bg-slate-100 dark:bg-slate-700 text-sm text-slate-600 dark:text-slate-300 p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" rows="2" />) : (<p onClick={() => { setTempDescription(task.description); handleUpdateTask(task.id, { isDescriptionEditing: true }); }} className="text-sm text-slate-500 dark:text-slate-400 cursor-pointer italic hover:text-slate-600 dark:hover:text-slate-300"> {task.description || "Beschreibung hinzufügen..."} </p>)} </div> {(isElaborating || task.aiContent) && (<div className="mt-4 bg-slate-50 dark:bg-slate-900/70 rounded-lg border border-slate-200 dark:border-slate-700 shadow-inner"> <div className="flex justify-between items-center p-3 cursor-pointer bg-slate-100 dark:bg-slate-800/50 rounded-t-lg hover:bg-slate-200 dark:hover:bg-slate-700/50 transition-colors" onClick={() => setIsAiContentVisible(!isAiContentVisible)} > <h4 className="font-semibold text-sm text-indigo-600 dark:text-indigo-400 flex items-center gap-2"> <Sparkles size={16} /> KI-Ausarbeitung </h4> <ChevronDown size={20} className={`text-slate-500 transform transition-transform duration-300 ${isAiContentVisible ? 'rotate-180' : ''}`} /> </div> <div className={`grid transition-[grid-template-rows] duration-500 ease-in-out ${isAiContentVisible ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'}`}> <div className="overflow-hidden"> <div className="p-4 border-t border-slate-200 dark:border-slate-700"> {isElaborating && !task.aiContent ? (<div className="flex flex-col items-center justify-center text-center py-8"> <BrainCircuit size={32} className="text-indigo-500 dark:text-indigo-400 animate-pulse" /> <h4 className="font-semibold text-sm text-indigo-600 dark:text-indigo-400 mt-3">KI-Ausarbeitung wird erstellt...</h4> <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">Sie können in der Zwischenzeit weiterarbeiten.</p> </div>) : task.isAiContentEditing ? (<textarea value={tempAiContent} onChange={(e) => setTempAiContent(e.target.value)} className="w-full h-48 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md p-2 border border-slate-300 dark:border-slate-600" />) : (<div ref={aiContentRef} className="prose prose-sm dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: decodeHtmlEntities(task.aiContent) }} />)} {!isElaborating && task.aiContent && (<div className="flex justify-end mt-4 space-x-3"> {task.isAiContentEditing ? (<> <button onClick={() => handleUpdateTask(task.id, { aiContent: tempAiContent, isAiContentEditing: false })} className="flex items-center space-x-1.5 text-sm text-slate-600 dark:text-slate-300 hover:text-indigo-600 dark:hover:text-indigo-400 font-semibold px-3 py-1 rounded-md bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors"><Save size={14} /><span>Speichern</span></button> <button onClick={handleSaveAndRework} className="flex items-center space-x-1.5 text-sm text-white bg-indigo-600 hover:bg-indigo-700 font-semibold px-3 py-1 rounded-md transition-colors"><BrainCircuit size={14} /><span>Speichern & KI-Überarbeitung</span></button> </>) : (<button onClick={() => { setTempAiContent(task.aiContent); handleUpdateTask(task.id, { isAiContentEditing: true }); }} className="flex items-center space-x-1 text-sm text-slate-600 dark:text-slate-400 hover:underline"><Edit size={14} /><span>Bearbeiten</span></button>)} </div>)} </div> </div> </div> </div>)} {task.subtasks && task.subtasks.length > 0 && (<div className="mt-4"> <TaskList tasks={task.subtasks} level={level + 1} parentId={task.id} onAddTask={onAddTask} onAddTaskAfter={onAddTaskAfter} onOpenSolveModal={onOpenSolveModal} numberingPrefix={`${numbering}.`} /> </div>)} <button onClick={() => handleAddTask(task.id)} className="mt-2 flex items-center space-x-2 text-sm text-slate-500 dark:text-slate-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"><Plus size={16} /><span>Unteraufgabe hinzufügen</span></button> </div> </div>);
    };
    const TaskList = ({ tasks, level = 0, parentId = null, onAddTask, onAddTaskAfter, onOpenSolveModal, numberingPrefix = '' }) => { return (<div> {tasks.map((task, index) => (<TaskItem key={task.id} task={task} level={level} parentId={parentId} onAddTask={onAddTask} onAddTaskAfter={onAddTaskAfter} onOpenSolveModal={onOpenSolveModal} numbering={`${numberingPrefix}${index + 1}`} />))} </div>); };

    return (
        <div className="bg-slate-100 dark:bg-slate-900 min-h-screen font-sans text-slate-900 dark:text-slate-100 transition-colors duration-300">
            <style>{` 
                 @keyframes breathing { 
                     0%, 100% { transform: scale(1); } 
                     50% { transform: scale(1.15); } 
                 } 
                 .animate-breathing { 
                     animation: breathing 2.5s ease-in-out infinite; 
                 } 
                 .tippy-box[data-theme~='light-border'] { 
                     background-color: #1e293b; /* bg-slate-800 */ 
                     border: 1px solid #475569; /* border-slate-600 */ 
                     color: white; 
                 } 
                 .tippy-arrow { 
                     color: #1e293b; 
                 } 
             `}</style>
            {/* Modal für Hilfe */}
            {isHelpModalOpen && (
                <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
                    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl p-6 w-full max-w-2xl border dark:border-slate-700 max-h-[90vh] overflow-y-auto">
                        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-slate-800 py-2">
                            <h3 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center gap-2"> <HelpCircle className="text-indigo-500 dark:text-indigo-400" /> Anleitung</h3>
                            <button onClick={() => setIsHelpModalOpen(false)} className="p-1 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700">
                                <X size={24} />
                            </button>
                        </div>
                        <div className="text-slate-600 dark:text-slate-300 space-y-6">
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">1. Projekt starten</h4>
                                <p>Geben Sie Ihr großes Ziel in das Haupt-Eingabefeld ein. Fügen Sie optional eine detailliertere Beschreibung hinzu. Klicken Sie dann auf <span className="font-semibold text-slate-800 dark:text-white">"Projekt starten"</span>. Die KI erstellt für Sie die ersten großen Aufgabenblöcke.</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">2. Aufgaben zerlegen (<ChevronDown size={16} className="inline-block" />)</h4>
                                <p>Jede große Idee besteht aus kleinen Schritten. Klicken Sie auf den <ChevronDown size={16} className="inline-block" />-Pfeil neben einer Aufgabe, um sie von der KI in detaillierte Unteraufgaben zerlegen zu lassen. Dies können Sie beliebig oft wiederholen, um tief ins Detail zu gehen.</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">3. Aufgaben ausarbeiten lassen (<Sparkles size={16} className="inline-block" />)</h4>
                                <p>Wenn Sie für eine Aufgabe einen konkreten Plan, eine Anleitung oder einen Text benötigen, klicken Sie auf das <Sparkles size={16} className="inline-block" />-Symbol. Daraufhin öffnet sich ein Fenster, in dem Sie der KI <span className="font-semibold text-slate-800 dark:text-white">optionale Zusatzanweisungen</span> geben können, um das Ergebnis noch besser zu machen.</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">4. Interaktiv arbeiten & bearbeiten (<Edit size={16} className="inline-block" />)</h4>
                                <p>Diese App ist dynamisch. Sie können jederzeit Titel, Beschreibungen und sogar die von der KI generierten Texte bearbeiten. Klicken Sie einfach auf den entsprechenden Text. Nach der Bearbeitung eines KI-Textes haben Sie die Wahl: Entweder nur Ihre Änderungen <span className="font-semibold text-slate-800 dark:text-white">speichern</span> oder die KI bitten, Ihre Version zu <span className="font-semibold text-slate-800 dark:text-white">überarbeiten</span>.</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">NEU: Text markieren & umwandeln</h4>
                                <p>Markieren Sie einen beliebigen Text im von der KI erstellten Inhalt. Es erscheint ein Menü, mit dem Sie den markierten Text direkt in eine neue <span className="font-semibold text-slate-800 dark:text-white">Unteraufgabe</span> oder eine neue <span className="font-semibold text-slate-800 dark:text-white">gleichrangige Aufgabe</span> umwandeln können. Oder Sie lassen genau diesen Abschnitt von der KI <span className="font-semibold text-slate-800 dark:text-white">detaillierter ausarbeiten</span>.</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">5. Die KI-Kontext-Strategie (<Sparkles size={16} className="inline-block" />)</h4>
                                <p>Eine KI vergisst leicht den Gesamtkontext. Um das zu verhindern, können Sie hier einstellen, wie viele "Erinnerungen" die KI erhält. Mehr Kontext führt zu relevanteren Ergebnissen, kann aber einen Moment länger dauern. Experimentieren Sie, um die beste Einstellung für Ihr Projekt zu finden!</p>
                            </div>
                            <div>
                                <h4 className="font-semibold text-lg text-indigo-600 dark:text-indigo-400 mb-2">6. Exportieren (<FileDown size={16} className="inline-block" />)</h4>
                                <p>Wenn Ihr Plan fertig ist, können Sie ihn ganz einfach als PDF, Markdown-Datei oder CSV-Tabelle exportieren, um ihn zu teilen oder in anderen Programmen weiterzuverwenden.</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Modal für Zusatz-Prompt */}
            {solveModalState.step !== 'closed' && (<div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4"> <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl p-6 w-full max-w-lg border dark:border-slate-700 max-h-[90vh] overflow-y-auto"> <div className="flex justify-between items-center mb-4"> <h3 className="text-xl font-bold text-slate-800 dark:text-white">Aufgabe lösen</h3> <button onClick={handleCloseSolveModal} className="p-1 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200 dark:hover:bg-slate-700"> <X size={24} /> </button> </div> <p className="text-slate-600 dark:text-slate-400 mb-4 text-sm font-semibold">Aufgabe: <span className="text-indigo-600 dark:text-indigo-400">{solveModalState.task?.title}</span></p> {solveModalState.step === 'choice' && (<> <p className="text-slate-600 dark:text-slate-400 mb-4">Wie soll die KI vorgehen?</p> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <button onClick={handleCopilotSelect} className="p-4 rounded-lg bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors text-left"> <div className="flex items-center gap-3"> <User className="text-indigo-500" size={24} /> <div> <p className="font-bold text-slate-800 dark:text-white">Co-Pilot</p> <p className="text-xs text-slate-500 dark:text-slate-400">KI stellt Rückfragen für eine präzise Lösung.</p> </div> </div> </button> <button onClick={handleAutopilotSelect} className="p-4 rounded-lg bg-slate-100 dark:bg-slate-700 hover:bg-slate-200 dark:hover:bg-slate-600 transition-colors text-left"> <div className="flex items-center gap-3"> <Bot className="text-indigo-500" size={24} /> <div> <p className="font-bold text-slate-800 dark:text-white">Autopilot</p> <p className="text-xs text-slate-500 dark:text-slate-400">KI trifft Annahmen und legt sofort los.</p> </div> </div> </button> </div> </>)} {solveModalState.step === 'autopilot_prompt' && (<> <p className="text-slate-600 dark:text-slate-400 mb-2">Geben Sie hier optional Details an:</p> <textarea value={solveModalState.additionalPrompt} onChange={(e) => setSolveModalState(prev => ({ ...prev, additionalPrompt: e.target.value }))} placeholder="Optional: z.B. 'Fasse dich kurz', 'Erstelle eine Tabelle'..." className="w-full h-28 p-3 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition" /> <div className="mt-4"> <p className="text-sm text-slate-500 dark:text-slate-400 mb-2">Oder wählen Sie eine Anregung:</p> <div className="flex flex-wrap gap-2"> {exampleAdditionalPrompts.map((prompt, index) => (<button key={index} onClick={() => setSolveModalState(prev => ({ ...prev, additionalPrompt: prev.additionalPrompt ? `${prev.additionalPrompt} ${prompt}` : prompt }))} className="bg-slate-200 dark:bg-slate-700 text-xs text-slate-700 dark:text-slate-300 px-2 py-1 rounded-full hover:bg-indigo-500 hover:text-white dark:hover:text-white transition-colors"> {prompt} </button>))} </div> </div> <div className="flex justify-end gap-4 mt-6"> <button onClick={handleCloseSolveModal} className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold px-4 py-2 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">Abbrechen</button> <button onClick={handleConfirmAutopilot} className="flex items-center justify-center bg-indigo-600 text-white font-semibold py-2 px-5 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition"> <Sparkles className="mr-2" size={20} /> Autopilot starten </button> </div> </>)} {solveModalState.step === 'copilot_asking' && (<div className="text-center py-8"> <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto"></div> <p className="mt-4 text-slate-500 dark:text-slate-400">KI analysiert die Aufgabe und stellt Rückfragen...</p> </div>)} {solveModalState.step === 'copilot_answering' && (<> <p className="text-slate-600 dark:text-slate-400 mb-4">Bitte beantworten Sie die folgenden Fragen der KI:</p> <div className="space-y-4"> {solveModalState.questions.map((q, i) => (<div key={i}> <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">{q}</label> <input type="text" onChange={(e) => setSolveModalState(prev => ({ ...prev, answers: { ...prev.answers, [q]: e.target.value } }))} className="w-full p-2 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition" /> </div>))} </div> <div className="flex justify-end gap-4 mt-6"> <button onClick={handleCloseSolveModal} className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white font-semibold px-4 py-2 rounded-lg hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors">Abbrechen</button> <button onClick={handleConfirmCopilot} className="flex items-center justify-center bg-indigo-600 text-white font-semibold py-2 px-5 rounded-lg shadow-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition"> <Sparkles className="mr-2" size={20} /> Aufgabe mit Antworten lösen </button> </div> </>)} </div> </div>)}

            <div className="container mx-auto p-4 md:p-8">
                <header className="relative text-center mb-8">
                    <div className="absolute top-0 right-0 hidden sm:flex items-center gap-2">
                        <Tooltip content="Entwicklung unterstützen">
                            <a href="https://paypal.me/VeniceWaveRecords" target="_blank" rel="noopener noreferrer" className="p-2 block rounded-full bg-slate-200 dark:bg-slate-700 text-red-500 dark:text-red-400 hover:bg-slate-300 dark:hover:bg-slate-600 hover:text-red-500 transition-colors">
                                <Heart size={24} className="animate-breathing" />
                            </a>
                        </Tooltip>
                        <Tooltip content="Hilfe">
                            <button onClick={() => setIsHelpModalOpen(true)} className="p-2 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors" aria-label="Hilfe anzeigen">
                                <HelpCircle size={24} />
                            </button>
                        </Tooltip>
                        <Tooltip content="Modus wechseln">
                            <button onClick={toggleTheme} className="p-2 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors" aria-label="Toggle Dark Mode">
                                {theme === 'light' ? <Moon size={24} /> : <Sun size={24} />}
                            </button>
                        </Tooltip>
                    </div>
                    <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-slate-800 dark:text-white pt-12 sm:pt-0">
                        <BrainCircuit className="inline-block mr-3 text-indigo-500" size={48} />
                        KI Projekt-Planer
                    </h1>
                    <div className="sm:hidden flex justify-center items-center gap-4 mt-4">
                        <Tooltip content="Entwicklung unterstützen">
                            <a href="https://paypal.me/VeniceWaveRecords" target="_blank" rel="noopener noreferrer" className="p-2 block rounded-full bg-slate-200 dark:bg-slate-700 text-red-500 dark:text-red-400 hover:bg-slate-300 dark:hover:bg-slate-600 hover:text-red-500 transition-colors">
                                <Heart size={24} className="animate-breathing" />
                            </a>
                        </Tooltip>
                        <Tooltip content="Hilfe">
                            <button onClick={() => setIsHelpModalOpen(true)} className="p-2 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors" aria-label="Hilfe anzeigen">
                                <HelpCircle size={24} />
                            </button>
                        </Tooltip>
                        <Tooltip content="Modus wechseln">
                            <button onClick={toggleTheme} className="p-2 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-300 dark:hover:bg-slate-600 transition-colors" aria-label="Toggle Dark Mode">
                                {theme === 'light' ? <Moon size={24} /> : <Sun size={24} />}
                            </button>
                        </Tooltip>
                    </div>
                    <p className="mt-4 text-lg text-slate-600 dark:text-slate-400">{motivation}</p>
                </header>

                <main>
                    {/* NEUER BEREICH: Kontext-Strategie */}
                    <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg mb-8 border border-slate-200 dark:border-indigo-500/30">
                        <h2 className="text-2xl font-semibold mb-4 text-indigo-600 dark:text-indigo-400 flex items-center gap-2">
                            <Sparkles size={24} />
                            KI-Kontext-Strategie
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Eine KI vergisst leicht das große Ganze, wenn sie sich auf eine kleine Detailaufgabe konzentriert. Dieses System wurde entwickelt, um genau das zu verhindern. Indem Sie hier eine Kontext-Strategie auswählen, geben Sie der KI bei jeder Anfrage die nötigen "Erinnerungen" mit. So bleiben die Ergebnisse relevant und auf Ihr Gesamtziel ausgerichtet.
                        </p>
                        <div className="space-y-3">
                            <label className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" checked={contextStrategies.strategy2} onChange={() => handleStrategyChange('strategy2')} className="h-5 w-5 rounded bg-slate-200 dark:bg-slate-700 border-slate-400 dark:border-slate-500 text-indigo-600 focus:ring-indigo-500" />
                                <span className="font-semibold text-slate-700 dark:text-slate-200">Intelligente Zusammenfassung (Standard)</span>
                                <Tooltip content="Die KI erhält das Hauptziel und die direkte übergeordnete Aufgabe als Kontext. Dies ist ein guter Kompromiss zwischen Geschwindigkeit und Genauigkeit.">
                                    <Info size={16} className="text-slate-500" />
                                </Tooltip>
                            </label>
                            <label className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" checked={contextStrategies.strategy1} onChange={() => handleStrategyChange('strategy1')} className="h-5 w-5 rounded bg-slate-200 dark:bg-slate-700 border-slate-400 dark:border-slate-500 text-indigo-600 focus:ring-indigo-500" />
                                <span className="font-semibold text-slate-700 dark:text-slate-200">Aufgaben-Pfad</span>
                                <Tooltip content='Die KI sieht den kompletten "Brotkrümel"-Pfad von der Hauptaufgabe bis zur aktuellen Unteraufgabe. Sehr präzise, um die genaue Position im Projekt zu verstehen.'>
                                    <Info size={16} className="text-slate-500" />
                                </Tooltip>
                            </label>
                            <label className="flex items-center space-x-3 cursor-pointer">
                                <input type="checkbox" checked={contextStrategies.strategy3} onChange={() => handleStrategyChange('strategy3')} className="h-5 w-5 rounded bg-slate-200 dark:bg-slate-700 border-slate-400 dark:border-slate-500 text-indigo-600 focus:ring-indigo-500" />
                                <span className="font-semibold text-slate-700 dark:text-slate-200">Kompletter Projekt-Bauplan</span>
                                <Tooltip content="Die KI erhält die gesamte Projektstruktur als Inhaltsverzeichnis. Maximaler Kontext, kann aber bei sehr großen Projekten zu längeren Antwortzeiten führen.">
                                    <Info size={16} className="text-slate-500" />
                                </Tooltip>
                            </label>
                        </div>
                    </div>


                    <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-lg mb-8 border border-slate-200 dark:border-slate-700">
                        <h2 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-100">Ihr großes Ziel</h2>
                        <div className="flex flex-col gap-4">
                            <div className="flex flex-col sm:flex-row gap-4">
                                <AutoGrowingTextarea
                                    value={mainProject}
                                    onChange={(e) => setMainProject(e.target.value)}
                                    onKeyDown={(e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); startProject(); } }}
                                    placeholder="z.B. eine Weltreise planen, eine App entwickeln..."
                                    className="flex-grow p-3 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition resize-none overflow-hidden"
                                />
                                <button onClick={startProject} disabled={loading['main']} className="hidden sm:flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-slate-900 transition-all duration-300 ease-in-out disabled:from-slate-400 disabled:to-slate-500 disabled:cursor-not-allowed">
                                    {loading['main'] ? (<><div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>Analysiere...</>) : (<><Sparkles className="mr-2" size={20} />Projekt starten</>)}
                                </button>
                            </div>
                            <div>
                                <textarea value={mainProjectDescription} onChange={(e) => setMainProjectDescription(e.target.value)} placeholder="Optionale Beschreibung: Fügen Sie hier weitere Details zu Ihrem Ziel hinzu..." className="w-full p-3 bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-white border-2 border-slate-300 dark:border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition" rows="4" />
                            </div>
                            <button onClick={startProject} disabled={loading['main']} className="sm:hidden flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-slate-900 transition-all duration-300 ease-in-out disabled:from-slate-400 disabled:to-slate-500 disabled:cursor-not-allowed">
                                {loading['main'] ? (<><div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>Analysiere...</>) : (<><Sparkles className="mr-2" size={20} />Projekt starten</>)}
                            </button>
                        </div>
                        <div className="mt-4 pt-4 border-t border-dashed border-slate-200 dark:border-slate-700">
                            <button onClick={() => setIsExamplesVisible(!isExamplesVisible)} className="text-sm text-indigo-600 dark:text-indigo-400 hover:underline flex items-center gap-1 mx-auto">
                                {isExamplesVisible ? <EyeOff size={16} /> : <Eye size={16} />}
                                {isExamplesVisible ? 'Beispiele verbergen' : 'Beispiele anzeigen'}
                            </button>
                        </div>
                        <div className={`transition-all duration-500 ease-in-out overflow-hidden ${isExamplesVisible ? 'max-h-48 overflow-y-auto mt-4' : 'max-h-0'}`}>
                            <div className="text-center">
                                <div className="flex flex-wrap gap-2 justify-center p-1">
                                    {exampleProjects.map((example, index) => (
                                        <button key={index} onClick={() => { setMainProject(example); setMainProjectDescription(''); }} className="bg-slate-200 dark:bg-slate-700 text-sm text-slate-700 dark:text-slate-300 px-3 py-1 rounded-full hover:bg-indigo-500 hover:text-white dark:hover:text-white transition-colors">
                                            {example}
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="task-container" ref={taskContainerRef}>
                        <TaskList tasks={tasks} onAddTask={handleAddTask} onAddTaskAfter={handleAddTaskAfter} onOpenSolveModal={handleOpenSolveModal} />
                        {tasks.length > 0 && (
                            <div className="flex justify-center mt-4">
                                <button onClick={() => handleAddTask(null)} className="flex items-center space-x-2 text-sm text-slate-500 dark:text-slate-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors bg-white dark:bg-slate-800 px-4 py-2 rounded-full shadow-sm border border-slate-200 dark:border-slate-700">
                                    <Plus size={16} /><span>Hauptaufgabe hinzufügen</span>
                                </button>
                            </div>
                        )}
                    </div>

                    {tasks.length > 0 && (
                        <div className="mt-12 text-center">
                            <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-300 mb-4">Projekt exportieren</h3>
                            <div className="flex justify-center items-center gap-4">
                                <button onClick={handleExportPDF} disabled={!libsLoaded} title={!libsLoaded ? "Export-Funktionen werden geladen..." : "Als PDF exportieren"} className="flex items-center gap-2 bg-slate-200 dark:bg-slate-700 hover:bg-red-800 text-slate-800 dark:text-white font-bold py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"> <FileDown size={18} /> PDF </button>
                                <button onClick={handleExportMD} disabled={!libsLoaded} title={!libsLoaded ? "Export-Funktionen werden geladen..." : "Als Markdown exportieren"} className="flex items-center gap-2 bg-slate-200 dark:bg-slate-700 hover:bg-gray-600 text-slate-800 dark:text-white font-bold py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"> <FileDown size={18} /> Markdown </button>
                                <button onClick={handleExportCSV} disabled={!libsLoaded} title={!libsLoaded ? "Export-Funktionen werden geladen..." : "Als CSV exportieren"} className="flex items-center gap-2 bg-slate-200 dark:bg-slate-700 hover:bg-green-800 text-slate-800 dark:text-white font-bold py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"> <FileDown size={18} /> CSV </button>
                            </div>
                        </div>
                    )}
                </main>

                <footer className="text-center mt-16 text-sm text-slate-500 dark:text-slate-400 space-y-3">
                    <p className="text-lg text-slate-600 dark:text-slate-400">Gemeinsam verwandeln wir Ideen in Imperien.</p>
                    <p className="flex items-center justify-center gap-2 text-slate-500 dark:text-slate-500">
                        Entwickelt mit <Heart size={16} className="text-red-500 inline-block" /> von Murat Eren & künstlicher Intelligenz.
                    </p>
                </footer>
            </div>
        </div>
    );
};

export default App;
