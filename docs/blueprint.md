# **App Name**: Project Weaver

## Core Features:

- Automated Task Decomposition: Automatically decomposes the main project goal into a hierarchy of actionable tasks and subtasks, leveraging AI to suggest logical steps.
- Interactive Project Customization: Provides an intuitive drag-and-drop interface for users to manually adjust task dependencies, reprioritize items, and fine-tune project workflows.
- AI Content Generation: For any selected task or subtask, uses an AI tool to elaborate and produce a helpful document incorporating existing text or other info. Provides rich HTML output, including Tailwind CSS for appealing display.
- Collaborative Task Management: Facilitates team collaboration through shared project boards with real-time updates, allowing users to assign tasks to specific team members.
- Contextual Knowledge Access: Seamlessly integrates with external knowledge repositories, pulling in relevant documentation, best practices, and templates to guide users through task execution.
- Project Export: Download project as CSV, Markdown, or PDF to enable further processing by external programs.
- AI Selection-Based Menu: Interactive AI context menu when content is highlighted to either create a subtask with it, a task, refine/elaborate, or delete text.

## Style Guidelines:

- Primary color: Indigo (#40A5E9) for focus and clarity.
- Background color: Light gray (#1E1B26) for a clean, minimal feel.
- Accent color: Purple (#C758D1) to highlight interactive elements and CTAs.
- Body and headline font: 'Inter', sans-serif, for a machined, objective, neutral look.
- Code font: 'Source Code Pro' for displaying code snippets.
- Use consistent, modern icons from Lucide to represent common actions and categories.
- Clean, card-based layout with intuitive hierarchy and clear task dependency visualization.